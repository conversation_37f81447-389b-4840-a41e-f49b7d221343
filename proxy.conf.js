// support yarn
let selector = process.env['npm_lifecycle_event'] || 'start';

const apiTarget = {
  'start'      : 'https://prod.borusancat.com/lgnd/',
  'start:stage': 'https://prod.borusancat.com/lgnq/',
  'start:prod' : 'https://prod.borusancat.com/lgnp/',
};

const rewrite = {
  // 'start': {pathRewrite: {'^/api': ''}}
};

const modeSelector = (pathSource) => {
  return pathSource[selector];
};

const PROXY_CONFIG = [
  {
    context     : [
      '/api',
    ],
    target      : `${modeSelector(apiTarget)}`,
    secure      : false,
    changeOrigin: true,
    logLevel    : 'debug',
    onProxyRes  : function(proxyRes, req, res) {
      proxyRes.headers['Access-Control-Allow-Origin'] = '*';
      proxyRes.headers['Access-Control-Allow-Headers'] = '*';
    },
  },
];

PROXY_CONFIG[0] = { ...rewrite[selector], ...PROXY_CONFIG[0] };
module.exports = PROXY_CONFIG;
