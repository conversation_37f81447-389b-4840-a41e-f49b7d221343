# BOOM 360 Harici sayfalar log ve event gönderimi

## <PERSON><PERSON><PERSON>
Aşağıdaki kodun siteye dahil edilmesi yeterli olacaktır.

DEV ortamı için
```
<script src="https://prod.borusancat.com/lgnd/portaluimobile/assets/js/boom-action.js"></script>
```
 STAGE ortamı için
```
<script src="https://prod.borusancat.com/lgnq/portaluimobile/assets/js/boom-action.js"></script>
```
 PROD ortamı için
```
<script src="https://prod.borusancat.com/lgnp/portaluimobile/assets/js/boom-action.js"></script>
```


## Log G<PERSON>nderme
```logAction(SECTION, SUBSECTION, PAYLOAD);```
örnek:
```
logAction('HOME_PAGE', 'LOG_BUTTON', {
  buttonId: 'test'
});
```

## Telefon İle Arama İşlevi

Kullanıcı telefon araması gibi aksiyonlar başlatabilir. 'tel:' gibi işlevlerin kaldırılıp yerine bu bu butondaki
eventler çağrılmalı. Bu tür native aksiyonlarda preventDeault yapılmalı

```logAction(NUMBER);```
örnek:
```
 // <a href="tel:433453454" onclick="callPhone(event)">Call Phone</a>

  function callPhone(event) {
    // prevent default flow
    event.preventDefault();

    sendCallPhoneAction('5554443322');

    return false;
  }
```

## Kullanıcı bilgileri alma
İlgili script koda dahil edildiğinde ve site BOOM 360 uyguklaması içerisinde açıldığında kullancı bilgileri ototamtik olarak localStorage'a yazılır.

BOOM360 keyi ile localstoragedan JSON olarak okunabilir.

```
 const user = localStorage.getItem('BOOM360')
 
 JSON.parse(localStorage.BOOM360)
 /*
 {
    'id'         : '08ac34e0-cab8-4b05-9702-9fc26d69ac80',
    'firstName'  : 'Tuncay',
    'lastName'   : 'Akdeniz',
    'email'      : '<EMAIL>',
    'mobile'     : '5555555555',
    'username'   : '<EMAIL>',
    'customer'   : {
      'name'          : 'CUSTOMER',
      'countryCode'   : 'TR',
      'cityName'      : null,
      'customerNumber': 'S00000',
      'customerId'    : '6c16794a-560f-11ee-8c99-0242ac120002'
    },
    'region'     : null,
    'language'   : 'tr',
    'countryCode': 'TR'
  }
  */

 ```
