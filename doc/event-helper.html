<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <title>Boom 360 event examples</title>
</head>
<body>

<!-- use IN DEV-->
<script src="https://prod.borusancat.com/lgnd/portaluimobile/assets/js/boom-action.js"></script>

<!-- use IN STAGE-->
<!--<script src="https://prod.borusancat.com/lgnd/portaluimobile/assets/js/boom-action.js"></script>-->

<!-- use IN PROD-->
<!--<script src="https://prod.borusancat.com/lgnd/portaluimobile/assets/js/boom-action.js"></script>-->


<h1>Hello word</h1>
<p id="user"></p>

Kullanıcını yaptığı sayfa açma ve bazı tıklma aksiyonlarını bu event ile loglayabilirsiniz
<br>
<button onclick="sendLogEvent()">Send Log event</button>


<br>
Kullanıcı telefon araması gibi aksiyonlar başlatabilir. 'tel:' gibi işlevlerin kaldırılıp yerine bu bu butondaki
eventler çağrılmalı. Bu tür native aksiyonlarda preventDeault yapılmalı
<br>
<a href="tel:433453454" onclick="callPhone(event)">Call Phone</a>
<script type="text/javascript">
  // ACCESS USER DATA
  window.addEventListener('load', function() {
    // access user data
    document.getElementById('user').innerText = localStorage.getItem('BOOM360');

  });

  //SEND LOG EVENT
  function sendLogEvent() {
    logAction('HOME_PAGE', 'LOG_BUTTON', {
      buttonId: 'test'
    });
  }

  //SEND CALL PHONE EVENT
  function callPhone(event) {
    // prevent default flow
    event.preventDefault();

    sendCallPhoneAction('5554443322');

    return false;
  }

  // CHECK IF BOOM APP
  if (navigator.userAgent.includes('BOOM')) {
    // DO something
  }

</script>
</body>
</html>
