{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"mainui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}, "@schematics/angular:application": {"strict": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/mainui", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets", "src/web.config"], "styles": ["src/styles.scss"], "scripts": [], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/"]}}, "configurations": {"temp": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.temp.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/temp/"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "3kb", "maximumError": "5kb"}]}, "development": {"index": {"input": "src/environments/index.development.html", "output": "index.html"}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/development/"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "3kb", "maximumError": "5kb"}]}, "stage": {"index": {"input": "src/environments/index.stage.html", "output": "index.html"}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.stage.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/stage/"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "3kb", "maximumError": "5kb"}]}, "production": {"index": {"input": "src/environments/index.production.html", "output": "index.html"}, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}], "stylePreprocessorOptions": {"includePaths": ["src/style/", "src/environments/scss/production"]}, "optimization": true, "outputHashing": "all", "sourceMap": false, "namedChunks": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2MB", "maximumError": "4MB"}, {"type": "anyComponentStyle", "maximumWarning": "3kb", "maximumError": "5kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "mainui:build"}, "configurations": {"temp": {"browserTarget": "mainui:build:temp"}, "development": {"browserTarget": "mainui:build:development"}, "stage": {"browserTarget": "mainui:build:stage"}, "production": {"browserTarget": "mainui:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "mainui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.scss"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "mainui:serve"}, "configurations": {"temp": {"devServerTarget": "mainui:serve:temp"}, "development": {"devServerTarget": "mainui:serve:development"}, "stage": {"devServerTarget": "mainui:serve:stage"}, "production": {"devServerTarget": "mainui:serve:production"}}}}}}, "defaultProject": "main<PERSON>", "cli": {"defaultCollection": "@ngxs/schematics", "analytics": false}}