{"name": "main<PERSON>", "version": "0.0.1", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.js --host 0.0.0.0", "start:stage": "ng serve --proxy-config proxy.conf.js --host 0.0.0.0", "start:prod": "ng serve --proxy-config proxy.conf.js --host 0.0.0.0", "build": "ng build --outputHashing=all && npm run post-build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "post-build": "node ./build/post-build.js", "build-development": "ng build --aot true --buildOptimizer true --configuration=development --buildOptimizer true --base-href=/lgnd/portaluimobile/ --deployUrl=https://borusancat360devm.azureedge.net/portaluimobile/ && npm run post-build", "build-stage": "ng build --aot true --buildOptimizer true --configuration=stage --buildOptimizer true --base-href=/lgnq/portaluimobile/ --deployUrl=https://borusancat360qam.azureedge.net/portaluimobile/ && npm run post-build", "build-production": "ng build --aot true --buildOptimizer true --configuration=production --buildOptimizer true --base-href=/lgnp/portaluimobile/ --deployUrl=https://borusancat360prodm.azureedge.net/portaluimobile/ && npm run post-build", "build-temp": "ng build --aot true --buildOptimizer true --configuration=temp --buildOptimizer true --base-href=/lgnz/portaluimobile/ --deployUrl=https://borusancat360zm.azureedge.net/portaluimobile/ && npm run post-build"}, "private": true, "dependencies": {"@angular/animations": "~11.0.5", "@angular/common": "~11.0.5", "@angular/compiler": "~11.0.5", "@angular/core": "~11.0.5", "@angular/forms": "~11.0.5", "@angular/google-maps": "^11.2.0", "@angular/localize": "~11.0.5", "@angular/platform-browser": "~11.0.5", "@angular/platform-browser-dynamic": "~11.0.5", "@angular/router": "~11.0.5", "@ng-bootstrap/ng-bootstrap": "^8.0.0", "@ng-select/ng-select": "^5.0.14", "@ngx-translate/core": "^13.0.0", "@ngxs/logger-plugin": "^3.3.2", "@ngxs/router-plugin": "^3.7.1", "@ngxs/schematics": "0.0.1-alpha.5", "@ngxs/storage-plugin": "^3.7.1", "@ngxs/store": "^3.3.2", "angularx-qrcode": "11.0.0", "bootstrap": "^4.5.0", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "ngx-loading": "^8.0.0", "ngx-moment": "^5.0.0", "rxjs": "~6.6.0", "tslib": "^2.0.0", "zone.js": "~0.10.2", "@piumaz/pull-to-refresh": "^3.0.0"}, "devDependencies": {"@angular-devkit/build-angular": "~0.1100.5", "@angular/cli": "~11.0.5", "@angular/compiler-cli": "~11.0.5", "@ngxs/devtools-plugin": "^3.7.1", "@ngxs/schematics": "^0.0.1-alpha.5", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~3.6.0", "@types/node": "^12.11.1", "codelyzer": "^6.0.0", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~5.1.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "typescript": "~4.0.2"}}