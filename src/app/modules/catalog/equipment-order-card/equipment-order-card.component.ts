import { Component, Input, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import {
  AddToCartPopupAction,
  AddToShoppingCart,
  RemoveFromShoppingCart,
  ShoppingCartTotalPriceAction,
  UpdateShoppingCartAction,
} from '../../customer/state/catalog/catalog.actions';
import { Observable, Subject, timer } from 'rxjs';
import { CatalogState } from '../../customer/state/catalog/catalog.state';
import { switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-equipment-order-card',
  templateUrl: './equipment-order-card.component.html',
  styleUrls: ['./equipment-order-card.component.scss'],
})
export class EquipmentOrderCardComponent implements OnInit {
  @Input()
  product: any;
  @Input()
  inCart = false;
  @Input()
  amount = 1;
  @Input()
  amountBtn = false;
  @Input()
  productPrice: number;

  @Select(CatalogState.shoppingCart)
  shoppingCart$: Observable<any>;

  showAddToShoppingCartBtn = true;
  showUpdateCartAnimation = false;
  shoppingCart = [];
  inShoppingCart = false;
  popupTimeout= null

  constructor(private readonly store: Store) {}

  ngOnInit() {
    this.shoppingCart$.subscribe((data) => {
      const productInShoppingCart = data.find(p => p.productId === this.product.productId)
      this.inShoppingCart = productInShoppingCart;
      this.amount = productInShoppingCart?.amount || 1
    });
  }

  increaseAmount() {
    if (this.product.stockLimit > 1 || !this.product.stockLimit) {
      this.store.dispatch(new UpdateShoppingCartAction(this.product.productId, true));
      this.store.dispatch(new ShoppingCartTotalPriceAction());
      this.triggerAddToBasketPopup();
    }
  }

  decreaseAmount() {
    this.triggerAddToBasketPopup("_update_shopping_cart");
    
    if(this.amount === 1){
      this.store.dispatch(new RemoveFromShoppingCart(this.product.productId));
      return this.store.dispatch(new ShoppingCartTotalPriceAction());
    }

    this.store.dispatch(new UpdateShoppingCartAction(this.product.productId, false));
    return this.store.dispatch(new ShoppingCartTotalPriceAction());
    
  }

  addToBasket() {
    this.triggerAddToBasketPopup()

    this.store.dispatch(
      new AddToShoppingCart({ ...this.product, amount: this.amount })
    );
    this.store.dispatch(new ShoppingCartTotalPriceAction());
    this.showAddToShoppingCartBtn = false;
  }

  removeFromBasket() {
    this.triggerAddToBasketPopup("_update_shopping_cart");
    this.store.dispatch(new RemoveFromShoppingCart(this.product.productId));
    this.store.dispatch(new ShoppingCartTotalPriceAction());
  }

  calculateProductPrice(price, amount = 1) {
    return price * amount;
  }

  triggerAddToBasketPopup(text?: string){
    this.store.dispatch(new AddToCartPopupAction(true, text));

    if (this.popupTimeout) {
      clearTimeout(this.popupTimeout);
    }
  
    this.popupTimeout = setTimeout(() => {
      this.store.dispatch(new AddToCartPopupAction(false));
      this.popupTimeout = null;
    }, 2500);
  }

  // inShoppingCart(id){
  //   return this.shoppingCart.includes(p => p.productId === id);
  // }

  getCurrencyIcon(currency){
    return `icon-${currency === 'TRY' ? 'tl' : currency?.toLowerCase()}`;
  }
}
