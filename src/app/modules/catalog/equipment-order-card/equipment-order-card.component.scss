.option-product-card{
    width: 150px;
    max-width: 150px;
    height: 250px;
    background: #fff;
    padding: 0.5rem;
    //box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;
    border-radius: 0.5rem;

    .product-title-option{
        word-break: break-all;
        overflow: hidden;
        height: 60px;
        margin-bottom: .25rem;
        display: -webkit-box;
        -webkit-line-clamp: 3; 
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-word;
    }

    .img-container{
        //min-width: 140px;
        min-height: 110px;
        height: 110px;
        //width: 140px;
    }

    .top-info{
        flex: 1;
    }

    .product-alternative-price{
        text-decoration: line-through;
    }

    .product-img{
        border-radius: 8px;
    }

    .amount-btns-container{
        border: 1px solid #4A8EB0;
        width: 100%;
        border-radius: 6px;

        .btn-increase, .btn-decrease{
            width: 30px;
            height: 30px;
            border-radius: 5px;
            border: 0 !important;
        }

        .btn-increase{
            border-top-left-radius: 0;
            border-bottom-left-radius: 0;
        }

        .btn-decrease{
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .amount-info{
            color: #4A8EB0;
        }
    }

    .add-to-basket-btn{
        width: 100%;
        border: 1px solid #BABABA;
        padding: .25rem .5rem;
        color: #505050;
    }
}

.product-card-in-cart{
    width: 100%;
    max-width: 100%;
    margin-bottom: 1rem;
    border-radius: 8px;
    padding: .25rem;
    position: relative;
    background-color: #FBFBFB;
    height: unset;
    box-shadow: unset;

    .img-container{
        min-width: 110px;
        min-height: 90px;
        width: 110px;
        height: 90px;
    }

    .product-title{
        max-width: 110px;
    }

    .top-info{
        display: flex;
        align-items: center;
        gap: 1rem;
        flex: unset;
    }

    .product-infos{
        padding-right: .5rem;
    }

    .action-shopping-card-btns{
        display: flex;
        justify-content: space-between;
        align-items: center;

        .add-to-basket-btn{
            margin-top: 0 !important;
        }
    }

    .piece-info{
        position: absolute;
        bottom: 0rem;
        right: .5rem;
    }
    
    .remove-basket-btn{
        position: absolute;
        top: .5rem;
        right: .5rem;
    }

    .amount-btns-container-in-cart{
        width: 130px;
        max-width: 130px;
        height: 25px;
        margin-bottom: 0.25rem;

        .btn{
            height: 25px;
        }
    }
}

@keyframes slideInFromBottom {
    75%{
        opacity: 1;
    }
    100%{
        opacity: 0;
    }
}

@keyframes slideOutToTop {
    0% {
        transform: translateY(0);
        opacity: 1;
    }
    100% {
        transform: translateY(-50%);
        opacity: 0;
    }
}

.update-shopping-cart-info{
    display: flex;
    align-items: center;
    gap: .5rem;
    padding: .25rem .5rem;
    height: 32px;
    position: absolute;
    inset: 0;
    background-color: #fff;
    color: #505050;
    border-radius: 7px;
    z-index: 12312;
    animation: slideOutToTop 500ms ease-in-out;
}

.added-update-basket-animation{
    opacity: 1;
    animation: slideInFromBottom 500ms ease-in-out;
}

.icon-success {
    font-size: 16px;
    padding-top: 1px;
    background: -webkit-linear-gradient(#00EFD1, #00ACEA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}