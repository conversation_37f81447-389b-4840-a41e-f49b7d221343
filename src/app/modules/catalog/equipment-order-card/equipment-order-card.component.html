<div class="d-flex flex-column option-product-card" [ngClass]="{ 'product-card-in-cart': inCart }">
  <div class="top-info">
    <div class="img-container">
      <img
        class="product-img"
        [src]="product?.imageUrl"
        [alt]="product?.productName"
        width="100%"
        height="100%"
      />
    </div>
    <div class="product-infos">
      <div class="product-title font-size-13px" [ngClass]="{ 'product-title-option': !inCart }">
        {{product?.productName}}
      </div>
      <span class="product-types font-size-13px">
        {{product?.productDetail}}
      </span>
      <div *ngIf="!inCart" class="product-alternative-price font-size-13px">
        {{ product?.actualPrice | currency }}
      </div>
      <div class="h6 font-weight-bold product-price d-flex align-items-center" *ngIf="productPrice">
        <i class="icon mr-1" [ngClass]="getCurrencyIcon(product?.priceCurrency)"></i>
        {{ (inCart ? calculateProductPrice(productPrice, product?.amount) :  productPrice) | number }}
      </div>
      <div *ngIf="inCart" class="">
        <div *ngIf="inShoppingCart && amountBtn" class="amount-btns-container amount-btns-container-in-cart position-relative d-flex align-items-center">
          <button (click)="decreaseAmount()" class="btn p-0 btn-info btn-decrease">
            -
          </button>
          <span class="flex-grow-1 text-center amount-info font-size-13px">
            {{ amount }} {{ "_piece" | translate }}
          </span>
          <button (click)="increaseAmount()" class="btn p-0 btn-info btn-increase" [ngClass]="{'disabled': (product.stockLimit === amount || (amount === 1 && product?.relationProductType === 1))}" >
            +
          </button>
        </div>
      </div>
    </div>
  </div>
  <div *ngIf="!inCart" class="action-shopping-card-btns">
    <div *ngIf="inShoppingCart" class="amount-btns-container position-relative d-flex align-items-center">
      <button (click)="decreaseAmount()" class="btn p-0 btn-info btn-decrease">
        -
      </button>
      <span class="flex-grow-1 text-center amount-info font-size-13px">
        {{ amount }} {{ "_piece" | translate }}
      </span>
      <button (click)="increaseAmount()" class="btn p-0 btn-info btn-increase" [ngClass]="{'disabled': product.stockLimit === amount}" >
        +
      </button>
    </div>
    <button
      *ngIf="!inShoppingCart"
      (click)="addToBasket()"
      class="btn btn-sm add-to-basket-btn font-weight-bold"
    >
      {{ "_add_to_basket" | translate }}
    </button>
  </div>
  <button (click)="removeFromBasket()" *ngIf="inCart && !product.findProductId" class="btn p-0 text-info remove-basket-btn">
    {{ "_delete" | translate }}
  </button>
</div>
