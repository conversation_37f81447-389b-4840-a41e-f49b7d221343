<div
  class="equipment-order-unlogin-form-container form-container px-3"
  id="form-container"
  *ngIf="!formSendStatus"
>
  <app-info-box [title]="'_info' | translate">
    {{ "_offer_form_info" | translate }}
  </app-info-box>
  <form (submit)="onSubmitForm()" [formGroup]="form">
    <div class="form-group">
      <input
        [placeholder]="'_name' | translate"
        class="form-control form-control"
        formControlName="Name"
        type="text"
        maxlength="155"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        [placeholder]="'_surname' | translate"
        class="form-control form-control"
        formControlName="Surname"
        type="text"
        maxlength="155"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Surname) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        [placeholder]="'_company_name' | translate"
        class="form-control form-control"
        formControlName="CompanyName"
        type="text"
        maxlength="155"
      />

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        [placeholder]="'_phone' | translate"
        class="form-control form-control"
        formControlName="PhoneNumber"
        (input)="onInputPhone($event)"
        type="tel"
        maxlength="15"
      />

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.PhoneNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.PhoneNumber) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        appInputMaxLength
        [name]="'Email'"
        [placeholder]="'_email' | translate"
        class="form-control form-control"
        formControlName="Email"
        type="text"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Email) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        [placeholder]="'_company_phone_number' | translate"
        class="form-control form-control"
        formControlName="CompanyPhoneNumber"
        (input)="onInputPhone($event)"
        type="tel"
        maxlength="15"
      />

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyPhoneNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyPhoneNumber) | translate }}
      </div>
    </div>
    <div class="form-group">
      <ng-select
        class="service-drp"
        [searchable]="true"
        (change)="onChangeCountry($event)"
        *ngIf="!(countryListLoading$ | async)"
        [placeholder]="'_country' | translate"
        [clearable]="false"
        [dropdownPosition]="'bottom'"
        formControlName="CountryCode"
        #country
      >
        <ng-option
          *ngFor="
            let country of searchCity(countryList$ | async, country.searchTerm)
          "
          [value]="country.code"
          >{{ country.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CountryCode) | translate }}
      </div>
    </div>
    <div
      class="form-group"
      *ngIf="
        (!!form.controls['CountryCode'].value ||
          (cityList$ | async)?.length > 0) &&
        isActiveCountry(form.controls['CountryCode'].value)
      "
    >
      <ng-select
        class="service-drp"
        [searchable]="true"
        *ngIf="!(cityListLoading$ | async)"
        [placeholder]="'_city' | translate"
        [clearable]="false"
        [dropdownPosition]="'bottom'"
        formControlName="City"
        #city
      >
        <ng-option
          *ngFor="let city of searchCity(cityList, city.searchTerm)"
          [value]="city.name"
          >{{ city.name }}</ng-option
        >
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.City) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.City) | translate }}
      </div>
    </div>
    <div class="form-group">
      <textarea
        appInputMaxLength
        appFormScroll
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{
          'd-block':
            !form.get('Description').valid && form.get('Description').touched
        }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <app-agreement-list
      [form]="form"
      [formType]="agreementTypeEnum.QuotationRequest"
      [showAgreements]="true"
    ></app-agreement-list>
    <ng-content></ng-content>
    <button
    appClickLog
    [section]="'CATALOG'"
    [subsection]="'QUOTATION_SEND'"
    [disabled]="form.valid === false"
    [data]="{
      brand: equipment?.brand,
      productGroupId: equipment?.productGroupId,
      productId: equipment?.productId,
      title: equipment?.title || equipment?.productName
    }"
    class="btn btn-sm btn-warning text-white w-100 mt-3">
      {{ "_get_offer" | translate }}
    </button>
  </form>
</div>
<app-loader [show]="(countryListLoading$ | async) || loading"></app-loader>
<app-success-modal *ngIf="formSendStatus" [message]="'_successfully_send_form'">
  <button class="btn btn-sm btn-warning text-white  w-100" (click)="navigateBack()">
    {{ "_return_back" | translate }}
  </button>
</app-success-modal>
