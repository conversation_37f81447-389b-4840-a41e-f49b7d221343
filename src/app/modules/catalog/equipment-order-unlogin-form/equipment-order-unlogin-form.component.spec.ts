/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { EquipmentOrderUnloginFormComponent } from './equipment-order-unlogin-form.component';

describe('EquipmentOrderUnloginFormComponent', () => {
  let component: EquipmentOrderUnloginFormComponent;
  let fixture: ComponentFixture<EquipmentOrderUnloginFormComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EquipmentOrderUnloginFormComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EquipmentOrderUnloginFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
