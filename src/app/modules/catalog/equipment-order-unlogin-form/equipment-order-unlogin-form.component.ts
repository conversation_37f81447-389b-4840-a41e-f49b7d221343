import { Component, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Select, Store } from '@ngxs/store';
import { CustomValidator } from 'src/app/util/custom-validator';
import {
  getFormErrorMessage,
  isShowFormError,
  validateAllFormFields,
} from 'src/app/util/form-error.util';
import { DefinitionState } from '../../definition/state/definition/definition.state';
import { Observable, Subject, of } from 'rxjs';
import { Country } from '../../definition/model/country.model';
import { GetAllCountryListAction } from '../../definition/state/definition/definition.actions';
import { takeUntil } from 'rxjs/operators';
import { City } from '../../definition/model/city.model';
import { GetCityListAction } from '../../definition/state/definition/definition.actions';
import { CustomerService } from '../../customer/service/customer.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { EventEmitter } from '@angular/core';
import { AgreementTypeEnum } from '../../definition/enum/agreement-type.enum';
import { LogService } from 'src/app/shared/service/log.service';

@Component({
  selector: 'app-equipment-order-unlogin-form',
  templateUrl: './equipment-order-unlogin-form.component.html',
  styleUrls: ['./equipment-order-unlogin-form.component.scss'],
})
export class EquipmentOrderUnloginFormComponent implements OnInit, OnDestroy {
  @Input()
  equipment: any;

  @Input()
  shoppingCart: any;

  @Input()
  leasingFormBody: any;

  @Output()
  onSuccessSendForm: EventEmitter<any> = new EventEmitter<any>();

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  agreementTypeEnum = AgreementTypeEnum
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    CompanyName: new FormControl(null, [Validators.required]),
    PhoneNumber: new FormControl(null, [
      Validators.required,
      Validators.minLength(7),
    ]),
    CompanyPhoneNumber: new FormControl(null, [
      Validators.required,
      Validators.minLength(7),
    ]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    Description: new FormControl(null, []),
    CountryCode: new FormControl(null, [Validators.required]),
    Brand: new FormControl(null, []),
    City: new FormControl(null, [Validators.required]),
    CampaignPermission: new FormControl(false, []),
    // KvkkPermission: new FormControl(, [Validators.required])
  });

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;

  cityList: City[];
  loading = false;
  formSendStatus = false;

  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly frameMessageService: FrameMessageService,
    private readonly log: LogService
  ) {}

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.store.dispatch(new GetAllCountryListAction());

    this.cityList$.pipe(takeUntil(this.subscriptions$)).subscribe((data) => {
      this.cityList = data;
    });
  }

  onSubmitForm() {
    if (!this.form.valid) {
      return validateAllFormFields(this.form);
    }
    const { value } = this.form;

    const validShoppingCart = this.shoppingCart?.map((product) => ({
      ProductId: product?.productId,
      Count: product?.amount,
    }));

    const formBody = {
      Name: value.Name,
      Surname: value.Surname,
      CompanyName: value.CompanyName,
      PhoneNumber: value.PhoneNumber,
      CompanyPhoneNumber: value.CompanyPhoneNumber,
      Email: value.Email,
      Description: value.Description,
      CountryCode: value.CountryCode,
      Brand: this.equipment?.brand,
      Model: this.equipment?.productName,
      City: value?.City,
      KvkkPermission: true,
      CampaignPermission: value?.agreements?.CAMPAIGN_REMINDER,
      MainProductId: this.equipment?.productId,
      CurrencyUnit: this.leasingFormBody?.currencyUnit === 'TRY' ? 0 : 1,
      PaymentType: this.leasingFormBody ? 1 : 0,
      LeasingCalculation: this.leasingFormBody,
      Carts: this.shoppingCart?.length ? validShoppingCart : null,
    };

    this.loading = true;

    this.customerService
      .sendOrderForm(formBody, {}, 'form/equipment/quotation')
      .subscribe(
        (res) => {
          this.loading = false;
          if (res.code === 0) {
            this.formSendStatus = true;
            this.onSuccessSendForm.emit();
          }
        },
        () => {
          this.loading = false;
          this.formSendStatus = false;
        }
      );
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        .filter((x) => x.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (
            a.name.toLowerCase().indexOf(search) >
            b.name.toLowerCase().indexOf(search)
          ) {
            return 1;
          }
          if (
            a.name.toLowerCase().indexOf(search) <
            b.name.toLowerCase().indexOf(search)
          ) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  onChangeCountry(countryCode: string) {
    const selectedCountry = this.getCountryByCode(countryCode);
    console.log('countryChange');
    this.form.patchValue({ City: null });
    if (this.isActiveCountry(countryCode)) {
      this.form.controls.City.setValidators([Validators.required]);
      return this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.City.clearValidators();
      this.form.controls.City.updateValueAndValidity();
      console.log('city', countryCode);
      return of([]);
    }
  }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  navigateBack(){
    this.frameMessageService.sendMessage(FrameMessageEnum.formBack, { action: 'publicCategory', navigate: false });
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
