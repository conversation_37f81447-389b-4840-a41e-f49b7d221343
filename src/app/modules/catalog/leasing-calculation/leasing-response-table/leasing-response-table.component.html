<div class="fit-body overflow-auto">
  <table style="font-size: 12px" class="table table-striped">
    <thead>
      <tr class="text-center">
        <th class="font-size-11px">{{ "_month" | translate }}</th>
        <th class="font-size-11px">
          {{ "_monthly_payment" | translate }}
        </th>
        <th class="font-size-11px">
          {{ "_principal_payment" | translate }}
        </th>
        <th class="font-size-11px">
          {{ "_interest_payment" | translate }}
        </th>
        <th class="font-size-11px">
          {{ "_opening_balance" | translate }}
        </th>
        <th class="font-size-11px">
          {{ "_closing_balance" | translate }}
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let payment of table">
        <td class="text-center">{{ payment?.paymentNumber }}</td>
        <td class="text-left text-nowrap pr-2 font-size-11px">
          <i class="icon" [class]="getCurrencyIcon(icon)"></i>
          {{ payment?.monthlyPayment | number }}
        </td>
        <td class="text-left text-nowrap pr-2 font-size-11px">
          <i class="icon" [class]="getCurrencyIcon(icon)"></i>
          {{ payment?.principalPayment | number }}
        </td>
        <td class="text-left text-nowrap pr-2 font-size-11px">
          <i class="icon" [class]="getCurrencyIcon(icon)"></i>
          {{ payment?.interestPayment | number }}
        </td>
        <td class="text-left text-nowrap pr-2 font-size-11px">
          <i class="icon" [class]="getCurrencyIcon(icon)"></i>
          {{ payment?.openingBalance | number }}
        </td>
        <td class="text-left text-nowrap pr-2 font-size-11px">
          <i class="icon" [class]="getCurrencyIcon(icon)"></i>
          {{ payment?.closingBalance | number }}
        </td>
      </tr>
    </tbody>
  </table>
</div>
<div class="d-flex flex-row justify-content-between">
  <app-info-box [title]="'_info' | translate">
    <span
      [innerHTML]="'_leasing_info_text_summary' | translate | safeHtml"
    ></span>
  </app-info-box>
</div>
