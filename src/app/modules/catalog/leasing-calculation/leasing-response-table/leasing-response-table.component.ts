import { AfterViewInit, Component, ElementRef, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-leasing-response-table',
  templateUrl: './leasing-response-table.component.html',
  styleUrls: ['./leasing-response-table.component.scss']
})
export class LeasingResponseTableComponent implements OnInit, AfterViewInit {
  @Input()
  icon: string;

  @Input()
  table: any;

  constructor(
    private readonly elementRef: ElementRef,
  ) { }

  ngOnInit() {
  }


  getCurrencyIcon(currency){
    return `icon-${currency === 'TRY' ? 'tl' : 'euro'}`;
  }

  ngAfterViewInit(): void {
    const leasingDetailText = this.elementRef.nativeElement.querySelector('#leasingDetailText');
    const leasingReadMore = this.elementRef.nativeElement.querySelector('#leasingReadMore');
    const leasingCloseMore = this.elementRef.nativeElement.querySelector('#leasingCloseMore');
    if (leasingReadMore && leasingDetailText && leasingCloseMore) {
      leasingReadMore.addEventListener('click', () => {
        leasingReadMore.classList.add('d-none');
        leasingCloseMore.classList.remove('d-none');
        leasingDetailText.classList.remove('d-none');
      });
      leasingCloseMore.addEventListener('click', () => {
        leasingReadMore.classList.remove('d-none');
        leasingCloseMore.classList.add('d-none');
        leasingDetailText.classList.add('d-none');
      });
    }
  }
}
