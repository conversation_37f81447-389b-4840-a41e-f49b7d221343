@import "variable/bootstrap-variable";

label {
  margin-bottom: 0;
}

.btn-sm {
  padding: 0.27rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.5;
  border-radius: 8px;
}

.btn-group > .btn{
  position: unset;
}

.form-check{
  position: unset;
  overflow-wrap: anywhere;
}

.btn-check {
  position: absolute;
  clip: rect(0, 0, 0, 0);
  pointer-events: none;
}

.btn-group{
}

.btn-group .btn {
  border-radius: 6px;
}

.last-box{
  border-top-right-radius: 6px !important;
  border-bottom-right-radius: 6px !important;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:last-child):not(.dropdown-toggle) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.btn-group > .btn-group:not(:first-child) > .btn, .btn-group > .btn:nth-child(n+3), .btn-group > :not(.btn-check) + .btn {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.nav-back {
  font-size: 18px;
}

.icon.leasing-currency-icon, .percent-icon, .euro-icon{
  position: absolute;
  right: 0;
  top: 0;
  font-size: 18px;
  line-height: 14px;
  border: 1px solid #CBD7DD;
  height: 50px;
  width: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: #CBD7DD;
  color: #FFF;
}

.leasing-radio-button{
  color: #8E8E8E;
  background-color: #EEF2F4;
  background-clip: padding-box;
  border: 1px solid #D7E5EA;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.leasing-radio-button.selected{
  color: #FFF;
  background-color: var(--warning);
  background-clip: padding-box;
  border: 1px solid var(--warning);
}
.leasingForm {
}

.leasing{
  height: 100vh;
  overflow: auto;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
}

.calculated-leasing-payment-rate{
  min-height: 120px;
}

.due-date-box{
  width: 55px;
  max-width: 55px;
}

.currency-box{
  // width: 80px;
  // max-width: 80px;
}