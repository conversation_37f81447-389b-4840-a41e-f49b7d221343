<div class="px-4 pb-5 leasing">
  <div class="h4 py-4 mb-3 text-center nav-back d-flex align-items-center font-size-18px">
    <i class="icon icon-back mr-4 float-left" (click)="back()"></i>
    <div class="ml-1">
      {{ "_example_leasing_calculation_plan" | translate }}
    </div>
  </div>
  <form (submit)="onSubmitForm()" class="leasingForm d-flex flex-column" [formGroup]="form">
    <!--  EquipmentPrice  -->
    <div class="form-group">
      <div class="h6">
        {{ '_equipment_price_without_tax' | translate }}
      </div>
      <div class="position-relative">
      <input
        [name]="'EquipmentPrice'"
        [placeholder]="'_equipment_price_without_tax' | translate"
        class="form-control form-control"
        formControlName="EquipmentPrice"
        (input)="calculateAdvanceRate($event)"
        type="text"
        maxlength="13"
        inputmode="numeric"
      />
      <i class="icon icon-euro euro-icon"></i>
      </div>
      <div
        [ngClass]="{ 'd-block': !form.get('EquipmentPrice').valid && form.get('EquipmentPrice').touched }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.EquipmentPrice) | translate }}
      </div>
    </div>
    <div class="d-flex justify-content-between align-items-end">
    <!--  DueDate  -->
    <div class="d-flex flex-column mb-4">
      <div class="h6">
        {{'_leasing_due_date_select' | translate}}
      </div>
      <div *ngIf="dueDateList?.length" class="form-check btn-group w-100 d-flex pl-0">
        <ng-container *ngFor="let dueDate of dueDateList; let last = last">
          <input  type="radio"
                  class="btn-check d-none"
                  [id]="dueDate"
                  [formControlName]="'DueDate'"
                  [value]="dueDate"
                  [name]="'DueDate'"
          >
          <label class="btn btn-sm leasing-radio-button due-date-box"
                 [class.selected]="form.get('DueDate').value === dueDate"
                 [class.last-box]="last"
                 [for]="dueDate">{{dueDate}}</label>
        </ng-container>
      </div>
      <div
      [ngClass]="{ 'd-block': !form.get('DueDate').valid && form.get('DueDate').touched }"
      class="invalid-feedback pl-3"
    >
      {{ getFormErrorMessage(form.controls.DueDate) | translate }}
    </div>
    </div>
    <!--  Currency  -->
    <div class="d-flex flex-column ml-3 mb-4">
      <div class="h6">
        {{'_leasing_currency_select' | translate}}
      </div>
      <div *ngIf="currencyList?.length" class="form-check btn-group w-100 d-flex flex-row pl-0">
        <ng-container *ngFor="let currency of currencyList; let last = last">
          <input type="radio"
                 class="btn-check d-none"
                 [id]="currency"
                 [formControlName]="'Currency'"
                 [value]="currency"
                 [name]="'Currency'"
          >
          <label class="btn btn-sm leasing-radio-button currency-box"
                 [class.selected]="form.get('Currency').value === currency"
                 [class.last-box]="last"
                 [for]="currency">{{currency}}</label>
        </ng-container>
      </div>
      <div
        [ngClass]="{ 'd-block': !form.get('Currency').valid && form.get('Currency').touched }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Currency) | translate }}
      </div>
    </div>
  </div>
    <!--  PaymentAmount  -->
    <div class="d-flex flex-column">
      <div class="h6">
        {{'_leasing_payment_amount' | translate}}
      </div>
      <div class="form-group position-relative">
        <input
          [name]="'PaymentAmount'"
          [placeholder]="'_leasing_payment_amount' | translate"
          class="form-control form-control"
          formControlName="PaymentAmount"
          type="text"
          maxlength="13"
          inputmode="numeric"
          (input)="calculateAdvanceRate($event, 'PaymentAmount')"
        />
        <i class="icon icon-euro leasing-currency-icon"></i>
        <div
          [ngClass]="{ 'd-block': getNumericEquipmentPrice() && !form.get('PaymentAmount').valid && form.get('PaymentAmount').touched}"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.PaymentAmount) | translate }}
        </div>
        <div
          [ngClass]="{ 'd-block': !getNumericEquipmentPrice() && form.get('PaymentAmount').touched}"
          class="invalid-feedback pl-3"
        >
          {{ '_enter_cost_of_good' | translate }}
        </div>
        <div
          [ngClass]="{ 'd-block': this.getNumericEquipmentPrice() && getNumericEquipmentPrice() < getNumericEquipmentPrice('PaymentAmount')}"
          class="invalid-feedback pl-3"
        >
          {{ '_down_payment_info' | translate }}
        </div>
      </div>

    </div>
    <!--  PaymentRate  -->
    <div class="d-flex flex-column">
      <div class="pb-2">
        {{'_leasing_payment_rate' | translate}}
      </div>
      <div *ngIf="paymentRate?.length" class="form-check btn-group mb-4 w-100 d-flex flex-wrap flex-row pl-0">
        <ng-container *ngFor="let opt of paymentRate">
          <input type="radio"
                 class="btn-check d-none"
                 [id]="opt.value"
                 [formControlName]="'PaymentRate'"
                 [value]="opt.title"
                 (change)="calculateAdvancePayment(opt.title)"
                 [name]="'PaymentRate'"
          >
          <label class="btn btn-sm leasing-radio-button"
                 [class.selected]="form.get('PaymentRate').value === opt.value && calculatedPaymentRate === opt.title || calculatedPaymentRate === opt.title"
                 [for]="opt.value">{{opt.title}}</label>
        </ng-container>
      </div>

    </div>
    <!-- <div class="form-group">
      <div class="pb-2">
        {{'_calculated_leasing_payment_rate' | translate}}
      </div>
      <div class="position-relative calculated-leasing-payment-rate">
        <input
          readonly
          [placeholder]="'_leasing_payment_rate' | translate"
          class="form-control form-control leasing-payment-rate"
          [value]="calculatedPaymentRate"
          type="number"
        />
        <div class="percent-icon">%</div>
      </div>
    </div> -->
    <input
      appClickLog
      [section]="'LEASING'"
      [subsection]="'LEASING_CALCULATE'"
      [data]="{
          brand: this.data?.brand,
          title: this.data?.title,
          productId: this.data?.productId,
          productGroupId: this.data?.productGroupId,
          url: this.data?.detailPageUrl || this.data?.webUrl,
          formValue: this.form?.value
        }"
      type="submit" class="btn btn-gradient text-white shadow btn-warning mt-auto mb-5 mx-5" [value]="'_show_example_leasing_plan' | translate">
  </form>
</div>
<app-loader [show]="loading"></app-loader>