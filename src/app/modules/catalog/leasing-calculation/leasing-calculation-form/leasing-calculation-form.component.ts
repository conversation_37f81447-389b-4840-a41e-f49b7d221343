import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {getFormErrorMessage, isShowFormError, isShowFormErrorTouched, validateAllFormFields} from '../../../../util/form-error.util';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {LoginState} from '../../../authentication/state/login/login.state';
import {GetBasisSettingsAction} from '../../../../shared/state/settings/settings.actions';
import {HeaderStatusAction, ResetLeasingAction} from '../../../customer/state/customer/customer.actions';
import {Select, Store} from '@ngxs/store';
import {CustomerService} from '../../../customer/service/customer.service';
import {TranslatePipe} from '@ngx-translate/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserModel} from '../../../authentication/model/user.model';
import {UserState} from '../../../customer/state/user/user.state';
import { LogService } from 'src/app/shared/service/log.service';
import { CustomerLeasingAction } from '../../../customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { Observable, Subject } from 'rxjs';
import { LeasingModel } from 'src/app/shared/models/customer.model';
import { takeUntil } from 'rxjs/operators';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';

@Component({
  selector: 'app-leasing-calculation-form',
  templateUrl: './leasing-calculation-form.component.html',
  styleUrls: ['./leasing-calculation-form.component.scss']
})
export class LeasingCalculationFormComponent implements OnInit {
  @Select(CustomerState.leasing)
  leasing$: Observable<LeasingModel>


  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isShowFormErrorTouched = isShowFormErrorTouched;

  @Input()
  data: any;
  @Input()
  leasingBody: any;
  // tslint:disable-next-line:no-output-on-prefix
  @Output()
  onBackPressed: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output()
  leasingData: EventEmitter<any> = new EventEmitter<any>();

  user: UserModel;
  form: FormGroup = new FormGroup({
    EquipmentPrice: new FormControl(null, [Validators.required]),
    DueDate: new FormControl(null, [Validators.required]),
    Currency: new FormControl(null, [Validators.required]),
    PaymentAmount: new FormControl(null, [Validators.required]),
    PaymentRate: new FormControl(null),
  });
  formSendStatus = false;
  loading: boolean;
  //showHeader: any = true;
  calculatedPaymentRate: any = 0
  paymentRate = [
    {
      value: 2,
      title: 20
    },
    {
      value: 3,
      title: 25
    },
    {
      value: 6,
      title: 50
    }
  ];
  currencyList = ['TRY', 'EUR'];
  dueDateList = [12, 24, 36];
  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly translate: TranslatePipe,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly log: LogService,
    private readonly frameMessageService: FrameMessageService,
  ) {
    this.data =
      this.router.getCurrentNavigation()?.extras?.state?.data || this.data;
  }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit(): void {
    this.user = this.store.selectSnapshot(LoginState.user);
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: '',
      })
    );
    // this.route.queryParams.subscribe(q => {
    //   this.showHeader = true
    // });

    const customer = this.store.selectSnapshot(UserState.currentCustomer);

    this.leasing$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        const patchValues = {
          EquipmentPrice: [data?.leasingData?.EquipmentPrice ?? this?.data?.equipmentPrice]?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, '.'),
          DueDate: data?.leasingData?.DueDate,
          Currency: data?.leasingData?.Currency,
          PaymentAmount: data?.leasingData?.PaymentAmount?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
        };
        this.form.patchValue(patchValues);
        this.calculatedPaymentRate = data?.leasingData?.formBody?.PaymentRate
      });
  }

  onSubmitForm() {
    const downPaymentControl = this.getNumericEquipmentPrice() && (this.getNumericEquipmentPrice() < this.getNumericEquipmentPrice('PaymentAmount'))
    console.log('submit', this.form);
    if (this.form.valid && !downPaymentControl) {
      this.form.patchValue({ EquipmentPrice: this.getNumericEquipmentPrice() || null, PaymentAmount: this.getNumericEquipmentPrice('PaymentAmount') || 0 })
      this.sendForm();
      console.log('valid');
    } else {
      validateAllFormFields(this.form);
    }
  }

  getNumericEquipmentPrice(name = 'EquipmentPrice'){
    return Number(this.form.get(name)?.value?.toString().replace(/\./g, ''))
  }

  sendForm() {
    const { value } = this.form

    const formBody = {
      ProductId: this.data?.productId ,
      ProductName: this.data?.productName,
      CostOfGoods:  this.getNumericEquipmentPrice(),
      DueDate: value.DueDate,
      CurrencyUnit: value.Currency === 'TRY' ? 0 : 1,
      PaymentRate: this.calculatedPaymentRate,
      MachineStatus: this.data?.machineStatus || 0
    }

    if (this.form.valid) {
      this.loading = true;
      this.leasingData.emit({...this.form?.value, calculatedPaymentRate: this.calculatedPaymentRate, formBody});
      this.store.dispatch(new CustomerLeasingAction(formBody, {...this.data, formBody, ...this.form?.value})).subscribe(
        () => {
          this.loading = false;
          this.router.navigate([], {
            queryParams: {formLeasingStatus: 0, leasingStatus: 1},
            queryParamsHandling: 'merge',
          }).then();
        },
        () => {
          this.loading = false;
        }
      );
    } else {
      validateAllFormFields(this.form);
    }
  }

  back() {
    window.history.back();
    console.log('k', window.history)

    if(this.data?.isMobileLeasing){
      this.frameMessageService.sendMessage(FrameMessageEnum.closeWebView);
    }

    this.store.dispatch(new ResetLeasingAction())
    this.onBackPressed.emit(true);
  }

  scrollTop(){
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]){
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  calculateAdvancePayment(rate: number){
    if(this.getNumericEquipmentPrice()){
      const equipmentPrice = this.getNumericEquipmentPrice()
      const calculatedPayment = Math.round((equipmentPrice * rate) / 100)
      const patchValues = {
        ...this.form,
        PaymentAmount: calculatedPayment?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
      };
      this.form.patchValue(patchValues);
    }
    this.calculateAdvanceRate()
    this.calculatedPaymentRate = rate
  }

  calculateAdvanceRate(event?: any, formControlName = 'EquipmentPrice'){
    if(event){
      let inputValue
      const input = event.target.value.replace(/[^0-9]/g, '');
      if (parseInt(input) > 999) {
        inputValue = input.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
      } else {
        inputValue = input;
      }

      const patchValues = {
        ...this.form.value,
        [formControlName]: inputValue
      }
      this.form.patchValue(patchValues);
    }

    if(this.getNumericEquipmentPrice() && this.getNumericEquipmentPrice('PaymentAmount')){
      const calculatedPaymentRate = (this.getNumericEquipmentPrice('PaymentAmount') * 100) / this.getNumericEquipmentPrice()
      console.log(calculatedPaymentRate, parseFloat(calculatedPaymentRate.toFixed(9)))
      if (Number.isInteger(calculatedPaymentRate)) {
        return this.calculatedPaymentRate = calculatedPaymentRate
      } else {
        return this.calculatedPaymentRate = calculatedPaymentRate.toFixed(9)
      }
    }
      return this.calculatedPaymentRate = 0
  }

  getCurrencyIcon() {
    const currencyValue = this.form.get('Currency')?.value
    if(!currencyValue) return ''

    return currencyValue === 'TRY' ? 'icon-tl' : 'icon-euro'

    // return this.form.get('Currency')?.value?.currencyUnit &&
    // (['tl', 'usd', 'euro', 'ruble' , 'tenge', 'manat', 'lari'].indexOf(this.form.get('Currency')?.value?.currencyUnit?.toLowerCase()) >= 0)
    //   ? 'icon-' + this.form.get('Currency')?.value?.currencyUnit?.toLowerCase()
    //   : 'icon-coins' ;
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
