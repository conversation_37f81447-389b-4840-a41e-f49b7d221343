import {AfterViewInit, Component, ElementRef, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {LoginState} from '../../../authentication/state/login/login.state';
import {HeaderStatusAction} from '../../../customer/state/customer/customer.actions';
import {Select, Store} from '@ngxs/store';
import {CustomerService} from '../../../customer/service/customer.service';
import {TranslatePipe} from '@ngx-translate/core';
import {ActivatedRoute, Router} from '@angular/router';
import {UserModel} from '../../../authentication/model/user.model';
import {environment} from '../../../../../environments/environment';
import {UserState} from '../../../customer/state/user/user.state';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LeasingModel, PaymentTableModel } from 'src/app/shared/models/customer.model';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-leasing-calculation-response',
  templateUrl: './leasing-calculation-response.component.html',
  styleUrls: ['./leasing-calculation-response.component.scss']
})
export class LeasingCalculationResponseComponent implements OnInit, AfterViewInit {

  @Select(CustomerState.leasing)
  leasing$: Observable<LeasingModel>

  @Input()
  data: any;
  // tslint:disable-next-line:no-output-on-prefix
  @Output()
  onBackPressed: EventEmitter<boolean> = new EventEmitter<boolean>();

  user: UserModel;
  //showHeader: number;
  loading: boolean;
  paymentTable: PaymentTableModel[];
  leasingCalculationId: string;
  leasingIcon = `${environment.assets}/leasing.svg`;
  equipmentImageNotFound = `${environment.assets}/not-found-equipment.png`;
  userData: any;
  leasingBody: any;
  today = new Date();
  formSendStatus = false;
  form: FormGroup = new FormGroup({
    Description: new FormControl(''),
  });

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly translate: TranslatePipe,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly elementRef: ElementRef,
  ) {
    this.data =
      this.router.getCurrentNavigation()?.extras?.state?.data || this.data;
  }

  ngOnInit(): void {
    this.user = this.store.selectSnapshot(LoginState.user);
    this.userData = this.store.selectSnapshot(UserState.basics);

    this.leasing$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if (data){
          this.leasingBody = data.leasingData
          this.paymentTable = data.paymentTable;
          this.leasingCalculationId = data.leasingCalculationId;
        }
    });

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_get_offer'),
      })
    );

    // this.route.queryParams.subscribe(q => {
    //   this.showHeader = 'showHeader' in q ? parseInt(q.showHeader, 10) : 0;
    // });
  }

  returnLeasingForm() {
    this.router.navigate([], {
      queryParams: {formLeasingStatus: 1},
      queryParamsHandling: 'preserve',
    }).then();
  }

  back() {
    this.onBackPressed.emit(true);
    history.back();
  }

  confirmAndCallPSSR() {
    this.loading = true;
    this.customerService.sendLeasingCalculateMail(this.leasingCalculationId).subscribe(
        () => {
          this.sendOffer()
        },
        () => {
          this.loading = false;
        }
      );
  }

  getCurrencyIcon(){
    return this.leasingBody?.formBody?.CurrencyUnit === 0 ? 'icon-tl' : 'icon-euro'
  }

  sendOffer(){
    const headers = {};
    const current = this.store.selectSnapshot(UserState.currentCustomer);

    this.loading = true;
    const { value } = this.form;

    const formBody = {
        Name: this.userData?.firstName || this.user?.firstName,
        Surname: this.userData?.lastName || this.user?.lastName,
        CompanyName: current?.customer?.name,
        CompanyId: current?.publicMenuHeaderCompany,
        PhoneNumber: this.userData?.mobile || this.user?.mobile,
        CompanyPhoneNumber: this.userData?.mobile || this.user?.mobile, //todo sorulacak
        Email: this.userData?.email.toLowerCase() || this.user?.email.toLowerCase(),
        Description: value?.Description,
        CountryCode: current?.groupKey ? current?.groupKey : 'TR',
        Brand: this.data?.brand || this.data?.productBrand,
        Model: this.data?.model || this.data?.productName,
    }

    this.customerService.sendOrderForm(formBody, headers, 'form/equipment/quotation')
      .subscribe(
        () => {
          this.loading = false;
          this.formSendStatus = true;
        },
        () => {
          this.loading = false;
          this.formSendStatus = false;
        }
      );
  }

  ngAfterViewInit(): void {
    const leasingDetailText = this.elementRef.nativeElement.querySelector('#leasingDetailText');
    const leasingReadMore = this.elementRef.nativeElement.querySelector('#leasingReadMore');
    const leasingCloseMore = this.elementRef.nativeElement.querySelector('#leasingCloseMore');
    if (leasingReadMore && leasingDetailText && leasingCloseMore) {
      leasingReadMore.addEventListener('click', () => {
        leasingReadMore.classList.add('d-none');
        leasingCloseMore.classList.remove('d-none');
        leasingDetailText.classList.remove('d-none');
      });
      leasingCloseMore.addEventListener('click', () => {
        leasingReadMore.classList.remove('d-none');
        leasingCloseMore.classList.add('d-none');
        leasingDetailText.classList.add('d-none');
      });
    }
  }
}
