<div class="leasingPage px-4 pb-5">
  <div class="h4 mb-0 py-4 text-center nav-back d-flex align-items-center font-size-18px">
    <i class="icon icon-back mr-4 float-left" (click)="back()"></i>
    <div class="ml-1">
      {{ "_example_leasing_calculation_plan" | translate }}
    </div>
  </div>
  <div class="leasingArea d-flex flex-column" *ngIf="!formSendStatus">
    <div class="leasingScroll mb-3">
      <div class="card border-0 shadow-sm mb-3">
        <div class="card-body">
          <div class="d-flex flex-row justify-content-between">
            <div class="mr-3">
              <div class="mb-3 font-size-14px">{{leasingBody?.title || leasingBody?.productName}}</div>
              <img *ngIf="leasingBody?.imageUrl" [src]="leasingBody?.imageUrl" width="80" height="80" [alt]="leasingBody?.title">
              <img *ngIf="!leasingBody?.imageUrl" [src]="equipmentImageNotFound" [alt]="leasingBody?.title">
            </div>
            <div class="d-flex flex-column justify-content-center">
              <div>{{'_dear'|translate}} {{userData?.firstName}} {{userData?.lastName}}</div>
              <small class="my-2 text-black-50">{{'_leasing_plan_description_text'|translate}}</small>
              <h4 class="my-2 font-weight-bold">

                <i class="icon icon-euro"></i>
                {{ leasingBody?.formBody?.CostOfGoods | number }}
              </h4>
              <div class="text-black-50 font-size-14px">{{'_leasing_total_price_without_tax'|translate}}</div>
            </div>
            <div></div>
          </div>
        </div>
      </div>
      <div class="text-info">{{'_details' | translate}}</div>
      <div class="leasingDetailArea">
        <div class="d-flex flex-row justify-content-between border-bottom py-3">
          <div>{{'_date' | translate}}</div>
          <div>{{ today | date: "dd.MM.yyyy" }}</div>
        </div>
        <div class="d-flex flex-row justify-content-between border-bottom py-3">
          <div>
            {{'_leasing_due_date' | translate}}
          </div>
          <div>{{ leasingBody?.formBody?.DueDate }} {{'_month' | translate}}</div>
        </div>
        <div class="d-flex flex-row justify-content-between border-bottom py-3">
          <div>
            {{'_leasing_deposit_price' | translate}}
          </div>
          <div>
            <i class="icon icon-euro"></i>
            {{ leasingBody?.PaymentAmount | number }}
          </div>
        </div>
        <div class="d-flex flex-column justify-content-between border-bottom py-3">
          <ngb-accordion>
            <ngb-panel>
              <ng-template ngbPanelTitle>
                <div class="d-flex justify-content-between border-0 p-0 m-0">
                  <div class="text-info">
                    {{'_leasing_monthly_payment_table' | translate}}
                  </div>
                  <div class="d-flex"><i class="text-decoration-none icon icon-chevron-down"></i></div>
                </div>
              </ng-template>
              <ng-template ngbPanelContent>
                <div class="fit-body overflow-auto" style="width: 100%">
                  <table style="font-size: 12px" class="table table-striped">
                    <thead>
                    <tr class="text-center">
                      <th>{{'_month'| translate}}</th>
                      <th>{{'_monthly_payment' | translate}}</th>
                      <th>{{'_principal_payment' | translate}}</th>
                      <th>{{'_interest_payment' | translate}}</th>
                      <th>{{'_opening_balance' | translate}}</th>
                      <th>{{'_closing_balance' | translate}}</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr *ngFor="let payment of paymentTable">
                      <td class="text-center">{{payment?.paymentNumber}}</td>
                      <td class="text-left text-nowrap payment-table-info">
                        <i class="icon" [class]="getCurrencyIcon()"></i>
                        {{payment?.monthlyPayment | number }}
                      </td>
                      <td class="text-left text-nowrap payment-table-info">
                        <i class="icon" [class]="getCurrencyIcon()"></i>
                        {{payment?.principalPayment | number }}
                      </td>
                      <td class="text-left text-nowrap payment-table-info">
                        <i class="icon" [class]="getCurrencyIcon()"></i>
                        {{payment?.interestPayment | number }}
                      </td>
                      <td class="text-left text-nowrap payment-table-info">
                        <i class="icon" [class]="getCurrencyIcon()"></i>
                        {{payment?.openingBalance | number }}
                      </td>
                      <td class="text-left text-nowrap payment-table-info">
                        <i class="icon" [class]="getCurrencyIcon()"></i>
                        {{payment?.closingBalance | number }}
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </ng-template>

            </ngb-panel>

          </ngb-accordion>
        </div>
        <div class="d-flex flex-row justify-content-between">
          <app-info-box [title]="'_info' | translate">
            <span [innerHTML]="('_leasing_info_text_summary' | translate)|safeHtml"></span>
          </app-info-box>
        </div>
      </div>

    </div>
    <form (submit)="sendOffer()" [formGroup]="form">
      <div class="h6">
        {{'_my_note' | translate}}
      </div>
      <textarea
        appInputMaxLength
        [name]="'Description'"
        [rows]="5"
        class="form-control mb-4"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
    </form>
    <button
      appClickLog
      [section]="'LEASING'"
      [subsection]="'LEASING_QUOT_SEND'"
      [data]="{
          brand: this.data?.brand,
          title: this.data?.title,
          productId: this.data?.productId,
          productGroupId: this.data?.productGroupId,
          url: this.data?.detailPageUrl || this.data?.webUrl,
          formValue: this.data?.leasingForm
        }"
      (click)="confirmAndCallPSSR()" class="btn btn-gradient btn-block text-white shadow btn-warning mt-auto mb-5">
        {{'_leasing_confirm_and_call_pssr' | translate}}
    </button>
  </div>
</div>
<app-loader [show]="loading"></app-loader>

<div class="leasing-customer-service" *ngIf="formSendStatus">
  <div class="mt-3 success-message d-flex flex-column align-items-center">
    <i class="icon icon-message-success d-inline-block mb-3"></i>
    <p class="text-center">{{ "_successfully_send_form" | translate }}</p>
  </div>
  <app-customer-service></app-customer-service>
</div>
