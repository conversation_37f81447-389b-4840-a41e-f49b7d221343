.leasingPage{
  background-color: #FAFAFA;
  overflow-y: auto;
  overflow-x: hidden;
  height: 100vh;
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  .table th, .table td{
    padding: 0;
  }
}

.icon-headset:before {
  color: #ffffff;
}

::ng-deep ngb-accordion .card {
  border-radius: 0;
  border: none;
  background-color: transparent;
}

::ng-deep ngb-accordion .card-body {
  padding: 0;
  background-color: transparent;
  border-radius: 0;
  border: 0;
}

::ng-deep ngb-accordion .card-header {
  border-radius: 0;
  padding: 0;
  background-color: #f9f9f9;
  border: none;

  .btn {
    width: 100%;
    float: left;
    text-align: left !important;
    box-shadow: none;
    border-radius: 0;
    border: none;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    font-family: roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    text-decoration: none;
    padding-left: 0;
    padding-right: 0;
  }

  .btn-link:hover {
    box-shadow: none;
    text-decoration: none;
    //color: #4a8eb0;
  }

  button:not(.collapsed) {
    .icon-chevron-down {
      transform: rotate(-180deg);
    }
  }

  button.collapsed .fw-bolder,
  button:not(.collapsed) .fw-bolder {
    font-weight: 600;
  }

  .icon-chevron-down {
    line-height: 1em;
    height: 1em;
    transition: all 0.4s ease;
  }
}


::ng-deep app-success-modal .content-area-body {
  z-index: 5 !important;
}

.leasing-customer-service{
  position: fixed;
  height: calc(100% - 64px);
  top: 64px;
  overflow: auto;
}

.success-message {
  font-size: 18px;
  font-weight: 600;
}

.icon-message-success {
  font-size: 40px;
  padding-top: 1px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.payment-table-info{
  padding-right: 5px !important;
}