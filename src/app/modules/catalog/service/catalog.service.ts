import { Store } from '@ngxs/store';
import { MenuModel } from '../../customer/model/menu.model';
import { OpenLinkAction } from '../../../shared/state/common/common.actions';
import { Injectable } from '@angular/core';
import { CatalogState } from '../../customer/state/catalog/catalog.state';
import { fullUrl } from '../../../util/full-url.util';
import { UserState } from '../../customer/state/user/user.state';
import { CommonState } from '../../../shared/state/common/common.state';
import { LoginState } from '../../authentication/state/login/login.state';
import { NotificationState } from '../../notification/state/notification/notification.state';
import { WindowManagerService } from '../../../shared/service/window-manager.service';

@Injectable()
export class CatalogService {
  constructor(
    private readonly store: Store,
    private windowManagerService: WindowManagerService,
  ) { }


  catalogActionHandler(item: MenuModel) {
    return this.handleDeeplink(item) || this.handleOutlineScheme(item);
    // return this.handleDeeplink(item);
  }

  handleDeeplink(item: MenuModel) {
    if (/(^borusancat360:\/\/)|(^https:\/\/(www\.)?borusancat.com\/b[dq]?\/)/.test(item?.webUrl)) {
      this.store.dispatch(new OpenLinkAction({
        url: item?.webUrl
      }));
      return true;
    }
    return false;
  }

  /**
   * Handle external deeplink and urls
   */
  handleOutlineScheme(item: MenuModel) {
    // /(^[A-Za-z0-9-_]*:\/\/\w*)/.test(item?.webUrl)
    if (item?.webUrl && item?.tagList?.length && item?.tagList?.indexOf('deeplink') !== -1) {
      this.store.dispatch(new OpenLinkAction({
        url: item?.webUrl
      }));
      return true;
    }
    return false;
  }

  navigateTaggedCatalog(tag) {
    const catalog = this.store.selectSnapshot(CatalogState.catalog);
    const found = this.foundInCatalog(catalog, tag);
    if (!found) {
      return;
    }
    this.clickHandler(found);
  }

  foundInCatalog(catalog: MenuModel[], tag) {
    for (const item of catalog) {
      if (item.tagList?.includes(tag)) {
        return item;
      }
      if (item.categories?.length) {
        const found = this.foundInCatalog(item.categories, tag);
        if (found) {
          return found;
        }
      }
    }
    return null;
  }

  clickHandler(item: MenuModel) {
    if (item?.isWeb && item?.tagList?.length && (
      item?.tagList?.indexOf('disableHeader') !== -1 ||
      item?.tagList?.indexOf('sendUserData') !== -1
    )) {
      this.openWebviewWithUser(item,
        item?.tagList?.indexOf('disableHeader') !== -1 ? { disableHeader: true } : {}
      );
      return true;
    }

    if (this.catalogActionHandler(item)) {
      return true;
    }

    // open in webview
    if (item?.isWeb) {
      this.openWebview({
        // url: fullUrl(item.webUrl + (item.webUrl.includes('?') ? '&' : '?') + `lang=${lng}`),
        url: fullUrl(item.webUrl),
        title: item.title
      });

      return true;
    }

    return false;
  }

  protected openWebview(data) {
    this.windowManagerService.openWebview(data);
  }

  protected openWebviewWithUser(item, extra = {}) {
    const userData = this.store.selectSnapshot(UserState.basics) || {};
    userData.mobile = userData?.mobile || ' ';
    userData.region = this.store.selectSnapshot(CommonState.currentRegion);
    userData.language = this.store.selectSnapshot(LoginState.language);
    userData.countryCode = this.store.selectSnapshot(UserState.currentCustomer)?.groupKey;
    this.openWebview({
      url: item.webUrl,
      title: item.title,
      // user,
      userData,
      metaData: {
        appSessionId: this.store.selectSnapshot(NotificationState.deviceToken),
      },
      listenEvent: true,
      backAction: true,
      ...extra
    });
  }


}
