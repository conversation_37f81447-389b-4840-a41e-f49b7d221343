import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppLinkViewComponent } from './component/categories/app-link-view/app-link-view.component';
import { CatalogTagComponent } from './catalog-tag/catalog-tag.component';
import { CatalogDetailComponent } from './component/catalog-detail/catalog-detail.component';
import { OrderFormComponent } from './component/order-form/order-form.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from '../../shared/shared.module';
import { ReactiveFormsModule } from '@angular/forms';
import { NewCatalogModule } from '../new-catalog/new-catalog.module';
import { CatalogRedirectComponent } from './component/catalog-redirect/catalog-redirect.component';
import { LeasingCalculationFormComponent } from './leasing-calculation/leasing-calculation-form/leasing-calculation-form.component';
import {
  LeasingCalculationResponseComponent
} from './leasing-calculation/leasing-calculation-response/leasing-calculation-response.component';
import { DemoRequestFormComponent } from './component/demo-request-form/demo-request-form.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { ContactModule } from '../customer/container/contact/contact.module';
import { EquipmentOrderComponent } from './equipment-order/equipment-order.component';
import { EquipmentOrderCardComponent } from './equipment-order-card/equipment-order-card.component';
import { LeasingResponseTableComponent } from './leasing-calculation/leasing-response-table/leasing-response-table.component';
import { CatalogService } from './service/catalog.service';
import { HasPermissionsModule } from 'src/app/export/file-upload/permissions/has-permissions.module';
import { EquipmentOrderHeaderComponent } from './equipment-order-header/equipment-order-header.component';
import { EquipmentOrderUnloginFormComponent } from './equipment-order-unlogin-form/equipment-order-unlogin-form.component';
import { EquipmentOrderContainerComponent } from './equipment-order-container/equipment-order-container.component';
import { EquipmentOrderTotalInfoComponent } from './component/equipment-order-total-info/equipment-order-total-info.component';
import { PccVlinkSurveyComponent } from '../customer/pcc-vlink-survey/pcc-vlink-survey.component';


@NgModule({
  declarations: [
    AppLinkViewComponent,
    CatalogTagComponent,
    CatalogDetailComponent,
    OrderFormComponent,
    PccVlinkSurveyComponent,
    CatalogRedirectComponent,
    LeasingCalculationFormComponent,
    LeasingCalculationResponseComponent,
    DemoRequestFormComponent,
    EquipmentOrderComponent,
    EquipmentOrderCardComponent,
    LeasingResponseTableComponent,
    EquipmentOrderHeaderComponent,
    EquipmentOrderUnloginFormComponent,
    EquipmentOrderContainerComponent,
    EquipmentOrderTotalInfoComponent
  ],
  exports: [
    AppLinkViewComponent,
    CatalogTagComponent,
    CatalogDetailComponent,
    OrderFormComponent,
    PccVlinkSurveyComponent,
    LeasingCalculationFormComponent,
    LeasingCalculationResponseComponent,
    LeasingResponseTableComponent,
    EquipmentOrderComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    SharedModule,
    ReactiveFormsModule,
    NewCatalogModule,
    NgSelectModule,
    NgbAccordionModule,
    ContactModule,
    HasPermissionsModule
  ],
  providers: [CatalogService]
})
export class CatalogModule {}
