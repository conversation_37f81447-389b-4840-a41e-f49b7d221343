import { Component, Input, OnInit } from '@angular/core';
import { getOS } from '../../../util/os.util';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-catalog-tag',
  templateUrl: './catalog-tag.component.html',
  styleUrls: ['./catalog-tag.component.scss']
})
export class CatalogTagComponent implements OnInit {
  getOS = getOS;

  assetUrl = environment.assets;

  @Input()
  tags: string[];

  constructor() { }

  ngOnInit(): void {
  }

  imageUrl(tag: string) {
    return tag.startsWith('imageUrl:') ? tag.replace('imageUrl:', '') : null;
  }
}
