<ng-container *ngFor="let tag of tags">
  <span class="tag" [ngSwitch]="tag">
<!--    <img *ngSwitchCase="'new'" class="tag-image" src="assets/new.svg" [alt]="tag"/>-->
<!--    <img *ngSwitchCase="'hot'" class="tag-image" src="assets/flame.svg" [alt]="tag"/>-->
<!--    <img *ngSwitchCase="'attention'" class="tag-image" src="assets/warning.svg" [alt]="tag"/>-->

    <i *ngSwitchCase="'new'" class="icon icon-fiber-new"  ></i>
    <i *ngSwitchCase="'hot'" class="icon icon-whatshot"  ></i>
    <i *ngSwitchCase="'live'" class="icon icon-feet"  ></i>
    <i *ngSwitchCase="'attention'" class="icon icon-priority-high"  ></i>

    <ng-container *ngSwitchCase="'iosApp'">
      <img *ngIf="getOS()==='IOS'" class="tag-image" src="assets/badges/appstore.svg">
    </ng-container>
    <ng-container *ngSwitchCase="'androidApp'">
      <img *ngIf="getOS()==='Android'" class="tag-image" src="assets/badges/playstore.svg">
    </ng-container>
    <ng-container *ngSwitchDefault>
          <img *ngIf="imageUrl(tag)" class="tag-image" [src]="imageUrl(tag)">
<!--          <img *ngIf="imageUrl(tag); else def;" class="tag-image" [src]="imageUrl(tag)">-->
          <ng-template #def>
            <span class="badge badge-pill badge-warning ml-1">{{tag}}</span>
          </ng-template>
    </ng-container>

  </span>

</ng-container>
