<div class="font-size-14px d-flex align-items-center pt-1">
  <ng-container *ngIf="showPrice">
    {{ "_total_amount" | translate }}
    <div *ngIf="!payInAdvance" class="text-nowrap mr-1">
      :
      <i class="icon icon-euro font-weight-bold"></i>
      <span class="font-weight-bold font-size-15px">{{shoppingCartTotalPrice | number}}</span>
    </div>
  </ng-container>
  <div *ngIf="showPaymentStatus" class="payment-status d-flex align-items-center justify-content-center font-size-10px ml-auto">
    {{ "_payment_type" | translate }}: <span class="text-info font-weight-bold">
      {{ (payInAdvance ? '_cash' : '_loan') | translate }}
    </span>
  </div>
</div>
<div class="total-info" *ngIf="(!showPrice && !payInAdvance) || showPrice">
  <div class="d-flex align-items-center justify-content-between">
    <div class="total-info-price d-flex align-items-end">
      <div *ngIf="!payInAdvance || loading" class="font-size-14px mr-2">
        {{ "_monthly" | translate }}:
      </div>
      <div class="d-flex align-items-center">
        <div *ngIf="loading">
          <cat-dot-loader></cat-dot-loader>
        </div>
        <div *ngIf="!loading" class="font-size-15px text-nowrap font-weight-bold mb-0">
          <i class="icon" [ngClass]="payInAdvance ? 'icon-euro' : getCurrencyIcon(currencyUnit ? 'EURO' : 'TRY')"></i>
            {{ (payInAdvance ? shoppingCartTotalPrice : monthlyPayment )| number }}
        </div>
      </div>

    </div>
    <button *ngIf="!payInAdvance && showRecalculateLeasingBtn"  (click)="setLoanCalculationStatus()" class="btn btn-sm btn-info mt-1 font-size-12px calculate-loan-again-btn d-flex align-items-center justify-content-center">
      <i class="icon icon-recalculate"></i>
      {{ "_calculate_loan_again" | translate }}
    </button>
  </div>
  <div *ngIf="!payInAdvance" class="text-info font-size-12px font-weight-semi-bold text-nowrap">
    {{ "_leasing_due_date" | translate }}: {{dueDate}}
  </div>
  <div class="font-size-11px mb-2">
    {{ "_except_vat" | translate }}
  </div>
</div>
