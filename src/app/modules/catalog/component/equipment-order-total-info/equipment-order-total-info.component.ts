import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-equipment-order-total-info',
  templateUrl: './equipment-order-total-info.component.html',
  styleUrls: ['./equipment-order-total-info.component.scss'],
})
export class EquipmentOrderTotalInfoComponent implements OnInit {
  @Input()
  payInAdvance: boolean;

  @Input()
  shoppingCartTotalPrice: any;

  @Input()
  loading: boolean;

  @Input()
  monthlyPayment: any;

  @Input()
  dueDate: number;

  @Input()
  currencyUnit: number;

  @Input()
  showRecalculateLeasingBtn = false;

  @Input()
  showPaymentStatus = true;

  @Input()
  showPrice = true;

  @Output()
  onSetLeasingCalculationStatus: EventEmitter<void> = new EventEmitter<void>();

  constructor() {}

  ngOnInit() {}

  getCurrencyIcon(currency: string) {
    return `icon-${currency === 'TRY' ? 'tl' : 'euro'}`;
  }

  setLoanCalculationStatus(){
    this.onSetLeasingCalculationStatus.emit();
  }
}
