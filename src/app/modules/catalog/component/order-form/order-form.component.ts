import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  getFormErrorMessage,
  isShowFormError,
  isShowFormErrorTouched,
  validateAllFormFields,
} from '../../../../util/form-error.util';
import { DefinitionState } from '../../../definition/state/definition/definition.state';
import { Observable } from 'rxjs';
import { Country } from '../../../definition/model/country.model';
import { Select, Store } from '@ngxs/store';
import { GetAllCountryListAction } from '../../../definition/state/definition/definition.actions';
import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CustomerService } from '../../../customer/service/customer.service';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { CustomValidator } from '../../../../util/custom-validator';
import { HeaderStatusAction } from '../../../customer/state/customer/customer.actions';
import { TranslatePipe } from '@ngx-translate/core';
import { LoginState } from '../../../authentication/state/login/login.state';
import { UserState } from '../../../customer/state/user/user.state';
import { Router } from '@angular/router';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';
import { UserModel } from 'src/app/modules/authentication/model/user.model';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { LogService } from '../../../../shared/service/log.service';

@Component({
  selector: 'app-order-form',
  templateUrl: './order-form.component.html',
  styleUrls: ['./order-form.component.scss'],
  animations: [
    trigger('preview', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.35s')]),
    ]),
  ],
  providers: [TranslatePipe],
})
export class OrderFormComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isShowFormErrorTouched = isShowFormErrorTouched;

  // tslint:disable-next-line: no-output-on-prefix
  @Output()
  onBackPressed: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output()
  onSuccessSendForm: EventEmitter<void> = new EventEmitter<void>();

  @Input()
  data: any;
  @Input()
  shoppingCart: any = [];
  @Input()
  showImg = true;
  @Input()
  leasingFormBody = null;

  user: UserModel;
  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [
      /*Validators.required*/
    ]),
    Surname: new FormControl(null, [
      /*Validators.required*/
    ]),
    CompanyName: new FormControl(null, [
      /*Validators.required*/
    ]),
    PhoneNumber: new FormControl(null, []),
    CompanyPhoneNumber: new FormControl(null, [
      Validators.required,
      Validators.minLength(7),
    ]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    Description: new FormControl(null, []),
    CountryCode: new FormControl(null, [Validators.required]),
    Brand: new FormControl(null, []),
    Model: new FormControl(null, []),
  });
  formSendStatus = false;
  agreementTypeEnum = AgreementTypeEnum;
  loading: boolean;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly translate: TranslatePipe,
    private readonly router: Router,
    private readonly log: LogService,
  ) {
    this.data =
      this.router.getCurrentNavigation()?.extras?.state?.data || this.data;
  }

  ngOnInit(): void {
    this.user = this.store.selectSnapshot(LoginState.user);
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetAllCountryListAction());
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_get_offer'),
      })
    );

    const user = this.store.selectSnapshot(LoginState.user);
    const userData = this.store.selectSnapshot(UserState.basics);
    const customer = this.store.selectSnapshot(UserState.currentCustomer);

    const firstName = userData?.firstName || user?.firstName;
    const lastName = userData?.lastName || user?.lastName;
    const mobile = userData?.mobile || user?.mobile;
    const email = userData?.email || user?.email;
    const patchValues = {
      Name: firstName,
      Surname: lastName,
      Email: email?.toLowerCase(),
      PhoneNumber: mobile,
      CompanyName: customer?.customer?.name,
      CountryCode: customer?.groupKey ? customer?.groupKey : 'TR',
      Brand: this.data?.brand,
      Model: this.data?.model || this.data?.productName,
    };
    this.form.patchValue(patchValues);
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  onSubmitForm() {
    console.log('submit', this.form.value);
    if (this.form.valid) {
      console.log('valid');
      this.sendForm();
    } else {
      validateAllFormFields(this.form);
    }
  }
  loggerFunc(){
    this.log.action('OpportunityProduct', 'Send_Final_Offer', {
      type: this.leasingFormBody ? 'CREDIT' : 'CASH', // 0: Peşin, 1: Taksitli
    }).subscribe();
  }

  sendForm() {
    // agreements)
    const entries = this.form.value?.agreements?.length
    && Object.entries(this.form.value?.agreements)
      ? Object.entries(this.form.value?.agreements?.value)
      : [].filter(([key, v]) => v).map((a) => a[0]);
    const headers = entries.length
      ? { ApprovedAgreementNames: entries.join(';') }
      : {};
    const current = this.store.selectSnapshot(UserState.currentCustomer);


    this.loading = true;
    const { value } = this.form;

    const validShoppingCart = this.shoppingCart?.map((product) => ({
      ProductId: product?.productId,
      Count: product?.amount,
    }));

    // if (this.shoppingCart?.length) {

    //   const formBody = {
    //     LeasingCalculation: this.leasingFormBody, // Leasing Id
    //     Carts: validShoppingCart,
    //     MainProductId: this.data?.productId,
    //     CustomerId: current?.customer?.id,
    //     CustomerName: value.CompanyName,
    //     CustomerEmail: value.Email,
    //     CustomerNumber: current?.customer?.customerNumber,
    //     CurrencyUnit:  !this.leasingFormBody ? 'EURO' : this.leasingFormBody?.currencyUnit === 'TRY' ? 0 : 1,
    //     PaymentType: this.leasingFormBody ? 1 : 0, // 0: Peşin, 1: Taksitli
    //     CountryName: null,
    //     Description: value.Description,
    //     Phone: value.CompanyPhoneNumber || value.PhoneNumber,
    //   };

    //   this.customerService.sendEmailByCart(formBody).subscribe(
    //     (res) => {
    //       this.loading = false;

    //       if (res.code === 0) {
    //         this.formSendStatus = true;
    //       }
    //     },
    //     () => {
    //       this.loading = false;
    //       this.formSendStatus = false;
    //     }
    //   );
    // }

    this.customerService
      .sendOrderForm(
        {
          Name: value.Name,
          Surname: value.Surname,
          CompanyName: value.CompanyName,
          CompanyId: current?.publicMenuHeaderCompany,
          PhoneNumber: value.PhoneNumber,
          CompanyPhoneNumber: value.CompanyPhoneNumber,
          Email: value.Email,
          Description: value.Description,
          CountryCode: value.CountryCode,
          Brand: value.Brand,
          Model: value.Model,
          City: current?.cityName,

          CustomerId: current?.customer?.id,
          MainProductId: this.data?.productId,
          CurrencyUnit:  this.leasingFormBody?.currencyUnit === 'TRY' ? 0 : 1,
          PaymentType: this.leasingFormBody ? 1 : 0,
          LeasingCalculation: this.leasingFormBody,
          Carts: this.shoppingCart?.length ? validShoppingCart : null,
        },
        headers,
        this.data?.formUrl
      )
      .subscribe(
        (res) => {
          this.loading = false;
          if (res.code === 0) {
            this.formSendStatus = true;
            this.onSuccessSendForm.emit();
          }
        },
        () => {
          this.loading = false;
          this.formSendStatus = false;
        }
      );
  }

  back() {
    // this.store.dispatch(new HeaderStatusAction({
    //   title: this.data.title
    // }));

    this.onBackPressed.emit(true);
    history.back();
  }

  // onChangeCountry(countryCode: string) {
  //   const selectedCountry = this.getCountryByCode(countryCode);
  //
  //   this.form.patchValue({ City: null });
  //   // if (this.isActiveCountry(countryCode)) {
  //   //   this.form.controls.City.setValidators([Validators.required]);
  //   //   this.store.dispatch(new GetCityListAction(selectedCountry.id));
  //   // } else {
  //   //   this.form.controls.City.clearValidators();
  //   //   this.form.controls.City.updateValueAndValidity();
  //   //   console.log('city', countryCode);
  //   // }
  // }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }
}
