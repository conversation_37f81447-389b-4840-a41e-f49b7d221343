<div class="form-container" id="form-container">
  <div class="px-4" *ngIf="!formSendStatus">
    <div class="catalog-detail">
      <div class="image-container">
        <img *ngIf="data?.imageUrl && showImg" [src]="data?.imageUrl" width="100%"/>
        <div class="bottom-right mb-2">{{ this.data?.title }}</div>
      </div>

    </div>


    <form (submit)="onSubmitForm()" [formGroup]="form">
      <!--    <div class="form-group">-->
      <!--      <input-->
      <!--        [placeholder]="'_name_surname' | translate"-->
      <!--        class="form-control form-control"-->
      <!--        formControlName="Name"-->
      <!--        type="text"-->
      <!--      />-->
      <!--      <div-->
      <!--        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"-->
      <!--        class="invalid-feedback pl-3"-->
      <!--      >-->
      <!--        {{ getFormErrorMessage(form.controls.Name) | translate }}-->
      <!--      </div>-->
      <!--    </div>-->

      <!--    <div class="form-group">-->
      <!--      <input-->
      <!--        [placeholder]="'_company_name' | translate"-->
      <!--        class="form-control form-control"-->
      <!--        formControlName="CompanyName"-->
      <!--        type="text"-->
      <!--      />-->

      <!--      <div-->
      <!--        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"-->
      <!--        class="invalid-feedback pl-3"-->
      <!--      >-->
      <!--        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}-->
      <!--      </div>-->
      <!--    </div>-->

      <!--    <div class="form-group">-->
      <!--      <input-->
      <!--        [placeholder]="'_phone_number' | translate"-->
      <!--        class="form-control form-control"-->
      <!--        formControlName="PhoneNumber"-->
      <!--        (input)="onInputPhone($event)"-->
      <!--        type="tel"-->
      <!--        maxlength="15"-->
      <!--      />-->

      <!--      <div-->
      <!--        [ngClass]="{ 'd-block': isShowError(form.controls.PhoneNumber) }"-->
      <!--        class="invalid-feedback pl-3"-->
      <!--      >-->
      <!--        {{ getFormErrorMessage(form.controls.PhoneNumber) | translate }}-->
      <!--      </div>-->
      <!--    </div>-->

      <app-info-box [title]="'_info' | translate">
        {{'_offer_form_info' | translate}}
      </app-info-box>
      <div class="form-group">
        <input
          appInputMaxLength
          appFormScroll
          [name]="'Phone'"
          [placeholder]="'_company_phone_number' | translate"
          class="form-control form-control"
          formControlName="CompanyPhoneNumber"
          (input)="onInputPhone($event)"
          type="tel"
          minlength="7"
        />

        <div
          [ngClass]="{ 'd-block': !form.get('CompanyPhoneNumber').valid && form.get('CompanyPhoneNumber').touched }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.CompanyPhoneNumber) | translate }}
        </div>
      </div>
      <!--    <div class="row">-->
      <!--      <div class="col">-->
      <!--        <div class="form-group">-->
      <!--          <ng-select-->
      <!--            class="service-drp"-->
      <!--            [searchable]="false"-->
      <!--            (change)="onChangeCountry($event)"-->
      <!--            *ngIf="!(countryListLoading$ | async)"-->
      <!--            [placeholder]="'_country' | translate"-->
      <!--            [clearable]="false"-->
      <!--            formControlName="CountryCode"-->
      <!--          >-->
      <!--            <ng-option-->
      <!--              *ngFor="let country of countryList$ | async"-->
      <!--              [value]="country.code"-->
      <!--              >{{ country.name }}</ng-option-->
      <!--            >-->
      <!--          </ng-select>-->
      <!--          <div-->
      <!--            [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"-->
      <!--            class="invalid-feedback pl-3"-->
      <!--          >-->
      <!--            {{ getFormErrorMessage(form.controls.CountryCode) | translate }}-->
      <!--          </div>-->
      <!--        </div>-->
      <!--      </div>-->
      <!--    </div>-->

      <!--    <div class="form-group">-->
      <!--      <input-->
      <!--        [placeholder]="'_email' | translate"-->
      <!--        class="form-control form-control"-->
      <!--        formControlName="Email"-->
      <!--        type="text"-->
      <!--      />-->

      <!--      <div-->
      <!--        [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"-->
      <!--        class="invalid-feedback pl-3"-->
      <!--      >-->
      <!--        {{ getFormErrorMessage(form.controls.Email) | translate }}-->
      <!--      </div>-->
      <!--    </div>-->
      <div class="form-group">
      <textarea
        appInputMaxLength
        appFormScroll
        [name]="'Description'"
        [placeholder]="'_offer_description' | translate"
        [rows]="5"
        class="form-control"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
        <div
          [ngClass]="{ 'd-block': !form.get('Description').valid && form.get('Description').touched }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Description) | translate }}
        </div>
      </div>
      <div class="form-group" *ngIf="!user">
        <div class="confirmation">
          <div class="cb-area">
            <app-agreement-list
              [form]="form"
              [formType]="agreementTypeEnum.QuotationRequest"
            ></app-agreement-list>
          </div>
        </div>
      </div>

      <!--    <div class="form-group">-->
      <!--      <div class="phone">+994 12 565 26 38</div>-->
      <!--    </div>-->
      <div class="form-group">
        <button
          appClickLog
          [section]="'CATALOG'"
          [subsection]="'QUOTATION_SEND'"
          [data]="{
          brand: this.data?.brand,
          productGroupId: this.data?.productGroupId,
          productId: this.data?.productId,
          title: this.data?.title || this.data?.productName
        }"
          (click)="loggerFunc()"

          class="btn btn-warning btn-gradient btn-block text-white"
          type="submit"
        >{{'_get_offer' | translate}}</button>
      </div>

    </form>
  </div>

  <!-- <div *ngIf="formSendStatus" [@preview] class="after-form-send">
    <div class="after-form-send-content text-center px-4">
      <i class="icon icon-message-success d-inline-block mb-4"></i>
      <div class="h3 mb-5">{{ "_successfully_send_form" | translate }}</div>
      <div
        class="btn btn-warning btn-gradient btn-block text-white shadow"
        (click)="back()"
      >
        {{ "_return_back" | translate }}
      </div>
    </div>
  </div> -->
  <!-- <app-error-modal
    [message]="formErrorMessage"
    [(status)]="formErrorStatus"
  ></app-error-modal> -->

  <app-success-modal *ngIf="formSendStatus" message="_successfully_send_form">
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="back()"
    >
      {{ "_return_back" | translate }}
    </div>
  </app-success-modal>

  <app-loader [show]="loading"></app-loader>
</div>
