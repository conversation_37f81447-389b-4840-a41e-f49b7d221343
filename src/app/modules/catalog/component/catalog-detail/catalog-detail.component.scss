.catalog-detail {
  .image-container {
    width: 100%;
    &-image {
      width: 100%;
    }
  }
  .description {
    margin-top:20px;
    padding-bottom: 120px;
    font-size: 0.9rem;

    .top-description{
      display: inline;
    }

    .read-more-button{
      font-size: 0.8rem;
        font-weight: 300;
        line-height: 0.5;
        color: #00aaff;

        .three-points{
          color: #505050;
            font-size: 1rem;
            font-weight: 400;
        }
    }
  }
  .footer {
    position: fixed;
    display: inline-block;
    width: 100%;
    bottom:0;
    padding:10px 10px;
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) / 2 + 10px);
    background:#fff;
    left:0;

  }
}

.left {
  line-height: 1rem;
  float: left;
  background: #4A8EB0;
  padding: 9px;
  border-radius: 4px;
  font-weight: bold;
  width: 48%;
  color: #fff;
}

.right {
  line-height: 1rem;
  float: right;
  background: #FFA300;
  padding: 9px;
  border-radius: 4px;
  font-weight: bold;
  width: 48%;
  color: #fff;
}

.demo-request-btn{
  border: 0;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
  border-radius: 16px;
  width: 101px;
  height: 31px;
  padding: 3px;
  position: absolute;
  right: 0;
  bottom: 0;
  margin-top: 1rem;

  .call-icon-text{
    font-size: 11px;
    line-height: 0.8rem;
    color: #FFA300;
  }
  
  .rounded-circle{
    width: 26px !important;
    height: 26px !important;
    background: #4A8EB0;
    flex-shrink: 0;
    padding-top: 5px;
    padding-left: 4.5px;
    background-color: #FFA300;
    display: flex;
    align-items: center;
    

    .icon{
      font-size: 18px;
      color: white;
    }
  }
}


.order-form {
  .form-item {
    display: block;
    margin-bottom: 15px;
    input[type=text],
    input[type=number] {
      width: 100%;
      background: #ECF2F4;
      border: 1px solid #D7E5EA;
      border-radius: 8px;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      box-sizing: border-box;
    }
    textarea {
      width: 100%;
      background: #ECF2F4;
      border: 1px solid #D7E5EA;
      border-radius: 8px;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      box-sizing: border-box;
      min-height: 100px;
      font-family: 'Poppins', sans-serif;
    }
    select {
      width: 100%;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      border-radius: 8px;
      border: 1px solid #D7E5EA;
      background: #ECF2F4;
      font-family: 'Poppins', sans-serif;
    }
    .legal-text {
      font-size: 12px;
      line-height: 15px;
      max-height: 100px;
      overflow: auto;
      margin: 20px 0;
    }
    .confirmation {
      width: 100%;
      display: inline-block;
      .cb-area {
        float:left;
        width:30px;
        input {
          width:20px;
          height: 20px;
          border-radius: 8px;
          border: 2px solid #868686;
          position: relative;
          top:2px;
        }
      }
      .legal-text-area {
        float:left;
        width: calc(100% - 45px);
        font-size: 12px;
        line-height: 15px;
      }
    }
    .phone {
      font-weight: bold;
      margin: 20px 0;
    }
    .btn {
      background: #FFA300;
      padding: 15px;
      border-radius: 4px;
      font-weight: bold;
      width: 100%;
      color: #fff;
      font-size: 16px;
    }

    .field-error {
      display: block;
      color: red;
      margin-top:5px;
    }
  }
}

.form-result {
  position: relative;
  height: calc(100vh - 80px);
  .content {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 0;
    transform: translateY(-100%);
    text-align: center;
    p {
      font-size: 22px;
      line-height: 29px;
      font-weight: bold;
      margin: 20px 0;
    }
    button {
      background: #FFA300;
      padding: 10px 15px;
      border-radius: 4px;
      font-weight: bold;
      width: initial;
      color: #fff;
      font-size: 16px;
    }
  }
}


.form-container {
  padding: 0;
  overflow-y: scroll;
  max-height: calc(100vh - 64px);
}

.header-share {
  position: absolute;
  top: 11px;
  right: 11px;
  line-height: 35px;
  color: #000;

  &-red{
    color: #ff7474;
  }
  img{
    width: 25px;
    filter: invert(69%) sepia(16%) saturate(6920%) hue-rotate(0deg) brightness(103%) contrast(106%);
  }

  span {
    font-size: 9px;
  }
}
