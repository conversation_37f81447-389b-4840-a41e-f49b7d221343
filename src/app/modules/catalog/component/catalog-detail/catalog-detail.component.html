<div *ngIf="detailShowStatus" class="inner-container">
  <div class="header-share mr-2">
    <img
      (click)="shareLink()"
      appClickLog
      [section]="'CATALOG'"
      [subsection]="'SUB_CATALOG_SHARE_CLICK'"
      [src]="
        phone == 'IOS' ? 'assets/ios_share.svg' : 'assets/android_share.svg'
      "
      class="icon"
      alt="Share"
    />
  </div>
  <div class="catalog-detail">
    <div class="image-container position-relative">
      <img
        class="image-container-image"
        [src]="data.imageUrl"
        width="100%"
        [alt]="data?.title"
      />
      <!--button
        appClickLog
        [section]="'CATALOG'"
        [subsection]="'CATALOG_DEMO_CLICK'"
        [data]="{
          brand: this.data?.brand,
          title: this.data?.title,
          productGroupId: this.data?.productGroupId,
          productId: this.data?.productId
        }"
        class="btn demo-request-btn bg-white cursor-pointer d-inline-flex"
        (click)="handleSetDemoForm()"
      >
        <div class="rounded-circle">
          <i class="icon icon-videocall cursor-pointer"></i>
        </div>
        <div class="ml-2 call-icon-text text-wrap text-left">{{'_request_equipment_demo' | translate}}</div>
      </ button -->
    </div>
    <div class="d-flex align-items-center mt-3" *ngIf="data?.showPrice && systemFeatureEquipmentOrder">
      <div class="h3 font-weight-bold text-nowrap mb-0 mr-5">€
        {{ data?.price | number }}
      </div>
    </div>
    <div class="description">
      <app-catalog-specs
        *ngIf="data?.showSpecifications && data.specifications?.length"
        [data]="data"
      ></app-catalog-specs>
      <ng-container
        *ngIf="
          !(data?.showSpecifications && data.specifications?.length) &&
          data?.description">
        <div class="top-description" [innerHTML]="showFullDescription ? data.description : truncatedDescription"></div>
        <span *ngIf="showReadMoreButton" class="read-more-button" (click)="toggleDescription()">
          <span *ngIf="!showFullDescription" class="three-points">...</span> {{ showFullDescription ? closeMore : readMore }}</span>
        <br><br>
        <div class="mb-2 " *ngFor="let spec of data.specifications">
          <div class="font-weight-semi-bold">{{spec.name}}</div>
          <div [innerHTML]="spec.value"></div>
        </div>
      </ng-container>
    </div>
    <div class="footer">
      <button
      *ngIf="systemFeatureEquipmentOrder && data?.isPrice"
        class="btn right text-white w-100 mb-2"
        (click)="handleSetEquipmentOrderPage()"
        appClickLog
        [section]="'CATALOG'"
        [subsection]="'EQUIPMENT_ORDER_CLICK'"
        [data]="{
            brand: this.data?.brand,
            title: this.data?.title,
            productId: this.data?.productId,
            productGroupId: this.data?.productGroupId,
            categoryName: this.data?.productGroupName,
            productName: this.data.productName,
            isPrice: this.data.isPrice
        }"
      >
        {{ "_get_offer" | translate }}
      </button>
      <button
        *ngIf="systemFeatureLeasingCalculation && (!systemFeatureEquipmentOrder || !data?.isPrice)"
        [hasPermission]="PermissionEnum.CatalogLeasing"
        appClickLog
        [section]="'CATALOG'"
        [subsection]="'CATALOG_LEASING_CLICK'"
        [data]="{
          brand: this.data?.brand,
          title: this.data?.title,
          productId: this.data?.productId,
          productGroupId: this.data?.productGroupId,
          url: this.data?.detailPageUrl || this.data?.webUrl
        }"
        class="btn btn-warning btn-gradient btn-block text-white p-1 mb-2"
        (click)="handleSetLeasingForm()"
      >
        {{ "_calculate_sample_payment_plan" | translate }}
      </button>
      <button
        [ngClass]="{'w-100': systemFeatureEquipmentOrder && data?.isPrice}"
        class="btn left"
        (click)="handleSetIframe()"
        appClickLog
        [section]="'CATALOG'"
        [subsection]="'CATALOG_URL_OPENED'"
        [data]="{
          brand: this.data?.brand,
          title: this.data?.title,
          productId: this.data?.productId,
          productGroupId: this.data?.productGroupId,
          url: this.data.detailPageUrl || this.data?.webUrl
        }"
      >
        {{ "_view_catalog" | translate }}
      </button>

      <button
        *ngIf="!systemFeatureEquipmentOrder || !data?.isPrice"
        class="btn right"
        (click)="handleSetOrderForm()"
        appClickLog
        [section]="'CATALOG'"
        [subsection]="'QUOTATION_CLICK'"
        [data]="{
          brand: this.data?.brand,
          title: this.data?.title,
          productGroupId: this.data?.productGroupId,
          productId: this.data?.productId
        }"
      >
        {{ "_get_offer" | translate }}
      </button>
    </div>
  </div>
</div>

<app-loader [show]="catalogEquipmentDetailLoading$ | async"></app-loader>
<app-order-form *ngIf="formStatus" [data]="data"></app-order-form>
<app-leasing-calculation-form
  *ngIf="formLeasingStatus"
  [data]="data"
  [leasingBody]="leasingData"
  (leasingData)="getLeasingData($event)"
></app-leasing-calculation-form>
<app-leasing-calculation-response
  *ngIf="leasingStatus"
  [data]="leasingData"
></app-leasing-calculation-response>
<app-demo-request-form
  *ngIf="formDemoStatus"
  [data]="data"
></app-demo-request-form>
<!-- <app-equipment-order
  *ngIf="equipmentOrderStatus"
  [equipmentData]="equipmentDetail"
></app-equipment-order> -->
