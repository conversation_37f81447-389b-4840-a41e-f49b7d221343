import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { ActivatedRoute, Router } from '@angular/router';
import { Location } from '@angular/common';
import { LogService } from '../../../../shared/service/log.service';
import { OpenModuleAction } from '../../../../shared/state/common/common.actions';
import { fullUrl } from '../../../../util/full-url.util';
import { HeaderStatusAction, ResetLeasingAction } from '../../../customer/state/customer/customer.actions';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { getOS } from 'src/app/util/os.util';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { Observable, Subject } from 'rxjs';
import { SystemFeature } from '../../../customer/response/settings.response';
import { systemFeature } from '../../../../util/system-feature.util';
import { takeUntil } from 'rxjs/operators';
import { TranslatePipe } from '@ngx-translate/core';
import {
  GetCatalogEquipmentDetailAction,
  ResetCatalogEquipmentDetailAction,
  ResetShoppingCartAction
} from 'src/app/modules/customer/state/catalog/catalog.actions';
import { CatalogState } from 'src/app/modules/customer/state/catalog/catalog.state';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { PermissionEnum } from 'src/app/modules/definition/enum/permission.enum';

@Component({
  selector: 'app-catalog-detail',
  templateUrl: './catalog-detail.component.html',
  styleUrls: ['./catalog-detail.component.scss'],
})
export class CatalogDetailComponent implements OnInit, OnDestroy {

  @Input()
  data: any;
  @Input()
  menuId: any;
  formStatus = 0;
  formLeasingStatus = 0;
  leasingStatus = 0;
  leasingData: any;
  formDemoStatus = 0;
  equipmentOrderStatus = 0;
  phone = getOS();
  detailShowStatus = true;
  systemFeatureLeasingCalculation: boolean;
  systemFeatureEquipmentOrder: boolean;
  showReadMoreButton = false;
  maxLength = 100;
  truncatedDescription: string = '';
  readMore: string;
  closeMore: string;
  showFullDescription = false;
  equipmentDetail: any = null;
  PermissionEnum = PermissionEnum;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  @Select(CatalogState.catalogEquipmentDetail)
  catalogEquipmentDetail$: Observable<any>;
  @Select(CatalogState.catalogEquipmentDetailLoading)
  catalogEquipmentDetailLoading$: Observable<boolean>;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly frameMessageService: FrameMessageService,
    private readonly location: Location,
    private readonly log: LogService,
    private readonly route: ActivatedRoute,
    private readonly translate: TranslatePipe,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new ResetCatalogEquipmentDetailAction());
    // this.shareContentVersion = appVersionController(this.store.selectSnapshot(CommonState.version), 'shareContent');
    this.route.queryParams.subscribe(q => {
      this.formStatus = 'formStatus' in q ? parseInt(q.formStatus, 2) : 0;
      this.formLeasingStatus = 'formLeasingStatus' in q ? parseInt(q.formLeasingStatus, 2) : 0;
      this.formDemoStatus = 'formDemoStatus' in q ? parseInt(q.formDemoStatus, 2) : 0;
      this.leasingStatus = 'leasingStatus' in q ? parseInt(q.leasingStatus, 2) : 0;
      this.readMore = this.translate.transform('_read_more');
      this.closeMore = this.translate.transform('_close_more');
      this.updateTruncatedDescription();
      this.equipmentOrderStatus = 'equipmentOrderStatus' in q ? parseInt(q.equipmentOrderStatus, 2) : 0;
      this.detailShowStatus = !(this.formStatus || this.leasingStatus
        || this.formLeasingStatus || this.formDemoStatus || this.equipmentOrderStatus);

      // Back Header Title Fix
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: true,
          closeButton: false,
          hamburgerMenu: false,
          notificationIcon: false,
          title: this.data.title,
        })
      );
    });

    this.log.action('CATALOG', 'CATALOG_DETAIL_OPENED', {
      brand: this.data?.brand,
      title: this.data?.title,
      productId: this.data?.productId,
      productGroupId: this.data?.productGroupId,
      categoryName: this.data.productGroupName,
      productName: this.data.productName,
      isPrice: this.data.isPrice
    }).subscribe();


    this.store.dispatch(new GetCatalogEquipmentDetailAction(this.data.productId, this.menuId));
    this.catalogEquipmentDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(catalogEquipmentDetail => {
        this.equipmentDetail = catalogEquipmentDetail;
        this.log.action('CATALOG', 'GET_CATALOG_EQUIPMENT_DETAIL', {
          productId: catalogEquipmentDetail?.productId,
          brand: catalogEquipmentDetail?.brand,
          productName: catalogEquipmentDetail?.productName
        });
      });

    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        const isBorusanUser = this.store.selectSnapshot(LoginState.user);
        this.systemFeatureLeasingCalculation = systemFeature('leasing_calculation', features, false);
        if (!isBorusanUser?.isLdapLogin) {
          this.systemFeatureEquipmentOrder = systemFeature('equipment_order', features, true);
        }

        if (isBorusanUser?.isLdapLogin) {
          this.systemFeatureEquipmentOrder = systemFeature('equipment_order_borusan_user', features, true);
        }
      });
  }

  handleSetIframe() {
    this.store.dispatch(new OpenModuleAction({
      url: this.data.detailPageUrl || fullUrl(this.data.webUrl),
      title: this.data.title
    }));

  }

  shareLink() {
    this.frameMessageService.sendMessage(FrameMessageEnum.shareContent, {
      url: this.equipmentDetail?.detailPageUrl || this.data.detailPageUrl
    });
  }

  handleSetOrderForm() {
    this.router.navigate([], {
      queryParams: { formStatus: 1 },
      queryParamsHandling: 'merge',
    }).then();
  }

  handleSetEquipmentOrderPage() {
    this.log.action('OpportunityProduct', 'Click_Offer').subscribe();
    this.store.dispatch(new ResetShoppingCartAction());

    this.router.navigate(['equipment-order', this.data?.productId]).then();
  }

  handleSetLeasingForm() {
    this.store.dispatch(new ResetLeasingAction());
    this.router.navigate([], {
      queryParams: { formLeasingStatus: 1, showHeader: 1 },
      queryParamsHandling: 'merge',
    }).then();
  }

  getLeasingData(data) {
    this.leasingData = {
      ...this.data,
      leasingForm: { ...data, date: new Date() }
    };
  }

  handleSetDemoForm() {
    this.router.navigate([], {
      queryParams: { formDemoStatus: 1 },
      queryParamsHandling: 'merge',
    }).then();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  updateTruncatedDescription() {
    if (this.data.description.length > this.maxLength) {
      this.showReadMoreButton = true;
      this.truncatedDescription = this.data.description.substring(0, this.maxLength);
    } else {
      this.showReadMoreButton = false;
      this.truncatedDescription = this.data.description;
    }
  }

  toggleDescription() {
    if (this.showReadMoreButton) {
      this.showFullDescription = !this.showFullDescription;
    }
  }
}
