<div *ngIf="!detail" class="inner-container" #wrapper>
  <ng-container [ngSwitch]="viewType">
    <ng-container *ngSwitchCase="MenuTypes.filterable">
      <app-catalog-filter-view
        [catalog]="category"
        [categories]="categories"
        (filterFormData)="setFilterData($event)"
        (onClick)="link($event)"
      ></app-catalog-filter-view>
    </ng-container>

    <ng-container *ngSwitchCase="MenuTypes.campaign">
      <app-category-campaign-view
        [category]="category"
        [dashboard]="false"
      ></app-category-campaign-view>
    </ng-container>
    <ng-container *ngSwitchCase="MenuTypes.card">
      <app-category-card-view
        [categories]="categories"
        (onClick)="link($event)"
      ></app-category-card-view>
      <div
        *ngIf="categories?.length === 0 || !categories"
        class="h-100 d-flex align-items-center justify-content-center flex-column"
      >
        <img [src]="warningIcon" width="72" height="72" />
        <div class="h5 mt-3">
          {{ "_empty_catalog_list" | translate }}
        </div>
      </div>
    </ng-container>
    <ng-container *ngSwitchCase="MenuTypes.app">
      <app-app-link-view
      [category]="category"
      ></app-app-link-view>
    </ng-container>

    <ng-container *ngSwitchDefault>
      <app-category-list-view
      [categories]="categories"
      (onClick)="link($event)"
      ></app-category-list-view>
    </ng-container>
  </ng-container>
</div>
<app-catalog-detail *ngIf="!!detail" [data]="detail" [menuId]="menuId"></app-catalog-detail>
<!--<app-iframe-view></app-iframe-view>-->
