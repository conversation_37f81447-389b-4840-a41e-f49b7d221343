@import "src/environments/scss/environment";

.link-categories {
  display: flex;
  flex-wrap: wrap;
  margin: -10px;
  .category {
    flex-basis: calc(50% - 20px);
    flex-grow: 0;
    margin: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    padding: 10px;
    border: 1px solid #f1f1f1;
    box-shadow: 0px 0px 6px 0px rgba(210, 210, 210, 0.64);
    .image-container {
      width:100%;
      img {
        width:100%
      }
    }
    .title {
      font-size:14px;
      color:#505050;
      margin-top:10px;
      text-align: center;
      line-height: 18px;
    }
  }
  &.list-view {
    border-top:1px solid #DBDBDB;
    margin-top:2px;
    .category {
      flex-basis: 100%;
      margin:0;
      padding:0;
      box-shadow: none;
      border:none;
      border-bottom:1px solid #DBDBDB;
      border-radius: 0;
      .title {
        text-align: left;
        padding:15px 15px;
        margin: 0;
        font-size:16px;
        background: url("#{$assets}/chevron.svg") no-repeat calc(100% - 20px) center;
      }
    }
  }
}


.order-form {
  .form-item {
    display: block;
    margin-bottom: 15px;
    input[type=text],
    input[type=number] {
      width: 100%;
      background: #ECF2F4;
      border: 1px solid #D7E5EA;
      border-radius: 8px;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      box-sizing: border-box;
    }
    textarea {
      width: 100%;
      background: #ECF2F4;
      border: 1px solid #D7E5EA;
      border-radius: 8px;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      box-sizing: border-box;
      min-height: 100px;
      font-family: 'Poppins', sans-serif;
    }
    select {
      width: 100%;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      border-radius: 8px;
      border: 1px solid #D7E5EA;
      background: #ECF2F4;
      font-family: 'Poppins', sans-serif;
    }
    .legal-text {
      font-size: 12px;
      line-height: 15px;
      max-height: 100px;
      overflow: auto;
      margin: 20px 0;
    }
    .confirmation {
      width: 100%;
      display: inline-block;
      .cb-area {
        float:left;
        width:30px;
        input {
          width:20px;
          height: 20px;
          border-radius: 8px;
          border: 2px solid #868686;
          position: relative;
          top:2px;
        }
      }
      .legal-text-area {
        float:left;
        width: calc(100% - 45px);
        font-size: 12px;
        line-height: 15px;
      }
    }
    .phone {
      font-weight: bold;
      margin: 20px 0;
    }
    .btn {
      background: #FFA300;
      padding: 15px;
      border-radius: 4px;
      font-weight: bold;
      width: 100%;
      color: #fff;
      font-size: 16px;
    }

    .field-error {
      display: block;
      color: red;
      margin-top:5px;
    }
  }
}

.form-result {
  position: relative;
  height: calc(100vh - 80px);
  .content {
    position: absolute;
    width: 100%;
    top: 50%;
    left: 0;
    transform: translateY(-100%);
    text-align: center;
    p {
      font-size: 22px;
      line-height: 29px;
      font-weight: bold;
      margin: 20px 0;
    }
    button {
      background: #FFA300;
      padding: 10px 15px;
      border-radius: 4px;
      font-weight: bold;
      width: initial;
      color: #fff;
      font-size: 16px;
    }
  }
}
