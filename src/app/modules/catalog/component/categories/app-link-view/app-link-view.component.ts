import { Component, Input, OnInit } from '@angular/core';
import { Store } from '@ngxs/store';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { environment } from '../../../../../../environments/environment';
import { MenuModel } from '../../../../customer/model/menu.model';
import { getOS } from '../../../../../util/os.util';
import { FrameMessageService } from '../../../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../../../core/enum/frame-message.enum';

@Component({
  selector: 'app-app-link-view',
  templateUrl: './app-link-view.component.html',
  styleUrls: ['./app-link-view.component.scss']
})
export class AppLinkViewComponent implements OnInit {

  @Input() category: MenuModel | any;

  private language: string;

  getOS = getOS;

  constructor(
    private readonly store: Store,
    private readonly frameMessageService: FrameMessageService
  ) { }

  ngOnInit(): void {
    this.language = this.store.selectSnapshot(LoginState.language);
  }

  redirectToApp(os) {
    return this.frameMessageService.sendMessage(FrameMessageEnum.openStore, {
      url: os === 'Android' ? this.category.androidUrl : (
        os === 'IOS' ? this.category.iosUrl : ''
      )
    });
  }

  /**
   * @unused
   */
  getImage(store: string) {
    const name = store + '-' +
      (['tr', 'ru', 'en'].indexOf(this.language) === -1 ? 'en' : this.language)
      + '.png';
    return environment.assets + '/badges/' + name;
  }
}
