import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MenuModel } from '../../../../customer/model/menu.model';

@Component({
  selector: 'app-category-list-view',
  templateUrl: './category-list-view.component.html',
  styleUrls: ['./category-list-view.component.scss'],
})
export class CategoryListViewComponent {
  @Input() categories: MenuModel[];
  @Output() onClick: EventEmitter<{
    item: MenuModel;
    index: number;
  }> = new EventEmitter();

  onClickItem(item: MenuModel, index: number) {
    this.onClick.emit({ item, index });
  }
}
