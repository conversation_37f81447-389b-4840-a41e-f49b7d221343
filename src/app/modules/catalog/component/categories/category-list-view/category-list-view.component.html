<div class="link-categories list-view">
  <div *ngFor="let item of categories; let i = index" class="category">
    <a (click)="onClickItem(item, i)"

       appClickLog [section]="'CATALOG'" [subsection]="'SUB_CATALOG_CLICK'"
       [data]="{
         id: item?.id,
         title: item?.title}"
    >
      <div class="title">{{ item.title }}

        <div class="float-right mr-3">
          <app-catalog-tag [tags]="item.tagList"></app-catalog-tag>
        </div>
      </div>
    </a>
<!--    <app-app-link-view *ngIf="item.isApp" [category]="item"></app-app-link-view>-->
  </div>
</div>
