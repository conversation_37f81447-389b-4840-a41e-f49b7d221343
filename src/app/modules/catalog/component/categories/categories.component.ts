import { Component, ElementRef, Input, OnD<PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { Observable, Subject, SubscriptionLike } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { CatalogState } from '../../../customer/state/catalog/catalog.state';
import { FetchCategoryAction, OpenMobileCatalogAction, UpdateCatalogAction, } from '../../../customer/state/catalog/catalog.actions';
import { ActivatedRoute, NavigationStart, Router } from '@angular/router';
import { MenuModel, MenuTypes } from '../../../customer/model/menu.model';
import { Location } from '@angular/common';
import { HeaderStatusAction } from '../../../customer/state/customer/customer.actions';
import { CustomerState } from '../../../customer/state/customer/customer.state';
import { LogService } from '../../../../shared/service/log.service';
import { filter, takeUntil } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { UserState } from '../../../customer/state/user/user.state';
import { SanitizedCustomerModel } from '../../../customer/model/sanitized-customer.model';
import { v4 as uuidv4 } from 'uuid';
import { GetBasisSettingsAction } from '../../../../shared/state/settings/settings.actions';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import { SettingsResponse, SettingsTagItemMapped } from '../../../customer/response/settings.response';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { CatalogService } from '../../service/catalog.service';


@Component({
  selector: 'app-categories',
  templateUrl: './categories.component.html',
  styleUrls: ['./categories.component.scss'],
})
export class CategoriesComponent implements OnInit, OnDestroy {
  @Input()
  categories: MenuModel[] = [];
  category: MenuModel;

  @Select(CatalogState.catalog)
  catalog$: Observable<MenuModel[]>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(SettingsState.basic)
  basicSettings$: Observable<SettingsResponse>;

  @ViewChild('wrapper') Wrapper: ElementRef;
  detail: any = null;

  menuId: string;

  MenuTypes = MenuTypes;
  viewType: MenuTypes = MenuTypes.list;
  catalogFetchKey = 'newFetchUrl';
  warningIcon = `${environment.assets}/warning.svg`;
  // catalogFetchKey = 'fetchUrl';

  protected mobileMenus: any = { // this menu is overwritten by setting/basic
    revizyonID: '1cab4f75-3536-4c18-951c-0287bd4b8e8d',
    bDahaID: '682b2ba6-43f2-458a-a9e4-ef5087209618',
    ikinciElID: '329d8557-5553-43d3-9b70-c659bb740722',
    ikinciElIDKZ: '0dedb337-45e6-4d97-87bd-9cae801bb54d',
    // kiralamaID: '43b5c8ea-9009-4605-b42f-8815f197b87d',
    kiralamaID: '8ceffe33-1b6a-477d-94f6-************',
  };
  protected mobileMenuTags: SettingsTagItemMapped = {};
  private filterFormData: any;

  protected subs: SubscriptionLike[] = [];
  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly location: Location,
    private readonly translateService: TranslateService,
    private readonly log: LogService,
    private readonly route: ActivatedRoute,
    private readonly translate: TranslatePipe,
    private frameMessageService: FrameMessageService,
    private catalogService: CatalogService,
  ) {
  }

  ngOnInit(): void {
    // back and forward operation
    const locationSub = this.location
      .subscribe(p => {
        // console.log('location change', p);
        // to update content, fire update catalog
        if (p.url.startsWith('/catalog')) {
          this.store.dispatch(new UpdateCatalogAction());
        }

        // restore scroll
        setTimeout(() => {
          // console.log('replace scroll', p.state.scrollPosition);
          if (this.Wrapper) {
            this.Wrapper.nativeElement.scrollTop = p.state?.scrollPosition || 0;
          }
          this.storeHistory();
        });

        this.filterFormData = null;
      });
    this.subs.push(locationSub);

    // store history when route changes
    this.router.events
      .pipe(takeUntil(this.subscriptions$))
      .pipe(filter(e => e instanceof NavigationStart))
      .subscribe(() => {
        // console.log('events', val);
        this.storeHistory();
      });

    // reset headers
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: 'Catalog',
      })
    );

    // Settings
    this.store.dispatch(new GetBasisSettingsAction());
    this.basicSettings$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(settings => {
        if (settings && settings.defaults) {
          this.mobileMenus = settings.defaults;
          this.mobileMenuTags = settings.defaultTags?.reduce((acc, item) => {
            acc[item.value] = item.name;
            return acc;
          }, {});
        }
      });

    const catalogSubs = this.catalog$.subscribe((catalog) => {
      // console.log('SUBS categories', catalog);
      if (this.Wrapper) {
        this.Wrapper.nativeElement.scroll(0, 0);
      }
      if (catalog) {
        let pointer: any = catalog;
        this.urlParts().map((item, index) => {
          if (item.indexOf('?') > -1) {
            return;
          }
          pointer = pointer?.[this.catalogFetchKey] && this.urlParts()?.[index++]
            ? this.fetchCatalog(pointer)
            : item === 'categories' && pointer?.categories?.length
              ? pointer.categories
              : pointer
                ? this.getProduct(pointer, item)
                : null;

        });
        this.category = pointer;
        // console.log('=====> pointer', pointer);

        this.store.dispatch(
          new HeaderStatusAction({
            title: pointer?.title,
          })
        );

        if (pointer?.isOuterMenuContent) {
          this.viewType = MenuTypes.campaign;
          return;
        }
        if (pointer?.isApp) {
          // console.log('data',pointer?.detail);
          // this.viewType = MenuTypes.app;
          // return;
        }

        if (pointer?.detail) {
          this.detail = pointer;
          return;
        }
        this.detail = null;

        if (pointer?.[this.catalogFetchKey]) {
          console.log('===>FETCH', pointer);
          this.fetchCatalog(pointer);
        }

        this.categories = pointer?.categories;

        // expertize form hidden
        // this.categories = this.categories?.filter(c => c.id !== '56c451f8-4dc4-448d-9ce4-925a29322ff0');

        if (!pointer?.viewType && pointer?.categories?.[0] && pointer?.categories?.[0].imageUrl) {
          this.viewType = MenuTypes.card;
        } else {
          this.viewType = pointer?.viewType || MenuTypes.list;
        }
      }
    });
    this.subs.push(catalogSubs);
  }

  link({ item, index }: { item: MenuModel; index: number }) {
    // console.log('clicked item', {
    //   item,
    //   this_url: this.location.path(),
    //   index,
    // });

    if (item?.tagList?.includes('isPDF')) {
      // TODO Düzeltilecek Geçici olarak yapıldı. Download a Bağlanması gerekiyor.
      this.frameMessageService.sendMessage(FrameMessageEnum.openCatalog, {
        id: '1cab4f75-3536-4c18-951c-0287bd4b8e8d',
        title: item.title,
        webUrl: item.webUrl,
      });
      return;
    }

    if (item?.tagList?.includes('inspectionForm')) {
      this.openInspectionForm();
      return;
    }

    if ((this.mobileMenus?.quotationId && item.id === this.mobileMenus?.quotationId)
      || item.id === '9521da4c-ed08-419d-a53e-748f51d6d9e8') {
      this.openPartForm(item);
      return;
    }

    // Mobile Menus
    if (Object.values(this.mobileMenus).indexOf(item.id) !== -1) {
      this.store.dispatch(new OpenMobileCatalogAction(item));
      return;
    }
    // Mobile Menus
    if (this.isMobileMenu(item)) {
      this.store.dispatch(new OpenMobileCatalogAction(item));
      return;
    }

    if (this.catalogService.clickHandler(item)) {
      return;
    }

    const currentUrl = this.location.path().substring(0, this.location.path().lastIndexOf('?'))
      ? this.location.path().substring(0, this.location.path().lastIndexOf('?'))
      : this.location.path();
    const queryParams = this.route.snapshot.queryParams;

    this.router.navigate(
      [currentUrl, 'categories', item?.id || item?.productId || item?.productGroupId],
      {
        queryParams
      }
    )
      .then(() => {
        console.log('===>Navigate && FETCH ', item);
        this.fetchCatalog(item);
      });

  }

  protected fetchCatalog(item, currentUrl = null) {


    if (item?.[this.catalogFetchKey]) {
      this.menuId = item.id;
      console.log(':::MEnu fetched 222', this.menuId);

      this.store.dispatch(
        new FetchCategoryAction(
          item[this.catalogFetchKey],
          this.urlParts(),
          currentUrl || this.location.path())
      );
    } else {
      if (item?.fetched) {
        this.menuId = item.id;
      }
      this.store.dispatch(new UpdateCatalogAction());
    }
  }

  protected urlParts(): any[] {
    const url = this.location.path();
    const rUrl = url.replace('/catalog/', '').split('/');

    if (rUrl?.length) {
      let rl = rUrl[rUrl.length - 1];
      let rp = rUrl[rUrl.length - 1];
      if (rUrl[rUrl.length - 1].indexOf('?') > 0) {
        rp = rl.slice(rl.indexOf('?'), rl.length);
        rl = rl.slice(0, rl.indexOf('?'));
        rUrl[rUrl.length - 1] = rl;
        rUrl.push(rp);
      }
    }

    return rUrl;
  }

  ngOnDestroy(): void {
    this.subs.map((sub) => {
      sub.unsubscribe();
    });

    window.history.replaceState({}, '');

    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  private storeHistory() {
    history.scrollRestoration = 'manual';
    window.history.replaceState({
      scrollPosition: this.Wrapper?.nativeElement.scrollTop,
      filterFormData: this.filterFormData,
    }, '');
    // console.log('store history::::: ', window.history);
  }

  private openPartForm(item) {
    const iframeAction = {
      url: environment.frameUrl + '/form/request-equipment-part',
      params: {
        showHeader: false,
        navigatedPage: 'Catalog',
      },
      active: true,
      closeButton: true,
      backButton: false,
      // pageTitle: item?.title,
      pageTitle: this.translateService.instant('_request_parts_offer'),
      previous: {
        headerStatus: this.store.selectSnapshot(CustomerState.header),
        url: 'history.back'
      },
    };
    // this.store.dispatch(new IframeAction(iframeAction, true));

    this.router.navigate(['module', item.id], { state: { iframeAction } }).then();
  }


  protected isMobileMenu(item: MenuModel) {
    return item?.tagList?.find(tag => this.mobileMenuTags?.[tag]);
  }

  protected getProduct(pointer, item) {
    return pointer.find(x => x?.id === item
      || x?.productGroupId === item
      || x?.productId === item);
  }

  setFilterData(e: any) {
    this.filterFormData = e;
    this.storeHistory();
  }

  protected openInspectionForm() {
    const iframeAction = {
      url: environment.frameUrl + '/expertise/init',
      params: {
        showHeader: false,
        navigatedPage: 'dashboard',
      },
      active: true,
      closeButton: true,
      backButton: false,
      pageTitle: this.translate.transform('_expertise_form'),
      previous: {
        headerStatus: this.store.selectSnapshot(CustomerState.header),
        navigate: this.location.path(),
      },
    };
    this.router.navigate(['module', uuidv4()], { state: { iframeAction } }).then();
  }
}
