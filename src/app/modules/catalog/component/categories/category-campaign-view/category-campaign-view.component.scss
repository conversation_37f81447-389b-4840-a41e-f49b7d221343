.link-categories {
  display: flex;
  align-items: center;
  gap: 1rem;
  // flex-direction: column;
  // flex-wrap: nowrap;
  // margin-bottom: 20px;
  // min-height: 440px;
  overflow: auto;
  scroll-snap-type: x mandatory;
  //scroll-snap-stop: always;
  padding-right: 1rem;
  
  &::-webkit-scrollbar{
    display: none;
  }

  .category-new {
    width: 100%;
    // height: 100%;
    max-height: max-content;
    min-height: 85px;
    border: 1px solid #f1f1f1;
    // box-shadow: 0px 30px 40px rgb(0 0 0 / 18%), 0px 10px 20px rgb(0 0 0 / 10%),
    //   0px 6px 4px rgb(0 0 0 / 3%);
    margin-bottom: 8px;
    border-radius: 8px;
    scroll-snap-align: center;
    scroll-snap-stop: always;
    width: 330px;
    height: 160px;

    img {
      border-radius: 8px;
      width: inherit;
      min-height: 85px;
      height: 100%;
    }

    &:nth-child(1){
      margin-left: 1rem;
    }

  }
}

.scroll-driven-bar{
  gap: 10px;

  .point-dot{
    width: 7px;
    height: 7px;
    background-color: #C0C0C0;
    border-radius: 50%;

    &.selected-point-dot{
      background-color: #FFA300;
    }
  }

  @media screen and (min-width: 575px){
    .point-dot{
      display: none;
    }
  }
}

.in-campaigns{
  flex-direction: column;
  padding-right: 0;

  .category-new{
    &:nth-child(1){
      margin-left: 0 !important;
    }
  }
}

.only-campaign{
  justify-content: center;
  padding-right: 0;

  .category-new{
    &:nth-child(1){
      margin-left: 0 !important;
    }
  }
}

.campaign-loading{
  width: 90%;
  margin: 0 auto;
  margin-bottom: .5rem;
}