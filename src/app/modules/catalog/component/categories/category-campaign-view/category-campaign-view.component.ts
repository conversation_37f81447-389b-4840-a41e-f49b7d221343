import { Location } from '@angular/common';
import { Component, ElementRef, HostListener, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject, combineLatest } from 'rxjs';
import { environment } from 'src/environments/environment';
import { OpenModuleAction, OpenStoreAction } from '../../../../../shared/state/common/common.actions';
import { LoginState } from '../../../../authentication/state/login/login.state';
import { CampaignModel, MenuModel } from '../../../../customer/model/menu.model';
import { CatalogAction, GetCampaignsAction } from '../../../../customer/state/catalog/catalog.actions';
import { CatalogState } from '../../../../customer/state/catalog/catalog.state';
import { takeUntil } from 'rxjs/operators';
import { CatalogService } from '../../../service/catalog.service';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { SanitizedCustomerModel } from 'src/app/modules/customer/model/sanitized-customer.model';

@Component({
  selector: 'app-category-campaign-view',
  templateUrl: './category-campaign-view.component.html',
  styleUrls: ['./category-campaign-view.component.scss']
})
export class CategoryCampaignViewComponent implements OnInit, OnDestroy {
  @Input()
  category: MenuModel;
  @Input()
  dashboard = true;

  @Select(CatalogState.campaigns)
  campaigns$: Observable<CampaignModel[]>;
  @Select(CatalogState.loading)
  campaignsLoading$: Observable<boolean>;
  @Select(CatalogState.catalog)
  catalog$: Observable<MenuModel[]>;
  @Select(LoginState.headerCompanies)
  headerCompanies$: Observable<any>;
  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  lang = '';
  campaigns = [];
  Array = Array;
  currentScrollElementIndex = 0;
  campaignsLoading = true;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly location: Location,
    private readonly router: Router,
    private readonly catalogService: CatalogService
  ) {}
  @ViewChild('scrollContainer') scrollContainer: ElementRef;
  @ViewChild('item') item: ElementRef;


  ngOnInit(): void {
    this.campaignsLoading$.subscribe(loading => {
      this.campaignsLoading = loading;
    })
    
    this.catalog$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(() => {
        const currentCustomer = this.store.selectSnapshot(UserState.currentCustomer);

        if (!this.store.selectSnapshot(CatalogState.campaigns).length){
          this.store.dispatch(new GetCampaignsAction(`/campaign/list?companyid=${currentCustomer?.publicMenuHeaderCompany}`));
        }

        this.currentScrollElementIndex = 0;
      });

    this.campaigns$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(campaignData => {
        if (campaignData){
          this.campaigns = campaignData;
        }
      });
    this.lang = this.store.selectSnapshot(LoginState.language);
  }

  onScroll(event: Event) {
    const element = event.target as HTMLElement; // Scroll olan container
    const scrollLeft = element?.scrollLeft + (32 + ((this.campaigns.length) * 16)) ; // Scroll olan container'ın sola olan uzaklığı
    const itemWidth = this.item?.nativeElement?.offsetWidth; // Scroll içindeki item'ın widthi
    const currentScrollElementIndex = Math.floor(scrollLeft / (itemWidth)); // scroll içinde tamamen gözüken item'ın indexi

    this.currentScrollElementIndex = currentScrollElementIndex;
  }

  getImageUrl(id: string) {
    return `${environment.imageApi}/image/campaign?campaignId=${id}&lang=${this.lang}`;
  }

  onClickItem(item: CampaignModel, index: number) {
    // const url = item.actionUrl + (item.actionUrl.includes('?') ? '&' : '?') +
    //   `lang=${this.lang}`;
    const url = item.actionUrl;

    if (url?.startsWith('borusancat360:')) {
      this.store.dispatch(new OpenStoreAction({ url }));
      return;
    }

    this.store.dispatch(
      new OpenModuleAction({
        url,
        title: item.title
      })
    );
  }

  scrollToItem(index: number) {
    const container = this.scrollContainer?.nativeElement; // Scroll containerını referans aldık
    const targetElement = container?.children[index]; // Scroll içindeki gitmek istediğimiz elemanın referansı

    if (targetElement) {
      targetElement.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center',
      });
    }
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
