<div class="campaign-loading" *ngIf="campaignsLoading">
  <app-skeleton-loader [height]="175"></app-skeleton-loader>
</div>
<div #scrollContainer class="link-categories campaign-view" [ngClass]="{'in-campaigns': !dashboard, 'only-campaign': campaigns.length === 1 }" (scroll)="onScroll($event)">
  <ng-container>
    <div
      *ngFor="let item of campaigns; let i = index"
      id="{{i}}"
      class="category-new"
      (click)="onClickItem(item, i)"
      #item
      appClickLog
      [section]="'BANNER'"
      [subsection]="'BANNER_CLICK'"
      [data]="{id:item.id, title: item.title, url: item.actionUrl}"
    >
      <img [src]="getImageUrl(item.id)" [alt]="item.title"/>
    </div>
  </ng-container>
</div>
<div class="scroll-driven-bar d-flex align-items-center justify-content-center mb-3" *ngIf="dashboard">
  <div 
    *ngFor="let _ of Array(campaigns.length); let i = index" 
    class="point-dot" 
    (click)="scrollToItem(i)"
    [ngClass]="{'selected-point-dot': i === currentScrollElementIndex}"
  >
  </div>
</div>
