<div class="link-categories card-view">
  <div *ngFor="let item of categories; let i = index" class="category">
    <a (click)="onClickItem(item, i)"
       appClickLog [section]="'CATALOG'" [subsection]="'SUB_CATALOG_CLICK'"
       [data]="{
         id: item?.id,
         title: item?.title,
         productId: item?.productId,
         productGroupName: item?.productGroupName,
         productGroupId: item?.productGroupId
        }"
    >

      <div id="image-container">
        <div id="element">
          <img height="" [src]="item.imageUrl" [alt]="item.title"/>
        </div>
      </div>
      <!--      <div class="image-container">-->
      <!--        <img height="" [src]="item.imageUrl" [alt]="item.title" />-->
      <!--      </div>-->
      <div class="title">{{ item.title }}</div>
    </a>
  </div>
</div>
