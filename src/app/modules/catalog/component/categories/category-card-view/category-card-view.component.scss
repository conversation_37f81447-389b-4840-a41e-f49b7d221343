.link-categories {
  display: flex;
  flex-wrap: wrap;
  margin: -10px;

  .category {
    flex-basis: calc(50% - 20px);
    flex-grow: 0;
    margin: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    padding: 10px;
    border: 1px solid #f1f1f1;
    box-shadow: 0px 0px 6px 0px rgba(210, 210, 210, 0.64);

    .title {
      font-size: 14px;
      color: #505050;
      margin-top: 10px;
      text-align: center;
      line-height: 18px;

      // NOTE - Uzun translateler için eklendi.
      word-break: break-word;
    }
  }
}

#image-container {
  display: inline-block;
  position: relative;
  width: 100%;
  &:before {
    content: "";
    display: block;
    margin-top: 75%;
  }
}

#element {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  //background-color: #f2f2f2;

  img {
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    max-width: 100%;
    max-height: 100%;
  }
}
