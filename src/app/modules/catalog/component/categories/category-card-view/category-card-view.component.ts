import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MenuModel } from '../../../../customer/model/menu.model';

@Component({
  selector: 'app-category-card-view',
  templateUrl: './category-card-view.component.html',
  styleUrls: ['./category-card-view.component.scss'],
})
export class CategoryCardViewComponent {
  @Input() categories: MenuModel[];
  @Output() onClick: EventEmitter<{
    item: MenuModel;
    index: number;
  }> = new EventEmitter();

  onClickItem(item: MenuModel, index: number) {
    this.onClick.emit({ item, index });
  }
}
