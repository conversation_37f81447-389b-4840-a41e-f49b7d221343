import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  getFormErrorMessage,
  isShowFormError,
  isShowFormErrorTouched,
  validateAllFormFields,
} from '../../../../util/form-error.util';
import { DefinitionState } from '../../../definition/state/definition/definition.state';
import { Observable } from 'rxjs';
import { Country } from '../../../definition/model/country.model';
import { Select, Store } from '@ngxs/store';
import { GetAllCountryListAction } from '../../../definition/state/definition/definition.actions';
import { CustomerService } from '../../../customer/service/customer.service';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { CustomValidator } from '../../../../util/custom-validator';
import { HeaderStatusAction } from '../../../customer/state/customer/customer.actions';
import { TranslatePipe } from '@ngx-translate/core';
import { LoginState } from '../../../authentication/state/login/login.state';
import { UserState } from '../../../customer/state/user/user.state';
import { Router } from '@angular/router';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';
import { UserModel } from 'src/app/modules/authentication/model/user.model';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import {SettingsState} from '../../../../shared/state/settings/settings.state';

@Component({
  selector: 'app-demo-request-form',
  templateUrl: './demo-request-form.component.html',
  styleUrls: ['./demo-request-form.component.scss']
})
export class DemoRequestFormComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isShowFormErrorTouched = isShowFormErrorTouched;

  // tslint:disable-next-line: no-output-on-prefix
  @Output()
  onBackPressed: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input()
  data: any;

  user: UserModel;
  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [
      Validators.required
    ]),
    Surname: new FormControl(null, [
      Validators.required
    ]),
    CompanyName: new FormControl(null, [
      Validators.required
    ]),
    PhoneNumber: new FormControl(null, []),
    CompanyPhoneNumber: new FormControl(null, [
      Validators.required,
      Validators.minLength(7),
    ]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    Description: new FormControl(null, [Validators.required]),
    CountryCode: new FormControl(null, [Validators.required]),
    Brand: new FormControl(null, []),
    Model: new FormControl(null, []),
  });
  formSendStatus = false;
  agreementTypeEnum = AgreementTypeEnum;
  loading: boolean;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly translate: TranslatePipe,
    private readonly router: Router
  ) {
    this.data =
      this.router.getCurrentNavigation()?.extras?.state?.data || this.data;
  }

  ngOnInit(): void {
    this.user = this.store.selectSnapshot(LoginState.user);
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(new GetAllCountryListAction());
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: 'Demo Talep Et',
      })
    );

    const user = this.store.selectSnapshot(LoginState.user);
    const userData = this.store.selectSnapshot(UserState.basics);
    const customer = this.store.selectSnapshot(UserState.currentCustomer);

    const firstName = userData?.firstName || user?.firstName;
    const lastName = userData?.lastName || user?.lastName;
    const mobile = userData?.mobile || user?.mobile;
    const email = userData?.email || user?.email;
    const patchValues = {
      Name: firstName,
      Surname: lastName,
      Email: email.toLowerCase(),
      PhoneNumber: mobile,
      CompanyName: customer?.customer?.name,
      CountryCode: customer?.groupKey ? customer?.groupKey : 'TR',
      Brand: this.data?.brand,
      Model: this.data?.model,
    };
    this.form.patchValue(patchValues);
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  onSubmitForm() {
    console.log('submit', this.form.value);
    if (this.form.valid) {
      console.log('valid');
      //this.sendForm();
    } else {
      validateAllFormFields(this.form);
    }
  }

  sendForm() {
    //
  }

  back() {
    this.onBackPressed.emit(true);
    history.back();
  }

  getCountryByCode(countryCode: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.code === countryCode);
  }

  isActiveCountry(countryCode: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.code === countryCode);

    return country?.isActive;
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

}
