<div class="form-container" id="form-container">
  <div class="px-4" *ngIf="!formSendStatus">
    <form (submit)="onSubmitForm()" [formGroup]="form">
      <app-info-box [title]="'_info' | translate">
        <PERSON><PERSON><PERSON><PERSON> i<PERSON>in demo talep edebilirsiniz.
      </app-info-box>
      <div class="form-group">
        <input
          [placeholder]="'_name_surname' | translate"
          class="form-control form-control"
          formControlName="Name"
          type="text"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Name) | translate }}
        </div>
      </div>
      <div class="form-group">
        <input
          [placeholder]="'_name_surname' | translate"
          class="form-control form-control"
          formControlName="Surname"
          type="text"
        />
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Surname) | translate }}
        </div>
      </div>
      <div class="form-group">
        <input
          appInputMaxLength
          appFormScroll
          [name]="'Phone'"
          [placeholder]="'_company_phone_number' | translate"
          class="form-control form-control"
          formControlName="CompanyPhoneNumber"
          (input)="onInputPhone($event)"
          type="tel"
          minlength="7"
        />
        <div
          [ngClass]="{
            'd-block':
              !form.get('CompanyPhoneNumber').valid &&
              form.get('CompanyPhoneNumber').touched
          }"
          class="invalid-feedback pl-3"
        >
          {{
            getFormErrorMessage(form.controls.CompanyPhoneNumber) | translate
          }}
        </div>
      </div>
      <div class="form-group">
        <input
          appInputMaxLength
          [name]="'CompanyName'"
          [placeholder]="'_company_name' | translate"
          class="form-control"
          formControlName="CompanyName"
          type="text"
          minlength="3"
        />

        <div
          [ngClass]="{
            'd-block':
              !form.get('CompanyName').valid && form.get('CompanyName').touched
          }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
        </div>
      </div>
      <div class="form-group">
        <ng-select
          class="service-drp"
          [searchable]="false"
          *ngIf="!(countryListLoading$ | async)"
          [placeholder]="'_country' | translate"
          [clearable]="false"
          formControlName="CountryCode"
        >
          <ng-option
            *ngFor="let country of countryList$ | async"
            [value]="country.code"
            >{{ country.name }}</ng-option
          >
        </ng-select>
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.CountryCode) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.CountryCode) | translate }}
        </div>
      </div>
      <div class="form-group">
        <textarea
          appInputMaxLength
          appFormScroll
          [name]="'Description'"
          [placeholder]="'_description' | translate"
          [rows]="5"
          class="form-control"
          formControlName="Description"
          minlength="3"
          style="resize: none;"
        ></textarea>
        <div
          [ngClass]="{
            'd-block':
              !form.get('Description').valid && form.get('Description').touched
          }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Description) | translate }}
        </div>
      </div>
      <div class="form-group" *ngIf="!user">
        <div class="confirmation">
          <div class="cb-area">
            <app-agreement-list
              [form]="form"
              [formType]="agreementTypeEnum.QuotationRequest"
            ></app-agreement-list>
          </div>
        </div>
      </div>
      <div class="form-group">
        <input
          appClickLog
          [section]="'DEMO'"
          [subsection]="'DEMO_REQUEST_CLICK'"
          [data]="{
            brand: this.data?.brand,
            productGroupId: this.data?.productGroupId,
            productId: this.data?.productId,
            title: this.data?.title
          }"
          [value]="'Demo Talep Et'"
          class="btn btn-warning btn-gradient btn-block text-white"
          type="submit"
        />
      </div>
    </form>
  </div>

  <app-success-modal *ngIf="formSendStatus" message="_successfully_send_form">
    <div
      class="btn btn-warning btn-gradient btn-block text-white shadow"
      (click)="back()"
    >
      {{ "_return_back" | translate }}
    </div>
  </app-success-modal>

  <app-loader [show]="loading"></app-loader>
</div>

