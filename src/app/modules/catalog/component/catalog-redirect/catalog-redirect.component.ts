import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { map } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { Store } from '@ngxs/store';
import { CatalogFindAction } from '../../../customer/state/catalog/catalog.actions';
import { CatalogMenu, FoundedCatalogModel } from '../../../customer/model/category.model';
import { CatalogState } from '../../../customer/state/catalog/catalog.state';
import { ErrorModalComponent } from '../../../../shared/component/error-modal/error-modal.component';
import { ModalService } from '../../../../shared/service/modal.service';
import { TranslateService } from '@ngx-translate/core';

@Component({
  template: `
    <app-loader [show]="true"></app-loader>`,
  selector: 'app-catalog-redirect'
})
export class CatalogRedirectComponent implements OnInit, OnDestroy {
  protected foundedCatalog: FoundedCatalogModel;

  protected catalogCreatorUrl: any;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly router: Router,
    private readonly store: Store,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    const params = this.activatedRoute.snapshot.params;
    this.store.dispatch(new CatalogFindAction(params?.top, params?.id))
      .pipe(map(() => this.store.selectSnapshot(CatalogState.foundedCatalog)))
      .subscribe((found) => {
        if (found) {
          this.foundedCatalog = found;
          const catalogTemp = this.catalogPathFinder(this.foundedCatalog?.menu, 'categories')?.reverse();
          if (this.catalogPathFinder(this.foundedCatalog?.products, 'products')) {
            catalogTemp?.push(...this.catalogPathFinder(this.foundedCatalog?.products, 'products')?.reverse());
          }

          if (!catalogTemp) {
            this.modalService.errorModal({
              message: this.translateService.instant('_catalog_not_found'),
              button: this.translateService.instant('_ok'),
              buttonClick: (modal: ErrorModalComponent) => {
                modal.close();
              }
            });
            this.router.navigate(['/']).then();
            return;
          }
          this.catalogCreatorUrl = catalogTemp.join('/categories/').split('/');
          this.redirectToCatalog();
        }
      }, () => {
        this.router.navigate(['/']).then();
        return;
      });
  }

  catalogPathFinder(listData, subListName) {
    if (listData?.length) {
      for (const item of listData) {
        const list = [];
        this.findBySelected(item, list, subListName);
        if (item?.isSelected === true) {
          list.push(item?.id || item?.productId || item?.productGroupId);
          return list;
        }
        if (list?.length > 0) {
          list.push(item?.id || item?.productId || item?.productGroupId);
          return list;
        }
      }
    }
    return null;
  }

  findBySelected(item: CatalogMenu | any, list, subListName): string {
    if (item.isSelected === true) {
      return item?.id || item?.productId || item?.productGroupId;
    }
    if (item?.[subListName] && item?.[subListName]?.length) {
      let desiredCatalog;
      for (const x of item?.[subListName]) {
        desiredCatalog = this.findBySelected(x, list, subListName);
        if (desiredCatalog) {
          list.push(x?.id || x?.productId || x?.productGroupId);
          return desiredCatalog;
        }
      }
    }
    return null;
  }

  redirectToCatalog() {
    console.log('redirectToCatalog:: ', this.catalogCreatorUrl);
    this.router.navigate(['catalog', ...this.catalogCreatorUrl]
      , {
        queryParams: {
          isCatalogRedirect: true
        },
        replaceUrl: true
      }).then();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
