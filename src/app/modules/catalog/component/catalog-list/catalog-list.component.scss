@import "environment";

.dashboard-links-container{
  position: relative;
  z-index: 0;
}

.dashboard-links {
  //position: absolute;
  //bottom:0;
  //width: calc(100% - 44px);
  //left:22px;
    padding: 1.5rem 22px;
    padding-bottom: calc(env(safe-area-inset-bottom, 0px) / 2 + 10px);
    position: relative;
    background-color: #fff;
    z-index: 1;
    min-height: 30%;
        
    &::before{
        content: " ";
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        border-top-right-radius: 2.5rem;
        background-color: #F9F5EF;
    }

  &.dashboard-passive-user {
    position: absolute;
    bottom: 0;
    width: 100%;
  }

  ul {
    padding: 0;
    list-style: none;
    margin-bottom: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;

    li {
      font-size: 14px;
      color: #303030;
      // background: url("#{$assets}/chevron.svg") no-repeat calc(100% - 20px) center;

      a {
        display: block;
        padding: .75rem 1.25rem;
        border-radius: 6px;
        //line-height: 16px;
        line-height: 1.5em;
        background-color: #FFFFFF;

        &:active{
          background-color: #fafafa;
        }

        &:hover{
          text-decoration: unset;
        }
      }
    }
  }
}