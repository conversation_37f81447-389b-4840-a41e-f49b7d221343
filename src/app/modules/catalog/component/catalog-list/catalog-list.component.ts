import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { UserState } from '../../../customer/state/user/user.state';
import { Observable, Subject } from 'rxjs';
import { SanitizedCustomerModel } from '../../../customer/model/sanitized-customer.model';
import { MenuModel } from '../../../customer/model/menu.model';
import { CatalogAction } from '../../../customer/state/catalog/catalog.actions';
import { CatalogState } from '../../../customer/state/catalog/catalog.state';
import { LoginState } from '../../../authentication/state/login/login.state';
import { takeUntil } from 'rxjs/operators';
import { WindowManagerService } from '../../../../shared/service/window-manager.service';
import { fullUrl } from '../../../../util/full-url.util';
import { Navigate } from '@ngxs/router-plugin';
import { PromotionAction } from '../../../promotion/state/promotion.action';
import { systemFeature } from 'src/app/util/system-feature.util';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { GetUserAgreementsAction } from 'src/app/modules/definition/state/definition/definition.actions';
import { AgreementTypeEnum } from 'src/app/modules/definition/enum/agreement-type.enum';
import { DefinitionState } from 'src/app/modules/definition/state/definition/definition.state';
import { OpenLinkAction, PageOpenedAction } from 'src/app/shared/state/common/common.actions';
import { PagesEnum } from 'src/app/shared/enum/pages.enum';
import { CustomerService } from 'src/app/modules/customer/service/customer.service';
import { Router } from '@angular/router';
import { CustomerModuleService } from '../../../customer/service/customer-module.service';
import { CatalogService } from '../../service/catalog.service';


@Component({
  selector: 'app-catalog-list',
  templateUrl: './catalog-list.component.html',
  styleUrls: ['./catalog-list.component.scss'],
})
export class CatalogListComponent implements OnInit, OnDestroy {
  protected subscriptions$: Subject<boolean> = new Subject();

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(UserState.id)
  userId$: Observable<string>;

  @Select(CatalogState.catalog)
  items$: Observable<MenuModel[]>;


  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  constructor(
    private readonly store: Store,
    private readonly windowManagerService: WindowManagerService,
    private readonly customerModuleService: CustomerModuleService,
    private readonly catalogService: CatalogService
  ) { }

  ngOnInit(): void {
    let isReturn = false;
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(currentCustomer => {
        if (currentCustomer) {
          let headerCompanies = currentCustomer.publicMenuHeaderCompany;
          if (!headerCompanies) {
            headerCompanies = this.store.selectSnapshot(LoginState.headerCompanies);
          }
          if (!currentCustomer?.passive) {
            this.store.dispatch(new GetUserAgreementsAction(AgreementTypeEnum.PromotionPortal, true));
            this.systemFeatures$.subscribe(features => {
              if (features) {
                const isBorusanUser = this.store.selectSnapshot(UserState.isBorusanUser);
                const boomCoinBorusanShow = systemFeature('loyality_point_borusan_user', features, false);
                const boomCoinShow = systemFeature('loyality_point', features, false);
                const getForceAprovalAgreement = !this.store.selectSnapshot(DefinitionState.getForceAprovalAgreement)?.length;

                if (!isReturn) {
                  isReturn = true;
                  if (isBorusanUser && boomCoinBorusanShow && getForceAprovalAgreement) {
                    this.store.dispatch(new PromotionAction());
                  } else if (boomCoinShow && !isBorusanUser && getForceAprovalAgreement) {
                    this.store.dispatch(new PromotionAction());
                  }
                }
              }
            });
          }

          this.store.dispatch(new CatalogAction(headerCompanies,
            !this.store.selectSnapshot(CatalogState.catalog)?.length));
        }
      });

    this.userId$.subscribe(id => {
      if (id) {
        const customers = this.store.selectSnapshot(UserState.customers);
        if (customers.length === 0) {
          const headerCompanies = this.store.selectSnapshot(LoginState.headerCompanies);
          this.store.dispatch(new CatalogAction(headerCompanies,
            !this.store.selectSnapshot(CatalogState.catalog)?.length));
        }
      }
    });


    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  clickItem(item: MenuModel, itemIndex) {
    if (this.checkBorusanBlockedAction(item)) {
      return;
    }
    if (item?.fetchUrl?.indexOf('campaign') > -1) {
      this.store.dispatch(new PageOpenedAction(PagesEnum.campaignListPage));
    } else {
      this.store.dispatch(new PageOpenedAction(PagesEnum.catalogListPage));
    }

    if (this.catalogService.catalogActionHandler(item)) {
      return;
    }

    if (item.isWeb) {
      return this.openWebview({
        url: fullUrl(item.webUrl),
        title: item.title
      });
    }

    if (item.tagList?.includes('innerModule')) {
      return this.customerModuleService.openModuleInner({
        relativeUrl: item.webUrl,
        pageTitle: item.title,
      });
    }

    this.store.dispatch(new Navigate(['catalog', item?.id]));
  }

  protected openWebview(data) {
    this.windowManagerService.openWebview(data);
  }

  checkBorusanBlockedAction(item: MenuModel): boolean {
    if (!this.borusanBlockedActions) {
      return false;
    }
    const enumType =
      item.tagList?.indexOf('MediaCenter') >= 0 ? BorusanBlockedActionsEnum.UseMediaCenter :
        item.tagList?.indexOf('Sitech') >= 0 ? BorusanBlockedActionsEnum.UseSitech :
          item.tagList?.indexOf('appBdaha') >= 0 ? BorusanBlockedActionsEnum.UseBdaha :
            item.tagList?.indexOf('appRental') >= 0 ? BorusanBlockedActionsEnum.UseRental :
              item.tagList?.indexOf('appPowerSystemsRental') >= 0 ? BorusanBlockedActionsEnum.UseRental :
                item.tagList?.indexOf('appUsed') >= 0 ? BorusanBlockedActionsEnum.UseUsed :
                  item.tagList?.indexOf('appPowerSystemsUsed') >= 0 ? BorusanBlockedActionsEnum.UseUsed
                    : null;
    if (enumType) {
      return this.borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum[enumType]) >= 0;
    }
    return false;
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

}
