<div class="dashboard-links-container">
  <div class="dashboard-links">
    <ul>
      <li *ngFor="let item of items$ | async; index as itemIndex">
        <a
          class="d-flex align-items-center justify-content-between catalog-link-element"
          (click)="clickItem(item, itemIndex)"
          appClickLog
          [section]="'DASHBOARD'"
          [subsection]="'CATALOG_CLICK'"
          [data]="{
            id: item?.id,
            title: item?.title
          }"
        >
          {{ item.title }}
          <div class="d-flex align-items-center justify-content-center">
            <app-catalog-tag [tags]="item.tagList"></app-catalog-tag>
            <i class="icon icon-chevron-right"></i>
          </div>
        </a>
      </li>
    </ul>
  </div>
</div>
