import { Component, Input, OnInit } from '@angular/core';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';

@Component({
  selector: 'app-equipment-order-header',
  templateUrl: './equipment-order-header.component.html',
  styleUrls: ['./equipment-order-header.component.scss']
})
export class EquipmentOrderHeaderComponent implements OnInit {
  @Input() 
  title: string;

  @Input()
  loginUser: boolean;


  constructor(
    private readonly frameMessageService: FrameMessageService
  ) { }

  ngOnInit() {
  }

  onClickBack(){
    if (!this.loginUser){
      this.frameMessageService.sendMessage(FrameMessageEnum.formBack, { action: 'publicCategory', navigate: false });
    }
    window.history.back();
  }

}
