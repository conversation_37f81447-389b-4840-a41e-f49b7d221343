import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Observable, Subject } from 'rxjs';
import { GetCatalogEquipmentDetailAction } from '../../customer/state/catalog/catalog.actions';
import { Select, Store } from '@ngxs/store';
import { CatalogState } from '../../customer/state/catalog/catalog.state';
import { takeUntil } from 'rxjs/operators';
import { LogService } from 'src/app/shared/service/log.service';
import { UpdateLanguageCodeAction } from '../../authentication/state/login/login.actions';

@Component({
  selector: 'app-equipment-order-container',
  templateUrl: './equipment-order-container.component.html',
  styleUrls: ['./equipment-order-container.component.scss']
})
export class EquipmentOrderContainerComponent implements OnInit, OnDestroy {
  @Select(CatalogState.catalogEquipmentDetail)
  catalogEquipmentDetail$: Observable<any>;
  @Select(CatalogState.catalogEquipmentDetailLoading)
  catalogEquipmentDetailLoading$: Observable<boolean>;

  catalogEquipmentDetail: any;
  loginUser = true;

  constructor(
    private readonly activatedRoute: ActivatedRoute,
    private readonly store: Store,
    private readonly log: LogService,
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.loginUser = this.activatedRoute.snapshot.queryParams['isUnLoginUser'] ? false : true;
    const lang = this.activatedRoute.snapshot.queryParams['language'];
    
    if(lang){
      this.store.dispatch(new UpdateLanguageCodeAction(lang));
    }

    const { id } = this.activatedRoute.snapshot.params;

    this.store.dispatch(new GetCatalogEquipmentDetailAction(id));
    this.catalogEquipmentDetail$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(catalogEquipmentDetail => {
      if (catalogEquipmentDetail) {
        this.catalogEquipmentDetail = catalogEquipmentDetail;
        this.log.action('CATALOG', 'GET_CATALOG_EQUIPMENT_DETAIL', {
          productId: catalogEquipmentDetail?.productId,
          brand: catalogEquipmentDetail?.brand,
          productName: catalogEquipmentDetail?.productName
        });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
