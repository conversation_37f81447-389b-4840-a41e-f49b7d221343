import { Component, OnInit, Input, ElementRef, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CatalogEquipmentModel } from '../../new-catalog/model/catalog-item.model';
import { Select, Store } from '@ngxs/store';
import { CatalogState } from '../../customer/state/catalog/catalog.state';
import { Observable, Subject } from 'rxjs';
import {
  getFormErrorMessage,
  validateAllFormFields,
} from 'src/app/util/form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  AddToShoppingCart,
  GetCatalogEquipmentDetailAction,
  ResetShoppingCartAction,
  ShoppingCartTotalPriceAction,
} from '../../customer/state/catalog/catalog.actions';
import { CustomerLeasingAction, HeaderStatusAction, ResetLeasingAction } from '../../customer/state/customer/customer.actions';
import { CustomerState } from '../../customer/state/customer/customer.state';
import { LeasingModel } from 'src/app/shared/models/customer.model';
import { LogService } from 'src/app/shared/service/log.service';
import { animate, state, style, transition, trigger } from '@angular/animations';
import { CustomerService } from '../../customer/service/customer.service';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-equipment-order',
  templateUrl: './equipment-order.component.html',
  styleUrls: ['./equipment-order.component.scss'],
  animations: [
    trigger('fade', [
      state('void', style({ opacity: '0' })),
      state('*', style({ opacity: '1' })),
      transition('* => *', [animate('1.5s')]),
    ]),
  ],
})
export class EquipmentOrderComponent implements OnInit, OnDestroy {
  @Input()
  equipmentData: any;

  @Input()
  loginUser = true;

  @Select(CatalogState.shoppingCart)
  shoppingCart$: Observable<any>;
  @Select(CatalogState.shoppingCartTotalPrice)
  shoppingCartTotalPrice$: Observable<number | string>;
  @Select(CustomerState.leasing)
  leasing$: Observable<LeasingModel>;
  @Select(CatalogState.addToShoppingCartPopup)
  popup$: Observable<boolean>;
  @Select(CatalogState.catalogEquipmentDetailLoading)
  catalogEquipmentDetailLoading$: Observable<boolean>;

  dueDateForm: FormGroup = new FormGroup({
    DueDate: new FormControl(12, [Validators.required]),
    Currency: new FormControl('EURO', [Validators.required]),
    Payment: new FormControl(20),
  });

  getFormErrorMessage = getFormErrorMessage;
  shoppingCartStatus = false;
  loanCalculationStatus = false;
  addedAnimation = false;
  shoppingCart = [];
  dueDateList = [12, 24, 36];
  currencyList = ['TRY', 'EURO'];
  shoppingCartTotalPrice: any = 0;
  payInAdvance = true;
  calculatedLoanValue = 0;
  loading = false;
  leasingStatus = false;
  leasingBody = null;
  showOfferForm = false;
  paymentTypeToggle = false;
  shoppingCartAmount = 0;
  popupDetail: any;
  monthlyPayment = 0;
  dueDate = 0;
  leasingFormBody = null;
  showbackToCartBtn = true;
  filteredShoppingCart = [];
  showWithoutEquipmentPriceForm = false;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly log: LogService,
    private readonly customerService: CustomerService,
    private readonly route: ActivatedRoute
  ) {}

  ngOnInit() {
    this.store.dispatch(new ResetLeasingAction());
    this.shoppingCart$.subscribe((data) => {
      this.shoppingCart = data;

      // if (!this.equipmentData?.showPrice){
      //   this.shoppingCartAmount = data.reduce((total, item) => total + item.amount, 0) - 1;
      // }else{
      // }
      this.shoppingCartAmount = data.reduce((total, item) => total + item.amount, 0);
    });

    this.shoppingCartTotalPrice$
    .pipe(takeUntil(this.subscriptions$), debounceTime(300))
    .subscribe((price: any) => {
      this.shoppingCartTotalPrice = price;
      this.calculatedLoanValue = Math.floor((price * (this?.dueDateForm?.value.Payment)) / 100);
      if (!this.payInAdvance){
        this.calculateLoan();
      }
    });

    if(this.shoppingCartTotalPrice === 0 && !this.equipmentData?.showPrice) {
      this.dueDateForm.patchValue({Payment: 0})
      this.changePaymentType();
    }

    this.leasing$.subscribe((data) => {
      this.leasingBody = data;
      this.monthlyPayment = data?.paymentTable[0]?.monthlyPayment;
    });

    this.popup$.subscribe(data => {
      this.popupDetail = data;
    });

    this.store.dispatch(new AddToShoppingCart({ ...this.equipmentData, amount: 1 }));
    this.store.dispatch(new ShoppingCartTotalPriceAction());
  }

  setShoppingCartStatus() {
    if (this.shoppingCartAmount === 1  && !this.equipmentData?.showPrice){
      this.showWithoutEquipmentPriceForm = true;

      if (this.loanCalculationStatus){
        this.loanCalculationStatus = false;
      }

      return;
    }

    this.goToCart();
  }

  goToCart(){
    if (this.showOfferForm){
      this.showOfferForm = false;
    }
    this.shoppingCartStatus = !this.shoppingCartStatus;
    this.loanCalculationStatus = false;
  }

  setLoanCalculationStatus() {
    this.loanCalculationStatus = !this.loanCalculationStatus;
    this.shoppingCartStatus = false;
    this.leasingStatus = false; // check control

    if (this.leasingFormBody){
      this.dueDateForm.patchValue({
        DueDate: this.leasingFormBody?.DueDate,
        Currency: this.leasingFormBody?.CurrencyUnit ? 'EURO' : 'TRY',
        Payment: this.leasingFormBody?.PaymentRate,
      });

      this.calculatedLoanValue = Math.floor((this.shoppingCartTotalPrice * this.leasingFormBody?.PaymentRate) / 100);
    }
  }

  showEquipmentOrderContainer(){
    return this.showWithoutEquipmentPriceForm || this.shoppingCartStatus;
  }

  onSubmitDueDateForm() {
    this.calculateLoan();
  }

  setAddedAnimation() {
    this.addedAnimation = true;

    setTimeout(() => {
      this.addedAnimation = false;
    }, 1500);
  }

  closeCart() {
    this.shoppingCartStatus = false;
  }

  calculateLoanValue(e) {
    const rate = e.target.value;
    this.calculatedLoanValue = Math.floor((this.shoppingCartTotalPrice * rate) / 100);
  }

  calculateLoan(showLeasingTable = true) {
    const { value } = this.dueDateForm;

    const formBody = {
      ProductId: this.equipmentData?.productId,
      ProductName: this.equipmentData?.productName,
      CostOfGoods: this.shoppingCartTotalPrice,
      DueDate: value.DueDate,
      CurrencyUnit: value.Currency === 'TRY' ? 0 : 1,
      PaymentRate: value.Payment,
      MachineStatus: 0,
    };

    const leasingFormBody = {
      ProductId: this.equipmentData?.productId,
      ProductName: this.equipmentData?.productName,
      CostOfGoods: this.shoppingCartTotalPrice,
      DueDate: value.DueDate,
      CurrencyUnit: value.Currency === 'TRY' ? 0 : 1,
      PaymentRate: value.Payment,
      MachineStatus: 0,
    };

    if (this.dueDateForm.valid) {
      this.loading = true;
      this.store.dispatch(new CustomerLeasingAction(leasingFormBody, {})).subscribe(
        () => {
          this.leasingFormBody = formBody;
          this.loading = false;
          this.leasingStatus = showLeasingTable;
          this.payInAdvance = false;
          this.dueDate = value.DueDate;
          this.log.action('CATALOG', 'CALCULATE_CATALOG_EQUIPMENT_DETAIL_LEASING', leasingFormBody);
        },
        () => {
          this.loading = false;
        }
      );
    } else {
      validateAllFormFields(this.dueDateForm);
    }
  }

  getCurrencyIcon(currency){
    return `icon-${currency === 'TRY' ? 'tl' : 'euro'}`;
  }

  getHalfCurrencyValue(value) {
    return value / 2;
  }

  openGetOfferForm(){
    this.showOfferForm = !this.showOfferForm;
    this.showbackToCartBtn = true;
  }

  backToCart(){
    this.showOfferForm = false;
  }

  bigModalStatusChange($event){
    if (this.payInAdvance){
      this.paymentTypeToggle = false;
    }
  }

  getDueDateForm(){
    this.dueDateForm = new FormGroup({
      DueDate: new FormControl(12, [Validators.required]),
      Currency: new FormControl('EURO', [Validators.required]),
      Payment: new FormControl(20),
    });
  }

  successSendForm($event){
    this.showbackToCartBtn = false;
    this.store.dispatch(new ResetShoppingCartAction());
    this.store.dispatch(new AddToShoppingCart({ ...this.equipmentData, amount: 1 }));
    this.store.dispatch(new ResetLeasingAction());
    this.store.dispatch(new ShoppingCartTotalPriceAction());
    this.leasingFormBody = null;
    this.paymentTypeToggle = false;
    this.payInAdvance = true;
    this.getDueDateForm();
  }

  showShoppingCart(){
    this.shoppingCartStatus = !this.shoppingCartStatus;
  }

  changePaymentType(){
    if(!this.equipmentData?.showPrice && !this.paymentTypeToggle){
      this.calculateLoan(false);
    }
    this.leasingBody = null;
    this.leasingFormBody = null;
    this.paymentTypeToggle = !this.paymentTypeToggle;
    if (this.paymentTypeToggle){
      this.setLoanCalculationStatus();
      this.leasingStatus = false;
    }

    if (!this.paymentTypeToggle){
      this.payInAdvance = true;
    }
  }

  closeWithoutEquipmentPriceForm(){
    this.showWithoutEquipmentPriceForm = false;
  }

  navigateBack() {
    this.leasingStatus = false;
  }

  onClickBack(){
      window.history.back();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
