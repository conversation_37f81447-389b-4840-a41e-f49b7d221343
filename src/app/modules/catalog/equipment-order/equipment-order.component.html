<app-equipment-order-header [loginUser]="loginUser" [title]="equipmentData?.productName"></app-equipment-order-header>
<div class="equipment-order" [ngClass]="{'d-none': showEquipmentOrderContainer()}">
  <button (click)="goToCart()" class="btn shopping-cart-btn">
    <i class="icon icon-shopping-cart"></i>
    <div class="shopping-cart-amount-status d-flex align-items-center justify-content-center">
      {{ shoppingCartAmount }}
    </div>
  </button>
  <div class="img-container">
    <img width="100%" [src]="equipmentData?.imageUrl" [alt]="equipmentData?.imageTitle" />
  </div>
  <div class="price-info-container mt-2">
    <div class="d-flex align-items-center" *ngIf="equipmentData?.showPrice">
      <div class="font-size-15px mr-2">{{ "_equipment_price" | translate }}</div>
      <div class="mb-0 font-weight-bold price-info d-flex align-items-center">
        <i class="icon mr-1" [ngClass]="getCurrencyIcon(equipmentData?.priceCurrency)" ></i>
        {{ equipmentData?.price | number }}
      </div>
    </div>
  </div>
  <ng-container *ngIf="equipmentData?.relationProducts?.length">
    <div class="mb-2 font-size-14px">
      {{ "_options" | translate }}
    </div>
    <div class="option-products d-flex justify-content-between overflow-auto">
      <ng-container *ngFor="let product of equipmentData?.relationProducts">
        <app-equipment-order-card
          *ngIf="product?.price !== 0"
          [product]="product"
          [productPrice]="product?.price"
        ></app-equipment-order-card>
      </ng-container>
    </div>
  </ng-container>
  <div class="mt-5" *ngIf="!equipmentData?.relationProducts?.length">
    <div class="mb-2" *ngFor="let spec of equipmentData?.specifications">
      <div class="font-weight-semi-bold">{{spec.name}}</div>
      <div [innerHTML]="spec.value"></div>
    </div>
  </div>


  <div class="total-info-container p-3">
    <app-equipment-order-total-info
      [dueDate]="dueDate"
      [loading]="loading"
      [currencyUnit]="leasingFormBody?.CurrencyUnit"
      [payInAdvance]="payInAdvance"
      [shoppingCartTotalPrice]="shoppingCartTotalPrice"
      [monthlyPayment]="monthlyPayment"
      [showPaymentStatus]="false"
      [showRecalculateLeasingBtn]="false"
      [showPrice]="equipmentData?.showPrice"
    ></app-equipment-order-total-info>

    <div class="get-info-btns d-flex flex-column mt-1">
      <div class="d-flex align-items-center justify-content-between">
        <span class="font-size-13px">
          {{ "_payment_type" | translate }}
        </span>
        <button *ngIf="!payInAdvance"  (click)="setLoanCalculationStatus()"
            appClickLog
            [section]="'OpportunityProduct'"
            [subsection]="'Calculate_again_offer'"
            class="btn p-0 text-info font-size-13px">{{ "_calculate_loan_again" | translate }}
        </button>
      </div>
      <div class="btn p-1 toggle-payment-type-btn w-100 mt-1">
        <span (click)="changePaymentType()"  class="payment-type-text" [ngClass]="{'text-info': !paymentTypeToggle}" >
          {{"_cash" | translate}}
        </span>
        <span (click)="changePaymentType()" class="payment-type-text"  [ngClass]="{'text-info': paymentTypeToggle}">
          {{"_loan" | translate}}
        </span>
        <div class="slider" [ngClass]="{'sliding': paymentTypeToggle}" ></div>
      </div>
      <button (click)="setShoppingCartStatus()" class="btn btn-sm btn-warning text-white mt-2 send-me-offer-btn">
        {{ "_send_me_offer" | translate }}
      </button>
    </div>
  </div>
</div>

<div class="shopping-cart-container" *ngIf="shoppingCartStatus">
  <div class="px-4 py-3 d-flex align-items-center justify-content-between">
    <h4>
      {{(showOfferForm ? '_get_offer' : '_my_cart') | translate}}
    </h4>
    <button (click)="showShoppingCart()" class="btn btn-sm">
    <i class="icon icon-x-bold font-size-16px"></i>
    </button>
  </div>

  <div class="d-flex flex-column carts-container">
    <div class="shopping-cart-modal-content pt-0 flex-grow-1 d-flex flex-column" [ngClass]="{'pb-3 px-3': !showOfferForm || loginUser}">
      <div class="product-cart-container flex-grow-1" *ngIf="!showOfferForm">
        <div
          *ngIf="!shoppingCart.length"
          class="h-100 empty-shopping-cart-info d-flex flex-column align-items-center justify-content-center"
        >
          <i class="icon icon-shopping-cart mb-2"></i>
          <p class="px-5 text-center">
            {{ "_empty_shopping_cart" | translate }}
          </p>
        </div>
        <ng-container *ngFor="let product of shoppingCart; let first = first">
          <app-equipment-order-card
            [amount]="product.amount"
            [productPrice]="((!equipmentData?.showPrice && !first) || equipmentData?.showPrice) ? product?.price : 0"
            [inCart]="true"
            [product]="product"
            [amountBtn]="first ? false : true"
          ></app-equipment-order-card>
        </ng-container>
      </div>
      <div class="d-flex align-items-center mb-2 px-3" *ngIf="showbackToCartBtn && showOfferForm">
        <button class="btn p-1 mr-2" (click)="backToCart()">
          <i class="icon icon-back"></i>
        </button>
        {{ "_back_to_shopping_cart" | translate }}
      </div>
    <div [ngClass]="{'h-100 d-flex flex-column': showOfferForm, 'overflow-auto': !loginUser}">
      <div class="offer-form-container" *ngIf="showOfferForm">
        <app-order-form
          *ngIf="loginUser"
          [data]="equipmentData"
          [shoppingCart]="shoppingCart"
          [showImg]="false"
          [leasingFormBody]="leasingFormBody"
          (onSuccessSendForm)="successSendForm($event)"
        >
        </app-order-form>
        <app-equipment-order-unlogin-form *ngIf="!loginUser"
          [shoppingCart]="shoppingCart"
          [leasingFormBody]="leasingFormBody"
          [equipment]="equipmentData"
          (onSuccessSendForm)="successSendForm($event)"
        >
          <app-equipment-order-total-info
          [dueDate]="dueDate"
          [loading]="loading"
          [currencyUnit]="leasingFormBody?.CurrencyUnit"
          [payInAdvance]="payInAdvance"
          [shoppingCartTotalPrice]="shoppingCartTotalPrice"
          [monthlyPayment]="monthlyPayment"
          [showPrice]="equipmentData?.showPrice"
        ></app-equipment-order-total-info>
        </app-equipment-order-unlogin-form>
      </div>
      <div class="mt-1" [ngClass]="{'mt-5 pt-2': showOfferForm, 'px-3': !loginUser && showOfferForm}">
        <ng-container *ngIf="shoppingCart.length">
          <app-equipment-order-total-info
            *ngIf="loginUser || !showOfferForm"
            [dueDate]="dueDate"
            [loading]="loading"
            [currencyUnit]="leasingFormBody?.CurrencyUnit"
            [payInAdvance]="payInAdvance"
            [shoppingCartTotalPrice]="shoppingCartTotalPrice"
            [monthlyPayment]="monthlyPayment"
            [showPaymentStatus]="true"
            [showRecalculateLeasingBtn]="!showOfferForm"
            (onSetLeasingCalculationStatus)="setLoanCalculationStatus()"
            [showPrice]="equipmentData?.showPrice"
          ></app-equipment-order-total-info>
        </ng-container>
      </div>
    </div>
    </div>
    <div class="shopping-cart-action-btns border-top p-3" *ngIf="!showOfferForm">
      <button
        *ngIf="!showOfferForm"
        (click)="openGetOfferForm()"
        class="col btn btn-sm create-offer-btn btn-warning text-white w-100">
        {{ "_create_offer" | translate }}
      </button>
    </div>
  </div>
</div>

<app-big-modal
  [(status)]="loanCalculationStatus"
  [headerText]="'_calculate_loan' | translate"
  (statusChange)="bigModalStatusChange($event)"
>
  <div class="calculate-loan-container">
    <ng-container *ngIf="!leasingStatus">
      <form
        (submit)="onSubmitDueDateForm()"
        [formGroup]="dueDateForm"
        class="calculate-loan-content px-3 py-2"
      >
        <div class="d-flex flex-column mb-4">
          <span class="mb-2 font-size-14px">
            {{ "_specify_expiry_period" | translate }}
          </span>
          <div
            *ngIf="dueDateList?.length"
            class="form-check btn-group w-100 d-flex pl-0 due-date-list"
          >
            <ng-container
              *ngFor="
                let dueDate of dueDateList;
                let last = last;
                let first = first
              "
            >
              <input
                type="radio"
                class="btn-check d-none"
                [id]="dueDate"
                [formControlName]="'DueDate'"
                [value]="dueDate"
                [name]="'DueDate'"
              />
              <label
                class="btn btn-sm due-date-btn"
                [class.selected]="dueDateForm.get('DueDate').value === dueDate"
                [class.last-box]="last"
                [class.first-box]="first"
                [for]="dueDate"
                >{{ dueDate }} {{ "_month" | translate }}</label
              >
            </ng-container>
          </div>
          <div
            [ngClass]="{
              'd-block':
                !dueDateForm.get('DueDate').valid &&
                dueDateForm.get('DueDate').touched
            }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(dueDateForm.controls.DueDate) | translate }}
          </div>
        </div>
        <div class="d-flex flex-column mb-4">
          <span class="mb-2 font-size-14px">
            {{ "_leasing_currency_select" | translate }}
          </span>
          <div
            *ngIf="currencyList?.length"
            class="form-check btn-group w-100 d-flex pl-0 due-date-list"
          >
            <ng-container
              *ngFor="
                let currency of currencyList;
                let last = last;
                let first = first;
              "
            >
              <input
                type="radio"
                class="btn-check d-none"
                [id]="currency"
                [formControlName]="'Currency'"
                [value]="currency"
                [name]="'Currency'"
              />
              <label
                class="btn btn-sm due-date-btn"
                [class.selected]="dueDateForm.get('Currency').value === currency"
                [class.last-box]="last"
                [class.first-box]="first"
                [for]="currency"
                >{{ currency }}</label
              >
            </ng-container>
          </div>
          <div
            [ngClass]="{
              'd-block':
                !dueDateForm.get('Currency').valid &&
                dueDateForm.get('Currency').touched
            }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(dueDateForm.controls.Currency) | translate }}
          </div>
        </div>
        <div class="form-group">
          <div class="mb-2 font-size-14px">{{ "_specify_down_payment_value" | translate }}</div>
          <div class="range-values row">
            <div class="font-size-13px font-weight-semi-bold col">
              0%
            </div>
            <div
              class="font-size-13px font-weight-semi-bold col text-center"
              *ngIf="dueDateForm.value.Payment"
            >
              {{ dueDateForm.get("Payment").value }}%
            </div>
            <div
              class="font-size-13px font-weight-semi-bold col text-right"
            >
              50%
            </div>
          </div>
          <input
            class="w-100"
            [formControlName]="'Payment'"
            [name]="'Payment'"
            type="range"
            min="0"
            max="50"
            step="10"
            (input)="calculateLoanValue($event)"
          />
          <div class="payment-info row">
            <div class="font-size-13px payment-info-row font-weight-semi-bold col d-flex align-items-center">
              <i class="icon icon-euro"></i>
              {{ 0 | number }}
            </div>
            <div *ngIf="dueDateForm.value.Payment" class="font-size-13px payment-info-row font-weight-semi-bold col text-center d-flex align-items-center justify-content-center">
              <i class="icon icon-euro"></i>
              {{ calculatedLoanValue | number }}
            </div>
            <div class="font-size-13px payment-info-row font-weight-semi-bold col text-right d-flex align-items-center justify-content-end">
              <i class="icon icon-euro"></i>
              {{ getHalfCurrencyValue(shoppingCartTotalPrice) | number }}
            </div>
          </div>
        </div>
      </form>
      <div
        class="mx-0 shopping-cart-action-btns border-top p-3 d-flex align-items-center justify-content-center"
      >
        <button
          appClickLog
          [section]="'OpportunityProduct'"
          [subsection]="'Calculate_Offer'"
          (click)="calculateLoan()"
          class="btn btn-sm btn-gray btn-calculate-loan mr-1 text-nowrap"
        >
          {{ "_calculate_loan" | translate }}
        </button>
      </div>
      <app-loader [show]="loading"></app-loader>
    </ng-container>
    <ng-container *ngIf="leasingStatus">
      <div class="d-flex align-items-center">
        <button class="btn" (click)="navigateBack()">
          <i class="icon icon-back"></i>
        </button>
        {{ "_example_leasing_calculation_plan" | translate }}
      </div>
      <div class="leasing-content px-3">
      <app-leasing-response-table
        [table]="leasingBody?.paymentTable"
        [icon]="dueDateForm.value.Currency"
      >
      </app-leasing-response-table>
      </div>
      <div class="py-2 px-3 border-top">
        <app-equipment-order-total-info
          [dueDate]="dueDate"
          [loading]="loading"
          [currencyUnit]="leasingFormBody?.CurrencyUnit"
          [payInAdvance]="payInAdvance"
          [shoppingCartTotalPrice]="shoppingCartTotalPrice"
          [monthlyPayment]="monthlyPayment"
          [showPrice]="equipmentData?.showPrice"
        ></app-equipment-order-total-info>
        <button
          appClickLog
          [section]="'OpportunityProduct'"
          [subsection]="'Apply_Offer'"
          (click)="setShoppingCartStatus()"
          class="w-100 btn btn-sm btn-warning text-white mt-2 send-me-offer-btn"
        >
          {{ "_apply_loan_offer" | translate }}
        </button>
      </div>
    </ng-container>
  </div>
</app-big-modal>

<div class="shopping-cart-container" style="z-index: 6;" *ngIf="showWithoutEquipmentPriceForm">
  <div class="px-4 py-3 d-flex align-items-center justify-content-between">
    <h4>
      {{'_get_offer' | translate}}
    </h4>
    <button (click)="closeWithoutEquipmentPriceForm()" class="btn btn-sm">
    <i class="icon icon-x-bold font-size-16px"></i>
    </button>
  </div>
  <div style="height: calc(100% - 77px); overflow: auto;">
    <div class="h-100 d-flex flex-column">

      <app-order-form
        *ngIf="loginUser"
        [data]="equipmentData"
        [shoppingCart]="shoppingCart"
        [showImg]="false"
        [leasingFormBody]="leasingFormBody"
        (onSuccessSendForm)="successSendForm($event)"
      >
      </app-order-form>
      <div class="p-3 mt-auto" *ngIf="loginUser">
        <app-equipment-order-total-info
          [dueDate]="dueDate"
          [loading]="loading"
          [currencyUnit]="leasingFormBody?.CurrencyUnit"
          [payInAdvance]="payInAdvance"
          [shoppingCartTotalPrice]="shoppingCartTotalPrice"
          [monthlyPayment]="monthlyPayment"
          [showPrice]="equipmentData?.showPrice"
        ></app-equipment-order-total-info>
      </div>
      <app-equipment-order-unlogin-form *ngIf="!loginUser"
        [shoppingCart]="shoppingCart"
        [leasingFormBody]="leasingFormBody"
        [equipment]="equipmentData"
        (onSuccessSendForm)="successSendForm($event)"
      >
      <div class="mb-3">
        <app-equipment-order-total-info
          [dueDate]="dueDate"
          [loading]="loading"
          [currencyUnit]="leasingFormBody?.CurrencyUnit"
          [payInAdvance]="payInAdvance"
          [shoppingCartTotalPrice]="shoppingCartTotalPrice"
          [monthlyPayment]="monthlyPayment"
          [showPrice]="equipmentData?.showPrice"
        ></app-equipment-order-total-info>
      </div>
      </app-equipment-order-unlogin-form>
    </div>
  </div>
</div>

<div
  *ngIf="popupDetail?.show"
  [@fade]
  class="added-basket-info d-flex align-items-center"
>
  <i class="icon icon-success"></i>
  <span class="font-size-13px text-nowrap">{{ (popupDetail?.text ? popupDetail?.text : "_added_to_shopping_cart") | translate }}</span>
</div>
<!-- <app-loader [show]="catalogEquipmentDetailLoading$ | async"></app-loader> -->
