.equipment-order {
  padding: 0 22px;
  padding-bottom: 1.5rem;
  background-color: #fafafa;
  height: calc(100vh - 200px);
  overflow-x: auto;

  .img-container {
    margin-top: 1rem;
    box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
    border-radius: 0.5rem;

    img {
      border-radius: 0.5rem;
      height: 220px;
    }
  }

  .shopping-cart-btn {
    position: fixed;
    top: 5px;
    right: 5px;

    .icon-shopping-cart {
      font-size: 19px;
    }

    .shopping-cart-amount-status {
      position: absolute;
      top: 5px;
      right: 8px;
      width: 16px;
      height: 16px;
      font-size: 12px;
      border-radius: 50%;
      color: #fff;
      background-color: #ffa300;
    }
  }

  .total-info-container {
    box-shadow: rgba(9, 30, 66, 0.25) 0px 4px 8px -2px,
      rgba(9, 30, 66, 0.08) 0px 0px 0px 1px;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: #fff;
    border-top-right-radius: 2rem;
    border-top-left-radius: 2rem;
  }

  .price-info-container {
    gap: 2rem;
    margin-bottom: 1rem;

    .price-info {
      font-size: 1.25rem;
    }
  }

  .option-products {
    display: grid !important;
    grid-template-columns: 1fr 1fr;
    place-items: center;
    padding-bottom: 4rem;
    gap: 0.75rem;
  }
}

:host::ng-deep app-big-modal .modal-header {
  border-bottom: 1px solid #ddd !important;
  font-size: 17px !important;
}

:host::ng-deep app-big-modal .modal-body {
  padding: 0 !important;
}

.calculate-loan-container {
  ::ng-deep app-loader {
    .backdrop {
      position: absolute !important;
    }

    .app-loader {
      top: 50% !important;
    }
  }
}

.shopping-cart-modal-content {
  height: calc(100% - 77px);

  .product-cart-container {
    overflow: auto;
  }

  .empty-shopping-cart-info {
    .icon-shopping-cart {
      font-size: 36px;
      font-weight: bold;
      color: #ffa300;
    }
  }
}

.create-offer-btn {
  height: 31px;
  padding: 0;
}

.btn-gray {
  background-color: #e4e8eb;
  color: #4a8eb0;
  padding: 0.25rem 0.75rem;
  font-weight: bold;
}

.send-me-offer-btn {
  height: 31px;
  padding: 0;
}

.btn-gray-outline {
  border: 2px solid #bababa;
  font-weight: bold;
}

.due-date-list {
  gap: 0.125rem;
}

.due-date-btn {
  color: #8e8e8e;
  background-color: #eef2f4;
  height: 50px;
  padding: 0.875rem;
  border: 1px solid #d7e5ea;

  &.selected {
    background-color: #ffa300;
    color: #fff;
  }

  &.first-box {
    border-top-left-radius: 6px !important;
    border-bottom-left-radius: 6px !important;
  }
}

.calculate-loan-content {
  height: 350px;
  max-height: 350px;
  overflow: auto;

  .payment-info {
    &-row {
      height: 20px;
    }
  }
}
.btn-calculate-loan {
  color: #505050;
}

input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: #eef2f4;
  cursor: pointer;
  height: 8px;
  border-radius: 4px;
  width: 100%;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  background-color: #ffa300;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
}

input[type="range"]::-moz-range-thumb {
  border: none;
  border-radius: 0;
  background-color: #ffa300;
  height: 1.25rem;
  width: 1.25rem;
  border-radius: 50%;
}

.leasing-content {
  height: 350px;
  max-height: 350px;
  overflow: auto;

  ::ng-deep app-leasing-response-table {
    .table {
      td,
      th {
        padding: 0;
      }
    }
  }
}

.offer-form-container {
  flex: 1;

  ::ng-deep app-order-form .form-container {
    app-success-modal .content-area {
      height: max-content !important;

      .content-area-body {
        //height: 500px;
        background: #fff;
        z-index: 12;
        padding: 1rem;
        height: calc(100vh - 77px);
      }
    }

    .px-4 {
      padding: 0 !important;
      margin: 0.15rem;
    }

    .btn-warning {
      // height: 30px;
      // padding: 0 !important;
    }
  }
}

.toggle-payment-type-btn {
  position: relative;
  border: 1px solid #ccc;
  border-radius: 1.5rem;
  background-color: #e4e8eb;
  display: grid;
  grid-template-columns: 1fr 1fr;

  .slider {
    position: absolute;
    top: 0;
    bottom: 0;
    width: 50%;
    border-radius: 1.5rem;
    background-color: rgba(#fff, 0.4);
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    transition: transform 0.5s ease-in-out;
    transform: translateX(0px);
    z-index: 122;
  }

  .sliding {
    transform: translateX(100%);
  }

  .text-info {
    pointer-events: none;
  }

  .payment-type-text {
    width: 100%;
    z-index: 123;
    transition: color 0.5s ease-in-out;
  }
}

.added-basket-info {
  position: fixed;
  left: 50%;
  transform: translate(-50%, -50%);
  bottom: 7.5rem;
  background-color: #28a745;
  color: #fff;
  box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  padding: 0.5rem 2rem;
  gap: 0.5rem;
  border-radius: 8px;
  z-index: 99999;

  .icon-success {
    font-size: 18px;
    padding-top: 1px;
    background: #fff;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }
}

.popup-container {
  position: fixed;
  inset: 0;
  background-color: transparent;
  z-index: 99999;
}

.font-size-15px {
  font-size: 15px;
}

.shopping-cart-container {
  height: 100vh;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fafafa;
}

.carts-container {
  height: calc(100% - 73px);
}
