import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { LoginResponseModel } from '../../authentication/model/login-response.model';
import { Store } from '@ngxs/store';
import { Observable, of } from 'rxjs';
import { LoginWithOttAction } from '../../authentication/state/login/login.actions';
import { Injectable } from '@angular/core';
import { LoginState } from '../../authentication/state/login/login.state';
import { takeWhile, timeoutWith } from 'rxjs/operators';

@Injectable()
export class OttResolver implements Resolve<LoginResponseModel | boolean> {
  constructor(
    private readonly store: Store,
  ) {
  }

  resolve({ queryParams: { ott } }: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<LoginResponseModel | boolean> {
    if (ott) {
      return this.store.dispatch(
        new LoginWithOttAction(ott),
      );
    } else {
      const loginLoading = this.store.selectSnapshot(LoginState.loginLoading);
      if (loginLoading) {
        return this.store.select(LoginState.loginLoading)
          .pipe(takeWhile(i => i === true))
          .pipe(timeoutWith(30000, of(false)));
      }
    }

    return of(true);
  }

}
