import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule, TranslatePipe } from '@ngx-translate/core';
import { ClickAwayRoutingModule } from './click-away-routing.module';
import { HasPermissionsModule } from 'src/app/export/file-upload/permissions/has-permissions.module';
import { PullToRefreshModule } from '@piumaz/pull-to-refresh';
import { ClickAwayUsersComponent } from './container/click-away-users/click-away-users.component';
import { ClickAwayUserCardComponent } from './component/click-away-user-card/click-away-user-card.component';
import { ClickAwayBadgeComponent } from './component/click-away-badge/click-away-badge.component';
import { ClickAwayUserProfileComponent } from './container/click-away-user-profile/click-away-user-profile.component';
import { CustomerLoadResolver } from '../customer/resolver/customer-load-resolver.service';
import { SharedModule } from '../../shared/shared.module';

@NgModule({
  declarations: [
    ClickAwayUsersComponent,
    ClickAwayUserCardComponent,
    ClickAwayBadgeComponent,
    ClickAwayUserProfileComponent,
  ],
  imports: [
    CommonModule,
    ClickAwayRoutingModule,
    TranslateModule,
    HasPermissionsModule,
    PullToRefreshModule,
    SharedModule,
  ],
  providers: [TranslatePipe, CustomerLoadResolver],
  exports: [],
})
export class ClickAwayModule {}
