<div
  style="
    min-height: 5rem;
    border-radius: 1rem;
    display: grid;
    grid-template-columns: 9fr 32fr;
    background-color: #ffffff;
  "
  class=""
>
  <!--  image-->

  <div class="image-wrapper" (click)="onClickCalendar(user.queueName)">
    <!--    <div-->
    <!--      style="-->
    <!--      background-color: #e4e4e4;-->
    <!--      display: flex;-->
    <!--      align-items: center;-->
    <!--      justify-content: center;-->
    <!--      border-top-left-radius: 1rem;-->
    <!--      border-bottom-left-radius: 1rem;-->

    <!--    "-->
    <!--    >-->
    <!--      <span class="text-white" style="font-size: 1.5rem">EA</span>-->
    <!--    </div>-->
    <img [attr.src]="user.imageUrl || defaultImage"/>
    <div class="status-badge ">
      <div>
        <app-click-away-badge
          [text]="statusMap(user.status) | translate"
          [width]="'max-content'"
          [bgColor]="bgColors(user.status)"
          [textColor]="textColors(user.status)"
          size="sm"
        ></app-click-away-badge>
      </div>
    </div>
  </div>

  <!--  body-->
  <div
    class="ml-1"
    style="
        display: flex;
        flex-direction: column;
        gap: 0.25rem;
        padding: 0.25rem 0.25rem 0.1rem 0.25rem;
      "
  >
    <div style="display: flex; align-items: center;">
      <!--  content -->
      <div class="user-content pr-1" >
        <div class="user-title-section" (click)="onClickCalendar(user.queueName)">
          <span class="user-title">{{ user.title }}</span>
          <span class="user-description">{{ user.description }}</span>
        </div>
        <div class="user-badges">
          <button
            class="btn calendar-button"
            (click)="onClickCalendar(user.queueName)"
          >
            <i class="icon icon-calendar"></i>
          </button>
          <div class="my-auto">
            <app-click-away-badge
              *ngIf="user.status === leaderCallStatusEnum.offline && user.nextAvailableDate"
              [text]="'_next_available' | translate"
              [textSecond]="user.nextAvailableDate | date: ' dd.MM.yyyy HH:mm'"
              [width]="'max-content'"
              size="sm"
            ></app-click-away-badge>
          </div>

        </div>
      </div>

      <!--  buttons -->
      <div class="actions">
        <div class="action-button-wrapper pl-1">
          <button
            [disabled]="user.status !== leaderCallStatusEnum.online"
            (click)="onClickCall(user)"
            class="btn call-button"
            [ngClass]="{
             'call-button-offline' : user.status === leaderCallStatusEnum.offline,
             'call-button-online' : user.status === leaderCallStatusEnum.online,
             'call-button-busy' : user.status === leaderCallStatusEnum.busy
            }"

          >
            <i class="icon icon-video-camera"></i>
          </button>
<!--          <div class="call-text text-center">-->
<!--            <span>Görüşme başlat</span>-->
<!--          </div>-->
        </div>
      </div>
    </div>
  </div>
</div>
