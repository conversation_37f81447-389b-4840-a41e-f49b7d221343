.icon-calendar {
  &:before {
    color: #ffa300;
  }
}

.icon-video-camera {
  &:before {
    color: #039A00;
  }
}

.image-wrapper {
  width: 110px;
  height: 110px;
  //border: 1px solid #ddd;
  border-radius: 10% 0 0 10%;
}

.image-wrapper img {
  object-fit: contain;
  min-width: 100%;
  min-height: 100%;
  width: auto;
  height: auto;
  max-width: 100%;
  max-height: 100%;
  border-radius: 10% 0 0 10%;
}

.user {
  &-content {
    border-right: 1px solid #f1f1f1;
    height: 105px;
    width: 80%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &-title-section {
    display: flex;
    flex-direction: column;
    //gap: 0.25rem
  }
  &-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.1em;
  }
  &-description {
    font-size: 10px;
    font-weight: 400;
    color: #777777;

    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3; /* number of lines to show */
    line-clamp: 3;
    -webkit-box-orient: vertical;
    line-height: 1.35em;
  }

  &-badges {
    display: flex;
    flex-direction: row;
    gap: 0.25rem;
    font-size: 9px;
    font-weight: 400;
  }

}


.calendar-button {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: #FFF5E2;
  display: flex;
  align-items: center;
  justify-content: center;
}

.call-button {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &-online, &-offline {
    background-color: #EFFFEE;
    border: 1px solid #039A00;
    &:disabled {
      opacity: 0.40;
      background-color: #EAEAEA;
      border: none;

      :before {
        color: #8F8F8F;
      }
    }
  }


  &-busy {
    background-color: #EFFFEE;
    &:disabled {
      opacity: 0.40;
      background-color: #FFDEDE;
      border: none;
      :before {
        color: #FF0000;
      }
    }
  }

}


.actions {
  width: 30%;
}

.action-button-wrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  flex-flow: row wrap;
  justify-content: space-around;
  gap: .25rem
}

.status-badge {
  font-size: 9px;
  font-weight: 400;
  display: flex;
  justify-content: center;
  position: relative;
  top: -19px;
}

.call-text {
  font-size: 9px;
  font-weight: 400;
}
