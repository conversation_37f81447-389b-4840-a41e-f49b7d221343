import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { LeaderUser } from '../../../customer/model/contact';
import { LeaderCallStatusEnum } from '../../../definition/enum/leader-call-status.enum';

@Component({
  selector: 'app-click-away-user-card',
  templateUrl: './click-away-user-card.component.html',
  styleUrls: ['./click-away-user-card.component.scss']
})
export class ClickAwayUserCardComponent implements OnInit {
  @Input() user: LeaderUser;

  @Output()
  clickCall: EventEmitter<LeaderUser> = new EventEmitter();

  leaderCallStatusEnum = LeaderCallStatusEnum;
  defaultImage = 'assets/images/avatar.jpg';

  constructor(
    private readonly router: Router
  ) { }

  ngOnInit() {
  }

  onClickCalendar(queueName: string) {
    this.router.navigate(['settings', 'click-away', queueName]);
  }

  onClickCall(user: any) {
    this.clickCall.emit(user);
  }

  statusMap(status) {
    return '_' + LeaderCallStatusEnum[status];
  }

  bgColors(type) {
    switch (type) {
      case LeaderCallStatusEnum.busy:
        return '#FFDEDE';
      case LeaderCallStatusEnum.online:
        return '#EFFFEE';
      case LeaderCallStatusEnum.offline:
      default:
        return '#EAEAEA';
    }
  }

  textColors(type) {
    switch (type) {
      case LeaderCallStatusEnum.busy:
        return '#FF0000';
      case LeaderCallStatusEnum.online:
        return '#039A00';
      case LeaderCallStatusEnum.offline:
      default:
        return '#6C6C6C';
    }
  }

}
