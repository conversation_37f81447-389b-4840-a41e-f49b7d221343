<div
  style="border-radius: 1rem; line-height: 1"
  class="px-3 py-2"
  [style.backgroundColor]="bgColor"
  [style.width]="width"
  [ngClass]="{'px-2 py-1': size === 'sm', 'px-3 py-2': size === 'default'}"
>
  <div [ngClass]="{'font-size-8px': size === 'sm', 'font-size-12px': size === 'default'}" [style.color]="textColor">
    {{ text | translate }}
  </div>

  <div *ngIf="textSecond" class="mt-1" [ngClass]="{'font-size-8px': size === 'sm', 'font-size-12px': size === 'default'}" [style.color]="textColor">
    {{ textSecond | translate }}
  </div>
</div>
