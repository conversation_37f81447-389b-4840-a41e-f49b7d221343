import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';
import { CustomerLoadResolver } from '../customer/resolver/customer-load-resolver.service';
import { ClickAwayUsersComponent } from './container/click-away-users/click-away-users.component';
import { ClickAwayUserProfileComponent } from './container/click-away-user-profile/click-away-user-profile.component';

const routes: Routes = [
  {
    path: '',
    component: null,
    canActivate: [PermissionGuard],
    resolve: [CustomerLoadResolver],
    children: [
      { path: '', component: ClickAwayUsersComponent },
      { path: ':userQueueName', component: ClickAwayUserProfileComponent }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ClickAwayRoutingModule {}
