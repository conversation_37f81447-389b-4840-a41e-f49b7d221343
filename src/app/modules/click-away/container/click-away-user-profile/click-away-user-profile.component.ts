import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject, timer } from 'rxjs';
import { LeaderCallDetail } from 'src/app/modules/customer/model/contact';
import { GetVideoCallLeaderDetail, GetVideoCallLeaderStatus } from 'src/app/modules/customer/state/contact/contact.actions';
import { ContactState } from 'src/app/modules/customer/state/contact/contact.state';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { DatePipe } from '@angular/common';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { Navigate } from '@ngxs/router-plugin';
import { LeaderCallStatusEnum } from '../../../definition/enum/leader-call-status.enum';
import { VideoCallManagerService } from '../../../../shared/service/video-call-manager.service';
import { map, take, takeUntil, tap } from 'rxjs/operators';
import { LogService } from '../../../../shared/service/log.service';

@Component({
  selector: 'app-click-away-user-profile',
  templateUrl: './click-away-user-profile.component.html',
  styleUrls: ['./click-away-user-profile.component.scss'],
  providers: [DatePipe],
})
export class ClickAwayUserProfileComponent implements OnInit, OnDestroy {
  queueName: string;
  videoCallLeaderDetail: LeaderCallDetail;
  availabilityDates: any[] = [];
  selectedFilterAvailabilityDate = [];
  showAvailabilityHours = false;

  currentMonth: number;
  currentYear: number;
  months = [];
  days = [];
  dates: any[] = [];
  blanks: number[] = [];
  today = new Date();
  lang = 'tr-TR';


  @Select(ContactState.videoCallLeaderDetailLoading)
  videoCallLeaderDetailLoading$: Observable<boolean>;

  @Select(ContactState.videoCallLeaderStatus)
  videoCallLeaderStatus$: Observable<number>;

  subscriptions$: Subject<boolean> = new Subject();
  leaderStatus: number;
  leaderCallStatusEnum = LeaderCallStatusEnum;
  defaultImage = 'assets/images/avatar.jpg';

  constructor(
    private readonly store: Store,
    private readonly activatedRoute: ActivatedRoute,
    private readonly translate: TranslatePipe,
    private readonly datePipe: DatePipe,
    private readonly translateService: TranslateService,
    private readonly videoCallManagerService: VideoCallManagerService,
    private readonly log: LogService,
  ) {}

  ngOnInit() {
    const directCall = this.activatedRoute.snapshot.queryParams?.directCall;

    this.log.action('CLICK_AWAY', 'CALENDAR_OPEN', {
      queueName: this.activatedRoute.snapshot.params.userQueueName
    }).subscribe();


    this.currentMonth = this.today.getMonth();
    this.currentYear = this.today.getFullYear();

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: true,
        notificationIcon: false,
        title: this.translate.transform('_just_a_click_away'),
      })
    );

    this.videoCallLeaderStatus$.subscribe(status => this.leaderStatus = status);
    // get detail  and generate calendar
    this.generateCalendar(this.today.getMonth(), this.today.getFullYear())
      .subscribe((leader) => {
        timer(0, 5000)
          .pipe(takeUntil(this.subscriptions$))
          .pipe(take(120))
          .subscribe((value) => {
            this.store.dispatch(new GetVideoCallLeaderStatus(leader.queueCode, value === 0));
          });

        if (directCall) {
          this.onClickCall();
        }
      });

    const lang =
      this.store.selectSnapshot(LoginState.language) ||
      this.translateService.currentLang ||
      this.translateService.defaultLang;

    this.getWeekDays(lang);
    this.getMonths(lang);
  }

  generateCalendar(month: number, year: number): Observable<any> {
    const firstDayOfMonth = new Date(year, month, 1).getDay() - 1;
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    const todayDate = this.datePipe.transform(
      new Date(year, month),
      'yyyy-MM-dd'
    );
    const nextMonthDate = this.datePipe.transform(
      new Date(
        new Date(year, month).setMonth(new Date(year, month).getMonth() + 1) - 1
      ),
      'yyyy-MM-dd'
    );
    const queueName = this.activatedRoute.snapshot.params.userQueueName;

    return this.store.dispatch(
      new GetVideoCallLeaderDetail({
        queueName,
        startDate: this.datePipe.transform(todayDate, 'yyyy-MM-dd'),
        endDate: this.datePipe.transform(nextMonthDate, 'yyyy-MM-dd'),
      })
    )
      .pipe(map(() => this.store.selectSnapshot(ContactState.videoCallLeaderDetail)))
      .pipe(tap((user) => {
        if (user) {
          this.videoCallLeaderDetail = user;
          this.availabilityDates = user.groupedAvailabilities;
          this.blanks = Array(firstDayOfMonth >= 0 ? firstDayOfMonth : 6).fill(
            0
          );
          const dates: any[] = Array.from(
            { length: daysInMonth },
            (_, i) => i + 1
          );

          this.dates = dates.map((date) => {
            const isAvailable = this.checkIsAvailability(date, user);
            // if (isAvailable && !this.selectedFilterAvailabilityDate.length) {
            //   this.filterAvailabilityDate();
            // }

            return {
              date,
              isAvailability: isAvailable,
            };
          });

          this.filterAvailabilityDate(null);
        }
      }));
  }

  previousMonth(): void {
    if (this.currentMonth === 0) {
      this.currentMonth = 11;
      this.currentYear--;
    } else {
      this.currentMonth--;
    }
    this.selectedFilterAvailabilityDate = [];
    this.generateCalendar(this.currentMonth, this.currentYear).subscribe();
  }

  nextMonth() {
    if (this.currentMonth === 11) {
      this.currentMonth = 0;
      this.currentYear++;
    } else {
      this.currentMonth++;
    }
    this.selectedFilterAvailabilityDate = [];
    this.generateCalendar(this.currentMonth, this.currentYear).subscribe();
  }

  navigateBack() {
    this.store.dispatch(new Navigate(['settings/click-away']));
  }

  getWeekDays(lang: string) {
    const days = [];
    const date = new Date();

    const firstDayOfWeek = new Date(
      date.setDate(
        date.getDate() - (date.getDay() === 0 ? 6 : date.getDay() - 1)
      )
    );

    for (let i = 0; i < 7; i++) {
      const currentDay = new Date(firstDayOfWeek);
      currentDay.setDate(firstDayOfWeek.getDate() + i);

      days.push(currentDay.toLocaleDateString(lang, { weekday: 'short' }));
    }

    this.days = days;
  }

  getMonths(lang: string) {
    const months = [];
    const date = new Date();

    for (let i = 0; i < 12; i++) {
      const currentMonth = new Date(date.getFullYear(), i);

      months.push(currentMonth.toLocaleDateString(lang, { month: 'long' }));
    }

    this.months = months;
  }

  checkIsAvailability(date: number, user: LeaderCallDetail) {
    return user.groupedAvailabilities?.some(
      (availability) => new Date(availability.date).getDate() === date
    );
  }

  filterAvailabilityDate(date: number) {
    this.showAvailabilityHours = true;
    if (!date) {
      this.selectedFilterAvailabilityDate =
        this.videoCallLeaderDetail?.groupedAvailabilities;
      return;
    }

    this.selectedFilterAvailabilityDate =
      this.videoCallLeaderDetail?.groupedAvailabilities?.filter(
        (availability) => new Date(availability.date).getDate() === date
      );
  }

  checkSelectedDate(date: any, selectedDate: any) {
    return date === new Date(selectedDate).getDate();
  }

  formatTime(time: string) {
    return time.replace(/:00$/, '');
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onClickCall() {
    this.videoCallManagerService.startVideoCall(this.videoCallLeaderDetail.queueName, 'justAClick', null, 'JUST_A_CLICK');

  }

}
