<div class="p-3 click-away-user-profile-container">
  <div class="bg-white p-3 user-profile-card">
    <button class="btn pl-0" (click)="navigateBack()">
      <i class="icon icon-back"></i>
    </button>
    <div class="d-flex flex-column align-items-center justify-content-center">
      <div class="image-wrapper">
        <!--    <div-->
        <!--      style="-->
        <!--      background-color: #e4e4e4;-->
        <!--      display: flex;-->
        <!--      align-items: center;-->
        <!--      justify-content: center;-->
        <!--      border-top-left-radius: 1rem;-->
        <!--      border-bottom-left-radius: 1rem;-->

        <!--    "-->
        <!--    >-->
        <!--      <span class="text-white" style="font-size: 1.5rem">EA</span>-->
        <!--    </div>-->
        <img
          [style.borderRadius]="'50%'"
          [attr.src]="videoCallLeaderDetail?.imageUrl || defaultImage"
          [width]="100"
          [height]="100"
        />
      </div>
      <h5 class="mt-2">{{ videoCallLeaderDetail?.name }}</h5>
      <span class="video-call-leader-description">
        {{ videoCallLeaderDetail?.description }}
      </span>

      <div class="actions">
        <div class="action-button-wrapper pl-1">
          <button
            [disabled]="leaderStatus !== leaderCallStatusEnum.online"
            (click)="onClickCall()"
            class="btn call-button"
            [ngClass]="{
             'call-button-offline' : leaderStatus === leaderCallStatusEnum.offline,
             'call-button-online' : leaderStatus === leaderCallStatusEnum.online,
             'call-button-busy' : leaderStatus === leaderCallStatusEnum.busy
            }"

          >
            <i class="icon icon-video-camera"></i>
          </button>
          <!--          <div class="call-text text-center">-->
          <!--            <span>Görüşme başlat</span>-->
          <!--          </div>-->
        </div>
      </div>

    </div>
    <div>
      <div class="calendar-container">
        <div class="calendar-header">
          <span>{{ months[currentMonth] }} {{ currentYear }}</span>
          <div class="action-btns d-flex align-items-center">
            <button
              [disabled]="currentMonth === today.getMonth() && currentYear === today.getFullYear()"

              class="btn btn-sm p-0" (click)="previousMonth()">
              <i class="icon icon-chev-left"></i>
            </button>
            <button class="btn btn-sm p-0" (click)="nextMonth()">
              <i class="icon icon-chev-right"></i>
            </button>
          </div>
        </div>
        <div class="calendar-body">
          <div class="calendar-days">
            <div *ngFor="let day of days">{{ day }}</div>
          </div>
          <div class="calendar-dates">
            <div *ngFor="let blank of blanks" class="calendar-date blank"></div>
            <div
              *ngFor="let date of dates"
              class="calendar-date"
              (click)="filterAvailabilityDate(date.date)"
              [ngClass]="{
                'available-date-dot':
                  date.isAvailability,
                'available-date': date.isAvailability && selectedFilterAvailabilityDate?.length === 1 && checkSelectedDate(
                  date.date,
                  selectedFilterAvailabilityDate[0]?.date
                ),
                'unavailable-date': !date.isAvailability
              }"
            >
              {{ date.date }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="seperate-bar"></div>
  <div *ngIf="selectedFilterAvailabilityDate.length" class="available-title ml-3">{{ "_available_hours" | translate }}</div>
  <div
    class="date-container bg-white"
    *ngFor="let day of selectedFilterAvailabilityDate"
  >
    <h6 class="date-container-title mb-0">
      {{ day.date | date : "dd.MM.YYYY" }}
    </h6>
    <div class="available-hours">
      <div
        class="available-hours-content"
        *ngFor="let availabilityDate of day.availableHours"
      >
        {{ formatTime(availabilityDate.startTime) }} -
        {{ formatTime(availabilityDate.endTime) }}
      </div>
    </div>
  </div>
</div>
<app-loader [show]="videoCallLeaderDetailLoading$ | async"></app-loader>
