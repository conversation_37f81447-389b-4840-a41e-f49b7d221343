.click-away-user-profile-container {
  height: calc(100vh - 64px);
  overflow: auto;
  padding-top: 0.5rem !important;
  background-color: #fafafa;

  .user-profile-card {
    padding: 1rem;
    border-radius: 1rem;
  }

  .video-call-leader-description {
    color: #a0a0a0;
  }
}

.calendar-container {
  width: 100%;
  text-align: center;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  font-weight: 500;
}

.calendar-body {
}

.calendar-days {
  display: flex;
  justify-content: space-around;
  font-weight: bold;
}

.calendar-dates {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  align-items: center;
  justify-content: center;
  place-items: flex-end;
}

.calendar-date {
  width: 40px;
  height: 40px;
  padding: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 10px 0;
  margin-top: 0;
}

.available-date{
  color: #039A00;
  border: 1px solid #039A00;
  background-color: #F1FFF0;
  border-radius: 50%;
  font-weight: 700;
}

.available-date-dot{
  color: #039A00;
  font-weight: bold;
  position: relative;

  &::after{
    content: '';
    width: 5px;
    height: 5px;
    background-color: #039A00;
    border-radius: 50%;
    position: absolute;
    bottom: 3px;
  }
}

.unavailable-date{
  opacity: .3;
  pointer-events: none;
}

.calendar-date.blank {
  background-color: transparent;
}

.seperate-bar {
  width: 90%;
  height: .5px;
  background-color: #E2E2E2;
  margin: 12px auto;
}


.date-container{
  padding: 1rem;
  border-radius: 1rem;

  &-title{
    padding-bottom: .5rem;
    border-bottom: .5px solid #E2E2E2;
  }
  .available-title {
    font-weight: 500;
  }
  .available-hours{
    display: grid;
    grid-template-columns: repeat(3, 1fr);

    &-content{
      padding: .75rem;
      text-wrap: nowrap;
      border-right: .5px solid #E2E2E2;
    }
  }
}


.icon-chev-right, .icon-chev-left{
  font-size: 18px;
  font-weight: bold;
}

.action-btns{
  gap: 1.25rem;
}

.btn:focus{
  box-shadow: none !important;
}

.call-button {
  width: 55px;
  height: 55px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &-online, &-offline {
    background-color: #EFFFEE;
    border: 1px solid #039A00;
    &:disabled {
      opacity: 0.40;
      background-color: #EAEAEA;
      border: none;

      :before {
        color: #8F8F8F;
      }
    }
  }


  &-busy {
    background-color: #EFFFEE;
    &:disabled {
      opacity: 0.40;
      background-color: #FFDEDE;
      border: none;
      :before {
        color: #FF0000;
      }
    }
  }

}

.actions {
  width: 30%;
}

.action-button-wrapper {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  flex-flow: row wrap;
  justify-content: space-around;
  gap: .25rem
}

