<div
  class="p-3"
  id="clickAway"
  style="
    height: calc(100vh - 64px);
    overflow: auto;
    padding-top: 0.5rem !important;
    background-color: #fafafa;
  "
>
  <div class="mb-3">
    <!--    <app-click-away-badge [text]="'_leader_call_info' | translate"></app-click-away-badge>-->
    <p>
      <app-info-box
        [title]=""
      >
        {{ '_leader_call_info' | translate }}
      </app-info-box>
    </p>
  </div>
  <div class="col-12 categories mx-auto pb-1 mt-2">
    <div #tabContainer class="categories-container d-flex align-items-center">
      <ng-container>
        <div class="categories-content px-3">
          <div
            class="text-container"
            [ngClass]="{ activeCategory: !filterValue }"
            (click)="setFilter(null)"
          >
            {{ "_all_categories" | translate }}
          </div>
          <span *ngIf="!filterValue" style="bottom: 16px"></span>
        </div>
      </ng-container>
      <ng-container *ngFor="let category of tags">
        <div class="categories-content px-3">
          <div
            class="text-container"
            [ngClass]="{ activeCategory: category === filterValue }"
            (click)="setFilter(category)"
          >
            {{ 'VideoCallQueueTag.' + category | translate }}
          </div>
          <span
            *ngIf="category === filterValue"
            style="bottom: 16px"
          ></span>
        </div>
      </ng-container>
    </div>
    <hr style="min-width: 100%" [style.width.px]="tabContainerWidth"/>
  </div>

  <div *ngFor="let user of filteredUsers; trackBy: trackByFn" class="mb-2">
    <app-click-away-user-card
      [user]="user"
      (clickCall)="onClickCall($event)"
    ></app-click-away-user-card>
  </div>
</div>
<app-loader [show]="loading$ | async"></app-loader>

<pull-to-refresh
  [sensitivity]="130"
  target="#clickAway"
  color="#ffa300"
  [autoDismiss]="true">
</pull-to-refresh>

