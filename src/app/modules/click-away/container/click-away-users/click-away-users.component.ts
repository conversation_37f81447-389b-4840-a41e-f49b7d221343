import { AfterViewInit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { Observable, Subject, timer } from 'rxjs';
import { take, takeUntil } from 'rxjs/operators';
import { PullToRefreshService } from '@piumaz/pull-to-refresh';
import { LeaderUser } from '../../../customer/model/contact';
import { ContactState } from '../../../customer/state/contact/contact.state';
import { VideoCallManagerService } from '../../../../shared/service/video-call-manager.service';
import { GetVideoCallLeaders } from '../../../customer/state/contact/contact.actions';

@Component({
  selector: 'app-click-away-users',
  templateUrl: './click-away-users.component.html',
  styleUrls: ['./click-away-users.component.scss'],
})
export class ClickAwayUsersComponent implements OnInit, OnDestroy, AfterViewInit {
  users: LeaderUser[] = [];

  @Select(ContactState.loading)
  loading$: Observable<boolean>;

  @Select(ContactState.videoCallLeaders)
  videoCallLeaders$: Observable<LeaderUser[]>;

  private subscriptions$: Subject<boolean> = new Subject();

  groupedFilters = {
    All: [{}],
    Y101: [{}],
    ZKRQ: [{}],
    ZMDA: [{}],
    ZSRT: [],
    ZCVQ: [],
    Y201: [],
  };
  filterValue = null;
  tags: any[] = [
    'Project Management',
    'Tecnical Support',
    'Other',
    'Test 123',
    'Test',
    'Test asdadsf',
  ];
  @ViewChild('tabContainer') tabContainer: ElementRef;
  tabContainerWidth: any;
  filteredUsers: LeaderUser[];


  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe,
    private readonly router: Router,
    private readonly videoCallManagerService: VideoCallManagerService,
    private pullToRefreshService: PullToRefreshService
  ) {}

  ngOnInit() {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: true,
        notificationIcon: false,
        title: this.translate.transform('_just_a_click_away'),
      })
    );

    timer(0, 5000)
      .pipe(takeUntil(this.subscriptions$))
      .pipe(take(120))
      .subscribe((value) => {
        this.store.dispatch(new GetVideoCallLeaders(value === 0));
      });

    this.videoCallLeaders$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((leaders) => {
        this.users = leaders;
        this.buildTags();
        this.filterUsers();
      });

    this.pullToRefreshService
      .refresh$()
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(() => {
        // console.log('::PULLED');
        this.store.dispatch(new GetVideoCallLeaders());
      });

  }

  filterUsers() {
    if (!this.filterValue) {
      this.filteredUsers = this.users;
      return;
    }
    this.filteredUsers = this.users.filter(user => user.tags.includes(this.filterValue));
  }

  setFilter(val: string) {
    this.filterValue = val;
    this.filterUsers();
  }

  ngAfterViewInit(): void {
    this.tabContainerWidth = this.tabContainer?.nativeElement?.scrollWidth;
  }

  trackByFn(index, item) {
    return item.queueName;
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onClickCall(user: LeaderUser) {
    this.videoCallManagerService.startVideoCall(user.queueName, 'justAClick', null, 'JUST_A_CLICK');
  }

  protected buildTags() {
    this.tags = [];

    this.users.map(user => {
      this.tags.push(...user.tags);
      this.tags = [...new Set(this.tags)];
      this.tabContainerWidth = this.tabContainer?.nativeElement?.scrollWidth;
    });
  }
}
