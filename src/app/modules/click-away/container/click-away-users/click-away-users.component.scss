@media (min-width: 560px) {
  .search-area {
    width: 100%;
    input {
      padding-left: 47px;
      margin-left: 5px;
      width: 95%;
    }
  }
  .categories{
    overflow-x: hidden;
    overscroll-behavior-x: contain;
    width: 90%;
  }
  .categories-container {
    justify-content: center;
  }
}

.categories{
  overflow-x: auto;
  overscroll-behavior-x: contain;
  //width: 90%;
}
.categories-container{
  display: grid;
  grid-auto-flow: column;
  grid-auto-columns: max-content;
  align-items: center;
  text-align: center;
}
::-webkit-scrollbar {
  width: 0;
  height: 0;
  background: transparent;
  border: none;
}
.categories-content {
  width: auto;
}
.categories-container .categories-content:last-child::after {
  content: '';
  position: absolute;
  width: 2rem;
  right: -2rem;
  opacity: 0;
}
.text-container {
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 100%;
  color: grey;
  opacity: 0.2;
  display: flex;
  justify-content: center;
}

.activeCategory{
  color: #4A8EB0 !important;
  opacity: 1;
}
.categories-content  span {
  content: '';
  position: absolute;
  width: 10px;
  height: 10px;
  background: #4A8EB0;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  border-radius: 50%;
}
