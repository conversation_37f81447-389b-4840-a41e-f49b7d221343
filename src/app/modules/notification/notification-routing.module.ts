import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NotificationsComponent } from './container/notifications/notifications.component';
import { NotificationDetailComponent } from './component/notification-detail/notification-detail.component';
import { NotificationListComponent } from './component/notification-list/notification-list.component';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';

const routes: Routes = [
  {
    path: '',
    component: NotificationsComponent,
    canActivate: [PermissionGuard],
    children: [
      { path: '', component: NotificationListComponent },
      { path: ':id', component: NotificationDetailComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class NotificationRoutingModule {}
