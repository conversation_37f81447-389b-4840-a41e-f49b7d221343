import { Action, Selector, State, StateContext } from '@ngxs/store';
import {
  GetCurrentNotificationsAction,
  GetOtherNotificationsAction,
  NewBubblePushNotification,
  NewDeviceToken,
  NewPushNotification, NotificationDeleteAction,
  NotificationEventAction,
  NotificationSettingsGetAction,
  NotificationSettingsSaveAction,
  NotificationUpdateAction,
  ResetPushNotification,
} from './notification.actions';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import { SoundDiagnosticActionModel } from '../../model/sound-diagnostic-action.model';
import { NotificationModel, NotificationSettingsModel, NotificationSettingsTypeModel } from '../../model/notification.model';
import { NotificationService } from '../../service/notification.service';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { Injectable, NgZone } from '@angular/core';
import { Router } from '@angular/router';
import { HealthCodeEnum } from '../../enum/health-code.enum';
import { isNumeric } from 'src/app/util/common.util';
import { PagingModel } from 'src/app/core/interfaces/http.response';


export interface NotificationStateModel {
  notifications: any[];
  currentNotifications: any[];
  currentNotificationsPaging: PagingModel;
  otherNotifications: any[];
  otherNotificationsPaging: PagingModel;
  pushNotification: NotificationModel<any>;
  deviceToken: string;
  loading: boolean;
  newIcon: boolean;
  notificationSettings: NotificationSettingsModel[];
  saveNotificationSettings: NotificationSettingsTypeModel[];
  saveLoading: boolean;
}

@State<NotificationStateModel>({
  name: 'notification_main',
  defaults: {
    notifications: null,
    currentNotifications: null,
    currentNotificationsPaging: null,
    otherNotifications: null,
    otherNotificationsPaging: null,
    pushNotification: null,
    deviceToken: null,
    loading: false,
    newIcon: false,
    notificationSettings: null,
    saveNotificationSettings: null,
    saveLoading: false,
  },
})
@Injectable()
export class NotificationState {

  constructor(
    private readonly notificationService: NotificationService,
    private readonly router: Router,
    private readonly zone: NgZone,
  ) { }

  @Selector()
  public static getState(state: NotificationStateModel) {
    return state;
  }

  @Selector()
  public static pushNotification({ pushNotification }: NotificationStateModel): NotificationModel<any> {
    return pushNotification;
  }

  @Selector()
  public static loading({ loading }: NotificationStateModel): boolean {
    return loading;
  }

  @Selector()
  public static deviceToken({ deviceToken }: NotificationStateModel): string {
    return deviceToken;
  }

  @Selector()
  public static notifications({ notifications }: NotificationStateModel): NotificationModel<any>[] {
    return notifications;
  }

  @Selector()
  public static currentNotifications({ currentNotifications }: NotificationStateModel): NotificationModel<any>[] {
    return currentNotifications;
  }

  @Selector()
  public static otherNotifications({ otherNotifications }: NotificationStateModel): NotificationModel<any>[] {
    return otherNotifications;
  }
  @Selector()
  public static currentNotificationsPaging({ currentNotificationsPaging }: NotificationStateModel): PagingModel {
    return currentNotificationsPaging;
  }

  @Selector()
  public static otherNotificationsPaging({ otherNotificationsPaging }: NotificationStateModel): PagingModel {
    return otherNotificationsPaging;
  }

  @Selector()
  public static newIcon({ newIcon }: NotificationStateModel): boolean {
    return newIcon;
  }

  @Selector()
  public static notificationSettings({ notificationSettings }: NotificationStateModel): NotificationSettingsModel[] {
    return notificationSettings;
  }

  @Selector()
  public static saveNotificationSettings({ saveNotificationSettings }: NotificationStateModel): NotificationSettingsTypeModel[] {
    return saveNotificationSettings;
  }

  @Selector()
  public static saveLoading({ saveLoading }: NotificationStateModel): boolean {
    return saveLoading;
  }

  static normalizeNotification(pushNotification) {

    // FCM manipulate notification data
    if (typeof pushNotification['gcm.notification.actionType'] !== 'undefined') {
      const sanitized: any = {
        ...pushNotification.aps?.alert,
      };
      for (let [key, value] of Object.entries(pushNotification)) {
        if (!pushNotification.hasOwnProperty(key) || !key.startsWith('gcm.notification.')) {
          continue;
        }
        key = key.replace('gcm.notification.', '');
        sanitized[key] = value;
      }
      pushNotification = sanitized;
    } else if (pushNotification.notification) {
      pushNotification = pushNotification.notification;
    }

    if (!pushNotification?.customerNumber && pushNotification.action?.CustomerNumber) {
      pushNotification.customerNumber = pushNotification.action.CustomerNumber;
      pushNotification.action.customerNumber = pushNotification.action.CustomerNumber;
    }

    // fix for time format, not valid in ios 2021-02-13 20:16:34Z > 2021-02-13T20:16:34Z(valid)
    pushNotification.date = pushNotification.date.replace(' ', 'T');

    // action is json encoded
    if (pushNotification.action && typeof pushNotification.action === 'string') {
      try {
        pushNotification.action = JSON.parse(pushNotification.action);
      } catch (e) { }
    }
    // message id comes in different key
    if (!pushNotification.messageId) {
      pushNotification.messageId = pushNotification.InboxMessageId;
    }

    switch (pushNotification.actionType) {
      case NotificationActionTypeEnum.SoundDiagnostic:
        if (!isNumeric(pushNotification.action.healthCode)) {
          pushNotification.action.healthCode = HealthCodeEnum[pushNotification.action.healthCode];
        }
        // sound notification details comes in top keys, @deprecated
        if (!pushNotification.action) {
          pushNotification.action = {
            equipmentNumber: pushNotification?.equipmentNumber,
            customerNumber: pushNotification?.customerNumber,
            serialNumber: pushNotification?.serialNumber,
            healthProbability: pushNotification?.healthProbability,
            soundId: pushNotification?.soundId,
            healthCode: pushNotification?.healthCode,
          } as SoundDiagnosticActionModel;

        }

    }
    return pushNotification;
  }

  @Action(NewBubblePushNotification)
  newBubblePushNotification(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { pushNotification }: NewBubblePushNotification): void {
    patchState({
      newIcon: true
    });
  }

  @Action(NewPushNotification)
  newPushNotification(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { pushNotification }: NewPushNotification): void {
    if (!pushNotification) {
      return;
    }

    pushNotification = NotificationState.normalizeNotification(pushNotification);
    console.log('LAST NOTIFICATION', pushNotification);

    patchState({
      pushNotification,
    });
    // this.store.dispatch(new Navigate(['/notifications', 'new']));

    setTimeout(() =>
      this.zone.run(() => {
        console.log('NAVIGATE /notifications/new ', this.router.url);
        this.router.navigate(['notifications', 'new'], { state: pushNotification });
        // this.store.dispatch(new Navigate(['/notifications', 'new']));
      }), 150);
  }

  @Action(ResetPushNotification)
  resetPushNotification(
    { getState, patchState }: StateContext<NotificationStateModel>,
    R: ResetPushNotification): void {
    patchState({
      pushNotification: null,
    });
  }

  @Action(NewDeviceToken)
  newDeviceToken(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { deviceToken }: NewDeviceToken): void {
    patchState({
      deviceToken,
    });
  }

  @Action(GetCurrentNotificationsAction)
  getCurrentNotificationsAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    a: GetCurrentNotificationsAction) {
    patchState({
      loading: true,
    });

    return this.notificationService.getCurrentCustomerMessages(getState().deviceToken, a.top, a.page)
      .pipe(
        tap((value) => {
          let list;
          if (value.data !== null) {
            list = a.page === 1 ? [] : getState().currentNotifications;
            list = list.concat(value?.data);
          }
          patchState({
            loading: false,
            currentNotifications: list,
            currentNotificationsPaging: value.paging
          });
        }),
        catchError((err) => {
          patchState({
            loading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(GetOtherNotificationsAction)
  getOtherNotificationsAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    a: GetOtherNotificationsAction) {
    patchState({
      loading: true,
    });
    return this.notificationService.getOtherCustomerMessages(getState().deviceToken, a.top, a.page)
      .pipe(
        tap((value) => {
          let list;
          if (value.data !== null) {
            list = a.page === 1 ? [] : getState().otherNotifications;
            list = list.concat(value?.data);
          }
          patchState({
            loading: false,
            otherNotifications: list,
            otherNotificationsPaging: value.paging
          });
        }),
        catchError((err) => {
          patchState({
            loading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(NotificationEventAction)
  notificationEventAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { eventType, messageId }: NotificationEventAction) {

    return this.notificationService.sendEvent({
      Token: getState().deviceToken,
      EventType: eventType,
      MessageId: messageId,
      UserAgent: navigator.userAgent,
    });

  }

  @Action(NotificationUpdateAction)
  notificationUpdateAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { notificationState }: NotificationUpdateAction) {
    patchState(notificationState);
  }

  @Action(NotificationDeleteAction)
  notificationDeleteAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { messageId }: NotificationDeleteAction) {

    return this.notificationService.deleteNotification({
      Token: getState().deviceToken,
      MessageIds: [messageId]
    });

  }

  @Action(NotificationSettingsGetAction)
  getNotificationsSettingsAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    notificationSettings: NotificationSettingsGetAction) {
    patchState({
      saveLoading: true,
    });

    return this.notificationService.getSettingsNotification()
      .pipe(
        tap((value) => {
          patchState({
            saveLoading: false,
            notificationSettings: value,
          });
        }),
        catchError((err) => {
          patchState({
            saveLoading: false,
          });
          return throwError(err);
        }),
      );

  }

  @Action(NotificationSettingsSaveAction)
  saveNotificationsSettingsAction(
    { getState, patchState }: StateContext<NotificationStateModel>,
    { notificationSettingsSave }: NotificationSettingsSaveAction) {
    // patchState({
    //   saveLoading: true,
    // });

    return this.notificationService.saveSettingsNotification(notificationSettingsSave)
      .pipe(
        tap((value) => {
          // patchState({
          //   saveLoading: false,
          // });
        }),
        catchError((err) => {
          // patchState({
          //   saveLoading: false,
          // });
          return throwError(err);
        }),
      );

  }

}
