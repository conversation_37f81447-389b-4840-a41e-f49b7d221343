import { NotificationModel, NotificationSettingsModel, NotificationSettingsTypeModel } from '../../model/notification.model';
import { NotificationStateModel } from './notification.state';

export class NotificationAction {
  public static readonly type = '[Notification] Add item';

  constructor(public payload: string) { }
}

export class NewPushNotification {
  public static readonly type = '[Notification] new push notification';

  constructor(public pushNotification: NotificationModel<any> | any) { }
}
export class NewBubblePushNotification {
  public static readonly type = '[Notification] bubble push';

  constructor(public pushNotification: NotificationModel<any> | any) { }
}

export class ResetPushNotification {
  public static readonly type = '[Notification] reset push notification';

  constructor() { }
}

export class NewDeviceToken {
  public static readonly type = '[Notification] device token';

  constructor(public deviceToken: string) { }
}

export class GetCurrentNotificationsAction {
  public static readonly type = '[Notification] get current';

  constructor(public top?: number, public page?: number) { }
}

export class GetOtherNotificationsAction {
  public static readonly type = '[Notification] get other';

  constructor(public top?: number, public page?: number) { }
}

export class NotificationEventAction {
  public static readonly type = '[Notification] event';

  constructor(public eventType: string, public messageId: string) { }
}

export class NotificationUpdateAction {
  public static readonly type = '[Notification] update';

  constructor(public notificationState: Partial<NotificationStateModel>) { }
}

export class NotificationDeleteAction {
  public static readonly type = '[Notification] delete';

  constructor(public messageId: string) { }
}

export class NotificationSettingsGetAction {
  public static readonly type = '[Notification] settings get';

  constructor() { }
}

export class NotificationSettingsSaveAction {
  public static readonly type = '[Notification] settings save';

  constructor(public notificationSettingsSave: NotificationSettingsTypeModel[]) { }
}
