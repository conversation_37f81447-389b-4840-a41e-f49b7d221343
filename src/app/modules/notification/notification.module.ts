import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { NotificationRoutingModule } from './notification-routing.module';
import { NotificationDetailComponent } from './component/notification-detail/notification-detail.component';
import { NotificationsComponent } from './container/notifications/notifications.component';
import { NotificationListComponent } from './component/notification-list/notification-list.component';
import { CustomerModule } from '../customer/customer.module';
import { BasicNotificationComponent } from './component/basic-notification/basic-notification.component';
import { DiagnosticNotificationComponent } from './component/diagnostic-notification/diagnostic-notification.component';
import { MomentModule } from 'ngx-moment';
import { TranslateModule } from '@ngx-translate/core';
import * as moment from 'moment';
import { SharedModule } from '../../shared/shared.module';
import { Store } from '@ngxs/store';
import { LoginState } from '../authentication/state/login/login.state';
import { FinancialNotificationComponent } from './component/financial-notification/financial-notification.component';
import { QuotationNotificationComponent } from './component/quotation-notification/quotation-notification.component';
import { QuotationCrmStatusNotificationComponent } from './component/quotation-crm-status-notification/quotation-crm-status-notification.component';
import { ServiceNotificationComponent } from './component/service-notification/service-notification.component';
import { DailyWorkNotificationComponent } from './component/daily-work-notification/daily-work-notification.component';
import {NgbAccordionModule, NgbDropdownModule, NgbTooltipModule} from '@ng-bootstrap/ng-bootstrap';
import { OrderStatusNotificationComponent } from './component/order-status-notification/order-status-notification.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { NotificationSettingsComponent } from './component/notification-settings/notification-settings.component';
import { CargoPushNotificationComponent } from './component/cargo-push-notification/cargo-push-notification.component';
import { RfmsPushNotificationComponent } from './component/rfms-push-notification/rfms-push-notification.component';
import { LoyalityPointNotificationComponent } from './component/loyality-point-notification/loyality-point-notification.component';
import { LinkedProductsNotificationComponent } from './component/linked-products-notification/linked-products-notification.component';
import { PssrSectionComponent } from './component/pssr-section/pssr-section.component';
import { MuneccimNotificationComponent } from './component/muneccim-notification/muneccim-notification.component';
import { PictureNotificationComponent } from './component/picture-notification/picture-notification.component';
import { AgreementSurveyComponent } from '../customer/component/agreement-survey/agreement-survey.component';
import { NotificationNewApprovalUserComponent } from './component/notification-new-approval-user/notification-new-approval-user.component';
import { SettingsModule } from '../customer/container/settings/settings.module';
import { ContactYourRepresentativeModalComponent } from './component/quotation-notification/call-representative-modal/call-representative-modal.component';

// import 'moment/locale/tr';

@NgModule({
  declarations: [
    NotificationsComponent,
    NotificationDetailComponent,
    NotificationListComponent,
    NotificationSettingsComponent,
    BasicNotificationComponent,
    DiagnosticNotificationComponent,
    FinancialNotificationComponent,
    QuotationNotificationComponent,
    QuotationCrmStatusNotificationComponent,
    ServiceNotificationComponent,
    DailyWorkNotificationComponent,
    OrderStatusNotificationComponent,
    CargoPushNotificationComponent,
    RfmsPushNotificationComponent,
    LoyalityPointNotificationComponent,
    LinkedProductsNotificationComponent,
    PssrSectionComponent,
    MuneccimNotificationComponent,
    PictureNotificationComponent,
    AgreementSurveyComponent,
    NotificationNewApprovalUserComponent,
    ContactYourRepresentativeModalComponent
  ],
  imports: [
    CommonModule,
    NotificationRoutingModule,
    CustomerModule,
    NgbAccordionModule,
    FormsModule,
    NgSelectModule,
    ReactiveFormsModule,
    NgbDropdownModule,
    NgbTooltipModule,
    MomentModule.forRoot({
      relativeTimeThresholdOptions: {
        m: 59,
      },
    }),
    TranslateModule,
    SharedModule,
    SettingsModule
  ],
})
export class NotificationModule {
  constructor(
    private readonly store: Store
  ) {
    moment.updateLocale('tr', {
      relativeTime: {
        future: '%s',
        past: '%s',
        s: 'yeni',
        ss: '%dsn',
        m: '1dk',
        mm: '%ddk',
        h: '1s',
        hh: '%ds',
        d: '1g',
        dd: '%dg',
        M: '1a',
        MM: '%da',
        y: '1y',
        yy: '%dy',
      },
    });
    moment.updateLocale('en', {
      relativeTime: {
        future: 'in %s',
        past: '%s',
        s: 'now',
        ss: '%ds',
        m: '1m',
        mm: '%dm',
        h: '1h',
        hh: '%dh',
        d: '1d',
        dd: '%dd',
        w: '1w',
        ww: '%dw',
        M: '1m',
        MM: '%dm',
        y: '1y',
        yy: '%dy'
      }
    });

    this.store.select(LoginState.language).subscribe(language => {
      moment.locale(language);
    });
  }

}
