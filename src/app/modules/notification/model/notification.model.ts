import { NotificationActionTypeEnum } from '../enum/notification-action-type.enum';
import { NotificationActionType } from './notification-action.model';


export interface NotificationModel<T extends NotificationActionTypeEnum> {
  messageId: string;
  actionType: T;
  action: NotificationActionType<T>;
  title: string;
  body: string;
  isRead: boolean;
  readAt: string;
  date: string;
  localizations: any[];

  companyId?: string;
  isAuth?: boolean;
  customerNumber?: string;
  CustomerNumber?: string;
  customerName?: string;
}

export interface NotificationSettingsModel {
  groupName: string;
  order: number;
  isHidden: boolean;
  notificationTypeList: NotificationSettingsTypeModel[];
}

export interface NotificationSettingsTypeModel {
  portalUserId?: string;
  notificationType: number;
  notificationTypeName?: string;
  isSystemSmsEnabled?: boolean;
  isSystemEmailEnabled?: boolean;
  isSystemPushEnabled?: boolean;
  isUserSmsEnabled?: boolean;
  isUserEmailEnabled?: boolean;
  isUserPushEnabled?: boolean;
  isHidden?: boolean;
  order?: number;
}
