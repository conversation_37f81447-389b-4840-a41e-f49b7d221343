import { NotificationActionTypeEnum } from '../enum/notification-action-type.enum';
import { SoundDiagnosticActionModel } from './sound-diagnostic-action.model';
import { LinkActionModel } from './link-action.model';
import {QuotationCrmStatusNfActionModel} from './quotation-crm-status-action.model';
import { InsiderActionModel } from './insider-action.model';

export type NotificationActionType<T extends NotificationActionTypeEnum>
  = T extends keyof NotificationActionModel ? NotificationActionModel[T] : any;

export interface NotificationActionModel {
  // type: '';
  [NotificationActionTypeEnum.SoundDiagnostic]?: SoundDiagnosticActionModel;
  [NotificationActionTypeEnum.Link]?: LinkActionModel;
  [NotificationActionTypeEnum.QuotationCrmStatusPush]: QuotationCrmStatusNfActionModel;
  [NotificationActionTypeEnum.InsiderPush]: InsiderActionModel;
}


