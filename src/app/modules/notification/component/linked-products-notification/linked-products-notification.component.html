<div class="linked-products">
  <div class="ndet-header">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px"
          [routerLink]="'../'"></i>
      </div>
      <div class="d-flex justify-content-center">
        <h4>{{ '_linked_products' | translate }}</h4>
      </div>
      <div class="">
      </div>
    </div>
  </div>
  <div class="notification overflow-hidden mt-3">
    <div class="status-pop">
      <div class="d-flex justify-content-center overflow-hidden ">
        <div class="font-weight-semi-bold h6 m-0 text-center">
          {{ '_cannot_receive_signal' | translate }}
        </div>
      </div>
    </div>
  </div>
  <div class="">
    <div *ngIf="day30?.length">
      <div class="col-12 text-center h4">{{ '_for_30_days' | translate }}</div>
    <div class="notification-item overflow-hidden mb-3">
      <div class="status-pop pb-3 pr-3">
          <div class="pt-1 grid-container">
            <div class="col-12 d-flex grid-item py-1" *ngFor="let item of day30">
               <div class="">
                <div class="d-flex align-items-center">
                  <i class="icon icon-wifi notification-item-color-green"></i>
                  <div *ngIf="item" class="product-link d-inline-block ml-2 d-flex">
                    <div class="d-flex justify-content-center" tooltipClass="wifi-icon"
                      placement="left">
                      {{ item }}
                    </div>
                  </div>
                 </div>
               </div>
            </div>
          </div>
      </div>
    </div>
    </div>
    <div *ngIf="day60?.length">
      <div class="col-12 text-center h4">{{ '_for_60_days' | translate }}</div>
    <div class="notification-item overflow-hidden mb-3">
      <div class="status-pop pb-3 pr-3">
          <div class="pt-1 grid-container">
            <div class="col-12 d-flex grid-item py-1" *ngFor="let item of day60">
               <div class="">
                <div class="d-flex align-items-center ">
                  <i class="icon icon-wifi notification-item-color-orange"></i>
                  <div *ngIf="item" class="product-link d-inline-block ml-2 d-flex">
                    <div class="d-flex justify-content-center" tooltipClass="wifi-icon"
                      placement="left">
                      {{ item }}
                    </div>
                  </div>
                 </div>
               </div>
            </div>
          </div>
      </div>
    </div>
    </div>
    <div *ngIf="day90?.length">
      <div class="col-12 text-center h4">{{ '_for_90_days' | translate }}</div>
    <div class="notification-item overflow-hidden mb-3">
      <div class="status-pop pb-3 pr-3">
          <div class="pt-1 grid-container">
            <div class="col-12 d-flex grid-item py-1" *ngFor="let item of day90">
               <div class="">
                <div class="d-flex align-items-center ">
                  <i class="icon icon-wifi notification-item-color-red"></i>
                  <div *ngIf="item" class="product-link d-inline-block ml-2 d-flex">
                    <div class="d-flex justify-content-center" tooltipClass="wifi-icon"
                      placement="left">
                      {{ item }}
                    </div>
                  </div>
                 </div>
               </div>
            </div>
          </div>
      </div>
    </div>
    </div>
    </div>
    <div class="label-text d-flex justify-content-center align-items-center">
      <span>{{
        getCustomer(
          notification?.action?.customerNumber || notification?.customerNumber
        )?.customer?.name.length > 35
          ? (getCustomer(
              notification?.action?.customerNumber ||
                notification?.customerNumber
            )?.customer?.name | slice: 0:32) + "..."
          : getCustomer(
              notification?.action?.customerNumber ||
                notification?.customerNumber
            )?.customer?.name
      }}</span>
    </div>
    <app-pssr-section [showAllButtons]="true" [notification]="notification"></app-pssr-section>
    <button type="button" class="action-btn btn btn-warning mb-2 text-white w-100" (click)="openLinkedEquipments()" > {{ "_to_equipment_list" | translate }} </button>
