import { Component, ElementRef, OnInit, Input, OnDestroy } from '@angular/core';
import { NotificationModel } from '../../model/notification.model';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { ModalService } from 'src/app/shared/service/modal.service';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';
import { CustomerRelationModel } from 'src/app/modules/customer/model/customer-relation.model';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { takeUntil } from 'rxjs/operators';
@Component({
  selector: 'app-linked-products-notification',
  templateUrl: './linked-products-notification.component.html',
  styleUrls: ['./linked-products-notification.component.scss']
})
export class LinkedProductsNotificationComponent implements OnInit, OnDestroy {

  day30: string[];
  day60: string[];
  day90: string[];

  @Input()
  notification: NotificationModel<NotificationActionTypeEnum.LinkedProducts>;


  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;

  customersList: CustomerRelationModel[];
  notificationActionTypeEnum = NotificationActionTypeEnum;


  constructor(
    private readonly customerModuleService: CustomerModuleService,
    private readonly modalService: ModalService,
    private readonly messageService: FrameMessageService,
    private readonly translateService: TranslateService,
    private readonly router: Router,
    private readonly elementRef: ElementRef,
    private readonly store: Store,
    ) { }

  protected subscriptions$: Subject<boolean> = new Subject();
  ngOnInit() {
    this.day30 = this.notification.action?.equipmentsFor30Days?.split(',')
      || this.notification.action?.EquipmentsFor30Days?.split(',');
    this.day60 = this.notification.action?.equipmentsFor60Days?.split(',')
      || this.notification.action?.EquipmentsFor60Days?.split(',');
    this.day90 = this.notification.action?.equipmentsFor90Days?.split(',')
      || this.notification.action?.EquipmentsFor90Days?.split(',');


    this.customers$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(customers => {
      this.customersList = customers;
    });


  }

  getCustomer(customerNumber: string) {
    if (!customerNumber) {
      return null;
    }
    return this.customersList.find(customer => customer.customer?.customerNumber === customerNumber);
  }

  openLinkedEquipments() {
    this.customerModuleService.openEquipmentModule(
      this.notification.action?.customerNumber || this.notification?.customerNumber,
      this.notification.action?.equipmentNumber || this.notification.action?.equipmentNumber,
      {
        replaceUrl: true, params: { IsProductLinked: 1,
          source: 'Muneccim',
          sourceRoot: 'MuneccimNotification'
         }
      });
  }


  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
