

.label-text {
  margin-bottom: 10px;
}

@import "variable/bootstrap-variable";

.ndet {
  &-header {
    h4 {
      color: #2C2C2C;
      font-weight: 700;
    }
  }

}


.notification {

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    border: 1px solid #d8d8d8;
    border-radius: 6px;
    position: relative;
    min-width: 308.675px;
    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;

      &-green {
        color: #5D8D1C;
      }

      &-orange {
        color: $warning;
      }

      &-red {
        color: #FF0000;
      }

      &-yellow {
        color: #fbff00;
      }
    }
  }
}

.label-text {
  font-size: 13px;
}

.action-btn {
  font-size: 16px;
  font-weight: 700;
}

.status-pop {
  padding: 16px 30px;
  min-height: 100px;
}

.btn-warning {
  background-color: $warning;

  &:focus {
    background-color: $warning;
    border-color: $warning;
  }

  &:hover {
    background-color: $warning;
    border-color: $warning;
  }

}
.grid-container{
  display: grid;
  grid-template-columns: 12ch auto;
  grid-template-rows: auto;
  float: left;
  margin: 0 auto;
  margin-bottom: 3px;
}
.grid-item{
  width: max-content;
}
.linked-products{
  height: calc(100vh - 110px);
  overflow-y: auto;
}
