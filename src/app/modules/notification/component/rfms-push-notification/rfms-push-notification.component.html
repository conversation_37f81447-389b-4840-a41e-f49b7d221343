<ng-container *ngIf="!formSendedModal">
  <div class="ndet-header">
    <div class="d-flex justify-content-between ">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <!-- <h4>{{ '_rfms_push_notification_header' | translate }}</h4> -->
        <h4>{{ '_notifications' | translate }}</h4>
      </div>
      <div class="">
      </div>
    </div>
  </div>

  <div class="notification-item overflow-hidden mb-3 mt-3">
    <div class="status-pop">
      <div class="d-flex justify-content-between mb-2 overflow-hidden align-items-end">
        <div class="font-weight-semi-bold h6 m-0">
          {{notification?.title}}
        </div>
      </div>
      <div class="label-text">
        {{ notification?.body }}
      </div>
      <div class="d-flex justify-content-between my-3 g-2">
        <div class="w-100 d-flex justify-content-between">
          <button class="flex-fill btn btn-success mr-1" (click)="clickYes()" [disabled]="clickYesLoading">
            {{ '_yes' | translate }}
          </button>
          <button *ngIf="rfmsFormStatus?.hasRfms" class="flex-fill btn btn-warning text-white ml-1" (click)="questionModal = true;">
            {{ '_no' | translate }}
          </button>
        </div>
      </div>
      <div class="label-text pt-2">
        <!-- <span>{{ getViewCustomerName(notify?.customerName || getCustomerName(notify.customerNumber)?.name) }}</span> -->
        {{ notification?.customerName?.length > 35 ? (notification?.customerName | slice: 0:32) + "..." : notification?.customerName }}
      </div>
    </div>
  </div>
</ng-container>

<app-basic-modal [(status)]="questionModal">
  <div *ngIf="!rfmsFormStatus?.hasCompletedForm else ignoreReason" class="rfms-question-list mb-2">
    <ul *ngIf="questions?.length">
      <li *ngFor="let question of questions">
        <div>
          <input type="radio" [id]="'q' + question.key" name="groupName" (click)="questionSelected(question)" />
          <label class="" [for]="'q' + question.key">
            {{ question.value }}
          </label>
        </div>
      </li>
    </ul>
    <div class="d-flex justify-content-center">
      <button class="btn btn-warning text-white" (click)="sendQuestionForm()" [disabled]="!selectedAnswer
        && (borusanBlockedActions && borusanBlockedActions.indexOf(BorusanBlockedActionsEnum.SubmitRfmsForm) >= 0)">
        {{ '_send' | translate }}
      </button>
    </div>
  </div>
  <ng-template #ignoreReason>
    <div class="card mb-3">
      <div class="card-header">
        {{ '_your_choise' | translate }}:
      </div>
      <div class="card-body">
        <b>
          {{ rfmsFormStatus?.ignoreReason }}
        </b>
      </div>
    </div>
  </ng-template>

</app-basic-modal>

<app-success-modal *ngIf="formSendedModal" message="_general_successfully_send_form">
  <button class="btn btn-warning text-white" (click)="closeSuccess()">
    {{ '_close' | translate }}
  </button>
</app-success-modal>

<app-loader [show]="(formLoading$ | async) || clickYesLoading"></app-loader>
