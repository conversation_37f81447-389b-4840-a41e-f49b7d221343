.ndet {
  &-header {
    h4 {
      color: #2C2C2C;
      font-weight: 700;
    }
  }

  &-title {
    font-weight: 500;
    font-size: 16px;
  }

  &-content {
    font-family: 'Poppins', sans-serif;
    font-weight: 400;
    font-size: 14px;
    color: #505050;
    line-height: 1.8em;
  }
}

.notification {

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    border: 1px solid #d8d8d8;
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;
    }
  }
}

.status-pop {
  padding: 16px 30px;
  min-height: 100px;
}

.label-text {
  font-size: 13px;
}

.rfms-question-list {
  width: 100% !important;

  ul {
    list-style: none;
    margin: 0;
    //margin: 0 9px;
    padding: 0;

    // max-height: 58vh;
    li {
      margin: 10px 0;

      &:first-child {
        margin-top: 0;
      }

      label {
        white-space: normal;
        overflow: hidden !important;
        text-overflow: ellipsis;
        max-width: 95%;
      }
    }
  }

  [type="radio"]:checked,
  [type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
  }

  [type="radio"]:checked+label,
  [type="radio"]:not(:checked)+label {
    position: relative;
    padding-left: 36px;
    cursor: pointer;
    display: inline-block;
    color: #666;
    font-size: 16px;
    line-height: 24px;
  }

  [type="radio"]:checked+label:before,
  [type="radio"]:not(:checked)+label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 18px;
    height: 18px;
    border: 1px solid #ddd;
    border-radius: 100%;
    background: #fff;
  }

  [type="radio"]:checked+label:before {
    border-color: #ffa300;
  }

  [type="radio"]:checked+label:after,
  [type="radio"]:not(:checked)+label:after {
    content: "";
    width: 12px;
    height: 12px;
    background: #ffa300;
    position: absolute;
    top: 3px;
    left: 3px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
  }

  [type="radio"]:not(:checked)+label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  [type="radio"]:checked+label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}

