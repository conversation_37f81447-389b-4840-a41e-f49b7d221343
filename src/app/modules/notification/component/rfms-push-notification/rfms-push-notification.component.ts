import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { RfmsFormStatusModel, RfmsIgnoreReasonsFormModel, RfmsIgnoreReasonsFormSendModel } from 'src/app/modules/customer/model/form.model';
import { MenuModel } from 'src/app/modules/customer/model/menu.model';
import { CatalogAction } from 'src/app/modules/customer/state/catalog/catalog.actions';
import { CatalogState } from 'src/app/modules/customer/state/catalog/catalog.state';
import { GetRfmsFormStatusAction, RfmsIgnoreReasonsFormAction, SendRfmsIgnoreReasonsFormAction } from 'src/app/modules/customer/state/form/form.actions';
import { FormState } from 'src/app/modules/customer/state/form/form.state';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import {SettingsState} from '../../../../shared/state/settings/settings.state';

@Component({
  selector: 'app-rfms-push-notification',
  templateUrl: './rfms-push-notification.component.html',
  styleUrls: ['./rfms-push-notification.component.scss']
})
export class RfmsPushNotificationComponent implements OnInit {

  @Input()
  notification: any;

  @Select(FormState.getRfmsIgnoreReasonsForm)
  questions$: Observable<RfmsIgnoreReasonsFormModel[]>;
  questions: RfmsIgnoreReasonsFormModel[];

  @Select(FormState.formLoading)
  formLoading$: Observable<boolean>;

  questionModal = false;
  selectedAnswer: any;

  @Select(FormState.sendRfmsIgnoreReasonsForm)
  rfmsSendRes$: Observable<any>;
  rfmsSendData: RfmsIgnoreReasonsFormSendModel;
  formSendedModal = false;

  @Select(FormState.getRfmsFormStatus)
  rfmsFormStatus$: Observable<RfmsFormStatusModel>;
  rfmsFormStatus: RfmsFormStatusModel;

  @Select(CatalogState.catalog)
  catalog$: Observable<MenuModel[]>;
  catalogList: MenuModel[];
  catalogTag: string;

  customerNumber: string;

  catalogTempListArray = [];
  catalogTagFind = false;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  protected subscriptions$: Subject<boolean> = new Subject();
  clickYesLoading: boolean;
  constructor(
    private readonly store: Store,
    private readonly router: Router,
  ) { }

  ngOnInit() {
    this.customerNumber = this.notification?.customerNumber || this.notification?.action?.customerNumber;
    this.store.dispatch(new GetRfmsFormStatusAction(this.customerNumber));
    this.rfmsFormStatus$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(status => {
        if (status) {
          this.rfmsFormStatus = status;

          if (status?.hasRfms && !status?.hasCompletedForm) {
            this.loadQuestionList();
          }
        }
      });

    this.catalogTag = this.notification?.action?.catalogTag;
    this.loadCatalogList();
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  loadQuestionList() {
    this.store.dispatch(new RfmsIgnoreReasonsFormAction());
    this.questions$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data?.length) {
          this.questions = data;
        }
      });
  }

  loadCatalogList() {
    this.catalogList = this.store.selectSnapshot(CatalogState.catalog);
    const headerCompanies = this.store.selectSnapshot(LoginState.headerCompanies);
    if (!this.catalogList?.length && headerCompanies) {
      this.store.dispatch(new CatalogAction(headerCompanies));
      this.catalog$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(catalog => {
          this.catalogList = catalog;
        });
    }
  }

  clickYes() {
    if (this.clickYesLoading) { return; }
    this.rfmsSendData = {
      IsAccept: true,
      IgnoreReason: 0,
      IgnoreReasonDescription: null,
      customerNumber: this.customerNumber
    };
    // ? RFMS Send
    this.clickYesLoading = true;
    this.store.dispatch(new SendRfmsIgnoreReasonsFormAction(this.rfmsSendData));
    this.rfmsSendRes$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.clickYesLoading = false;
          this.redirectToCatalog();
        }
      }, () => this.clickYesLoading = false);
  }

  redirectToCatalog() {
    if (!this.catalogList?.length) {
      console.log('Default RFMS Redirect');
      this.router.navigate(['catalog', '2', 'categories', '1']);
      return;
    }

    const redirect = [];
    redirect.push('catalog');
    // ! Warning Recursice Function
    this.catalogList.map((catalog) => {
      if (this.catalogTagFind) {
        return;
      }
      this.catalogTempListArray[0] = catalog.id;
      this.findCategoriesTagRecursive(catalog, 1);
    });

    if (this.catalogTagFind) {
      this.catalogTempListArray.map((x, index) => {
        redirect.push(x.toString());
        if (index !== (this.catalogTempListArray?.length - 1)) {
          redirect.push('categories');
        }
      });
      console.log('RFMS Redirect', redirect);
      this.router.navigate(redirect);
      return;
    }

    this.router.navigate(['catalog', '2', 'categories', '1']);
  }

  findCategoriesTagRecursive(c, k) {
    if (c.tagList?.length && c?.tagList?.indexOf(this.catalogTag) !== -1) {
      this.catalogTempListArray.splice(k);
      this.catalogTagFind = true;
    } else if (c.categories && c.categories.length && !this.catalogTagFind) {
      c.categories.map((x) => {
        if (!this.catalogTagFind) {
          this.catalogTempListArray[k] = x.id;
          this.findCategoriesTagRecursive(x, k + 1);
        }
      });
    }
  }

  questionSelected(selected) {
    this.selectedAnswer = selected;
  }

  sendQuestionForm() {
    this.rfmsSendData = {
      IsAccept: false,
      IgnoreReason: this.selectedAnswer.key,
      IgnoreReasonDescription: this.selectedAnswer.value,
      customerNumber: this.customerNumber
    };
    this.questionModal = false;

    // ? RFMS Send
    this.store.dispatch(new SendRfmsIgnoreReasonsFormAction(this.rfmsSendData));
    this.rfmsSendRes$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.formSendedModal = true;
        }
      });
  }

  closeSuccess() {
    this.router.navigate(['/']);
  }
}
