import { Component, Input, OnInit } from '@angular/core';
import { NotificationModel } from '../../model/notification.model';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import { Router } from '@angular/router';

@Component({
  selector: 'app-loyality-point-notification',
  templateUrl: './loyality-point-notification.component.html',
  styleUrls: ['./loyality-point-notification.component.scss']
})
export class LoyalityPointNotificationComponent implements OnInit {


  @Input()
  notification: NotificationModel<NotificationActionTypeEnum.LoyalityPointPush>;
  pointExpireDate: string;

  constructor(private readonly router: Router) {}

  NavToPromotion() {
    this.router.navigate(['/promotion/coin']);
  }

  ngOnInit() {
    const pattern = /(\d{4})(\d{2})(\d{2})/;
    this.pointExpireDate = this.notification.action.pointExpireDate.replace(pattern, '$1-$2-$3');
  }

}
