<div class="ndet-header">
  <div class="d-flex justify-content-between">
    <div>
      <i class="icon icon-back font-size-18px"
        [routerLink]="'../'"></i>
    </div>
    <div class="d-flex justify-content-center">
      <h4>{{"_boom_coin" | translate}}</h4>
    </div>
    <div class="">
    </div>
  </div>
</div>
<div class="notification overflow-hidden mt-3">
  <div class="status-pop">
    <div class="d-flex justify-content-center mb-2 overflow-hidden ">
      <div class="font-weight-semi-bold h6 m-0 ">
        {{notification?.title}}
      </div>
    </div>
    <div class="label-text">
      {{ notification?.body }}
    </div>
  </div>
</div>
<div *ngIf="notification"
  class="pb-2">
  <div class="notification-item overflow-hidden mb-3">
    <div [ngClass]="{
      'notification-item-color-green':
        notification.action.processType === 'Gain',
      'notification-item-color-orange':
      notification.action.processType === 'Refund',
        'notification-item-color-yellow':
        notification.action.processType === 'Spend',
      'notification-item-color-red':
      notification.action.processType === 'Expire'
    }"
      class="notification-item-color notification-item-color-green"></div>
    <div class="status-pop pb-3 pr-3">
      <div>
        <div class="row pt-1">
          <div class="col-6">{{'_loyalty_push_point' | translate}}</div>
          <div class="col-5">
            : {{ notification.action.point }}
          </div>
          <div class="col-6 pt-1">{{'_loyalty_push_point_expire_date' | translate}}</div>
          <div class="col-5">
            : {{ pointExpireDate}}
          </div>
          <div class="col-6 pt-1">{{'_loyalty_push_process_type' | translate}}</div>
          <div class="col-5">
            : {{ '_loyalty_type.' + notification.action.processType | translate }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
<button type="button" class="action-btn btn btn-warning mb-2 text-white w-100" (click)="NavToPromotion()">{{"_boom_coin" | translate }}</button>
