import { Component, Input, OnInit } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { CustomerService } from 'src/app/modules/customer/service/customer.service';
import { environment } from 'src/environments/environment';
import { NotificationModel } from '../../model/notification.model';
import { NotificationState } from '../../state/notification/notification.state';

@Component({
  selector: 'app-service-notification',
  templateUrl: './service-notification.component.html',
  styleUrls: ['./service-notification.component.scss']
})
export class ServiceNotificationComponent implements OnInit {

  @Input() notification: any;
  serviceRequest: any;

  notificationIcon = `${environment.assets}/notifications.svg`;
  loading: boolean;

  @Select(NotificationState.notifications)
  notifications$: Observable<NotificationModel<any>[]>;

  mapLocation = `${environment.assets}/map_location.png`;
  sameOtherService: any[];
  serviceLocationShow = true;

  constructor(
    private readonly customerService: CustomerService,
    private readonly customerModuleService: CustomerModuleService,
  ) { }

  ngOnInit() {
    this.loading = true;
    this.loadServiceData();
    if (this.notification.other) {
      this.sameOtherService = [
        ...new Map(this.notification.other.map((item) => [item.date, item])).values(),
      ];
    }
  }

  goServiceModule() {
    // TODO Servis detay sayfasına yönlendirilecek yada scroll özelliği verilecek
    this.customerModuleService.openServiceModule(
      this.getCustomerNumber(), this.notification.action.serviceNumber, false,
      {
        replaceUrl: true, params: { hasServiceNumber: this.notification.action.serviceNumber,
          source: 'service',
          sourceRoot: 'serviceNotification'
         }
      });
  }

  goServiceMap(service) {
    const serviceMapRedirect = true;
    this.customerModuleService.openServiceModule(
      this.getCustomerNumber(),
      this.notification.action.serviceNumber,
      serviceMapRedirect);
  }

  loadServiceData(){
    if (this.notification?.action?.serviceNumber && this.getCustomerNumber()) {
      this.customerService.serviceDetail(
        this.notification?.action?.serviceNumber, this.getCustomerNumber())
        .subscribe(
          (data) => {
            if (data) {
              this.serviceRequest = data;
            }
            this.loading = false;
          },
          (err) => {
            this.loading = false;
          }
        );
    }
  }

  protected getCustomerNumber() {
    return this.notification?.customerNumber
      || this.notification?.action?.customerNumber
      || this.notification?.action?.CustomerNumber
      || this.notification?.CustomerNumber;
  }

}
