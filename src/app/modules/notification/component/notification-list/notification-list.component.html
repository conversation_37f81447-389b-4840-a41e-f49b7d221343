<div>
  <div class="nlist">
    <app-tabs style="word-break: break-word;" (isDownEvent)="isDownEvent($event)">
      <app-tab *ngIf="showNotificationSettings" [isIcon]="false" [icon]="'icon-notification_settings'">
        <app-notification-settings></app-notification-settings>
      </app-tab>
      <app-tab [active]="true" [isIcon]="!(isBorusanUser)"
        [name]=" (currentHide && !(isBorusanUser) ? '_other' : '_notifications') | translate" (scrollEnd)="scrollEnd($event)">
        <ng-container *ngIf="(currentNotifications$ | async)?.length || (otherNotifications$ | async)?.length; else emptyList">
          <!-- other notifications -->
          <ng-container *ngIf="currentHide && !(isBorusanUser)">
            <div *ngIf="(otherNotifications$ | async)?.length else emptyOtherList">
              <div *ngFor="let group of otherGroupedNotifications" class="time-sep">
                <ng-container *ngIf="isTimeControl(group)">
                  <div>
                    <h6 *ngIf="group[1]?.length">{{ group[0] | translate }}</h6>
                  </div>
                  <div class="time-block pl-2">
                    <div *ngFor="let notification of group[1]">
                      <div class="notification d-flex justify-content-between" (click)="openDetail(notification)" appClickLog [section]="'NOTIFICATION'"
                        [subsection]="'OPEN_DETAIL_CLICK'" [data]="{ messageId: notification.messageId }">
                        <!-- ? Notification Icon -->
                        <div *ngIf="notification.actionType === notificationActionTypeEnum.PseCampaignPush" class="bell-icon d-flex justify-items-center" ngClass="bg-color-f" >
                          <img class="pse-icon" [src]="notificationLadyBirdIcon" />
                        </div>
                        <div *ngIf="notification.actionType !== notificationActionTypeEnum.PseCampaignPush" class="bell-icon d-flex justify-items-center" ngClass="{{getNotificationStyle(notification)?.bg}}">
                          <i class="icon" ngClass="{{getNotificationStyle(notification)?.icon}}"></i>
                        </div>
                        <div class="flex-grow-1 m-auto" [ngClass]="{'fw-bolder': !notification.isRead}">
                          <ng-container [ngSwitch]="notification.actionType">
                            <ng-container *ngSwitchCase="notificationActionTypeEnum.ServiceStatusChange">
                              {{ notification.body }}
                            </ng-container>
                            <ng-container *ngSwitchCase="notificationActionTypeEnum.Muneccim">
                              {{ notification.body }}
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                              {{ notification.title }}
                            </ng-container>
                          </ng-container>
                          <span class="px-1">
                            {{ notification.date | amTimeAgo }}
                          </span>
                          <div>
                            <small>
                              {{ getCustomerName(notification?.customerName || notification?.action?.customerName) }}
                            </small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
            <ng-template #emptyOtherList>
              <div class="d-flex flex-column mx-3 px-3 mb-1 align-middle mt-5 pt-5">
                <div class="text-center mb-2">
                  <img [src]="notificationIcon" />
                </div>
                <div class="h3 mb-5">
                  <div class="text-wrap text-center font-size-22px">
                    {{ "_other_notification_empty" | translate }}
                  </div>
                </div>
              </div>
            </ng-template>
          </ng-container>
          <!-- current notifications -->
          <ng-container *ngIf="!currentHide">
            <div *ngIf="(currentNotifications$ | async)?.length else emptyCurrentList">
              <div *ngFor="let group of currentGroupedNotifications" class="time-sep">
                <ng-container *ngIf="isTimeControl(group)">
                  <div>
                    <h6 *ngIf="group[1]?.length">{{ group[0] | translate }}</h6>
                  </div>
                  <div class="time-block pl-2">
                    <div *ngFor="let notification of group[1]">
                      <div class="notification d-flex justify-content-between" (click)="openDetail(notification)" appClickLog [section]="'NOTIFICATION'"
                        [subsection]="'OPEN_DETAIL_CLICK'" [data]="{ messageId: notification.messageId }">
                        <!-- ? Notification Icon -->
                        <div *ngIf="notification.actionType === notificationActionTypeEnum.PseCampaignPush" class="bell-icon d-flex justify-items-center" ngClass="bg-color-f" >
                          <img class="pse-icon" [src]="notificationLadyBirdIcon" />
                        </div>
                        <div *ngIf="notification.actionType !==notificationActionTypeEnum.PseCampaignPush" class="bell-icon d-flex justify-items-center" ngClass="{{getNotificationStyle(notification)?.bg}}">
                          <i class="icon" ngClass="{{getNotificationStyle(notification)?.icon}}"></i>
                        </div>
                        <div class="flex-grow-1 m-auto" [ngClass]="{'fw-bolder': !notification.isRead}">
                          <ng-container [ngSwitch]="notification.actionType">
                            <ng-container *ngSwitchCase="notificationActionTypeEnum.ServiceStatusChange">
                              {{ notification.body }}
                            </ng-container>
                            <ng-container *ngSwitchCase="notificationActionTypeEnum.Muneccim">
                              {{ notification.body }}
                            </ng-container>
                            <ng-container *ngSwitchDefault>
                              {{ notification.title }}
                            </ng-container>
                          </ng-container>
                          <span class="px-1">
                            {{ notification.date | amTimeAgo }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </ng-container>
              </div>
            </div>
            <ng-template #emptyCurrentList>
              <div class="d-flex flex-column mx-3 px-3 mb-1 align-middle mt-5 pt-5">
                <div class="text-center mb-2">
                  <img [src]="notificationIcon" />
                </div>
                <div class="h3 mb-5">
                  <div class="text-wrap text-center font-size-22px">
                    {{ "_current_notification_empty" | translate }}
                  </div>
                </div>
              </div>
            </ng-template>
          </ng-container>
        </ng-container>
        <ng-template #emptyList>
          <div *ngIf="!(loading$ | async)" class="d-flex flex-column mx-5 px-3 mb-1 align-middle mt-5 pt-5">
            <div class="text-center mb-2">
              <img [src]="notificationIcon" />
            </div>
            <div class="h3 mb-5">
              <div class="text-wrap text-center font-size-22px">
                {{ "_notification_list_empty" | translate }}
              </div>
            </div>
          </div>
        </ng-template>
      </app-tab>
    </app-tabs>
  </div>
</div>
