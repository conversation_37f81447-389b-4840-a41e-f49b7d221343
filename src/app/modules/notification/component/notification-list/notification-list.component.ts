import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { NotificationModel } from '../../model/notification.model';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import * as moment from 'moment';
import { Select, Store } from '@ngxs/store';
import { Router } from '@angular/router';
import { environment } from '../../../../../environments/environment';
import { NotificationState } from '../../state/notification/notification.state';
import {
  GetCurrentNotificationsAction,
  GetOtherNotificationsAction,
  NotificationDeleteAction,
  NotificationEventAction
} from '../../state/notification/notification.actions';
import { Observable, Subject } from 'rxjs';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { NotificationEventEnum } from '../../enum/notification-event.enum';
import { OpenLinkAction, PageOpenedAction } from '../../../../shared/state/common/common.actions';
import { IncomingMessageService } from '../../../../core/service/incoming-message.service';
import { IncomingMessageEnum } from '../../../../core/enum/incoming-message.enum';
import { take, takeUntil } from 'rxjs/operators';
import { ModalService } from '../../../../shared/service/modal.service';
import { ErrorModalComponent } from '../../../../shared/component/error-modal/error-modal.component';
import { TranslateService } from '@ngx-translate/core';
import { UserState } from '../../../customer/state/user/user.state';
import { CustomerRelationModel } from 'src/app/modules/customer/model/customer-relation.model';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';
import { PagingModel } from 'src/app/core/interfaces/http.response';
import { PagesEnum } from 'src/app/shared/enum/pages.enum';


@Component({
  selector: 'app-notification-list',
  templateUrl: './notification-list.component.html',
  styleUrls: ['./notification-list.component.scss'],
})
export class NotificationListComponent implements OnInit, OnDestroy {

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<CustomerRelationModel>;
  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;
  @Select(UserState.isBorusanUser)
  isBorusanUser$: Observable<boolean>;
  isBorusanUser: boolean;

  @Select(NotificationState.loading)
  loading$: Observable<boolean>;

  @Select(NotificationState.currentNotifications)
  currentNotifications$: Observable<NotificationModel<any>[]>;
  currentGroupedNotifications: [string, NotificationModel<any>[]][];
  @Select(NotificationState.currentNotificationsPaging)
  currentNotificationsPaging$: Observable<PagingModel>;
  currentNotificationsPaging: PagingModel;
  currentNotificationsPage = 1;

  @Select(NotificationState.otherNotifications)
  otherNotifications$: Observable<NotificationModel<any>[]>;
  otherGroupedNotifications: [string, NotificationModel<any>[]][];
  @Select(NotificationState.otherNotificationsPaging)
  otherNotificationsPaging$: Observable<PagingModel>;
  otherNotificationsPaging: PagingModel;
  otherNotificationsPage = 1;

  notificationActionTypeEnum = NotificationActionTypeEnum;
  notificationIcon = `${environment.assets}/notifications.svg`;
  notificationLadyBirdIcon = `${environment.assets}/pse-boom360.svg`;
  protected subscriptions$: Subject<boolean> = new Subject();

  customerNumber: string;
  customersList: CustomerRelationModel[];
  currentHide: boolean;
  serviceNotificationsSeperated: any[];

  showNotificationSettings = false;
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly frameMessageService: FrameMessageService,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly translateService: TranslateService,
    private readonly modalService: ModalService
  ) { }

  ngOnInit(): void {
    this.loadCustomer();
    this.loadSystemFeatures();
    this.getNotifications();

    this.store.dispatch(new PageOpenedAction(PagesEnum.notificationListPage));
    // refresh notification list when mobile frame closed
    this.incomingMessageService.observable(IncomingMessageEnum.webviewReopened)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.loadCurrentNotifications();
      });
    this.isBorusanUser$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => this.isBorusanUser = x);
  }

  scrollEnd(e) {
    if (e && this.currentHide) {
      // other
      console.log('other');
      if (this.otherNotificationsPaging.pageNumber * this.otherNotificationsPaging.pageSize < this.otherNotificationsPaging.totalCount) {
        this.otherNotificationsPage++;
        this.reloadOtherNotifications();
      }
    } else if (e && !this.currentHide) {
      // current
      console.log('current');
      if (this.currentNotificationsPaging.pageNumber * this.currentNotificationsPaging.pageSize
        < this.currentNotificationsPaging.totalCount) {
        this.currentNotificationsPage++;
        this.reloadCurrentNotifications();
      }
    }
  }

  openDetail(notification: NotificationModel<any>) {
    switch (notification.actionType) {
      case NotificationActionTypeEnum.Rate_Us:
        return this.rateUs(notification);
      case NotificationActionTypeEnum.Feedback:
        return this.feedbackNotification(notification);
      // case NotificationActionTypeEnum.Muneccim:
      //   return this.isServiceIndication(notification);
      case NotificationActionTypeEnum.Link:
        return this.linkNotification(notification);
      case NotificationActionTypeEnum.InsiderPush:
        return this.insiderNotification(notification);
      case NotificationActionTypeEnum.ServiceSurvey:
        const expDate = moment.utc(notification.action.surveyLinkExpireDate);

        if (notification.action.surveyLinkExpireDate && expDate.isBefore(moment())) {
          setTimeout(i => {
            this.modalService.errorModal({
              message: this.translateService.instant('_survey_expired'),
              button: this.translateService.instant('_ok'),
              buttonClick: (modal: ErrorModalComponent) => {
                modal.close();
              }
            }).onResult().subscribe(() => {
              this.reloadNotifications();
            });
            this.router.navigate(['notifications'], { replaceUrl: true });
          });

          this.store.dispatch(new NotificationDeleteAction(notification.messageId));

          return;
        }
        break;

    }

    this.router.navigate(['notifications', notification.messageId], { state: notification });
  }

  getNotifications() {
    this.currentNotifications$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(nf => {
        if (!nf) { return; }
        this.currentGroupedNotifications = this.notificationDataGrouper(nf);
      });
    this.otherNotifications$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(nf => {
        if (!nf) { return; }
        this.otherGroupedNotifications = this.notificationDataGrouper(nf, true);
      });
    this.currentNotificationsPaging$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(p => this.currentNotificationsPaging = p);
    this.otherNotificationsPaging$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(p => this.otherNotificationsPaging = p);
  }

  notificationDataGrouper(notifications, isOther?: boolean) {
    const groupedNotifications = {
      _last_day: [],
      _new: [],
      _this_month: [],
      _older_than: [],
    };
    const serviceNotificationsNumbers: any = {};
    const nf = notifications;
    nf.map(item => {
      item = { ...item };
      const now = moment();
      const nDate = moment(item.date);
      const diff = now.diff(nDate, 'days');
      // ? ServiceStatusChange
      if (item.actionType === NotificationActionTypeEnum.ServiceStatusChange ||
        item.actionType === NotificationActionTypeEnum.ServiceDocumentUploaded) {
        if (item?.action?.serviceNumber in serviceNotificationsNumbers) {
          return serviceNotificationsNumbers[item.action.serviceNumber].other.push(item);
        }
        (item as any).other = (item as any).other || [];
        serviceNotificationsNumbers[item.action.serviceNumber] = item;
      }
      if (diff < 1 && isOther) {
        return groupedNotifications._last_day.push(item);
      } else if (diff < 7) {
        return groupedNotifications._new.push(item);
      } else if (diff < 20) {
        return groupedNotifications._this_month.push(item);
      }
      return groupedNotifications._older_than.push(item);
    });
    return Object.entries(groupedNotifications);
  }

  loadCustomer() {
    this.customers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customers => {
        this.customersList = customers;
        this.currentCustomer$
          .pipe(take(1))
          .subscribe(currentCustomer => {
            if (currentCustomer) {
              this.customerNumber = currentCustomer.customer?.customerNumber;
              this.loadCurrentNotifications();
            }
          });
      });
  }

  loadSystemFeatures() {
    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        if (features) {
          const isProductNotificationSettings = environment.envName === 'production)' ? false : true;
          this.showNotificationSettings = systemFeature('notification_settings', features, isProductNotificationSettings);
        }
      });
  }

  protected loadCurrentNotifications() {
    // return this.store.dispatch(new GetNotificationsAction());
    this.reloadCurrentNotifications();
  }

  protected loadOtherNotifications() {
    this.reloadOtherNotifications();
  }

  protected reloadNotifications() {
    this.reloadCurrentNotifications();
    this.reloadOtherNotifications();
  }

  protected reloadCurrentNotifications() {
    this.store.dispatch(new GetCurrentNotificationsAction(13, this.currentNotificationsPage));
  }

  protected reloadOtherNotifications() {
    this.store.dispatch(new GetOtherNotificationsAction(13, this.otherNotificationsPage));
  }

  private feedbackNotification(notification: NotificationModel<NotificationActionTypeEnum.Feedback>) {
    // send read event
    this.store.dispatch(new NotificationEventAction(
      NotificationEventEnum.Clicked,
      notification.messageId,
    ));
    // reload notifications
    this.reloadNotifications();

    // send to APP
    return this.frameMessageService.sendMessage(FrameMessageEnum.feedbackNotification, notification);
  }

  protected insiderNotification(notification: NotificationModel<NotificationActionTypeEnum.InsiderPush>) {
    return this.linkNotification(notification, notification.action.deeplink);
  }

  protected linkNotification(notification: NotificationModel<any>, url: string = null) {
    // send read event
    this.store.dispatch(new NotificationEventAction(
      NotificationEventEnum.Clicked,
      notification.messageId,
    ));
    // open url
    this.store.dispatch(new OpenLinkAction({
      url: url || notification.action.actionUrl,
    }));
    // this.store.dispatch(new OpenModuleAction({
    //   url: url || notification.action.actionUrl,
    //   title: notification.title
    // }));

    // reload notifications
    return this.reloadNotifications();
  }


  isTimeControl(group: any[]) {
    return !!group[1]?.length;
  }

  private rateUs(notification: NotificationModel<any>) {
    // send read event
    this.store.dispatch(new NotificationEventAction(
      NotificationEventEnum.Clicked,
      notification.messageId,
    ));
    // delete notification
    this.store.dispatch(new NotificationDeleteAction(notification.messageId))
      .subscribe(res => {
        // reload notifications
        this.reloadNotifications();
      });
    // send to APP
    return this.frameMessageService.sendMessage(FrameMessageEnum.rate_us, notification);
  }

  isDownEvent(event: any) {
    this.currentHide = event;
    if (this.currentHide) {
      this.reloadOtherNotifications();
    }
  }

  getNotificationStyle(notification: NotificationModel<any>): { bg: string; icon: string; } {
    const e = this.notificationActionTypeEnum;
    const css = {
      [e.FinancialInfo]: { bg: 'bg-color-w', icon: 'icon-priority-high' },
      [e.Rate_Us]: { bg: 'bg-color-f', icon: 'icon-rate' },
      [e.FinancialsCurrentDebt]: { bg: 'bg-color-w', icon: 'icon-priority-high' },
      [e.FinancialsForwardTermDebt]: { bg: 'bg-color-d', icon: 'icon-priority-high' },
      [e.QuotationExpire]: { bg: 'bg-color-d', icon: 'icon-priority-high' },
      [e.QuotationUpdated]: { bg: 'bg-color-w', icon: 'icon-priority-high' },
      [e.NewQuotation]: { bg: 'bg-color-w', icon: 'icon-priority-high' },
      [e.Muneccim]: { bg: 'bg-color-w', icon: 'icon-repair' },
      [e.SoundDiagnostic]: { bg: 'bg-color-f', icon: 'icon-sound' },
      default: { bg: 'bg-color-s', icon: 'icon-notification' }
    };
    return css[notification.actionType] || css.default;
  }

  getCustomerName(customerName: any) {
    return customerName?.length > 35
      ? customerName.slice(0, 32) + '...'
      : customerName;
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
