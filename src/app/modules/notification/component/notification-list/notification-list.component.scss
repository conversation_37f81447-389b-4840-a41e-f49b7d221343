.nlist {
  // max-height: calc(100vh - 65px);
  // overflow-y: scroll;
  // overflow-x: hidden;
  padding-left: 20px;
  padding-right: 18px;

  .time-sep {
    color: #2C2C2C;

    .time-block {
      color: #505050;
      font-size: 13px;
      line-height: 1.4em;
      padding-top: 17px;
      padding-bottom: 12px;

      span {
        color: #8e8e8e;
      }
      small {
        color: #8e8e8e !important;
      }

      .notification {
        padding-bottom: 25px;

        .pse-icon{
          width: 70%;
          margin-left: 5px;
        }
      }

    }
  }

}


.detail-btn {
  box-sizing: border-box;
  border-radius: 4px;
}

.read-btn {
  background: #FFFFFF;
  border: 1px solid #BABABA;
}

.bell-icon {
  min-width: 35px;
  width: 35px;
  height: 35px;
  border-radius: 20px;
  margin: auto;
  margin-left: -8px;
  margin-right: 10px;

  i {
    margin: auto;
    font-size: 20px;
  }
}

.fw-bolder{
  font-weight: bolder;
}

.circular-data {
  margin: 2.5px;
  width: 40px;
  height: 40px;
  background: #d7e5ea;
  border: 2px solid #d7e5ea;
  border-radius: 50%;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  color: #8c8c8c;
  font-weight: normal;
  font-size: 20px;
  line-height: 30px;
  margin-right: 19px;
}
.align-middle {
  align-items: center;
}
.bg-color-w{
  background-color: #FFE297;
  color: #FE9800;
  border: 1px solid #FE9800;
  border-radius: 50%;
}
.bg-color-d{
  background-color: #FFACAC;
  color: #E84B4B;
  border: 1px solid #E84B4B;
  border-radius: 50%;
}
.bg-color-s{
  background-color: #ECECEC;
  color: #B0B0B0;
  border: 1px solid #B0B0B0;
  border-radius: 50%;
}
.bg-color-f{
  background-color: transparent;
  color: #FFA300;
  border: 1px solid #FFA300;
  border-radius: 50%;
}

::ng-deep .nlist ngb-accordion .card {
  border-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

::ng-deep .nlist ngb-accordion .card-body {
  padding: 0.7em;
}

::ng-deep .nlist ngb-accordion .card-header {
  border-radius: 0;
  padding: 0;
  background-color: #f9f9f9;
  border: none;

  .btn {
    width: 100%;
    float: left;
    text-align: left !important;
    box-shadow: none;
    border-radius: 0;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);

  }
  .btn-link:hover,
  .btn-link:focus {
    box-shadow: none;
    text-decoration: none;
  }


  button:not(.collapsed) {
    font-weight: 600;

    .icon-chevron-down {
      transform: rotate(-180deg);
    }
  }

  .icon-chevron-down {
    line-height: 1em;
    height: 1em;
    transition: all 0.4s ease;
  }

}
