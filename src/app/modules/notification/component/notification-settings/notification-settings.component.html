<div class="mb-3" *ngIf="notificationSettings">
  <div class="mt-3">
    <h6>{{ '_notifications_preferences' | translate }}</h6>
  </div>
  <ngb-accordion [closeOthers]="true">
    <ngb-panel id="id-{{index}}" *ngFor="let settings of notificationSettings; let index = index">
      <ng-template *ngIf="!settings?.isHidden" ngbPanelTitle>
        <div class="d-flex justify-content-between">
          <div class="d-flex">{{settings?.groupName}}</div>
          <div class="d-flex"><i class="text-decoration-none icon icon-chevron-down"></i></div>
        </div>
      </ng-template>
      <ng-template ngbPanelContent>
        <ng-container *ngFor="let item of settings?.notificationTypeList; let i = index">
          <form name="name-{{item?.notificationType}}">
            <div *ngIf="!item?.isHidden" class="card mb-1 px-3 py-1 border-0">
              <div class="w-100 d-flex flex-row flex-nowrap justify-content-between">
                <div class="text-wrap">
                  {{ item.notificationTypeName }}
                </div>
                <div>
                  <div *ngIf="(saveItem?.parentIndex === index && saveItem?.childIndex === i) else showInput"
                    style="padding-right: 19px;align-self: end;">
                    <div class="border text-lg-right rounded-circle d-flex justify-content-center align-items-center spinner">
                      <i class="icon icon-spinner8 border-light text-warning"></i>
                    </div>
                  </div>
                  <ng-template #showInput>
                    <input [id]="'isUserPushEnabled' + i" name="isUserPushEnabled" class="form-check-input" type="checkbox"
                      [checked]="item?.isUserPushEnabled" (click)="onItemClick($event, item, index, i)"
                      [disabled]="!item?.isSystemPushEnabled || saveItem">
                    <label [for]="'isUserPushEnabled' + i"></label>
                  </ng-template>
                </div>
              </div>
              <div class="d-none">
                <ul class="list-group">
                  <li class="list-group-item">
                    <input [id]="'isUserSmsEnabled' + i" name="isUserSmsEnabled" class="form-check-input" type="checkbox"
                      [checked]="item?.isUserSmsEnabled" (click)="onItemClick($event, item, index, i)"
                      [disabled]="!item?.isSystemSmsEnabled">
                    <label [for]="'isUserSmsEnabled' + i">{{ '_sms' | translate }}</label>
                  </li>
                  <li class="list-group-item">
                    <input [id]="'isUserEmailEnabled' + i" name="isUserEmailEnabled" class="form-check-input" type="checkbox"
                      [checked]="item?.isUserEmailEnabled" (click)="onItemClick($event, item, index, i)"
                      [disabled]="!item?.isSystemEmailEnabled">
                    <label [for]="'isUserEmailEnabled' + i">{{ '_email' | translate }}</label>
                  </li>
                </ul>
              </div>
            </div>
          </form>
        </ng-container>
      </ng-template>
    </ngb-panel>
  </ngb-accordion>
</div>
