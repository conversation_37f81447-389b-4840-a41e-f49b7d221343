import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { NotificationSettingsModel, NotificationSettingsTypeModel } from '../../model/notification.model';
import { NotificationSettingsGetAction, NotificationSettingsSaveAction } from '../../state/notification/notification.actions';
import { NotificationState } from '../../state/notification/notification.state';

@Component({
  selector: 'app-notification-settings',
  templateUrl: './notification-settings.component.html',
  styleUrls: ['./notification-settings.component.scss']
})
export class NotificationSettingsComponent implements OnInit, OnDestroy {

  notificationSettings: NotificationSettingsModel[];
  @Select(NotificationState.notificationSettings)
  notificationSettings$: Observable<NotificationSettingsModel[]>;

  @Select(NotificationState.saveLoading)
  saveLoading$: Observable<boolean>;
  saveItem: {
    parentIndex: number,
    childIndex: number,
  };

  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly store: Store,
  ) { }

  ngOnInit() {

    // ? Notification Settings
    this.store.dispatch(new NotificationSettingsGetAction());
    this.notificationSettings$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(settings => this.notificationSettings = settings);
  }

  onItemClick(event: any, item: NotificationSettingsTypeModel, parentIndex: number, childIndex: number) {
    this.saveItem = { parentIndex, childIndex };
    const form = document.forms['name-' + item.notificationType];
    const changeData = [];
    changeData.push(
      {
        isUserSmsEnabled: form.elements['isUserSmsEnabled'].checked,
        isUserEmailEnabled: form.elements['isUserEmailEnabled'].checked,
        isUserPushEnabled: form.elements['isUserPushEnabled'].checked,
        notificationType: item.notificationType,
      }
    );
    this.store.dispatch(new NotificationSettingsSaveAction(changeData));
    setTimeout(() => {
      this.store.dispatch(new NotificationSettingsGetAction());
      this.saveLoading$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(load => {
          if (!load) {
            this.saveItem = null;
          }
        });
    }, 300);
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
