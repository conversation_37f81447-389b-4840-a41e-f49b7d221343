<div class="muneccim-notification">
  <div class="ndet-header mb-4">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ "_muneccim_campaign_notification" | translate }}</h4>
      </div>
      <div class=""></div>
    </div>

    <div *ngIf="equipmentRevisionCampaign else nullCampaign" class="card custom-card-discount my-4">
      <div class="card-header">{{ equipmentRevisionCampaign?.title | translate }}</div>
      <div class="card-body">
        <div class="d-flex flex-row justify-content-between">
          <div class="d-flex flex-column flex-grow-1">
            <div class="d-flex flex-column flex-grow-1">
              <div class="font-size-14px text-center">
                {{ descriptionLink?.before }}
                <ng-container *ngIf="descriptionLink?.isTextBefore else isTextAfter">
                  {{descriptionText}}
                  <a *ngIf="descriptionLink?.url" (click)="descriptionGoLink()">{{descriptionLink.text}}</a>
                </ng-container>
                <ng-template #isTextAfter>
                  <a *ngIf="descriptionLink?.url" (click)="descriptionGoLink()">{{descriptionLink.text}}</a>
                  {{descriptionText}}
                </ng-template>
                {{ descriptionLink?.after }}
              </div>
            </div>
            <div *ngIf="equipmentRevisionCampaign?.type === '_discount'" class="text-center text-success mb-2"
                 style="font-size: 25px; font-weight: bolder; line-height: 1;">
              {{ equipmentRevisionCampaign?.amount }} {{ equipmentRevisionCampaign?.currency }}<br>
              <div class="font-size-16px text-center">{{ "_discount" | translate }}</div>
            </div>
          </div>
          <div [ngSwitch]="equipmentRevisionCampaign?.type" class="d-flex justify-content-center w-40px text-success">
            <i *ngSwitchCase="'_campaign'" class="icon icon-campaign fs-40px"></i>
            <i *ngSwitchCase="'_opportunity'" class="icon icon-coupon fs-40px"></i>
            <div *ngSwitchCase="'_percent_discount'"
                 [style.font-size]="equipmentRevisionCampaign?.amount === 100 ? '14px': 'inherit'"
                 class="circle-discount-40 align-self-center text-center" style="line-height: 0.7;">
              %{{ equipmentRevisionCampaign?.amount }}
            </div>
            <i *ngSwitchCase="'_discount'"
               class="icon {{ equipmentRevisionCampaign?.currency | currencyIcon }} fs-40px"></i>
            <i *ngSwitchDefault class="icon icon-campaign fs-40px"></i>
          </div>
        </div>
      </div>
    </div>
    <ng-template #nullCampaign>
      <app-empty-content *ngIf="!loading" [iconName]="'campaign'" [message]="'_campaign_is_empty'"
                         [hasBackButton]="true">
        <div *ngIf="equipmentModule" class="btn btn-warning btn-block text-white btn-sm" (click)="goEquipmentDetailMuneccim()">
          {{ '_to_equipment_list' | translate }}
        </div>
      </app-empty-content>
    </ng-template>
    <ng-container *ngIf="equipmentRevisionCampaign">
      <div *ngIf="equipmentNumbers && equipmentModule" class="card custom-card-discount mb-4">
        <div class="card-header">
          {{ '_equipments_management' | translate }}
        </div>
        <div class="card-body p-0 bg-white">
          <div class="d-flex flex-column justify-content-center align-items-center">
            <ng-container *ngFor="let e of equipmentNumbers | slice:0:equipmentLimit; let i = index; last as last">
              <div class="row p-2 w-100"
                   [ngClass]="{'border-bottom' : !last}">
                <div class="equipment w-100 d-flex" [class.hidden]="equipmentsDetail[i]">
                  <div class="align-self-center equipment-image mr-3">
                  <app-image-preview [title]="equipmentsDetail[i]?.brand + ' ' + equipmentsDetail[i]?.model"
                                     [model]="equipmentsDetail[i]?.model"
                                     [imageSize]="ImageSizeEnum.mobilethumbnailsize"
                                     [id]="equipmentsDetail[i]?.productHierarchy"
                                     [maxHeight]="'50px'"></app-image-preview>
                </div>
                <div class="flex-fill">
                  <div
                    *ngIf="equipmentLoading && !equipmentsDetail[i]"
                    class="loading-in-div text-lg-right rounded-circle d-flex justify-content-center align-items-center ml-2"
                    [class.spinner]="equipmentLoading && !equipmentsDetail[i]">
                    <i class="icon icon-spinner8 border-light" *ngIf="equipmentLoading && !equipmentsDetail[i]"></i>
                  </div>
                  <div class="font-weight-bold text-secondary">
                    {{ equipmentsDetail[i]?.brand }} {{ equipmentsDetail[i]?.model }}
                  </div>
                  <div class="font-size-13px text-secondary mb-2">
                    {{ equipmentsDetail[i]?.serialNumber | serialFormat}}
                    <div class="d-inline-block ml-2" *ngIf="equipmentsDetail[i]?.equipmentRevisionCampaign">
                      <div [ngSwitch]="equipmentsDetail[i]?.equipmentRevisionCampaign?.type"
                           class="d-flex justify-content-center text-success font-size-16px fw-bolder">
                        <i *ngSwitchCase="'_campaign'" class="icon icon-campaign"></i>
                        <i *ngSwitchCase="'_opportunity'" class="icon icon-coupon"></i>
                        <i *ngSwitchCase="'_discount'"
                           class="icon {{ equipmentsDetail[i]?.equipmentRevisionCampaign?.currency | currencyIcon }}"></i>
                        <span *ngSwitchCase="'_percent_discount'">%</span>
                        <i *ngSwitchDefault class="icon icon-campaign"></i>
                      </div>
                    </div>
                    <div *ngIf="
                              equipmentsDetail[i]?.revisionIndicator === 'yellow' ||
                              equipmentsDetail[i]?.revisionIndicator === 'orange' ||
                              equipmentsDetail[i]?.revisionIndicator === 'red'
                            " class="repair-icon d-inline-block ml-2" [ngClass]="{
                              'text-yellow': equipmentsDetail[i]?.revisionIndicator === 'yellow',
                              'text-warning': equipmentsDetail[i]?.revisionIndicator === 'orange',
                              'text-danger': equipmentsDetail[i]?.revisionIndicator === 'red'
                            }">
                      <div class="d-flex justify-content-center">
                        <i class="icon icon-repair"></i>
                      </div>
                    </div>
                    <div *ngIf="equipmentsDetail[i]?.isProductLink" class="product-link d-inline-block ml-2">
                      <div class="d-flex justify-content-center" [ngbTooltip]="'_connected_equipment' | translate"
                           tooltipClass="wifi-icon"
                           placement="left">
                        <i class="icon icon-wifi" [style.color]="equipmentsDetail[i]?.productLinkColor"></i>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="btn-group float-right" ngbDropdown placement="bottom-right bottom-left bottom" role="group"
                     aria-label="Button group with nested dropdown">
                  <i class="icon icon-dot3 h-min" ngbDropdownToggle></i>
                  <div class="equipment-menu mt-0 dropdown-menu" ngbDropdownMenu>
                    <button *ngIf="equipmentsDetail[i]" class="pl-2" ngbDropdownItem (click)="goEquipmentDetail(equipmentsDetail[i])">
                      <i class="icon icon-equipment pr-2"></i>
                      {{ "_go_equipment_detail" | translate }}
                    </button>
                    <button *ngIf="equipmentsDetail[i]" class="pl-2" ngbDropdownItem [disabled]="requestServiceDisabled"
                            (click)="onClickRequestService(equipmentsDetail[i])">
                      <i class="icon icon-edit pr-2"></i>
                      {{ "_create_service" | translate }}
                    </button>
                    <button *ngIf="equipmentsDetail[i]" class="pl-2" ngbDropdownItem (click)="startExpertise()">
                      <i class="icon icon-search pr-2"></i>
                      {{ "_expertise_form" | translate }}
                    </button>
                  </div>
                </div>
                </div>
              </div>
            </ng-container>
          </div>
        </div>
      </div>
      <div *ngIf="equipmentModule" class="btn btn-warning btn-block text-white btn-sm" (click)="goEquipmentDetailMuneccim()">
        {{ '_to_equipment_list' | translate }}
      </div>
    </ng-container>
  </div>

</div>

<app-success-modal [inModal]="true" [(status)]="requestServiceCreated" [message]="'_general_successfully_send_form'">
</app-success-modal>

<app-loader [show]="loading"></app-loader>
