import {Component, Input, OnInit} from '@angular/core';
import {EquipmentModel, EquipmentRevisionCampaignModel} from '../../../customer/model/equipment.model';
import {Select, Store} from '@ngxs/store';
import {LogService} from '../../../../shared/service/log.service';
import {FormService} from '../../../customer/service/form.service';
import {Router} from '@angular/router';
import {FrameMessageEnum} from '../../../../core/enum/frame-message.enum';
import {FrameMessageService} from '../../../../core/service/frame-message.service';
import {LoginState} from '../../../authentication/state/login/login.state';
import {CustomerModuleService} from '../../../customer/service/customer-module.service';
import {CustomerService} from '../../../customer/service/customer.service';
import {ImageSizeEnum} from '../../../../shared/enum/image-size.enum';
import {UserState} from '../../../customer/state/user/user.state';
import {ModuleCodeEnum} from '../../../../shared/enum/module-code.enum';
import {Observable, Subject} from 'rxjs';
import {CustomerRelationModel} from '../../../customer/model/customer-relation.model';
import {takeUntil} from 'rxjs/operators';
import {TranslateService} from '@ngx-translate/core';

@Component({
  selector: 'app-muneccim-notification',
  templateUrl: './muneccim-notification.component.html',
  styleUrls: ['./muneccim-notification.component.scss']
})
export class MuneccimNotificationComponent implements OnInit {
  @Input() notification: any;
  equipmentRevisionCampaign: EquipmentRevisionCampaignModel = null;

  equipmentNumbers: string[];
  equipmentsDetail: EquipmentModel[] = [];
  // showContact: boolean;
  descriptionLink: any;

  equipmentLoading = true;
  loading = true;
  ImageSizeEnum = ImageSizeEnum;
  requestServiceDisabled: boolean;
  requestServiceCreated: boolean;

  equipmentLimit = 5;
  equipmentModule = false;

  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;
  protected subscriptions$: Subject<boolean> = new Subject();
  descriptionText: string;
  customersList: CustomerRelationModel[];

  constructor(
    private readonly frameService: FrameMessageService,
    private readonly logger: LogService,
    private readonly formService: FormService,
    private readonly store: Store,
    private readonly customerModuleService: CustomerModuleService,
    private readonly router: Router,
    private readonly customerService: CustomerService,
    private readonly translateService: TranslateService,
  ) { }

  ngOnInit(): void {
    const campaignId = this.notification.action?.campaignId;

    this.getCampaignDetail(campaignId);
    this.customers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((c) => {
        if (c) {
          this.customersList = c;
          const customer = this.customerModuleService.findCustomerWithModule(this.getCustomerNumber(), ModuleCodeEnum.Equipment);
          this.equipmentModule = !!customer;
          if(customer) {
            this.getEquipmentsDetails(this.getCustomer(this.getCustomerNumber()));
          }
        }
      });
  }

  getCustomer(customerNumber: string) {
    if (!customerNumber) {
      return null;
    }
    return this.customersList.find(customer => customer.customer?.customerNumber === customerNumber);
  }

  getEquipmentsDetails(customer) {
    if (!customer) {
      return;
    }
    const header = {
      CustomerId: customer?.customer?.id,
      HeaderCompanies: customer?.modules.find((item) => item.code === ModuleCodeEnum.Equipment)?.headerCompanies
    };
    const equipments = this.notification.action?.equipmentNumber;
    this.equipmentNumbers = equipments?.includes(';') ? equipments?.split(';') : equipments?.split(',');
    this.equipmentNumbers?.map((e, i) => {
      if (i > this.equipmentLimit) {
        return;
      }
      this.customerService.equipmentDetail(e, header)
        .subscribe(eDetails => {
            if (eDetails) {
              this.equipmentsDetail.push(eDetails);
              if (this.equipmentNumbers?.length === this.equipmentsDetail?.length) {
                this.equipmentLoading = false;
              }
            }
          },
          () => {
            this.equipmentLoading = false;
          });
    });
  }

  getCampaignDetail(campaignId: string) {
    this.customerService.equipmentRevisionCampaign(campaignId)
      .subscribe(campaign => {
          if (campaign) {
            this.equipmentRevisionCampaign = campaign;
            this.loading = false;
            this.setDescription();
          }
        },
        () => {
          this.loading = false;
        });
  }

  goEquipmentDetail(equipment) {
    this.customerModuleService.openEquipmentModule(this.getCustomerNumber(), equipment?.equipmentNumber);
  }

  goEquipmentDetailMuneccim() {
    this.customerModuleService.openEquipmentModule(this.getCustomerNumber(), null,
      {
        replaceUrl: true, params: {
          isServiceIndicator: 1,
          source: 'Muneccim',
          sourceRoot: 'MuneccimNotification'
        }
      });
  }

  onClickRequestService(equipment) {
    this.createServiceRequest(equipment);
  }

  getCustomerNumber() {
    return this.notification?.customerNumber
      || this.notification?.CustomerNumber
      || this.notification?.action?.customerNumber
      || this.notification?.action?.CustomerNumber;
  }

  createServiceRequest(equipment) {
    // const user = this.store.selectSnapshot(LoginState.user);
    const user = this.store.selectSnapshot(UserState.basics);
    const customer = this.store.selectSnapshot(LoginState.customer);
    const company = this.store.selectSnapshot(LoginState.company);

    const data = {
      Name: user?.firstName,
      Surname: user?.lastName,
      Email: user?.email.toLowerCase(),
      CompanyPhone: user?.mobile,
      CompanyName: customer?.name,
      CompanyId: company?.id,

      EquipmentSerialNumber: equipment?.serialNumber,
      ServiceCategory: 'Maintenance', // ServiceCategoryEnum.Maintenance,
      IsEquipmentWorking: true,
      CountryCode: company?.countryCode, // equipment.CountryCode,
      EquipmentCity: equipment?.city,
      EquipmentTown: equipment?.region,
      // EquipmentDistrict: equipment?.district,
      EquipmentRevisionCampaignTitle: equipment?.equipmentRevisionCampaign?.title,
      EquipmentRevisionCampaignDescription: equipment?.equipmentRevisionCampaign?.description,
      EquipmentRevisionCampaignAmount: equipment?.equipmentRevisionCampaign?.amount,
      EquipmentRevisionCampaignCurrency: equipment?.equipmentRevisionCampaign?.currency,
      EquipmentRevisionCampaignType: equipment?.equipmentRevisionCampaign?.type,
      EquipmentRevisionCampaignId: equipment?.equipmentRevisionCampaign?.id,
      RevisionIndicator: equipment?.revisionIndicator,
      Description: 'Muneccim Notification',
      EquipmentType: equipment?.EquipmentType,
      AttachmentIdList: [],
      Source: 'Notification',
      SourceRoot: 'Notification'
    } as any;
    // if (value.EquipmentType) {
    //   data.EquipmentType = value.EquipmentType;
    // }
    this.requestServiceDisabled = true;
    this.formService.sendRequestService(data).subscribe(
      () => {
        this.requestServiceCreated = true;
      },
      () => {
      }
    );
  }

  startExpertise() {
    this.customerModuleService.openExpertiseForm();
  }

  descriptionGoLink() {
    const url = this.descriptionLink?.url;
    if (!url) {
      return;
    }
    if (this.descriptionLink.open) {
      this.frameService.sendMessage(FrameMessageEnum.openStore, {
        url
      });
    } else {
      const title = this.descriptionLink.title || this.translateService.instant('_campaign');
      this.frameService.sendMessage(FrameMessageEnum.openModule, {
        url, title
      });
    }
  }

  setDescription() {
    const desc = this.equipmentRevisionCampaign?.description;
    if (desc.split('<u>').length > 0 && desc.search('<u>') !== -1) {
      this.descriptionText = this.equipmentRevisionCampaign?.description;

      this.descriptionText = this.descriptionText
        .slice(this.descriptionText.indexOf('<u>') + 3)
        .split('</u>')[0]
        ?.replace('<u>', '')
        ?.replace('</u>', '');

      this.descriptionLink = {
        text: this.getTag2Text(this.descriptionText, '<txt>'),
        url: this.getTag2Text(this.descriptionText, '<a>'),
        open: this.getTag2Text(this.descriptionText, '<oe>') === 'true',
        title: this.getTag2Text(this.descriptionText, '<t>'),
        before: desc.substring(0, desc.indexOf('<u>')),
        after: desc.slice(desc.indexOf('</u>') + 4),
        isTextBefore: desc.indexOf(this.descriptionText) < desc.indexOf('<txt>'),
      };
    } else {
      this.descriptionText = desc;
    }
  }

  getTag2Text(text, tag) {
    if (text.indexOf(tag) !== -1) {
      const tagStart = tag;
      const tagEnd = text.indexOf(tag.replace('<', '</')) > 0
        ? tag.replace('<', '</')
        : tag.replace('<', '</ ');
      this.descriptionText = text.replace(
        tagStart +
        text.slice(text.indexOf(tagStart) + tagStart.length, text.indexOf(tagEnd)) +
        tagEnd, '');
      return text.slice(text.indexOf(tagStart) + tagStart.length, text.indexOf(tagEnd));
    }
    return null;
  }

}
