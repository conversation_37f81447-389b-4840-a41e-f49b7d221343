.muneccim-notification {
  .ndet {
    &-header {
      h4 {
        color: #2C2C2C;
        font-weight: 700;
      }
    }

    &-title {
      font-weight: 500;
      font-size: 16px;
    }

    &-content {
      font-family: 'Poppins', sans-serif;
      font-weight: 400;
      font-size: 14px;
      color: #505050;
      line-height: 1.8em;
    }

  }

  .custom-card-discount {
    width: 100%;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05),
    0 0 15px rgba(0, 0, 0, 0.05);
    border-radius: 6px;
    border: 1px solid #dee2e6;
  }

  .circle-discount-40 {
    border: 2px solid #5E9731;
    color: #5E9731;
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bolder;
    font-size: 17px;
  }

  .w-40px {
    width: 40px;
    height: 40px;
    min-width: 40px;
    min-height: 40px;
  }

  .fs-40px {
    font-size: 40px;
  }

  .edit-icon-white::before {
    color: #fff;
  }

  .card > .card-header {
    border-top-right-radius: 6px;
    border-top-left-radius: 6px;
  }

  .loading-in-div {
    height: 100%;
    width: 100%;
    justify-self: center;
    align-self: center;
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 2px;
  }
  .h-min {
    height: min-content;
  }
  ::ng-deep .dropdown-toggle:after {
    display: none;
  }

  .equipment-image{
    width: 86px;
    height: 50px;
  }
}

ngb-accordion {
  width: 100%;
}

ngb-accordion ::ng-deep .card {
  border-radius: 0;
  border: 1px solid rgba(0, 0, 0, 0.125);
  border-left: none;
  border-right: none;
  background-color: transparent;
}

ngb-accordion ::ng-deep .card:first-child {
  border: none;
}

ngb-accordion ::ng-deep .card:last-child {
  border-bottom: none;
  border-bottom-right-radius: 6px;
  border-bottom-left-radius: 6px;
}

ngb-accordion .card-body {
  padding: 0.7em;
}

ngb-accordion ::ng-deep .card-header {
  border-radius: 0;
  padding: 0;
  background-color: #f9f9f9;
  border: none;

  .btn {
    width: 100%;
    float: left;
    text-align: left !important;
    box-shadow: none;
    border-radius: 0;
    //border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);

  }

  .btn-link {
    width: 100%;
    //color: #4a8eb0;
  }

  .btn-link:hover {
    box-shadow: none;
    text-decoration: none;
    //color: #4a8eb0;
  }


  button:not(.collapsed) {
    font-weight: 600;
    //background-color: #F5F4F4;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.075);

    //color: #4a8eb0;

    .icon-chevron-down {
      transform: rotate(-180deg);
    }
  }

  .icon-chevron-down {
    line-height: 1em;
    height: 1em;
    transition: all 0.4s ease;
  }
}

ngb-accordion .btn:focus .btn:hover {
  box-shadow: none;
}

.muneccim-success-modal {
  width: 100%;
  align-items: center;
  justify-content: center;

  .icon-message-success {
    font-size: 40px;
    padding-top: 1px;
    background: -webkit-linear-gradient(#00EFD1, #00ACEA);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  &-body {
    padding: 2rem;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    .icon {
      //font-size: 50px;
      margin-bottom: 1rem;
    }
    &-message {
      font-size: 18px;
      font-weight: 600;
    }
    &-detail {
      font-size: 18px;
      font-weight: 400;
    }
  }
}
