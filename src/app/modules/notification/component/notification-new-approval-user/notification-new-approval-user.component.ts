import { Component, Input, OnInit } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { GetActiveApplicationsAction, GetMyUsersAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { map, takeUntil } from 'rxjs/operators';
import { Navigate } from '@ngxs/router-plugin';
import { UserState } from '../../../customer/state/user/user.state';
import { SanitizedCustomerModel } from '../../../customer/model/sanitized-customer.model';
import { FindAndChangeCustomerAction } from '../../../customer/state/user/user.actions';

@Component({
  selector: 'app-notification-new-approval-user',
  templateUrl: './notification-new-approval-user.component.html',
  styleUrls: ['./notification-new-approval-user.component.scss']
})
export class NotificationNewApprovalUserComponent implements OnInit {

  @Select(CustomerState.activeApplications)
  activeApplications$: Observable<any>;

  @Select(CustomerState.activeApplicationsLoading)
  activeApplicationsLoading$: Observable<boolean>;

  @Select(CustomerState.myUsersLoading)
  myUsersLoading$: Observable<boolean>;

  @Select(UserState.customers)
  customers$: Observable<SanitizedCustomerModel[]>;

  @Input()
  notification: any;

  protected subscriptions$: Subject<boolean> = new Subject();

  activeApplications: any;
  catchUser: any;

  constructor(
    private readonly store: Store
  ) {}

  ngOnInit() {
    this.store.dispatch(new FindAndChangeCustomerAction(this.notification.customerNumber))
      .subscribe(() => {
        this.store.dispatch(new GetMyUsersAction());
        this.store.dispatch(new GetActiveApplicationsAction())
          .pipe(takeUntil(this.subscriptions$))
          .pipe(map(() => this.store.selectSnapshot(CustomerState.activeApplications)))
          .subscribe(data => {
            this.catchUser = data?.applications?.find(user => user.applicationId === this.notification.action.applicationId);
            if (!this.catchUser) { // If there is no user for this notification, go to the list page
              this.store.dispatch(new Navigate(['/settings/account-manager/awaiting-approvals']));
            }
          });

      });

  }
}
