@import "variable/bootstrap-variable";

.ndet {
  &-header {
    h4 {
      color: #2C2C2C;
      font-weight: 700;
    }
  }

}


.notification {

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    border: 1px solid #d8d8d8;
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;

      &-green {
        background: #5D8D1C;
      }

      &-orange {
        background: $warning;
      }

      &-red {
        background: #FF0000;
      }
    }
  }
}

.label-text {
  font-size: 13px;
}

.equipment-image {
  min-width: 100px;
  width: 100px;
  height: 80px;
  position: relative;
  //background-color: #EEF2F4;
  img {
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);

    max-height: 100%;
    max-width: 100%;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
  }
}

.action-btn {
  font-size: 16px;
  font-weight: 700;
}

.status-pop {
  padding: 16px 30px;
  min-height: 100px;
}

.btn-warning {
  background-color: $warning;

  &:focus {
    background-color: $warning;
    border-color: $warning;
  }

  &:hover {
    background-color: $warning;
    border-color: $warning;
  }

}
