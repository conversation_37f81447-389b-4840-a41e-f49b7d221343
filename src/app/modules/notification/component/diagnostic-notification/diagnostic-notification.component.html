<div *ngIf="notify">
  <div class="ndet-header mb-4">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ "_sound_diagnostic" | translate }}</h4>
      </div>
      <div class=""></div>
    </div>
  </div>

  <div *ngIf="notify.action" class="d-flex mb-4 align-items-center">
    <div class="equipment-image mr-2">
      <img class="img-fluid" [attr.src]="this.equipmentImageUrl(notify.action)"/>
    </div>
    <div class="flex-fill">
      <div class="font-weight-bold text-secondary">
        {{ notify.action.equipmentBrand }} {{ notify.action.equipmentModel }}
      </div>
      <div class="font-size-13px text-secondary">
        {{ notify.action.serialNumber | serialFormat}}
      </div>
    </div>
  </div>
  
  <div class="notification-item overflow-hidden mb-3">
    <div
      [ngClass]="{
        'notification-item-color-green': healthy(),
        'notification-item-color-orange': unHealthy(),
        'notification-item-color-red': healthCheckFail()
      }"
      class="notification-item-color notification-item-color-green"
    ></div>
    <div class="status-pop pb-3 pr-3">
      <div
        class="d-flex justify-content-between mb-2 overflow-hidden align-items-end"
      >
        <div class="font-weight-semi-bold h6 m-0">
          {{ notify.date | date: "dd.MM.Y HH:mm" }}
        </div>
        <div
          [ngClass]="{ 'text-success': healthy(), 'text-warning': unHealthy() }"
          class="font-weight-semi-bold"
          style="top: 0"
        >
          <i *ngIf="healthy()" class="icon icon-success font-size-22px"></i>
        </div>
      </div>
      <div class="label-text">
        {{ notify.body }}
      </div>
      <div class="label-text pt-2">
        <!-- <span>{{ getViewCustomerName(notify?.customerName || getCustomerName(notify.customerNumber)?.name) }}</span> -->
        {{ notify?.customerName?.length > 35 ? (notify?.customerName | slice: 0:32) + "..." : notify?.customerName }}
      </div>
    </div>
  </div>
  <app-warning-box [title]="'_warning' | translate" [ngSwitch]="notify.action.healthCode">
    <ng-container *ngSwitchCase="HealthCodeEnum.Error">
      {{'_sound_diagnostic_error_warning' | translate}}</ng-container>
    <ng-container *ngSwitchCase="HealthCodeEnum.Healthy">
      {{'_sound_diagnostic_healthy_warning' | translate}}</ng-container>
    <ng-container *ngSwitchCase="HealthCodeEnum.Unhealthy">
      {{'_sound_diagnostic_unhealthy_warning' | translate}}</ng-container>

  </app-warning-box>

  <div class="d-flex flex-column">
    <div
      *ngIf="healthy()"
      [routerLink]="['/']"
      class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white"
    >
      {{ "_return_to_home" | translate }}
    </div>
    <div
      *ngIf="unHealthy()"
      (click)="createServiceRequest()"
      class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white"
      appClickLog
      [section]="'SOUND_NOTIFICATION'"
      [subsection]="'SERVICE_REQUEST_CLICK'"
      [data]="{ 
        messageId: notify.messageId,
        equipmentNumber: notify.action.equipmentNumber,
        healthCode: notify.action.healthCode
      }"
    >
      {{ "_create_service_request" | translate }}
    </div>
    <div
      *ngIf="healthCheckFail()"
      (click)="tryAgain()"
      class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white"
      appClickLog
      [section]="'SOUND_NOTIFICATION'"
      [subsection]="'TRY_AGAIN_CLICK'"
      [data]="{ 
        messageId: notify.messageId,
        equipmentNumber: notify.action.equipmentNumber,
        healthCode: notify.action.healthCode
      }"
    >
      {{ "_try_again" | translate }}
    </div>
  </div>
</div>
