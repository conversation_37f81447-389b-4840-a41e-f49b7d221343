import { Component, Input, OnInit } from '@angular/core';
import { NotificationModel } from '../../model/notification.model';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import { HealthCodeEnum } from '../../enum/health-code.enum';
import { EquipmentModel } from '../../../customer/model/equipment.model';
import { Store } from '@ngxs/store';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { environment } from '../../../../../environments/environment';
import { CustomerModuleService } from '../../../customer/service/customer-module.service';
import { TranslateService } from '@ngx-translate/core';
import { ModuleCodeEnum } from '../../../../shared/enum/module-code.enum';
import { SoundDiagnosticActionModel } from '../../model/sound-diagnostic-action.model';
import { UserAction } from '../../../customer/state/user/user.actions';

@Component({
  selector: 'app-diagnostic-notification',
  templateUrl: './diagnostic-notification.component.html',
  styleUrls: ['./diagnostic-notification.component.scss'],
})
export class DiagnosticNotificationComponent implements OnInit {
  @Input()
  set notification(
    value: NotificationModel<NotificationActionTypeEnum.SoundDiagnostic>
  ) {
    if (this.notify !== value) {
      this.notify = value;
      // this.getEquipment();
    }
  }

  notify: NotificationModel<NotificationActionTypeEnum.SoundDiagnostic>;

  equipment: EquipmentModel;
  HealthCodeEnum = HealthCodeEnum;

  constructor(
    // private readonly customerService: CustomerService,
    private readonly store: Store,
    private readonly frameMessageService: FrameMessageService,
    private readonly customerModuleService: CustomerModuleService,
    private readonly translateService: TranslateService
  ) {}

  ngOnInit(): void {
    // baska customera gidilip geri gelindiginde onceki secili customera gecebilmesi icin
    // tetiklenmesi gerekiyor, bknz UserState.findCurrentCustomer AppStorage'dan gelir
    this.store.dispatch(new UserAction(false));
  }

  healthy() {
    return this.notify?.action?.healthCode === HealthCodeEnum.Healthy;
  }

  unHealthy() {
    return this.notify?.action?.healthCode === HealthCodeEnum.Unhealthy;
  }

  healthCheckFail() {
    return this.notify?.action?.healthCode === HealthCodeEnum.Error;
  }

  createServiceRequest() {
    // this.store.dispatch(new UpdateHeaderCompaniesAction(module.headerCompanies));

    this.customerModuleService
      .findAndNavigate(this.notify?.action?.customerNumber,
        ModuleCodeEnum.Equipment,
        {
          relativeUrl: 'form/request-service/' + this.notify.action.equipmentNumber,
          pageTitle: this.translateService.instant('_create_service_request'),
          params: {
            showHeader: false,
            navigatedPage: 'Notification',
            source: 'SoundDiagnostic',
            sourceRoot: 'SoundDiagnosticNotification'
          },
          closeButton: false,
          backButton: true,
        }
      );
  }

  tryAgain() {
    this.frameMessageService.sendMessage(FrameMessageEnum.voice_record, {
      CustomerNumber: this.notify?.action?.customerNumber,
      EquipmentNumber: this.notify.action.equipmentNumber,
      SerialNumber: this.notify.action.serialNumber,
      Source: 'Notification',
    });
  }

  equipmentImageUrl(equipment: SoundDiagnosticActionModel) {
    return (
      environment.api +
      '/image/equipment?&model=' +
      equipment.equipmentModel +
      '&imageSize=mobilethumbnailsize'
    );
  }

  // private getEquipment() {
  //   this.equipment = null;
  //   this.customerService
  //     .equipmentDetail(this.notify.action.equipmentNumber)
  //     .subscribe((equipment) => {
  //       this.equipment = equipment;
  //     });
  // }
}
