import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CustomerModel, PssrList } from 'src/app/shared/models/customer.model';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { LogService } from 'src/app/shared/service/log.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';

@Component({
  selector: 'cat-contact-your-representative-modal',
  templateUrl: './call-representative-modal.component.html',
  styleUrls: ['./call-representative-modal.component.scss']
})
export class ContactYourRepresentativeModalComponent implements OnInit {
  @Input()
  contactModal = false;

  @Output() handleClose = new EventEmitter<boolean>()

  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;

  pssrList: PssrList[] | any = [];

  constructor(
    private readonly store: Store,
    private readonly frameService: FrameMessageService,
    private readonly logger: LogService
  ) { }

  private subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.customer$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(customer => {
      if (customer) {
        console.log(customer, 'customer')
        this.pssrList = customer.details?.pssrList.filter(pssr => {
          if (pssr.titles.find(data => data === 'PSSR')) {
            return pssr.mailList.length || pssr.telephoneList.length;
          }
          return false;
        });
      }
    });
  }

  onCall(phone: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_PHONE_CLICK', { phone })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onMail(mail: string) {
    this.logger
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_MAIL_CLICK', { mail })
      .subscribe();
    this.frameService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
  }

  handleContactModal() {
    this.handleClose.emit();
  }


  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
