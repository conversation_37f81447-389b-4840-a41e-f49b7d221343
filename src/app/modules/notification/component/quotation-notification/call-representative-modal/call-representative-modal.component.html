<app-basic-modal
  *ngIf="contactModal"
  [(status)]="contactModal"
  (statusChange)="handleContactModal()"
  [headerText]="'_contact_with_customer_service' | translate"
>
  <div class="mb-3">
    <div *ngIf="pssrList">
      <ul class="service-person">
        <li class="service-person-item" *ngFor="let persons of pssrList">
          <div class="service-person-item-name">
            {{ persons.pssrName }}
          </div>
          <div class="pssr-title">
            <ng-container *ngFor="let title of persons.titles; let i = index">
              {{ "_customer_title_" + title | translate }}
              {{ i !== persons.titles.length - 1 ? "-" : "" }}
            </ng-container>
          </div>

          <ul class="service-person-item-detail">
            <li
              class="service-person-item-detail-item"
              *ngFor="let phones of persons.telephoneList"
              (click)="onCall(phones.telephoneNumber)"
            >
              <div class="icon-area">
                <i class="icon icon-phone-call"></i>
              </div>
              {{ phones.telephoneNumberShort || phones.telephoneNumber }}
            </li>
          </ul>

          <ul class="service-person-item-detail">
            <li
              class="service-person-item-detail-item"
              *ngFor="let mail of persons.mailList"
              (click)="onMail(mail.mailAdress)"
            >
              <div class="icon-area">
                <i class="icon icon-contact"></i>
              </div>
              {{ mail.mailAdress }}
            </li>
          </ul>
        </li>
      </ul>
    </div>
  </div>
<app-basic-modal>
