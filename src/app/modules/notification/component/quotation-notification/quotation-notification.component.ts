import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Select } from '@ngxs/store';
import * as moment from 'moment';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CustomerRelationModel } from 'src/app/modules/customer/model/customer-relation.model';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';

@Component({
  selector: 'app-quotation-notification',
  templateUrl: './quotation-notification.component.html',
  styleUrls: ['./quotation-notification.component.scss']
})
export class QuotationNotificationComponent implements OnInit, OnDestroy {
  @Input()
  notification: any;

  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;

  customerNumber: string;
  contactModal = false;

  customersList: CustomerRelationModel[];
  notificationActionTypeEnum = NotificationActionTypeEnum;

  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly customerModuleService: CustomerModuleService,
  ) { }

  ngOnInit() {
    this.customers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customers => {
        this.customersList = customers;
        this.customerNumber = this.getCustomer(
          this.notification.action?.customerNumber || this.notification?.customerNumber)?.customer?.customerNumber;
      });
  }

  // openQuotationModule(customerNumber: string) {
  //   this.customerModuleService.openQuotationModule(customerNumber);
  // }

  goSelectedQuotationModule(customerNumber: string, quotationNumber: string) {
    this.customerModuleService.openQuotationModule(
      customerNumber,
      this.notification?.action?.quotationNumber || this.notification?.quotationNumber || quotationNumber
    );
  }

  getCustomer(customerNumber: string) {
    if (!customerNumber) {
      return null;
    }
    return this.customersList.find(customer => customer?.customer?.customerNumber.includes(customerNumber));
  }

  getDate(date) {
    if (moment(date).isValid() && date !== '0001-01-01T00:00:00') {
      return moment(date).format('DD.MM.YYYY');
    } else {
      return null;
    }
  }

  openContactModal() {
    this.contactModal = !this.contactModal;
    document.documentElement.style.overflow = 'hidden';
  }

  handleCloseRepresentativeModal() {
    this.openContactModal();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
