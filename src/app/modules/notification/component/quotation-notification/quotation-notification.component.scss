@import "variable/bootstrap-variable";

.ndet {
  &-header {
    h4 {
      color: #2C2C2C;
      font-weight: 700;
    }
  }

}


.notification {

  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    border: 1px solid #d8d8d8;
    border-radius: 6px;
    position: relative;

    &-color {
      position: absolute;
      width: 10px;
      height: 100%;
      left: 0;
      top: 0;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
      z-index: 1;

      &-green {
        background: #5D8D1C;
      }

      &-orange {
        background: $warning;
      }

      &-red {
        background: #FF0000;
      }
    }
  }
}

.label-text {
  font-size: 13px;
}

.action-btn {
  font-size: 16px;
  font-weight: 700;
}

.status-pop {
  padding: 16px 30px;
  min-height: 100px;
}

.btn-warning {
  background-color: $warning;

  &:focus {
    background-color: $warning;
    border-color: $warning;
  }

  &:hover {
    background-color: $warning;
    border-color: $warning;
  }

}
