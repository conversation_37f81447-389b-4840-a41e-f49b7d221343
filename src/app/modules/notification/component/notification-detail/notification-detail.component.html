<div class="px-4 mt-2" [ngSwitch]="notification?.actionType">
  <ng-container
    *ngSwitchCase="notificationActionTypeEnum.ApplicationApproved"
  ></ng-container>
  <app-diagnostic-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.SoundDiagnostic"
  ></app-diagnostic-notification>
  <app-financial-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.FinancialsCurrentDebt"
  ></app-financial-notification>
  <app-financial-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.FinancialsForwardTermDebt"
  ></app-financial-notification>
  <app-quotation-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.QuotationExpire"
  ></app-quotation-notification>
  <app-quotation-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.QuotationUpdated"
  ></app-quotation-notification>
  <app-quotation-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.NewQuotation"
  ></app-quotation-notification>
  <app-service-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.ServiceStatusChange"
  ></app-service-notification>
  <app-service-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.ServiceDocumentUploaded"
  ></app-service-notification>
  <app-agreement-approval
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.ApprovalAgreementUpdated"
  ></app-agreement-approval>
  <app-agreement-approval
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.InformalAgreementUpdated"
  ></app-agreement-approval>
  <app-daily-work-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.DailyWorkOrderReport"
  ></app-daily-work-notification>
  <app-daily-work-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.DailyPlannedWorkCreated"
  ></app-daily-work-notification>
  <app-daily-work-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.DailyPlannedWorkUpdated"
  ></app-daily-work-notification>
  <app-daily-work-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.DailyPlannedWorkDeleted"
  ></app-daily-work-notification>
  <app-order-status-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.OrderStatusChange"
  ></app-order-status-notification>
  <app-cargo-push-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.CargoStatusChange"
  ></app-cargo-push-notification>
  <app-rfms-push-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.RfmsPush"
  ></app-rfms-push-notification>
  <app-loyality-point-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.LoyalityPointPush"
  ></app-loyality-point-notification>
  <app-linked-products-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.LinkedProducts"
  ></app-linked-products-notification>
  <app-muneccim-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.Muneccim"
  ></app-muneccim-notification>
  <app-picture-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.PictureNotification"
  ></app-picture-notification>
  <app-notification-new-approval-user
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.AccountManagerNewApproval"
  ></app-notification-new-approval-user>
  <app-quotation-crm-status-notification
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.QuotationCrmStatusPush"
  ></app-quotation-crm-status-notification>
  <app-agreement-survey
    [notification]="notification"
    *ngSwitchCase="notificationActionTypeEnum.SurveyCampaign"
  ></app-agreement-survey>

  <app-basic-notification
    [notification]="notification"
    *ngSwitchDefault
  ></app-basic-notification>
</div>
<app-loader [show]="loader || redirectLoading"></app-loader>
