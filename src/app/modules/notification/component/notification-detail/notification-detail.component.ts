import { AfterViewInit, Component, OnDestroy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NotificationModel } from '../../model/notification.model';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { NotificationState } from '../../state/notification/notification.state';
import {
  GetCurrentNotificationsAction,
  GetOtherNotificationsAction,
  NotificationDeleteAction,
  NotificationEventAction,
  ResetPushNotification
} from '../../state/notification/notification.actions';
import { NotificationEventEnum } from '../../enum/notification-event.enum';
import { Navigate } from '@ngxs/router-plugin';
import { LogService } from '../../../../shared/service/log.service';
import { CustomerModuleService } from '../../../customer/service/customer-module.service';
import { TranslateService } from '@ngx-translate/core';
import { LoginState } from '../../../authentication/state/login/login.state';
import * as moment from 'moment';
import { ErrorModalComponent } from '../../../../shared/component/error-modal/error-modal.component';
import { ModalService } from '../../../../shared/service/modal.service';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { ChangeCustomerAction, UserAction } from 'src/app/modules/customer/state/user/user.actions';
import {
  HeaderStatusMainAction,
  SidebarStatusAction,
  StaticFeedbackAction
} from 'src/app/modules/customer/state/customer/customer.actions';
import { UpdateAppStorageAction } from 'src/app/shared/state/common/common.actions';
import { LogSubSectionEnum } from 'src/app/modules/definition/enum/log-sub-section.enum';
import { filter, map, take, takeUntil } from 'rxjs/operators';
import { SanitizedCustomerModel } from '../../../customer/model/sanitized-customer.model';
import { CustomerState } from '../../../customer/state/customer/customer.state';
import { CustomerRelationModel } from 'src/app/modules/customer/model/customer-relation.model';
import { IncomingMessageService } from '../../../../core/service/incoming-message.service';

@Component({
  selector: 'app-notification-detail',
  templateUrl: './notification-detail.component.html',
  styleUrls: ['./notification-detail.component.scss'],
})
export class NotificationDetailComponent implements OnInit, AfterViewInit, OnDestroy {
  notification: NotificationModel<any>;

  notificationActionTypeEnum = NotificationActionTypeEnum;

  @Select(NotificationState.pushNotification)
  pushNotification$: Observable<NotificationModel<any>>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(UserState.userLoading)
  userLoading$: Observable<boolean>;

  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;

  protected subscriptions$: Subject<boolean> = new Subject();

  loader: boolean;
  customers: CustomerRelationModel[];
  redirectLoading: boolean;

  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly log: LogService,
    private readonly customerModuleService: CustomerModuleService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly frameMessageService: FrameMessageService,
    private readonly incomingMessageService: IncomingMessageService
  ) {
    this.notification = this.router.getCurrentNavigation()?.extras?.state as NotificationModel<any>;
  }

  sendEvent() {
    this.store.dispatch(new NotificationEventAction(
      NotificationEventEnum.Clicked,
      this.notification.messageId,
    ));
  }

  ngOnInit(): void {
    this.customers$.subscribe(customers => this.customers = customers);
  }

  ngAfterViewInit(): void {
    if (this.notification) {
      console.log('New Notificaiton: ' + JSON.stringify(this.notification));
      this.sendEvent();
      if (!this.notification?.customerName) {
        this.notification = {
          ...this.notification,
          customerName: this.getCustomerName(this.notification),
        };
      }

      this.store.dispatch(new SidebarStatusAction(false));
      switch (this.notification.actionType) {
        // case NotificationActionTypeEnum.ServiceStatusChange:
        //   this.customerModuleService.openServiceModule(this.notification.action?.customerNumber);
        //   break;
        case NotificationActionTypeEnum.ProductSos:
          this.productSOS();
          break;
        case NotificationActionTypeEnum.FinancialInfo:
          this.customerModuleService.openFinancialModule(this.notification.action?.customerNumber);
          break;
        case NotificationActionTypeEnum.OpenModule:
          this.openInternalApps();
          break;
        case NotificationActionTypeEnum.NewCampaign:
          this.openNewCampaign();
          break;
        case NotificationActionTypeEnum.ApplicationApproved:
          this.loader = true;
          this.openApprovedCustomer(this.notification);
          break;
        case NotificationActionTypeEnum.BoomShopBasketReminderPush:
          this.incomingMessageService.open_module(this.notification.action.redirectUrl);
          this.goToNotificationList();
          break;
        case NotificationActionTypeEnum.BoomShopSpecialReminderPush:
          this.incomingMessageService.open_module(this.notification.action.redirectUrl);
          this.goToNotificationList();
          break;
        case NotificationActionTypeEnum.BoomShopFileUploadPush:
          this.incomingMessageService.open_module(this.notification.action.redirectUrl);
          this.goToNotificationList();
          break;
        case NotificationActionTypeEnum.PromotionPush:
          this.customerModuleService.openPromotionPortal();
          this.goToNotificationList();
          break;
        case NotificationActionTypeEnum.ReconciliationAssignmentPush:
          this.customerModuleService.openFinancialModule(this.notification.action?.customerNumber, true);
          break;
        case NotificationActionTypeEnum.Muneccim:
          return this.openServiceIndication(this.notification);
        case NotificationActionTypeEnum.ServiceSurvey:
          return this.serviceSurvey();
        case NotificationActionTypeEnum.Feedback:
          this.feedbackNotification(this.notification);
          break;
        case NotificationActionTypeEnum.RateUsForm:
          this.staticFeedBack(this.notification);
          break;
        case NotificationActionTypeEnum.PseCampaignPush:
          this.newPSECampaign(this.notification);
          break;
        case NotificationActionTypeEnum.EquipmentAgendaCheckoutInformationPush:
          this.equipmentAgendaCheckoutInfo(this.notification);
          break;
        case NotificationActionTypeEnum.EquipmentAgendaNoteReminderPush:
          this.equipmentAgendaCheckoutInfo(this.notification);
          break;
        case NotificationActionTypeEnum.ServiceChat:
          this.openServiceChat(this.notification);
          break;
        case NotificationActionTypeEnum.AccountManagerApproveReminder:
          this.goToAccountManagerAwaitingApprovals();
          break;
        case NotificationActionTypeEnum.AccountManagerActionReminder:
          this.goToAccountManagerRoleList();
          break;
        case NotificationActionTypeEnum.ActivitySurveyReminder:
          this.activitySurveyReminder();
          break;
        case NotificationActionTypeEnum.NewActivitySurvey:
          this.newActivitySurvey();
          break;
        case NotificationActionTypeEnum.NewPulseSurvey:
          this.newPulseSurvey();
          break;
        case NotificationActionTypeEnum.BoomGuru:
          this.boomGuru();
          break;
      }
      this.setRedirectLoading();
    } else {
      this.store.dispatch(new Navigate(['notifications']));
    }

    this.pushNotification$.subscribe(notification => {
      if (notification) {
        this.currentCustomer$
          .pipe(takeUntil(this.subscriptions$))
          .pipe(filter(Boolean), take(1))
          .subscribe(() => {
            this.notification = notification;
            this.log.action('NOTIFICATION', 'OPEN_PUSH', {
              messageId: notification.messageId
            });
          });
      }
    });

  }


  ngOnDestroy(): void {
    this.store.dispatch(new ResetPushNotification());
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  private openNewCampaign() {
    console.log('t', this.notification);
    const lang = this.store.selectSnapshot(LoginState.language);
    const iframeAction = {
      url:
        this.notification.action.campaignUrl +
        (this.notification.action.campaignUrl.includes('?') ? '&' : '?') +
        `lang=${lang}`,
      active: true,
      closeButton: true,
      pageTitle: this.notification.title,
    };

    this.router.navigate(['module', this.notification.messageId], { state: { iframeAction } });
  }

  protected reloadNotifications() {
    return this.store.dispatch([new GetCurrentNotificationsAction(), new GetOtherNotificationsAction()]);
  }

  private feedbackNotification(notification: NotificationModel<NotificationActionTypeEnum.Feedback>) {
    // send read event
    this.store.dispatch(new NotificationEventAction(
      NotificationEventEnum.Clicked,
      notification.messageId,
    ));

    // send to APP
    this.frameMessageService.sendMessage(FrameMessageEnum.feedbackNotification, notification);

    this.goToNotificationList();
  }

  private goToNotificationList() {
    this.router.navigate(['notifications'], { replaceUrl: true });
  }

  private goToAccountManagerRoleList() {
    this.router.navigate(['settings/account-manager/customers'], { replaceUrl: true });
  }

  private goToAccountManagerAwaitingApprovals() {
    this.router.navigate(['settings/account-manager/awaiting-approvals'], { replaceUrl: true });
  }


  private openApprovedCustomer(notifications: any) {
    let customerNumber = notifications?.customerNumber || notifications?.action?.customerNumber;
    if (!customerNumber) {
      customerNumber = this.store.selectSnapshot(UserState.currentCustomer)?.customer?.customerNumber;
    }

    const userAction$ = this.store.dispatch(new UserAction());

    userAction$.pipe(takeUntil(this.subscriptions$)).subscribe(userData => {
        if (userData?.user?.customers.length) {
          const customer = userData?.user?.customers.find((group) => {
            return group?.customer?.customerNumber === customerNumber;
          });
          if (customer) {
            this.store.dispatch(new ChangeCustomerAction(customer));
            this.store.dispatch(new HeaderStatusMainAction());
            this.store.dispatch(new UpdateAppStorageAction({
              currentCustomerTitle: customer.groupTitle
            }));

            this.log
              .action(LogSubSectionEnum.HEADER, 'COMPANY_CHANGED', {
                customerNumber: customer?.customer?.customerNumber,
                displayName: customer?.displayName,
              })
              .subscribe();
            this.store.dispatch(new Navigate(['dashboard'])).subscribe(() => {
              this.loader = false;
            });
          } else {
            this.loader = false;
            this.store.dispatch(new Navigate(['notifications']));
          }
        }
      },
      err => {
        console.log('Error', err);
        this.loader = false;
        this.store.dispatch(new Navigate(['dashboard']));
      });

  }

  private openInternalApps() {
    const url = this.notification?.action?.actionUrl;
    if (!url) {
      return;
    }

    this.frameMessageService.sendMessage(FrameMessageEnum.openStore, { url });
  }

  openServiceIndication(notification: any) {
    const customerNumber = notification.action?.customerNumber || notification?.customerNumber;
    let equipmentNumber = notification.action?.equipmentNumber || notification?.equipmentNumber;
    const serialNumber = notification.action?.serialNumber || notification?.serialNumber;
    const isMultiple = equipmentNumber?.includes(',') || equipmentNumber?.includes(';')
      || serialNumber?.includes(',') || serialNumber?.includes(';');
    // Campaign Muneccim
    if (isMultiple && (notification.action?.campaignId || notification.action?.CampaignId)) {
      return;
    }
    // Multiple Muneccim
    if (isMultiple) {
      equipmentNumber = null;
    }
    this.customerModuleService.openEquipmentModule(customerNumber, equipmentNumber,
      {
        replaceUrl: true, params: {
          isServiceIndicator: 1,
          source: 'Muneccim',
          sourceRoot: 'MuneccimNotification'
        }
      });
  }

  protected staticFeedBack(notification: NotificationModel<any>) {
    console.log('staticFeedBack: ', notification);
    this.store.dispatch(new StaticFeedbackAction())
      .pipe(map(() => this.store.selectSnapshot(CustomerState.staticFeedback)))
      .subscribe(feedback => {
        this.frameMessageService.sendMessage(FrameMessageEnum.staticFeedback, feedback);
      });
  }

  private getCustomerName(notification: NotificationModel<any>): string {
    const customerNumber = notification?.customerNumber || notification?.action?.customerNumber;
    const customerName = this.customers?.find(c => c.customer?.customerNumber === customerNumber)?.customer?.name;
    return this.notification?.customerName || this.notification?.action?.customerName || customerName || '';
  }

  // Waiting Redirect Loading
  private setRedirectLoading() {
    this.redirectLoading = this.notification.actionType === NotificationActionTypeEnum.ProductSos;
    setTimeout(() => {
      this.redirectLoading = false;
    }, 1000);
  }

  newPSECampaign(notification: any) {
    const customerNumber = notification.action?.customerNumber || notification?.customerNumber;
    let equipmentNumber = notification.action?.equipmentNumber || notification?.equipmentNumber;

    if (equipmentNumber.includes(';')) {
      equipmentNumber = null;
    }
    this.customerModuleService.openEquipmentModule(customerNumber, equipmentNumber, {
      replaceUrl: true, params: { isPse: 1, }
    });
  }

  equipmentAgendaCheckoutInfo(notification: any) {
    const customerNumber = notification.action?.customerNumber || notification?.customerNumber;
    const equipmentModel = notification.action?.equipmentModel || notification?.equipmentModel;
    const equipmentNumber = notification.action?.equipmentNumber || notification?.equipmentNumber;
    const serialNumber = notification.action?.serialNumber || notification?.serialNumber;
    const isActiveRoute = 2;
    this.customerModuleService.openEquipmentAgenda(customerNumber, equipmentNumber,
      {
        replaceUrl: true, params: {
          serialNumber, equipmentModel, isActiveRoute
        }
      });
  }

  openServiceChat(notification: any) {
    const customerNumber = notification.action?.customerNumber;
    const serviceNumber = notification.action?.serviceNumber;

    this.customerModuleService.openServiceChat(customerNumber, serviceNumber, { replaceUrl: true });
  }

  protected serviceSurvey(): void {
    const expDate = moment.utc(this.notification.action.surveyLinkExpireDate);

    if (this.notification.action.surveyLinkExpireDate && expDate.isBefore(moment())) {
      setTimeout(() => {
        this.modalService.errorModal({
          message: this.translateService.instant('_survey_expired'),
          button: this.translateService.instant('_ok'),
          buttonClick: (modal: ErrorModalComponent) => {
            modal.close();
          }
        }).onResult().subscribe(() => {
          this.reloadNotifications();
        });
        this.goToNotificationList();
      });

      this.store.dispatch(new NotificationDeleteAction(this.notification.messageId));

      return;
    }

    this.frameMessageService.openSurvey({
      smsLinkUrl: this.notification.action.surveyLink,
      linkExpireDateUtc: this.notification.action.surveyLinkExpireDate
    }, this.notification.messageId, true);
  }

  protected productSOS() {
    const SerialNumber = this.notification?.action?.serialNumber || this.notification?.action?.SerialNumber;
    setTimeout(() => {
      this.customerModuleService.openEquipmentModule(
        this.notification?.customerNumber ||
        this.notification.action?.customerNumber,
        this.notification.action?.equipmentNumber,
        {
          replaceUrl: true,
          params: {
            SerialNumber
          }
        });
    }, 1000);
  }

  protected activitySurveyReminder() {
    this.customerModuleService.openSurveyForm({}, this.getCustomerNumber());
  }

  protected newActivitySurvey() {
    this.customerModuleService.openSurveyForm({
      link: '/survey/' + this.notification.action.activityId
    }, this.getCustomerNumber());
  }

  protected newPulseSurvey() {
    this.customerModuleService.openSurveyForm({
      link: '/pulse-survey/' + this.notification.action.activityId
    }, this.getCustomerNumber());
  }

  protected getCustomerNumber() {
    return this.notification?.customerNumber
      || this.notification?.action?.customerNumber
      || this.notification?.action?.CustomerNumber
      || this.notification?.CustomerNumber;
  }

  private boomGuru() {
    this.customerModuleService.openBoomGuruForm({
      boomGuruId: this.notification.action?.boomGuruId
    });
  }
}
