<div *ngIf="notification">
  <div class="ndet-header mb-4">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ "_quotation_notification" | translate }}</h4>
      </div>
      <div class=""></div>
    </div>
  </div>
  <div class="pt-4"></div>
  <div class="notification-item overflow-hidden mb-3">
    <div
      class="notification-item-color"
      [ngClass]="getStatusColor()"
    ></div>
    <div class="status-pop pb-3 pr-3">
      <div
        *ngIf="
          notification?.action?.customerNumber || notification?.customerNumber
        "
        class="d-flex mb-4"
      >
        <div class="flex-fill" *ngIf="notification?.title">
          <div class="font-weight-bold text-secondary">
            {{ notification?.title }}
          </div>
        </div>
      </div>
      <div>
        <div class="row pt-1"
             *ngIf="notification?.action?.quotationNumber !== null || notification?.action?.quotationNumber !== ''">
          <div class="col-6">{{ "_offer_no" | translate }}</div>
          <div class="col pl-0 align-self-center">
            : {{ notification?.action?.quotationNumber }}
          </div>
        </div>
        <hr class="my-1" *ngIf="notification?.action?.quotationAmount !== null">
        <div class="row" *ngIf="notification?.action?.quotationAmount !== null">
          <div class="col-6">{{ "_quotation_amount" | translate }}</div>
          <div class="col pl-0 align-self-center" *ngIf="notification?.action?.quotationAmount !== 0">
            : {{ notification?.action?.quotationAmount  | number: ''  }}
            {{ notification?.action?.quotationCurrency }}
          </div>
          <div class="col pl-0 align-self-center" *ngIf="notification?.action?.quotationAmount === 0">
            : {{ notification?.action?.quotationNetAmount | number: '' }}
            {{ notification?.action?.quotationCurrency }} + {{ "_kdv" | translate }}
          </div>
        </div>
        <hr class="my-1"
            *ngIf="notification?.action?.quotationValidDate && getDate(notification?.action?.quotationValidDate)">
        <div class="row"
             *ngIf="notification?.action?.quotationValidDate && getDate(notification?.action?.quotationValidDate)">
          <div class="col-6">{{ "_operation_date" | translate }}</div>
          <div class="col pl-0 align-self-center">
            : {{ getDate(notification?.action?.quotationValidDate) }}
          </div>
        </div>
      </div>
      <div class="label-text pt-1">
        <span>{{
          getCustomer(customerNumber)?.customer?.name.length > 35
            ? (getCustomer(
            notification?.action?.customerNumber ||
            notification?.customerNumber
          )?.customer?.name | slice: 0:32) + "..."
            : getCustomer(
              notification?.action?.customerNumber ||
              notification?.customerNumber
            )?.customer?.name
          }}</span>
      </div>
    </div>
  </div>
  <div class="d-flex flex-column" *ngIf="customerNumber">
    <button
      type="button"
      class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white"
      (click)="
      goSelectedQuotationModule(customerNumber,
          notification?.action?.quotationNumber
        )
      "
    >
      {{ "_go_to_offer" | translate }}
    </button>
  </div>
</div>
