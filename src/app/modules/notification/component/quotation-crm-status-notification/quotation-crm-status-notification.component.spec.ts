import { ComponentFixture, TestBed } from '@angular/core/testing';

import { QuotationCrmStatusNotificationComponent } from './quotation-crm-status-notification.component';

describe('QuotationCrmStatusNotificationComponent', () => {
  let component: QuotationCrmStatusNotificationComponent;
  let fixture: ComponentFixture<QuotationCrmStatusNotificationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ QuotationCrmStatusNotificationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(QuotationCrmStatusNotificationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
