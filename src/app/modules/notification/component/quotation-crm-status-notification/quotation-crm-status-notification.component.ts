import {Component, Input, On<PERSON><PERSON>roy, OnInit} from '@angular/core';
import {NotificationModel} from '../../model/notification.model';
import {NotificationActionTypeEnum} from '../../enum/notification-action-type.enum';
import * as moment from 'moment/moment';
import {Select} from '@ngxs/store';
import {UserState} from '../../../customer/state/user/user.state';
import {Observable, Subject} from 'rxjs';
import {CustomerRelationModel} from '../../../customer/model/customer-relation.model';
import {takeUntil} from 'rxjs/operators';
import {CustomerModuleService} from '../../../customer/service/customer-module.service';

@Component({
  selector: 'app-quotation-crm-status-notification',
  templateUrl: './quotation-crm-status-notification.component.html',
  styleUrls: [
    './quotation-crm-status-notification.component.scss',
    '../basic-notification/notification-shared.scss'
  ]
})
export class QuotationCrmStatusNotificationComponent implements OnInit, OnDestroy {

  @Input()
  notification: NotificationModel<NotificationActionTypeEnum.QuotationCrmStatusPush>;

  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;

  customerNumber: string;

  customersList: CustomerRelationModel[];

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly customerModuleService: CustomerModuleService,
  ) {
  }

  ngOnInit(): void {
    this.customers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customers => {
        this.customersList = customers;
      });
    this.customerNumber = this.notification?.customerNumber || this.notification?.action?.customerNumber;
  }

  goSelectedQuotationModule(customerNumber: string, quotationNumber: string) {
    this.customerModuleService.openQuotationModule(
      customerNumber,
      this.notification?.action?.quotationNumber || quotationNumber
    );
  }

  getCustomer(customerNumber: string) {
    if (!customerNumber) {
      return null;
    }
    return this.customersList.find(customer =>
      customer?.customer?.customerNumber.includes(customerNumber)
    );
  }

  getDate(date) {
    if (moment(date).isValid() && date !== '0001-01-01T00:00:00') {
      return moment(date).format('DD.MM.YYYY');
    } else {
      return null;
    }
  }

  getStatusColor(): string {
    return this.notification?.action?.quotationCrmStatus === 'Success' ?
      'notification-item-color-green' :
      this.notification?.action?.quotationCrmStatus === 'Fail' ?
        'notification-item-color-red' :
        'notification-item-color-gray';
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
