import { Component, Input, OnInit } from '@angular/core';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';

@Component({
  selector: 'app-order-status-notification',
  templateUrl: './order-status-notification.component.html',
  styleUrls: ['./order-status-notification.component.scss']
})
export class OrderStatusNotificationComponent implements OnInit {

  @Input()
  notification: any;

  constructor(
    public readonly customerModuleService: CustomerModuleService,
  ) { }

  ngOnInit() {
  }

  goToSparePart(){
    this.customerModuleService.openSparePartModule(this.notification?.customerNumber || this.notification?.action?.customerNumber);
  }

}
