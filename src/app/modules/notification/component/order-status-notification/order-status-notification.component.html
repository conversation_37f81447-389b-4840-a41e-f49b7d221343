<div class="order_status">
  <div class="ndet-header mb-4">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ "_order_status_notification" | translate }}</h4>
      </div>
      <div class=""></div>
    </div>
  </div>
  <div>
    <div>
      {{ notification.title }}
    </div>
    <div *ngIf="notification?.action?.orderId" class="row no-gutters pt-1">
      <div class="col-5">
        {{ "_order_id" | translate }}
      </div>
      <div class="col-7 two-dots align-self-center">
        {{ notification?.action?.orderId }}
      </div>
    </div>
    <div *ngIf="notification?.action?.netPrice" class="row no-gutters pt-1">
      <div class="col-5">
        {{ "_price" | translate }}
      </div>
      <div class="col-7 two-dots align-self-center">
        {{ notification?.action?.netPrice }} {{ notification?.action?.currency }}
      </div>
    </div>
    <div *ngIf="notification?.action?.pssr" class="row no-gutters pt-1">
      <div class="col-5">
        {{ "_customer_title_PSSR" | translate }}
      </div>
      <div class="col-7 two-dots align-self-center">
        {{ notification?.action?.pssr }}
      </div>
    </div>
    <div *ngIf="notification?.action?.address" class="row no-gutters pt-1">
      <div class="col-5">
        {{ "_address" | translate }}
      </div>
      <div class="col-7 two-dots align-self-center">
        {{ notification?.action?.address }}
      </div>
    </div>
    <div class="row pt-2 label-text no-gutters">
      <span> {{ notification?.customerName?.length > 35 ? (notification?.customerName | slice: 0:32) + "..." : notification?.customerName }}</span>
    </div>
    <div *ngIf="this.notification?.customerNumber || this.notification?.action?.customerNumber" class="d-flex flex-column mt-3 mb-5">
      <button type="button" class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white" (click)="goToSparePart()">
        {{ "_boom_shop" | translate }}
      </button>
    </div>
  </div>
</div>
