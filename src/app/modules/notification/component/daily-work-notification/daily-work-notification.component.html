<div *ngIf="notification" class="daily-work-plan">
  <div class="ndet-header mb-3">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ getDailyHeader() | translate }}</h4>
      </div>
      <div></div>
    </div>
  </div>
  <div style="height: calc(100vh - 170px); overflow-y: auto" *ngIf="dailyWork?.length; else nonJob">
    <app-pssr-section [notification]="notification"></app-pssr-section>
    <ngb-accordion [destroyOnHide]="false" [closeOthers]="true" (panelChange)="toggleAccordion($event)">
      <ng-container *ngFor="let job of dailyWork; let i = index">
        <ngb-panel id="id-{{ i }}" [disabled]="this.notification.actionType === 'DailyPlannedWorkDeleted'">
          <ng-template ngbPanelTitle>
            <div class="d-flex justify-content-between">
              <div class="d-flex flex-column flex-fill" style="margin-right: -16px">
                <div class="row no-gutters pt-1" *ngIf="job?.WorkOrderNumber || job?.workOrderId || job?.workOrderNumber">
                  <div class="col-5 fw-bolder">
                    {{ "_service_number" | translate }}
                  </div>
                  <div class="col-7 two-dots">
                    {{ job?.WorkOrderNumber || job?.workOrderId  || job?.workOrderNumber }}
                  </div>
                </div>
                <div class="row no-gutters pt-1" *ngIf="job?.WorkOrderSerialNumber || job?.serialNumber">
                  <div class="col-5 fw-bolder">
                    {{ "_machine_serial_number" | translate }}
                  </div>
                  <div class="col-7 two-dots">
                    {{ job?.WorkOrderSerialNumber || job?.serialNumber }}
                  </div>
                </div>
                <div class="row no-gutters pt-1" *ngIf="job?.StartDate || getStartDate()">
                  <div class="col-5 fw-bolder">
                    {{ "_start_date" | translate }}
                  </div>
                  <div class="col-7 two-dots">
                    {{ job?.StartDate || getStartDate() }}
                  </div>
                </div>
                <div class="row no-gutters pt-1" *ngIf="job?.EndDate || getEndDate()">
                  <div class="col-5 fw-bolder">
                    {{ "_end_date" | translate }}
                  </div>
                  <div class="col-7 two-dots">
                    {{ job?.EndDate || getEndDate() }}
                  </div>
                </div>
                <div class="row no-gutters pt-1" *ngIf="job?.planRejectReason">
                  <div class="col-5 fw-bolder">
                    {{ "_reason_for_change" | translate }}
                  </div>
                  <div class="col-7 two-dots">
                    {{ job?.planRejectReason | translate }}
                  </div>
                </div>
              </div>
              <div class="d-flex align-self-center" style="margin-right: -10px">
                <i *ngIf="this.notification.actionType !== 'DailyPlannedWorkDeleted'" class="text-decoration-none icon icon-chevron-down"> </i>
              </div>
            </div>
          </ng-template>

          <ng-template ngbPanelContent>
            <div class="answer fit-body">
              <!-- Service Detail - START -->
              <div class="service-request-item overflow-hidden" *ngIf="!loading && serviceRequest">
                <div [style.background-color]="serviceRequest?.serviceStatusColor" class="service-request-item-color">
                </div>
                <div class="service-request-item-detail py-2">
                  <div>
                    <div class="d-flex justify-content-between mb-2 overflow-hidden">
                      <div class="font-weight-semi-bold h6">
                        {{
                        serviceRequest?.model || ("_component" | translate)
                        }}
                      </div>
                      <div [style.color]="serviceRequest?.serviceStatusColor" class="font-weight-semi-bold mr-3">
                        {{ serviceRequest?.workOrderStatus?.currentStatusName }}
                      </div>
                    </div>

                    <div class="row no-gutters pt-1">
                      <div class="col-5">
                        {{ "_service_number" | translate }}
                      </div>
                      <div class="col-7 two-dots align-self-center">
                        {{
                        job?.serviceNumber ||
                        serviceRequest?.serviceNumber ||
                        "-"
                        }}
                      </div>
                    </div>
                    <div class="row no-gutters pt-1">
                      <div class="col-5">
                        {{ "_machine_serial_number" | translate }}
                      </div>
                      <div class="col-7 two-dots align-self-center">
                        {{ serviceRequest?.equipmentSerialNumber || "-" }}
                      </div>
                    </div>
                    <ng-container *ngIf="serviceRequest?.isCrc == false">
                      <div [ngSwitch]="
                          serviceRequest?.workOrderStatus?.serviceApplication
                        " class="row no-gutters pt-1">
                        <div *ngSwitchCase="'Cdoms'" class="col-5">
                          {{ "_acceptance_date" | translate }}
                        </div>
                        <div *ngSwitchCase="'Weking'" class="col-5">
                          {{ "_planned_date" | translate }}
                        </div>
                        <div *ngSwitchCase="'Sim'" class="col-5">
                          {{ "_start_date" | translate }}
                        </div>
                        <div *ngSwitchCase="'WO'" class="col-5">
                          {{ "_request_date" | translate }}
                        </div>
                        <div *ngSwitchDefault class="col-5">
                          {{ "_request_date" | translate }}
                        </div>
                        <div class="col-7 two-dots">
                          {{
                          serviceRequest?.startDate
                          | date: "dd.MM.YYYY HH:mm" || "-"
                          }}
                        </div>
                      </div>
                    </ng-container>
                    <ng-container *ngIf="serviceRequest?.isCrc == true">
                      <div class="row no-gutters pt-1">
                        <div class="col-5">
                          {{
                          (serviceRequest?.workOrderStatus?.endDate
                          ? "_dispatch_date"
                          : "_acceptance_date"
                          ) | translate
                          }}
                        </div>
                        <div class="col-7 two-dots">
                          {{
                          (serviceRequest?.workOrderStatus?.endDate
                          ? serviceRequest?.workOrderStatus?.endDate
                          : serviceRequest?.startDate
                          ) | date: "dd.MM.YYYY HH:mm" || "-"
                          }}
                        </div>
                      </div>
                    </ng-container>
                  </div>

                  <div *ngIf="serviceRequest?.workOrderStatus">
                    <div class="row no-gutters pt-1" *ngIf="serviceRequest?.workOrderStatus?.currentStatusName">
                      <div class="col-5">{{ "_status" | translate }}</div>
                      <div class="col-7 two-dots align-self-center">
                        {{
                        serviceRequest?.workOrderStatus?.currentStatusName ||
                        "-"
                        }}
                      </div>
                    </div>
                  </div>
                  <!-- ? Service Location Tracking -->
                  <div *ngIf="
                      serviceRequest?.workOrderStatus?.hasArventoNode ||
                      serviceRequest?.workOrderStatus?.servicePlateNumber
                    ">
                    <div class="row no-gutters pt-1">
                      <div class="col-5">
                        {{ "_service_car_plate" | translate }}
                      </div>
                      <div class="col-7 two-dots d-flex">
                        <div class="flex-grow-1">
                          <div>
                            {{
                            serviceRequest?.workOrderStatus
                            ?.servicePlateNumber || "-"
                            }}
                          </div>
                          <div appClickLog [section]="'SERVICE'" [subsection]="'SERVICE_MAP'" [data]="{
                              serviceNumber: serviceRequest.serviceNumber
                            }" (click)="goServiceMap(serviceRequest)" *ngIf="
                              serviceRequest?.workOrderStatus?.hasArventoNode &&
                              serviceLocationShow
                            " class="text-success" style="word-wrap: break-word">
                            {{ "_service_map_location_tracking" | translate }}
                          </div>
                        </div>
                        <div appClickLog [section]="'SERVICE'" [subsection]="'SERVICE_MAP'" [data]="{
                            serviceNumber: serviceRequest.serviceNumber
                          }" (click)="goServiceMap(serviceRequest)" *ngIf="
                            serviceRequest?.workOrderStatus?.hasArventoNode &&
                            serviceLocationShow
                          " class="d-flex justify-content-center align-items-center">
                          <div class="align-items-center">
                            <img height="26" [src]="mapLocation" alt="Map"/>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="row no-gutters pt-1">
                    <div class="col-5">{{ "_description" | translate }}</div>
                    <div class="col-7 two-dots align-self-center">
                      <span class="align-self-center" style="word-wrap: break-word">
                        {{ serviceRequest?.description || notification?.body || "-" }}
                      </span>
                    </div>
                  </div>

                  <!-- ? Service File -->
                  <!-- <div *ngIf="serviceRequest.workOrderStatus">
                    <div class="pt-2" *ngIf="serviceRequest.workOrderStatus.attachments.length > 0">
                      <div [@loadMore] class="row no-gutters d-flex align-items-center py-2" *ngFor="
                                  let attachment of getAttachments(serviceRequest)
                                " catUserClick [section]="'SERVICE_LIST'" [subsection]="'ATTACHMENT_DOWNLOAD'"
                        [data]="{attachmentId: attachment.id}" catDownloadFile [downloadType]="DownloadTypeEnum.attachment"
                        [downloadParams]="{
                                  attachment: attachment,
                                  serviceOrganization: serviceRequest.serviceOrganization,
                                  workOrderNumber: serviceRequest.workOrderStatus.workOrderNumber
                                }" (downloadLoading)="loadingAttachment = $event ? attachment.id : null">
                        <div [id]="attachment.id"
                          class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
                          [class.spinner]="loadingAttachment === attachment.id">
                          <i class="icon icon-spinner8" *ngIf="loadingAttachment === attachment.id"></i>
                          <i class="icon icon-download" *ngIf="loadingAttachment !== attachment.id"></i>
                          <a class="d-none" [download]="attachment.id"></a>
                        </div>
                        <div>
                          <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
                            {{
                            (attachment.uploadType === 23 ? "_dispatch_report"
                            : attachment.uploadType === 18 ? "_reception_report"
                            : attachment.uploadType === 2 ? "_technical_report"
                            : attachment.uploadType === 5 ? "_test_report"
                            : attachment.uploadType === 0 ? "_unknown_attachment"
                            : attachment.uploadTypeDescription === "SERVICE_FORM" ? "_service_form"
                            : ""
                            ) | translate
                            }}
                          </div>
                          <div *ngIf="attachment.dateUpload" class="text-muted font-size-12px ml-3">
                            {{ customDate(attachment.dateUpload) | date: 'shortDate' }}
                          </div>
                        </div>
                      </div>
                      <div (click)="lessMoreFunc(serviceRequest.serviceNumber)"
                        *ngIf="serviceRequest.workOrderStatus.attachments.length > 5" class="mt-1 text-center">
                        <div class="font-weight-medium">
                          {{ (loadMore[serviceRequest.serviceNumber] ? '_less' : '_more') | translate }}
                        </div>
                      </div>
                    </div>
                  </div> -->

                  <!-- ? Survey -->
                  <!-- <div class="pt-2 border-top" *ngIf="serviceRequest.surveys as survey">
                    <div catUserClick [section]="'SERVICE_LIST'" [subsection]="'SURVEY_CLICK'"
                      [data]="{ surveyId: survey.id }" class="row no-gutters d-flex align-items-center py-2"
                      (click)="openSurvey(survey)">
                      <div
                        class="survey border text-info border-info rounded-circle d-flex justify-content-center align-items-center">
                        <i class="icon icon-contract"></i>
                      </div>

                      <div class="survey-name text-info ml-3 font-size-14px font-weight-bold">
                        {{ "_service_survey" | translate }}
                      </div>
                    </div>
                  </div> -->

                </div>
              </div>
              <!-- Service Detail - END -->
              <!-- Service Not Found - START -->
              <div *ngIf="!loading && !serviceRequest" style="min-height: 150px"
                class="d-flex justify-content-center align-items-center">
                <div class="h3">
                  <div class="text-wrap text-center font-size-18px">
                    {{ "_work_order_list_empty" | translate }}
                  </div>
                </div>
              </div>
              <!-- Service Not Found - END -->
              <div *ngIf="loading" style="min-height: 150px"
                class="spinner d-flex justify-content-center align-items-center">
                <i class="icon icon-spinner8"></i>
              </div>
            </div>
          </ng-template>
        </ngb-panel>
      </ng-container>
    </ngb-accordion>
    <!-- ? Customer Name -->
    <div *ngIf="notification?.customerName" class="label-text pt-2 customer-name-card">
      <span>{{
        notification?.customerName?.lenght > 35
          ? (notification?.customerName | slice: 0:32) + "..."
          : notification?.customerName
        }}
      </span>
    </div>
    <div class="d-flex flex-column mt-5">
      <button *ngIf="customerNumber" type="button" class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white" (click)="goServiceModule()">
        {{ "_go_to_service" | translate }}
      </button>
    </div>
  </div>
  <ng-template #nonJob>
    <div class="d-flex flex-column mx-3 px-3 mb-1 align-middle mt-5 pt-5">
      <div class="text-center mb-2">
        <img [src]="notificationIcon" [alt]="'_detail_notification_not_found' | translate"/>
      </div>
      <div class="h3 mb-5">
        <div class="text-wrap text-center font-size-22px">
          {{ "_detail_notification_not_found" | translate }}
        </div>
      </div>
    </div>
  </ng-template>
</div>
