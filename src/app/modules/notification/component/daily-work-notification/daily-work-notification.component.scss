@import "variable/bootstrap-variable";

.daily-work-plan {
  .ndet {
    &-header {
      h4 {
        color: #2C2C2C;
        font-weight: 700;
      }
    }

  }

  .icon-headset:before {
    color: #ffffff;
  }

  ::ng-deep ngb-accordion .card {
    border-radius: 6px;
    // margin-bottom: 1rem;
  }

  ::ng-deep ngb-accordion .card-body {
    padding: 0.7em;
  }

  ::ng-deep ngb-accordion .card-header {
    border-radius: 0;
    padding: 0;
    background-color: #f9f9f9;
    border: none;

    .btn {
      width: 100%;
      float: left;
      text-align: left !important;
      box-shadow: none;
      border-radius: 0;
      //border: none;
      border-bottom: 1px solid rgba(0, 0, 0, 0.125);
      font-family: roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      text-decoration: none;
    }

    .btn-link:hover {
      box-shadow: none;
      text-decoration: none;
      //color: #4a8eb0;
    }


    button:not(.collapsed) {
      .icon-chevron-down {
        transform: rotate(-180deg);
      }
    }

    button.collapsed .fw-bolder,
    button:not(.collapsed) .fw-bolder {
      font-weight: 600;
    }

    .icon-chevron-down {
      line-height: 1em;
      height: 1em;
      transition: all 0.4s ease;
    }

  }

  ngb-accordion .btn:focus .btn:hover {
    box-shadow: none;
  }

  .service-request {
    &-item {
      background: #ffffff;
      mix-blend-mode: normal;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05), 0 10px 20px rgba(0, 0, 0, 0.1);
      border-radius: 6px;
      position: relative;
      font-family: roboto, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      color: #2c2c2c;
      // border: 1px solid #d8d8d8;

      &-detail {
        margin-left: 15px;
      }

      // border: 1px solid #d8d8d8;
      & * {
        word-break: break-word;
      }

      &-color {
        position: absolute;
        width: 10px;
        height: 100%;
        left: 0;
        top: 0;
        border-top-left-radius: 6px;
        border-bottom-left-radius: 6px;
        z-index: 1;

        &-green {
          background: #5d8d1c;
        }

        &-orange {
          background: $warning;
        }
      }
    }
  }

  .two-dots {
    padding-left: 25px;

    &:before {
      display: inline-block;
      content: ":";
      margin-left: -25px;
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .date-text {
    font-size: 13px;
    line-height: 1.4em;
    color: #8e8e8e;
  }

  .loading {
    min-height: 70px;
    position: relative;
    margin: 0 15px 15px 0;
    padding: 15px;
  }

  .wrench-empty {
    font-size: 40px;
  }

  .label-text {
    font-size: 13px;
  }

  .download-file {
    width: 40px;
    height: 40px;
    background-color: #f5f5f5;
    font-size: 18px;
  }

  .survey {
    width: 40px;
    height: 40px;
    background-color: #f5f5f5;
    font-size: 18px;
  }

  .service-menu-button {
    box-shadow: none;
    line-height: 1.5em;
    font-size: 20px;
  }

  .action-btn {
    font-size: 16px;
    font-weight: 700;
  }

}

.customer-name-card {
  padding: 0.75rem 1rem;
  border: 1px solid #d8d8d8;
  border-radius: 6px;
  background: #f9f9f9;
}
