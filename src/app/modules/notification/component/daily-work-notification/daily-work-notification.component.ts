import {Component, ElementRef, Input, OnInit} from '@angular/core';
import {Select, Store} from '@ngxs/store';
import {Observable} from 'rxjs';
import {CustomerModuleService} from 'src/app/modules/customer/service/customer-module.service';
import {CustomerService} from 'src/app/modules/customer/service/customer.service';
import {ContactWorkingHoursModel} from 'src/app/shared/models/contact-working-hours.model';
import {ContactWorkingHoursAction} from 'src/app/shared/state/settings/settings.actions';
import {SettingsState} from 'src/app/shared/state/settings/settings.state';
import {environment} from 'src/environments/environment';
import * as moment from 'moment-timezone';
import {NotificationState} from '../../state/notification/notification.state';
import {NotificationModel} from '../../model/notification.model';
import {FrameMessageEnum} from '../../../../core/enum/frame-message.enum';
import {FrameMessageService} from '../../../../core/service/frame-message.service';
import {ModalService} from '../../../../shared/service/modal.service';
import {TranslateService} from '@ngx-translate/core';
import {Router} from '@angular/router';

@Component({
  selector: 'app-daily-work-notification',
  templateUrl: './daily-work-notification.component.html',
  styleUrls: ['./daily-work-notification.component.scss']
})
export class DailyWorkNotificationComponent implements OnInit {

  @Input()
  notification: any;

  dailyWork: any;

  loading: boolean;
  notificationIcon = `${environment.assets}/notifications.svg`;
  serviceRequest: any;
  selectedServiceNumber: string;
  mapLocation = `${environment.assets}/map_location.png`;
  serviceLocationShow = true;
  customerNumber: string;

  @Select(SettingsState.contactWorkingHours) contactWorkingHours$: Observable<ContactWorkingHoursModel[]>;
  contactWorkingHours: ContactWorkingHoursModel[];
  isShowCallCenterModal: boolean;
  @Select(NotificationState.notifications)
  notifications$: Observable<NotificationModel<any>[]>;

  constructor(
    private readonly customerService: CustomerService,
    private readonly customerModuleService: CustomerModuleService,
    private readonly store: Store,
    private readonly messageService: FrameMessageService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly router: Router,
    private readonly elementRef: ElementRef
  ) { }

  ngOnInit() {
    this.loading = true;
    this.dailyWork = this.notification.actionType === 'DailyWorkOrderReport'
      ? JSON.parse(this.notification.action?.PlannedJobs || this.notification.action?.plannedJobs)
      : [this.notification.action];

    this.store.dispatch(new ContactWorkingHoursAction());
    this.contactWorkingHours$.subscribe((res) => {
      this.contactWorkingHours = res;
    });
  }

  getDailyHeader() {
    const t = {
      DailyWorkOrderReport: '_daily_work_order',
      DailyPlannedWorkCreated: '_daily_work_created',
      DailyPlannedWorkUpdated: '_daily_work_updated',
      DailyPlannedWorkDeleted: '_daily_work_deleted',
    };
    return t[this.notification.actionType];
  }

  toggleAccordion(e) {
    this.customerNumber = this.getCustomerNumber();
    if (!this.customerNumber) {
      this.notifications$.subscribe(nf => {
        if (nf) {
          this.customerNumber = nf.find(x => x.messageId === this.notification.messageId).customerNumber;
        }
        this.loading = false;
      });
    }
    this.selectedServiceNumber = this.dailyWork[e.panelId.slice(3)]?.WorkOrderNumber || this.dailyWork[e.panelId.slice(3)]?.workOrderNumber;
    if (!this.serviceRequest || this.serviceRequest?.serviceNumber !== this.selectedServiceNumber) {
      const workorderNumber = this.dailyWork[e.panelId.slice(3)]?.WorkOrderNumber
        || this.notification.action?.workOrderId
        || this.dailyWork[e.panelId.slice(3)]?.workOrderNumber;
      this.getServiceDetail(workorderNumber, this.customerNumber);
    }
  }

  getServiceDetail(workorderNumber, customerNumber) {
    this.loading = true;
    this.customerService.serviceDetail(
      workorderNumber,
      customerNumber)
      .subscribe(data => {
          this.serviceRequest = data;
          this.loading = false;
        },
        err => this.loading = false
      );
  }

  goServiceModule() {
    // TODO Servis detay sayfasına yönlendirilecek yada scroll özelliği verilecek
    this.customerModuleService.openServiceModule(
      this.getCustomerNumber(), this.selectedServiceNumber);
  }

  goServiceMap(service) {
    const serviceMapRedirect = true;
    this.customerModuleService.openServiceModule(
      this.getCustomerNumber(),
      this.selectedServiceNumber,
      serviceMapRedirect);
  }

  getStatusLabel(serviceRequest: any) {
    // do we need to translate
    switch (serviceRequest.serviceStatus) {
      case 'Completed':
        return '_completed';
      case 'InProgress':
        return '_continues';
      default:
        return '_continues';
    }
    // return serviceRequest.serviceStatus !== 'InProgress';
  }

  onCall(phone: string) {
    this.messageService.sendMessage(FrameMessageEnum.call_phone, {phone});
  }

  onClickCallCenter() {
    if (!this.checkWorkingHours('CONTACTUS')) {
      return;
    }

    const values = this.findElement('CONTACTUS').phoneNumbers;
    if (values.length > 1) {
      this.isShowCallCenterModal = true;
    } else {
      this.onCall(values[0]);
    }
  }

  private findElement(code) {
    if (this.contactWorkingHours?.length > 0) {
      return this.contactWorkingHours.find((c) => c.code === code);
    }
    return null;
  }

  private checkWorkingHours(key) {
    const hour = this.findElement(key);
    if (hour) {
      const time = moment.tz(hour.workTimezoneCode || 'UTC').format('HH:mm');
      console.log('time', time);
      if (!(time > hour.workStartTime && time < hour.workEndTime)) {
        const modal = this.modalService.errorModal({
          message: this.translateService.instant('_working_hours_error'),
          button: this.translateService.instant('_write_us'),
          buttonClick: () => {
            this.router.navigate(['contact', 'writeus']);
            modal.close(true);
          }
        }, this.elementRef.nativeElement);

        return false;
      }
    }
    return true;
  }

  contactValues(code) {
    return this.findElement(code).phoneNumbers;
  }

  get isShowCallCenter() {
    return this.findElement('CONTACTUS');
  }

  getStartDate() {
    const a = this.notification.action;
    return a.newWorkStartDate || a.newStartDate || a.oldStartDate || a.oldWorkStartDate || null;
  }

  getEndDate() {
    const a = this.notification.action;
    return a.newWorkEndDate || a.newEndDate || a.oldEndDate || a.oldWorkEndDate || null;
  }

  protected getCustomerNumber() {
    return this.notification?.customerNumber
      || this.notification?.action?.customerNumber
      || this.notification?.action?.CustomerNumber
      || this.notification?.CustomerNumber;
  }
}
