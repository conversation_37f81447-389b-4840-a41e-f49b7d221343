<div *ngIf="notification">
  <div class="ndet-header mb-4">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ "_dept_notification" | translate }}</h4>
      </div>
      <div class=""></div>
    </div>
  </div>
  <div class="pt-4"></div>
  <div class="notification-item overflow-hidden mb-3">
    <div
      [ngClass]="{
        'notification-item-color-green':
          notification.actionType === notificationActionTypeEnum.FinancialsForwardTermDebt,
        'notification-item-color-orange':
          notification.actionType === notificationActionTypeEnum.FinancialsCurrentDebt
      }"
      class="notification-item-color notification-item-color-green"
    ></div>
    <div class="status-pop pb-3 pr-3">
      <div
        *ngIf="
          notification?.action?.customerNumber || notification?.customerNumber
        "
        class="d-flex mb-4"
      >
        <div class="flex-fill" *ngIf="notification?.title">
          <div class="font-weight-bold text-secondary">
            {{ notification?.title }}
          </div>
        </div>
      </div>
      <div>
        <div class="row pt-1" *ngIf="notification?.action?.debtAmount !== null || notification?.action?.debtAmount !== ''">
          <div class="col-5"> {{ "_debt_amount" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.debtAmount }} {{ notification?.action?.debtCurrency  }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="notification?.action?.debtDate">
          <div class="col-5"> {{ "_debt_date" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.debtDate | date: "dd.MM.Y" }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="notification?.action?.expiryDate">
          <div class="col-5"> {{ "_debt_expiry_date" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.expiryDate | date: "dd.MM.Y" }}
          </div>
        </div>
      </div>
      <div class="label-text pt-2">
        <span>{{
          getCustomer(
            notification?.action?.customerNumber || notification?.customerNumber
          )?.customer?.name.length > 35
            ? (getCustomer(
                notification?.action?.customerNumber ||
                  notification?.customerNumber
              )?.customer?.name | slice: 0:32) + "..."
            : getCustomer(
                notification?.action?.customerNumber ||
                  notification?.customerNumber
              )?.customer?.name
        }}</span>
      </div>
    </div>
  </div>
  <div class="d-flex flex-column">
    <button
      type="button"
      class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white"
      (click)="openfinancialModule()"
    >
      {{ "_financials" | translate }}
    </button>
  </div>
</div>
