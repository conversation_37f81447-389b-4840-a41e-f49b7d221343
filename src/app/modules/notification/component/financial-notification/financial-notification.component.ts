import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CustomerRelationModel } from 'src/app/modules/customer/model/customer-relation.model';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';

@Component({
  selector: 'app-financial-notification',
  templateUrl: './financial-notification.component.html',
  styleUrls: ['./financial-notification.component.scss']
})
export class FinancialNotificationComponent implements OnInit, OnDestroy {
  @Input()
  notification: any;

  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;

  customersList: CustomerRelationModel[];
  notificationActionTypeEnum = NotificationActionTypeEnum;

  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly customerModuleService: CustomerModuleService,
    ) { }

  ngOnInit() {
    this.customers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customers => {
        this.customersList = customers;
      });

  }

  getCustomer(customerNumber: string) {
    if (!customerNumber) {
      return null;
    }
    return this.customersList.find(customer => customer.customer?.customerNumber === customerNumber);
  }

  openfinancialModule(){
    this.customerModuleService.openFinancialModule(this.notification.action?.customerNumber || this.notification?.customerNumber);
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
