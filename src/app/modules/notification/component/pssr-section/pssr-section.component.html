<div class="d-flex justify-content-between my-3 g-2 w-100">
  <div class="d-flex align-items-center btn btn-info btn-sm mr-1 text-left contact-button-left" appClickLog [section]="'NOTIFICATION'"
    [subsection]="'CALL_CENTER_CLICK'" [class.mr-2]="
    notification?.action?.callCallCenterButtonEnabled === 'true'
  " *ngIf="
    notification?.action?.callCallCenterButtonEnabled === 'true' &&
    isShowCallCenter || showAllButtons
  " (click)="onClickCallCenter()">
    <i class="icon icon-headset mx-2"></i>
    {{ "_call_center" | translate }}
  </div>
  <div class="d-flex align-items-center btn btn-info btn-sm  text-left contact-button" routerLink="/contact/customerservice" appClickLog
    [section]="'NOTIFICATION'" [subsection]="'CONTACT_REPRESENTATIVE_CLICK'" [class.mr-2]="notification?.action?.callSIMButtonEnabled === 'true'"
    *ngIf="notification?.action?.callPssrButtonEnabled === 'true' || showAllButtons">
    <i class="icon icon-phone-call mx-2"></i>
    {{ "_contact_with_customer_service" | translate }}
  </div>
</div>
<app-basic-modal *ngIf="isShowCallCenterModal" [(status)]="isShowCallCenterModal"
  [headerText]="'_call_center' | translate">
  <div class="service-content">
    <ul class="service-person-phones">
      <li class="service-person-phone" *ngFor="let number of contactValues('CONTACTUS')" (click)="onCall(number)">
        <i class="icon icon-phone"></i>
        {{ number }}
      </li>
    </ul>
  </div>
</app-basic-modal>
