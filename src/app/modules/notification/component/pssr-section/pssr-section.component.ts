import { Component, OnInit, ElementRef, Input } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import * as moment from 'moment';
import { Observable } from 'rxjs';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { ContactWorkingHoursModel } from 'src/app/shared/models/contact-working-hours.model';
import { ModalService } from 'src/app/shared/service/modal.service';
import { ContactWorkingHoursAction } from 'src/app/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';

@Component({
  selector: 'app-pssr-section',
  templateUrl: './pssr-section.component.html',
  styleUrls: ['./pssr-section.component.scss']
})
export class PssrSectionComponent implements OnInit {

  constructor(
    private readonly store: Store,
    private readonly messageService: FrameMessageService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly router: Router,
    private readonly elementRef: ElementRef,
  ) { }

  @Select(SettingsState.contactWorkingHours) contactWorkingHours$: Observable<ContactWorkingHoursModel[]>;
  contactWorkingHours: ContactWorkingHoursModel[];
  @Input()
  showAllButtons: boolean;
  @Input()
  notification: any;
  isShowCallCenterModal: boolean;

  ngOnInit() {
    this.store.dispatch(new ContactWorkingHoursAction());
    this.contactWorkingHours$.subscribe((res) => {
      this.contactWorkingHours = res;
    });

  }

  onCall(phone: string) {
    this.messageService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onClickCallCenter() {
    if (!this.checkWorkingHours('CONTACTUS')) {
      return;
    }

    const values = this.findElement('CONTACTUS').phoneNumbers;
    if (values.length > 1) {
      this.isShowCallCenterModal = true;
    } else {
      this.onCall(values[0]);
    }
  }

  private findElement(code) {
    if (this.contactWorkingHours?.length > 0) {
      return this.contactWorkingHours.find((c) => c.code === code);
    }
    return null;
  }

  private checkWorkingHours(key) {
    const hour = this.findElement(key);
    if (hour) {
      const time = moment.tz(hour.workTimezoneCode || 'UTC').format('HH:mm');
      console.log('time', time);
      if (!(time > hour.workStartTime && time < hour.workEndTime)) {
        const modal = this.modalService.errorModal({
          message: this.translateService.instant('_working_hours_error'),
          button: this.translateService.instant('_write_us'),
          buttonClick: () => {
            this.router.navigate(['contact', 'writeus']);
            modal.close(true);
          }
        }, this.elementRef.nativeElement);

        return false;
      }
    }
    return true;
  }

  contactValues(code) {
    return this.findElement(code).phoneNumbers;
  }
  get isShowCallCenter() {
    return this.findElement('CONTACTUS');
  }

  getStartDate() {
    const a = this.notification.action;
    return a.newWorkStartDate || a.newStartDate || a.oldStartDate || a.oldWorkStartDate || null;
  }
  getEndDate() {
    const a = this.notification.action;
    return a.newWorkEndDate || a.newEndDate || a.oldEndDate || a.oldWorkEndDate || null;
  }
}
