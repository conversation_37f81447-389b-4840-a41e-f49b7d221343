<div class="linked-products d-flex justify-content-between flex-column">

  <div class="master">
    <div class="ndet-header">
      <div class="d-flex justify-content-between">
        <div>
          <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
        </div>
        <!-- <div class="d-flex justify-content-center"> -->
        <!-- <h4>{{ '_linked_products' | translate }}</h4> -->
        <!-- </div> -->
        <div class="">
        </div>
      </div>
    </div>

<!--    <ng-container-->
<!--      *ngIf="notification?.action?.innerHtml else imageContainer">-->
<!--      <div class="content">-->
<!--        <div [innerHTML]="notification?.action?.innerHtml | safeHtml"></div>-->
<!--      </div>-->
<!--    </ng-container>-->
<!--    <ng-template #imageContainer>-->
    <div class="content" *ngIf="notification?.action?.imageUrl">
      <div>
        <div class="col-12 text-center h4" *ngIf="headerText">{{ headerText }}</div>
        <div class="content-text mb-3" *ngIf="bodyText">
          {{ bodyText }}
        </div>
        <div class="notification-item overflow-hidden mb-3">
          <img [src]="notification?.action?.imageUrl" alt="" class="img">
        </div>
      </div>
    </div>
<!--    </ng-template>-->
  </div>

  <div class="footer">
    <span class="mb-2">{{
      getCustomer(
      notification?.action?.customerNumber || notification?.customerNumber
      )?.customer?.name.length > 35
      ? (getCustomer(
      notification?.action?.customerNumber ||
      notification?.customerNumber
      )?.customer?.name | slice: 0:32) + "..."
      : getCustomer(
      notification?.action?.customerNumber ||
      notification?.customerNumber
      )?.customer?.name
      }}</span>
      <button type="button" class="action-btn btn btn-warning mb-2 text-white w-100" (click)="goBack()"> {{
        "_return_back" | translate }} </button>
  </div>

</div>
