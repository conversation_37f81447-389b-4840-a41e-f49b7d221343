import { Component, OnInit, Input, OnDestroy } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CustomerRelationModel } from 'src/app/modules/customer/model/customer-relation.model';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';
import { NotificationModel } from '../../model/notification.model';

@Component({
  selector: 'app-picture-notification',
  templateUrl: './picture-notification.component.html',
  styleUrls: ['./picture-notification.component.scss']
})
export class PictureNotificationComponent implements OnInit, OnDestroy {


  @Input()
  notification: NotificationModel<NotificationActionTypeEnum.PictureNotification>;
  bodyText: string;
  headerText: string;

  @Select(UserState.customers)
  customers$: Observable<CustomerRelationModel[]>;

  customersList: CustomerRelationModel[];
  notificationActionTypeEnum = NotificationActionTypeEnum;


  constructor(

    ) { }

  protected subscriptions$: Subject<boolean> = new Subject();
  ngOnInit() {
    this.customers$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(customers => {
      this.customersList = customers;
    });
    this.bodyText = (this.notification.action?.body) || (this.notification?.body);
    this.headerText = (this.notification.action?.title) || (this.notification?.title);
  }

  getCustomer(customerNumber: string) {
    if (!customerNumber) {
      return null;
    }
    return this.customersList.find(customer => customer.customer?.customerNumber === customerNumber);
  }

  goBack() {
    history.back();
  }


  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
