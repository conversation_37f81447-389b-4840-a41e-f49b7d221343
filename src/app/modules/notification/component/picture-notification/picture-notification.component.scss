@import "variable/bootstrap-variable";
.linked-products{
  height: calc(100vh - 110px);
  overflow-y: auto;
}

.ndet {
  &-header {
    h4 {
      color: #2C2C2C;
      font-weight: 700;
    }
  }

}
.img {
  width: 100%;
  min-height: 85px;
  //max-height: 200px;
  border: 1px solid #f1f1f1;
  box-shadow: rgba(0, 0, 0, 0.24) 0 3px 8px;
  border-radius: 8px;
}
.notification {
  &-item {
    background: #FFFFFF;
    mix-blend-mode: normal;
    border-radius: 6px;
    position: relative;
    min-width: 308.675px;
  }
}

.label-text {
  font-size: 13px;
}

.action-btn {
  font-size: 16px;
  font-weight: 700;
}

.btn-warning {
  background-color: $warning;

  &:focus {
    background-color: $warning;
    border-color: $warning;
  }

  &:hover {
    background-color: $warning;
    border-color: $warning;
  }

}
.content {
  width: 100%;
}

.footer {
  font-size: 13px;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 55%;
  justify-content: flex-end;
  align-items: center;
}
