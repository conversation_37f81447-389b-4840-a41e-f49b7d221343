<div class="ndet-header">
  <div class="d-flex justify-content-between ">
    <div>
      <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
    </div>
    <div class="">
      <h4>{{'_notifications' | translate}}</h4>
    </div>
    <div class="">
    </div>
  </div>
</div>

<div class="notification-item overflow-hidden mb-3 mt-3">
  <div class="status-pop pb-3 pr-3">
    <div
      class="d-flex justify-content-between mb-2 overflow-hidden align-items-end"
    >
      <div class="font-weight-semi-bold h6 m-0">
        {{notification?.title}}
      </div>
    </div>
    <div class="label-text">
      {{ notification?.body }}
    </div>
    <div class="label-text pt-2">
      <!-- <span>{{ getViewCustomerName(notify?.customerName || getCustomerName(notify.customerNumber)?.name) }}</span> -->
      {{ notification?.customerName?.length > 35 ? (notification?.customerName | slice: 0:32) + "..." : notification?.customerName }}
    </div>
  </div>
</div>