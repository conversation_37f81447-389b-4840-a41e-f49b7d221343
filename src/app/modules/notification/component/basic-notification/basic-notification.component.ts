import { Component, Input, OnInit } from '@angular/core';
import { NotificationModel } from '../../model/notification.model';
import { NotificationActionTypeEnum } from '../../enum/notification-action-type.enum';

@Component({
  selector: 'app-basic-notification',
  templateUrl: './basic-notification.component.html',
  styleUrls: ['./basic-notification.component.scss'],
})
export class BasicNotificationComponent implements OnInit {
  @Input()
  notification: NotificationModel<NotificationActionTypeEnum.SoundDiagnostic>;

  constructor() { }

  ngOnInit(): void {
  }

}
