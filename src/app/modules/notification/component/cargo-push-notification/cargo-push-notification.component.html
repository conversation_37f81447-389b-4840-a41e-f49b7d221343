<div *ngIf="notification">
  <div class="ndet-header mb-4">
    <div class="d-flex justify-content-between">
      <div>
        <i class="icon icon-back font-size-18px" [routerLink]="'../'"></i>
      </div>
      <div class="">
        <h4>{{ "_cargo_status_notification" | translate }}</h4>
      </div>
      <div class=""></div>
    </div>
  </div>
  <div class="pt-4"></div>
  <div class="notification-item overflow-hidden mb-3">
    <div class="status-pop pb-3 pr-3">
      <div
        *ngIf="
          notification?.action?.customerNumber || notification?.customerNumber
        "
        class="d-flex mb-4"
      >
        <div class="flex-fill" *ngIf="notification?.title">
          <div class="font-weight-bold text-secondary notification-header">
            {{ notification?.title }}
          </div>
        </div>
      </div>
      <div>
        <div class="row pt-1" *ngIf="notification?.action?.orderId">
          <div class="col-5 align-div"> {{ "_order_id" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.orderId }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="notification?.action?.cargoKey">
          <div class="col-5 align-div"> {{ "_cargo_key" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.cargoKey }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="notification?.action?.cargoStatus">
          <div class="col-5 align-div"> {{ "_cargo_status" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.cargoStatus }}
          </div>
        </div>
        <div class="row pt-1" *ngIf="notification?.action?.cargoUnit">
          <div class="col-5 align-div"> {{ "_cargo_unit" | translate }} </div>
          <div class="col-6">
            : {{ notification?.action?.cargoUnit }}
          </div>
        </div>
      </div>
      <div class="label-text pt-2 align-div align-div-company-name">
        <span>{{ notification?.customerName?.length > 35 ? (notification?.customerName | slice: 0:32) + "..." : notification?.customerName }}</span>
      </div>
    </div>
  </div>
  <div *ngIf="showSparePartButton" class="d-flex flex-column mt-3 mb-5">
    <button type="button" class="action-btn btn btn-info btn-sm px-4 mb-2 text-white" (click)="goToSparePart(true)">
      {{ "_detail" | translate }}
    </button>
    <button type="button" class="action-btn btn btn-warning btn-sm px-4 mb-2 text-white" (click)="goToSparePart()">
      {{ "_boom_shop" | translate }}
    </button>
  </div>
</div>
