import { ComponentFixture, TestBed } from '@angular/core/testing';

import { CargoPushNotificationComponent } from './cargo-push-notification.component';

describe('CargoPushNotificationComponent', () => {
  let component: CargoPushNotificationComponent;
  let fixture: ComponentFixture<CargoPushNotificationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ CargoPushNotificationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(CargoPushNotificationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
