import { Component, Input, OnInit } from '@angular/core';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { environment } from 'src/environments/environment';
import { ModuleCodeEnum } from '../../../../shared/enum/module-code.enum';

@Component({
  selector: 'app-cargo-push-notification',
  templateUrl: './cargo-push-notification.component.html',
  styleUrls: ['./cargo-push-notification.component.scss']
})
export class CargoPushNotificationComponent implements OnInit {

  @Input() notification: any;

  notificationIcon = `${environment.assets}/notifications.svg`;
  showSparePartButton = true;
  customerNumber: string;

  constructor(
    private readonly customerModuleService: CustomerModuleService,
  ) { }

  ngOnInit(): void {
    this.customerNumber = this.notification?.customerNumber || this.notification?.action.customerNumber
      || this.notification?.CustomerNumber || this.notification?.action.CustomerNumber;
    this.showSparePartButton = !!(this.customerModuleService.findCustomerWithModule(this.customerNumber, ModuleCodeEnum.SparePart));
  }

  goToSparePart(goOrder?: boolean): void {
    if (goOrder) {
      const options = this.notification.action.orderId ?
        {query: 'page=order-detail&parameters=' + this.notification.action.orderId} :
        {query: 'page=orders'};
      this.customerModuleService.openSparePartModule(this.notification.customerNumber, options);
      return;
    }
    this.customerModuleService.openSparePartModule(this.notification.customerNumber);
  }

}
