export enum NotificationActionTypeEnum {
  DetailPush = 'DetailPush',
  News = 'News',
  Link = 'Link',
  ApplicationStatusChange = 'ApplicationStatusChange',
  QuotationDetail = 'QuotationDetail',
  EquipmentInfo = 'EquipmentInfo',
  NewCampaign = 'NewCampaign',
  OrderStatusChange = 'OrderStatusChange',
  ServiceStatusChange = 'ServiceStatusChange',
  SoundDiagnostic = 'SoundDiagnostic',
  AlternatingPush = 'AlternatingPush',
  Feedback = 'Feedback',
  FinancialInfo = 'FinancialInfo',
  SilentPush = 'SilentPush',
  ServiceSurvey = 'ServiceSurvey',
  Rate_Us = 'Rate_Us',
  RateUsForm = 'RateUsForm',
  ApplicationApproved = 'ApplicationApproved',
  FinancialsCurrentDebt = 'FinancialsCurrentDebt',
  FinancialsForwardTermDebt = 'FinancialsForwardTermDebt',
  QuotationExpire = 'QuotationExpire',
  QuotationUpdated = 'QuotationUpdated',
  QuotationCrmStatusPush = 'QuotationCrmStatusPush',
  NewQuotation = 'NewQuotation',
  OpenModule = 'OpenModule',
  Muneccim = 'Muneccim',
  ApprovalAgreementUpdated = 'ApprovalAgreementUpdated',
  InformalAgreementUpdated = 'InformalAgreementUpdated',
  ProductSos = 'ProductSos',
  DailyWorkOrderReport = 'DailyWorkOrderReport',
  DailyPlannedWorkCreated = 'DailyPlannedWorkCreated',
  DailyPlannedWorkUpdated = 'DailyPlannedWorkUpdated',
  DailyPlannedWorkDeleted = 'DailyPlannedWorkDeleted',
  AgreementChange = 'AgreementChange',
  ServiceDocumentUploaded = 'ServiceDocumentUploaded',
  CargoStatusChange = 'CargoStatusChange',
  RfmsPush = 'RfmsPush',
  LoyalityPointPush = 'LoyalityPointPush',
  PromotionPush = 'PromotionPush',
  LinkedProducts = 'LinkedProducts',
  PictureNotification = 'PictureNotification',
  ReconciliationAssignmentPush = 'ReconciliationAssignmentPush',
  PseCampaignPush = 'PseCampaignPush',
  InsiderPush = 'InsiderPush',
  EquipmentAgendaCheckoutInformationPush = 'EquipmentAgendaCheckoutInformationPush',
  EquipmentAgendaNoteReminderPush = 'EquipmentAgendaNoteReminderPush',
  ServiceChat = 'WorkOrderChatPush',
  SurveyCampaign = 'SurveyCampaign',
  AccountManagerApproveReminder = 'AccountManagerApproveReminder',
  AccountManagerNewApproval = 'AccountManagerNewApproval',
  AccountManagerActionReminder = 'AccountManagerActionReminder',
  BoomShopBasketReminderPush = 'BoomShopBasketReminderPush',
  BoomShopSpecialReminderPush = 'BoomShopSpecialReminderPush',
  BoomShopFileUploadPush = 'BoomShopFileUploadPush',
  ActivitySurveyReminder = 'ActivitySurveyReminder',
  NewActivitySurvey = 'NewActivitySurvey',
  NewPulseSurvey = 'NewPulseSurvey',
  BoomGuru = 'BoomGuru',
}
