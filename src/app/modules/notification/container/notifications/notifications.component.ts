import {
  AfterContentChecked,
  ChangeDetector<PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON>roy,
  OnInit,
} from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusAction } from '../../../customer/state/customer/customer.actions';
import { NotificationState } from '../../state/notification/notification.state';
import { Observable } from 'rxjs';
import { IframeAction } from '../../../customer/state/iframe/iframe.actions';
import { NotificationUpdateAction } from '../../state/notification/notification.actions';
import { TranslatePipe } from '@ngx-translate/core';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
  providers: [TranslatePipe],
})
export class NotificationsComponent
  implements OnInit, OnDestroy, AfterContentChecked {
  @Select(NotificationState.loading)
  loading$: Observable<boolean>;

  constructor(
    private readonly store: Store,
    private readonly cdRef: ChangeDetectorRef,
    private readonly translatePipe: TranslatePipe
  ) {}

  ngOnInit(): void {
    // turn of new notification icon
    this.store.dispatch(
      new NotificationUpdateAction({
        newIcon: false,
      })
    );

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        title: this.translatePipe.transform('_notifications'),
        hamburgerMenu: true,
        notificationIcon: false,
      })
    );

    this.store.dispatch(
      new IframeAction({
        active: true,
        closeButton: true,
        pageTitle: this.translatePipe.transform('_notifications'),
      })
    ); // TODO find a better way to set title
  }

  ngOnDestroy() {
    this.store.dispatch(
      new IframeAction({
        active: false,
        closeButton: false,
        pageTitle: null,
      })
    );
  }

  ngAfterContentChecked() {
    this.cdRef.detectChanges();
  }
}
