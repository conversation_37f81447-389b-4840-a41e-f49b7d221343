import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { NotificationModel, NotificationSettingsModel, NotificationSettingsTypeModel } from '../model/notification.model';

@Injectable({
  providedIn: 'root',
})
export class NotificationService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  // inbox/getMessages Removed
  // getNotifications(deviceToken) {
  //   return this.http.get<HttpResponse<NotificationModel<any>[]>>(`${environment.api}/inbox/getMessages`, {
  //     params: {
  //       token: deviceToken,
  //     },
  //   })
  //     .pipe(
  //       map(val => {
  //         if (val.code === 0) {
  //           return val.data;
  //         }
  //         return null;
  //       }),
  //     );
  // }

  getCurrentCustomerMessages(token, top, page) {
    return this.http.post<HttpResponse<NotificationModel<any>[]>>(`${environment.api}/inbox/getCurrentCustomerMessages`, { token, top, page })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val;
          }
          return null;
        }),
      );
  }

  getOtherCustomerMessages(token, top, page) {
    return this.http.post<HttpResponse<NotificationModel<any>[]>>(`${environment.api}/inbox/getOtherCustomerMessages`, { token, top, page })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val;
          }
          return null;
        }),
      );
  }

  sendEvent(body: {
    MessageId: string,
    UserAgent: string,
    EventType: string,
    Token: string,
  }) {
    return this.http.post<HttpResponse<NotificationModel<any>[]>>(`${environment.api}/inbox/sendEvent`, body)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  deleteNotification(body: {
    MessageIds: string[],
    Token: string,
  }) {
    return this.http.post<HttpResponse<NotificationModel<any>[]>>(`${environment.api}/inbox/deleteMessage`, body)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        }),
      );
  }

  getSettingsNotification() {
    return this.http.get<HttpResponse<NotificationSettingsModel[]>>(`${environment.api}/notificationsetting/get`)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  saveSettingsNotification(body: NotificationSettingsTypeModel[]) {
    return this.http.post<HttpResponse<NotificationSettingsTypeModel[]>>(`${environment.api}/notificationsetting/save`, body)
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

}
