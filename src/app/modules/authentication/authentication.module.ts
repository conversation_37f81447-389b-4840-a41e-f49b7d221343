import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AuthenticationRoutingModule } from './authentication-routing.module';
import { LoginComponent } from './container/login/login.component';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { SsoComponent } from './container/sso/sso.component';
import { SharedModule } from '../../shared/shared.module';


@NgModule({
  declarations: [LoginComponent, SsoComponent],
  imports: [
    AuthenticationRoutingModule,
    CommonModule,
    TranslateModule,
    ReactiveFormsModule,
    SharedModule,
  ],
})
export class AuthenticationModule {}
