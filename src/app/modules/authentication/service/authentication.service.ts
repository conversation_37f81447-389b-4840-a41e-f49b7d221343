import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { SetMustChangePasswordAction } from '../../../shared/state/common/common.actions';
import { LoginResponseModel, RefreshTokenResponseModel, } from '../model/login-response.model';

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  constructor(
    private readonly http: HttpClient,
    private readonly store: Store,
    private readonly router: Router
  ) {}

  loginWithOtt(ott: string): Observable<LoginResponseModel> {
    return this.http
      .post<HttpResponse<LoginResponseModel>>(`${environment.api}/user/xott`, { ott }, {
        headers: { TOKEN_FREE: 'true' },
      })
      .pipe(
        map((val) => {
          console.log(val);
          if (val.code === 0) {
            return val.data;
          }

          return null;
        })
      );
  }

  refreshToken(
    token: string,
    refreshToken: string
  ): Observable<RefreshTokenResponseModel> {
    return this.http
      .post<HttpResponse<RefreshTokenResponseModel>>(
        `${environment.api}/user/refreshtoken`,
        { token, refreshToken },
        {
          headers: { TOKEN_FREE: 'true' },
        }
      )
      .pipe(
        map((val) => {
          console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }


  liteMe() {
    return this.http.get<HttpResponse<any>>(`${environment.api}/liteuser/me`, {}).pipe(
      map(val => {
        if (val?.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  liteOTT() {
    return this.http.get<HttpResponse<any>>(`${environment.api}/liteuser/ott`, {}).pipe(
      map(val => {
        if (val?.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  liteXOTT(ott: string) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/xott`, {
      params: { ott },
    }).pipe(
      map(val => {
        if (val?.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  liteUserConvert(body: any, headers: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/liteuser/convert/to/portaluser`, body,
      {
        headers
      }
    ).pipe(
      map(val => {
        if (val?.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

  webLogin(email: string): Observable<any> {
    return this.http
      .post<HttpResponse<any>>(`${environment.api}/weblogin/start`, {
        email,
      })
      .pipe(
        map((val) => {
          return val.data;
        })
      );
  }

  loginWithSSOToken(token: string): Observable<LoginResponseModel> {
    return this.http
      .post<HttpResponse<LoginResponseModel>>(
        `${environment.api}/weblogin/validate`,
        {
          token,
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }

          return null;
        })
      );
  }

  liteUserRemove(description: any) {
    return this.http.post<HttpResponse<any>>(`${environment.api}/liteuser/form/delete`,
    {
      description,
    }
    ).pipe(
      map(val => {
        if (val?.code === 0) {
          return val.data;
        }
        return null;
      }),
    );
  }

}
