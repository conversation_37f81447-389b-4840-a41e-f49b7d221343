import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngxs/store';
import { environment } from 'src/environments/environment';
import { LoginType } from '../../enum/login-type.enum';
import { WebLoginAction } from '../../../authentication/state/login/login.actions';
import { UserService } from '../../../customer/service/user.service';


@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss'],
})
export class LoginComponent implements OnInit, OnDestroy {
  showPassword = false;
  loginForm: FormGroup;

  loading: boolean;
  userType: LoginType = null;
  webLogin = true;
  loginType = LoginType;
  formSendStatus = false;
  environment = environment;

  constructor(
    private readonly fb: FormBuilder,
    private readonly userService: UserService,
    private readonly store: Store,
  ) {
  }

  ngOnInit(): void {
    this.loginForm = this.fb.group({
      username: [
        '',
      ],
      language: [
        localStorage.getItem('boom_web_lang') || 'en',
        Validators.required,
      ],
    });
  }

  login() {
    if (this.webLogin) {
      this.store
        .dispatch(
          new WebLoginAction(this.loginForm.value.username)
        )
        .subscribe(() => {
          // this.userType = this.store.selectSnapshot(LoginState.userType);
        });
    } else {

    }
  }


  defaultLogin() {
    this.webLogin = true;
    this.loginForm.controls['username'].enable();
    this.loginForm.removeControl('password');
  }

  ngOnDestroy(): void {
  }

}
