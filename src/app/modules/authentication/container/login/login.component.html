<div class="login-form" >
  <form *ngIf="loginForm" [formGroup]="loginForm" style="width: 70%">
    <div
      class="alert alert-danger"
      *ngIf="userType === loginType.Error"
      (click)="userType = null"
    >
      {{ "_user_not_found" | translate }}
    </div>

    <div class="input-group mb-3" style="position: relative">
      <input
        appInputLength
        [name]="'Email'"
        type="text"
        name="email"
        id="l_email"
        placeholder="{{ '_email' | translate }}"
        class="form-control"
        [ngClass]="{ 'is-invalid': userType === loginType.Error }"
        formControlName="username"
        [attr.disabled]="!webLogin ? true : null"
      />
      <i
        class="icon btn icon-close-circle password-show-hide"
        *ngIf="!webLogin"
        (click)="defaultLogin()"
      ></i>
    </div>

    <button
      class="login-btn"
      (click)="login()"
      [disabled]="loginForm.invalid || loading"
    >
      {{ (webLogin ? "_next" : "_login") | translate }}
    </button>
  </form>
</div>

<!--<app-success-modal-->
<!--  *ngIf="formSendStatus"-->
<!--  [app]="true"-->
<!--  [(status)]="formSendStatus"-->
<!--  message="_forgot_password_success"-->
<!--  (statusChange)="backLogin()"-->
<!--&gt;</app-success-modal>-->

<!--<ngx-loading [show]="loading"></ngx-loading>-->
