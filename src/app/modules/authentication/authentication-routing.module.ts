import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LoginComponent } from './container/login/login.component';
import { SsoResolver } from './resolver/sso.resolver';
import { SsoComponent } from './container/sso/sso.component';

const routes: Routes = [
  { path: 'login', component: LoginComponent },
  {
    path: 'sso',
    component: SsoComponent,
    resolve: [SsoResolver],
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AuthenticationRoutingModule {}
