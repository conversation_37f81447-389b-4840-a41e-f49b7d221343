import { UserModel } from './user.model';
import { CompanyModel } from '../../../shared/models/company.model';
import { CustomerModel } from '../../../shared/models/customer.model';

export interface RefreshTokenResponseModel {
  token: string;
  tokenExpiresIn: number;
  tokenExpiresAt: Date;
  refreshToken: string;
  refreshTokenExpiresIn: number;
  refreshTokenExpiresAt: Date;
  language: string;
  user: UserModel;
}

export interface LoginResponseModel extends RefreshTokenResponseModel {
  headerCompanies: string;
  customer: CustomerModel;
  company: CompanyModel; // @deprecated
  companies: CompanyModel[];
  mustChangePassword: boolean;
  appSessionId: string;
}
