export interface UserModel extends BorusanUserLdapData, MobileVerifyStatus {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  mobile: string;
  username: string;
  isGdprApproved?: boolean;
  boomClubUser?: boolean;
}

export interface BorusanUserLdapData {
  isLdapLogin: boolean;
  isLdapForceSearchCustomer: boolean;
  ldapFullname?: string;
  ldapUsername?: string;
}

export interface MobileVerifyStatus {
  isMobileVerified?: boolean;
  forceMobileVerify?: boolean;
}
