import { LoginResponseModel, RefreshTokenResponseModel } from '../../model/login-response.model';
import {TrackerUserModel} from '../../../../shared/state/common/common.state';

export class LoginWithOttAction {
  public static readonly type = '[Login] with OTT';

  constructor(public ott: string) { }
}

export class UpdateLanguageCodeAction {
  public static readonly type = '[Login] Update Language Code';

  constructor(public languageCode: string) {
  }
}

export class UpdateLoadingAction {
  public static readonly type = '[Login] loading';

  constructor(public loading: boolean) {
  }
}

export class UpdateHeaderCompaniesAction {
  public static readonly type = '[Login] header companies';

  constructor(public headerCompanies: string) {
  }
}

export class LogoutAction {
  public static readonly type = '[Login] Logout';

  constructor(public callApi = true) {
  }
}

export class LiteLogoutAction {
  public static readonly type = '[Lite Login] Logout';

  constructor() {
  }
}

export class LogoutProcessAction {
  public static readonly type = '[Login] LogoutProcess';

  constructor(public callApi = true) {
  }
}

export class ClearTokenAction {
  public static readonly type = '[Login] clear token';

  constructor() {
  }
}

export class SetLoginResponseAction {
  public static readonly type = '[Login] set token';

  constructor(public loginResponse: LoginResponseModel) {
  }
}

export class ChangeTokenAction {
  public static readonly type = '[Login] change token';

  constructor(public value: RefreshTokenResponseModel, public sendToMobile = false) {
  }
}

export class RefreshTokenAction {
  public static readonly type = '[Login] refresh token';

  constructor() {
  }
}

export class TrackerUserAction {
  public static readonly type = '[Login] tracker user';

  constructor(public trackerUser: TrackerUserModel) {
  }
}

export class WebLoginAction {
  public static readonly type = '[Login] web Login action';
  constructor(public email: string) {}
}

export class LoginWithSSOTokenAction {
  public static readonly type = '[Login] with TOKEN';
  constructor(public ott: string) {}
}

export class SetCATSignInCode {
  public static readonly type = '[Login] Cat SignIn Code';
  constructor(public code: string) {}
}

export class ClearCATSignInCode {
  public static readonly type = '[Login] Clear Cat SignIn Code';
  constructor() {}
}
