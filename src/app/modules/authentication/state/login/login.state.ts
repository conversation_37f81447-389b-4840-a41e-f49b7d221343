import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import {
  ChangeTokenAction,
  ClearCATSignInCode,
  ClearTokenAction,
  LoginWithOttAction,
  LoginWithSSOTokenAction,
  LogoutAction,
  LogoutProcessAction,
  RefreshTokenAction,
  SetCATSignInCode,
  SetLoginResponseAction,
  TrackerUserAction,
  UpdateHeaderCompaniesAction,
  UpdateLanguageCodeAction,
  UpdateLoadingAction,
  WebLoginAction,
} from './login.actions';
import { AuthenticationService } from '../../service/authentication.service';
import { UserModel } from '../../model/user.model';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { CustomerModel } from '../../../../shared/models/customer.model';
import { CompanyModel } from '../../../../shared/models/company.model';
import { LoginResponseModel } from '../../model/login-response.model';
import { UserService } from '../../../customer/service/user.service';
import { Navigate } from '@ngxs/router-plugin';
import { UpdateUserLoadingAction } from '../../../customer/state/user/user.actions';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { NewDeviceToken } from '../../../notification/state/notification/notification.actions';
import { UserState } from '../../../customer/state/user/user.state';
import { TrackerUserModel } from '../../../../shared/state/common/common.state';
import { LoginType } from '../../enum/login-type.enum';
import { SetMustChangePasswordAction } from '../../../../shared/state/common/common.actions';
import { getCookie, setCookie } from '../../../../util/cookie.util';
import { LogService } from '../../../../shared/service/log.service';

export interface LoginStateModel {
  loginResponse: LoginResponseModel;
  customer: CustomerModel;
  company: CompanyModel;
  companyId: string;
  user: UserModel;
  token: string;
  refreshToken: string;
  headerCompanies: string;
  language: string;
  loginLoading: boolean;
  trackerUser: TrackerUserModel;
  CATSignInCode: string;
  loginType: LoginType;
}

@State<LoginStateModel>({
  name: 'login_main',
  defaults: {
    loginResponse: null,
    customer: null,
    company: null,
    companyId: null,
    user: null,
    token: null,
    refreshToken: null,
    headerCompanies: null,
    language: null,
    loginLoading: false,
    trackerUser: null,
    CATSignInCode: null,
    loginType: null,
  },
})
@Injectable()
export class LoginState {
  constructor(
    private readonly authenticationService: AuthenticationService,
    private readonly userService: UserService,
    private readonly store: Store,
    private readonly frameMessageService: FrameMessageService,
    private readonly log: LogService,
  ) {}

  @Selector()
  public static getState(state: LoginStateModel): LoginStateModel {
    return state;
  }

  @Selector()
  public static loginResponse({
    loginResponse,
  }: LoginStateModel): LoginResponseModel {
    return loginResponse;
  }

  @Selector()
  public static customer({ customer }: LoginStateModel): CustomerModel {
    return customer;
  }

  @Selector()
  public static company({ company }: LoginStateModel): CompanyModel {
    return company;
  }

  @Selector()
  public static companyId({ companyId }: LoginStateModel): string {
    return companyId;
  }

  @Selector()
  public static headerCompanies({ headerCompanies }: LoginStateModel): string {
    return headerCompanies;
  }

  @Selector()
  public static user({ user }: LoginStateModel): UserModel {
    return user;
  }

  @Selector()
  public static token({ token }: LoginStateModel): string {
    return token;
  }

  @Selector()
  public static refreshToken({ refreshToken }: LoginStateModel): string {
    return refreshToken;
  }

  @Selector()
  public static language({ language }: LoginStateModel): string {
    return language;
  }

  @Selector()
  public static loginLoading({ loginLoading }: LoginStateModel): boolean {
    return loginLoading;
  }

  @Selector()
  public static trackerUser({ trackerUser }: LoginStateModel): TrackerUserModel {
    return trackerUser;
  }


  @Selector()
  public static CATSignInCode({ CATSignInCode }: LoginStateModel): string {
    return getCookie('CATSignInCode');
  }

  @Selector()
  public static loginType({ loginType }: LoginStateModel): LoginType {
    return loginType;
  }

  @Selector()
  public static isSAMLLogin({
    loginType,
  }: LoginStateModel): boolean {
    return loginType === LoginType.Saml;
  }

  @Action(LoginWithOttAction)
  public loginWithOttAction(
    { patchState, getState }: StateContext<LoginStateModel>,
    { ott }: LoginWithOttAction
  ): Observable<any> {
    // if (getState().user) {
    //   console.log('already logged in');
    //   return of('');
    // }
    console.log('XOTT REQUEST ' + new Date());
    patchState({
      loginLoading: true,
    });

    return this.authenticationService.loginWithOtt(ott).pipe(
      tap((value) => {
        if (value.appSessionId) {
          this.store.dispatch(new NewDeviceToken(value.appSessionId));
        }
        patchState({
          loginResponse: value,
          loginLoading: false,
          customer: value?.customer,
          company: value?.company,
          user: value?.user,
          token: value?.token,
          refreshToken: value?.refreshToken,
          headerCompanies: value?.headerCompanies,
          language: value?.language,
        });

        if (value.mustChangePassword) {
          this.store.dispatch(new Navigate(['settings', 'passwordchange']));
          this.store.dispatch(
            new SetMustChangePasswordAction(value.mustChangePassword)
          );
          return;
        }

        this.store.dispatch(new Navigate(['/']));
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        console.log('INVALID OTT: ' + err.message);
        return throwError(err);
      })
    );
  }

  @Action(UpdateLanguageCodeAction)
  updateLanguageCodeAction(
    { patchState }: StateContext<LoginStateModel>,
    { languageCode }: UpdateLanguageCodeAction
  ): void {
    patchState({
      language: languageCode,
    });
  }

  @Action(UpdateLoadingAction)
  updateLoading(
    { getState, patchState }: StateContext<LoginStateModel>,
    { loading }: UpdateLoadingAction
  ): void {
    patchState({
      loginLoading: loading,
    });
  }

  @Action(UpdateHeaderCompaniesAction)
  updateHeaderCompany(
    { getState, patchState }: StateContext<LoginStateModel>,
    { headerCompanies }: UpdateHeaderCompaniesAction
  ): void {
    if (!headerCompanies) {
      const currentCustomer = this.store.selectSnapshot(UserState.currentCustomer);
      // console.log('Header companies updated AS publicMenuHeaderCompany', currentCustomer.publicMenuHeaderCompany);
      patchState({
        headerCompanies: currentCustomer.publicMenuHeaderCompany
      });
      return;
    }

    // console.log('Header companies updated', headerCompanies);
    patchState({
      headerCompanies,
    });
  }

  @Action(LogoutAction)
  logoutAction(
    { getState, patchState }: StateContext<LoginStateModel>,
    logoutAction: LogoutAction
  ): void {
    console.log('LOGOUT ACTION');
    if (logoutAction.callApi) {
      this.userService.logout(getState().token).subscribe();
    }

    patchState({
      loginResponse: null,
      customer: null,
      company: null,
      companyId: null,
      user: null,
      token: null,
      refreshToken: null,
      headerCompanies: null,
      language: null,
      loginLoading: false,
      trackerUser: null,
      CATSignInCode: null,
      loginType: null,
    });
  }

  @Action(LogoutProcessAction)
  logoutProcessAction(
    ctx: StateContext<LoginStateModel>,
    action: LogoutProcessAction
  ): void {
    this.store.dispatch(new UpdateUserLoadingAction(true));
    // second attempt, and release loading
    setTimeout(() => {
      // this.frameMessageService.sendMessage(FrameMessageEnum.logout);
      this.store.dispatch(new UpdateUserLoadingAction(false));
    }, 10000);

    this.store.dispatch(new LogoutAction(action.callApi));
    this.frameMessageService.sendMessage(FrameMessageEnum.logout);
  }

  @Action(ClearTokenAction)
  clearTokenAction(
    { patchState }: StateContext<LoginStateModel>
  ): void {
    patchState({
      loginResponse: null,
      customer: null,
      company: null,
      companyId: null,
      user: null,
      token: null,
      refreshToken: null,
      headerCompanies: null,
      language: null,
      loginLoading: false,
    });
  }

  @Action(SetLoginResponseAction)
  setLoginResponseAction(
    { getState, patchState }: StateContext<LoginStateModel>,
    { loginResponse }: SetLoginResponseAction
  ): any {
    if (loginResponse.appSessionId) {
      this.store.dispatch(new NewDeviceToken(loginResponse.appSessionId));
    }
    patchState({
      loginResponse,
      loginLoading: false,
      customer: loginResponse?.customer,
      company: loginResponse?.company || loginResponse.companies?.[0],
      user: loginResponse?.user,
      token: loginResponse?.token,
      refreshToken: loginResponse?.refreshToken,
      headerCompanies: loginResponse?.headerCompanies,
      language: loginResponse?.language,
      loginType: getState()?.loginType || LoginType.Mobile,
    });
  }

  @Action(RefreshTokenAction)
  refreshToken({ patchState, getState }: StateContext<LoginStateModel>) {
    patchState({
      loginLoading: true,
    });
    const { token, refreshToken, loginResponse } = getState();
    return this.authenticationService.refreshToken(token, refreshToken).pipe(
      tap((value) => {
        patchState({
          loginResponse: { ...loginResponse, ...value },
          loginLoading: false,
          user: value?.user,
          token: value?.token,
          refreshToken: value?.refreshToken,
          language: value?.language,
        });
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        this.frameMessageService.sendMessage(FrameMessageEnum.unauthorised);
        return throwError(err);
      })
    );
  }

  @Action(ChangeTokenAction)
  changeToken(
    { patchState, getState }: StateContext<LoginStateModel>,
    action: ChangeTokenAction
  ) {
    const { loginResponse } = getState();
    const value = action.value;
    patchState({
      loginResponse: { ...loginResponse, ...value },
      loginLoading: false,
      user: value?.user,
      token: value?.token,
      refreshToken: value?.refreshToken,
      language: value?.language,

    });
    if (action.sendToMobile) {
      this.frameMessageService.sendMessage(
        FrameMessageEnum.changeToken,
        value
      );
    }
  }

  @Action(TrackerUserAction)
  trackerUser(
    { patchState }: StateContext<LoginStateModel>,
    { trackerUser }: TrackerUserAction
  ) {
    patchState({
      trackerUser
    }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        return throwError(err);
      });
  }

  @Action(WebLoginAction)
  public webLoginAction(
    { patchState }: StateContext<LoginStateModel>,
    { email }: WebLoginAction
  ) {
    if (!email) {
      return this.store.dispatch(new Navigate(['/auth/sso'], { action: 'login' }));
    }

    patchState({ loginLoading: true });
    return this.authenticationService.webLogin(email).pipe(
      tap((res) => {
        const userType = this.getLoginType(res.userType);
        patchState({ loginLoading: false });
        if (userType === LoginType.Saml) {
          this.frameMessageService.sendMessage(FrameMessageEnum.samlLoggedIn);
          this.store.dispatch(new Navigate(['/auth/sso'], { email }));
        }
      })
    );
  }

  getLoginType(userType): LoginType {
    switch (userType) {
      case 0:
        return LoginType.Error;
        break;
      case 1:
        return LoginType.Saml;
        break;
      case 4:
        return LoginType.Error;
        break;

      default:
        return LoginType.Mobile;
        break;
    }
  }

  @Action(LoginWithSSOTokenAction)
  public LoginWithTokenAction(
    { patchState, getState }: StateContext<LoginStateModel>,
    { ott }: LoginWithSSOTokenAction
  ): Observable<any> {
    patchState({
      loginLoading: true,
    });
    this.log.action('SAML_LOGIN', 'TOKEN_HANDLED', {}).subscribe();

    return this.authenticationService.loginWithSSOToken(ott).pipe(
      tap((value) => {
        patchState({
          loginResponse: value,
          loginLoading: false,
          customer: value?.customer,
          company: value?.company,
          user: value?.user,
          token: value?.token,
          refreshToken: value?.refreshToken,
          headerCompanies: value?.headerCompanies,
          language: value?.language,
          loginType: LoginType.Saml,
        });
        this.log.action('SAML_LOGIN', 'SUCCESS', {
          email: value?.user?.email,
        }).subscribe();

        this.store.dispatch(new ChangeTokenAction(value, true));

        if (value.mustChangePassword) {
          this.store.dispatch(new Navigate(['settings', 'passwordchange']));

          this.store.dispatch(
            new SetMustChangePasswordAction(value.mustChangePassword)
          );
          return;
        }

        this.store.dispatch(new Navigate(['/dashboard']));
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        this.log.action('SAML_LOGIN', 'TOKEN_FAIL', {
          error: err.error
        }).subscribe();
        this.store.dispatch(new LogoutProcessAction());
        return throwError(err);
      })
    );
  }

  @Action(SetCATSignInCode)
  public setCATSignInCode(
    { patchState }: StateContext<LoginStateModel>,
    { code }: SetCATSignInCode
  ) {
    setCookie('CATSignInCode', code, {
      path: '/',
      // expireDays: (1 / 24) / 6, // it means 10 min
    });
    // patchState({ CATSignInCode: code });
  }

  @Action(ClearCATSignInCode)
  public clearCATSignInCode(
    { patchState }: StateContext<LoginStateModel>,
    action: ClearCATSignInCode
  ) {
    setCookie('CATSignInCode', 'code', {
      path: '/',
      expireDays: -10,
    });
  }

}
