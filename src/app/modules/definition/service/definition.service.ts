import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../../environments/environment';
import { City } from '../model/city.model';
import { Country, CountryNameModel } from '../model/country.model';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { AgreementDetailModel, AgreementModel, UserAgreementsListModel } from '../model/agreement.model';
import { AgreementTypeEnum } from '../enum/agreement-type.enum';
import jsonSafeParse from '../../../util/json-safe-parse';
import { LoginState } from '../../authentication/state/login/login.state';
import { Store } from '@ngxs/store';

@Injectable({
  providedIn: 'root',
})
export class DefinitionService {
  constructor(private readonly http: HttpClient, private readonly store: Store) { }

  countryList(all = false): Observable<Country[]> {
    const url = all
      ? `${environment.api}/geolocation/allcountries`
      : `${environment.api}/geolocation/countries`;

    return this.http.get<HttpResponse<Country[]>>(url).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  cityList(countryId: string): Observable<City[]> {
    return this.http
      .get<HttpResponse<City[]>>(`${environment.api}/geolocation/cities`, {
        params: { countryId },
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  countryNames(): Observable<CountryNameModel[]> {
    return this.http
      .get<HttpResponse<CountryNameModel[]>>(
        `${environment.api}/geolocation/countryNames`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  agreement(position: AgreementTypeEnum, onlyNotApproved: boolean): Observable<AgreementModel[]> {
    const params = new HttpParams()
      .set('position', position.toString())
      .set('onlyNotApproved', onlyNotApproved.toString());
    return this.http
      .get<HttpResponse<AgreementModel[]>>(`${environment.api}/agreement/get`, { params })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            val.data.map((item) => {
              const nextIndex = 0;
              const override = item.overrideDescriptions.find(
                (o) => o.position === position
              );
              if (override) {
                item.description = override.description;
              }

              item.descParts = item.description?.split('{').map((str) => {
                const [text, linkText] = str.split('}').reverse();

                let url = linkText ? item.details[nextIndex].url : null;
                if (url && !environment.production) {
                  url = url.replace(
                    'https://prod.borusancat.com/lgnd/api',
                    'http://' + window.location.host + '/api'
                  );
                }
                return { linkText, text, url };
              });
            });

            return val.data;
          }
          return null;
        })
      );
  }

  getAgreementContent(url: string): Observable<string> {
    return this.http.get(url.includes('?language') ? url : url + '?language=' + this.store.selectSnapshot(LoginState.language), { responseType: 'text' });
  }

  approveAgreement(headers): Observable<any> {
    return this.http
      .get<any>(`${environment.api}/agreement/approve`, {
        headers,
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  approveUserAgreement(headers): Observable<any> {
    return this.http
      .get<any>(`${environment.api}/agreement/approveagreements`, {
        headers,
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  approveUserRequiredAgreement(agreementId): Observable<any> {
    return this.http
      .post<any>(`${environment.api}/agreement/approveUserRequiredAgreement`, {}, {
        params: {
          agreementId
        }
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  skipUserRequiredAgreement(agreementId): Observable<any> {
    return this.http
      .post<any>(`${environment.api}/agreement/skipUserRequiredAgreement`, {}, {
        params: {
          agreementId
        }
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  approveUserAgreementFromNotification(agreementNames){
    return this.http
      .get<any>(`${environment.api}/agreement/approveagreements`, {
        headers: {
          ApprovedAgreementNames: agreementNames
        }
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  agreementDetails(agreementName: string): Observable<AgreementDetailModel[]> {
    return this.http
      .get<HttpResponse<AgreementDetailModel[]>>(`${environment.api}/agreement/detail?agreementName=${agreementName}`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  // kullanımı sonlandı sonra silelim
  // getUserAgreements(position: AgreementTypeEnum): Observable<UserAgreementsListModel[]> {
  //   return this.http
  //     .get<HttpResponse<UserAgreementsListModel[]>>(`${environment.api}/agreement/getuseragreements`, {
  //       params: { position },
  //     })
  //     .pipe(
  //       map((val) => {
  //         if (val.code === 0) {
  //           return val.data;
  //         }
  //         return null;
  //       })
  //     );
  // }
}
