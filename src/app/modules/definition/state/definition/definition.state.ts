import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import {
  ApproveUserAgreementAction,
  GetAgreementsAction,
  GetAllCountryListAction,
  GetCityListAction,
  GetCountryListAction,
  GetCountryNameListAction,
  GetUserAgreementsAction,

} from './definition.actions';
import { City } from '../../model/city.model';
import { Country, CountryNameModel } from '../../model/country.model';
import { DefinitionService } from '../../service/definition.service';
import { AgreementModel, UserAgreementsListModel } from '../../model/agreement.model';
import { UserState } from '../../../customer/state/user/user.state';
import { CustomerService } from '../../../customer/service/customer.service';
import { GetUserAwaitingAgreements } from 'src/app/modules/customer/state/customer/customer.actions';

export interface DefinitionStateModel {
  countryList: Country[];
  countryNameList: CountryNameModel[];
  cityList: City[];
  countryListLoading: boolean;
  cityListLoading: boolean;
  agreement: AgreementModel[];
  userAgreements: UserAgreementsListModel[];
  agreementLoading: boolean;
  getForceApprovalAgreement: UserAgreementsListModel[];
  approveUserAgreementSended: boolean;
}

@State<DefinitionStateModel>({
  name: 'definition',
  defaults: {
    countryList: [],
    cityList: [],
    countryNameList: [],
    countryListLoading: false,
    cityListLoading: false,
    agreement: [],
    userAgreements: [],
    agreementLoading: false,
    getForceApprovalAgreement: null,
    approveUserAgreementSended: null,
  },
})
@Injectable()
export class DefinitionState {

  constructor(
    private readonly definitionService: DefinitionService,
    private readonly store: Store,
    private readonly customerService: CustomerService
  ) {
  }

  @Selector()
  public static allCountryList({ countryList }: DefinitionStateModel): Country[] {
    return countryList;
  }

  @Selector()
  public static countryList({ countryList }: DefinitionStateModel): Country[] {
    return countryList;
  }

  @Selector()
  public static cityList({ cityList }: DefinitionStateModel): City[] {
    return cityList;
  }

  @Selector()
  public static countryNameList({ countryNameList }: DefinitionStateModel): CountryNameModel[] {
    return countryNameList;
  }

  @Selector()
  public static countryListLoading({ countryListLoading }: DefinitionStateModel): boolean {
    return countryListLoading;
  }

  @Selector()
  public static cityListLoading({ cityListLoading }: DefinitionStateModel): boolean {
    return cityListLoading;
  }

  @Selector()
  public static getAgreements({ agreement }: DefinitionStateModel): AgreementModel[] {
    return agreement;
  }

  @Selector()
  public static getUserAgreements({ userAgreements }: DefinitionStateModel): UserAgreementsListModel[] {
    return userAgreements;
  }

  @Selector()
  public static getAgreementLoading({ agreementLoading }: DefinitionStateModel): boolean {
    return agreementLoading;
  }

  @Selector()
  public static getForceAprovalAgreement({ getForceApprovalAgreement }: DefinitionStateModel): UserAgreementsListModel[] {
    return getForceApprovalAgreement;
  }

  @Selector()
  public static approveUserAgreementSend({ approveUserAgreementSended }: DefinitionStateModel): boolean {
    return approveUserAgreementSended;
  }

  @Action(GetCountryListAction)
  getCountryListAction({ patchState }: StateContext<DefinitionStateModel>) {
    patchState({
      countryListLoading: true,
    });
    return this.definitionService.countryList().pipe(
      tap((value) => {
        patchState({
          countryList: value,
          countryListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          countryListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetAllCountryListAction)
  getAllCountryListAction({ patchState }: StateContext<DefinitionStateModel>) {
    patchState({
      countryListLoading: true,
    });
    return this.definitionService.countryList(true).pipe(
      tap((value) => {
        patchState({
          countryList: value,
          countryListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          countryListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetCityListAction)
  getCityListAction({ patchState }: StateContext<DefinitionStateModel>, { countryId }: GetCityListAction) {
    patchState({
      cityListLoading: true,
    });
    return this.definitionService.cityList(countryId).pipe(
      tap((value) => {
        patchState({
          cityList: value,
          cityListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          cityListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetCountryNameListAction)
  getCountryNameListAction({ patchState }: StateContext<DefinitionStateModel>,) {
    patchState({
      countryListLoading: true,
    });
    return this.definitionService.countryNames().pipe(
      tap((value) => {
        patchState({
          countryNameList: value,
          countryListLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          countryListLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetAgreementsAction)
  getAgreements({ patchState }: StateContext<DefinitionStateModel>,
    { agreementType, onlyNotApproved }: GetAgreementsAction) {
    patchState({
      agreementLoading: true,
    });
    return this.definitionService.agreement(agreementType, onlyNotApproved).pipe(
      tap((value) => {
        patchState({
          agreement: value,
          agreementLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          agreementLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetUserAgreementsAction)
  getUserAgreements({ patchState }: StateContext<DefinitionStateModel>, { position, onlyNotApproved }: GetUserAwaitingAgreements) {
    patchState({
      agreementLoading: true,
    });

    return this.customerService.getUserAwaitingAgreements(position, onlyNotApproved)
      .pipe(tap((value) => {
          let forceApprovals: any = value;
          // TODO remove it, temporary KZ coin agreement disable
          if (this.store.selectSnapshot(UserState.currentCustomer).countryCode === 'KZ') {
            forceApprovals = forceApprovals.filter(a =>
              !['PROMOTION_PORTAL_CLARIFICATION_TEXT', 'PROMOTION_PORTAL_CONSENT_AGREEMENT']
                .includes(a.name));
          }

          patchState({
            userAgreements: value,
            agreementLoading: false,
            getForceApprovalAgreement: forceApprovals,
          });
        }),
        catchError((err) => {
          patchState({
            agreementLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(ApproveUserAgreementAction)
  approveUserAgreementState(
    { patchState }: StateContext<DefinitionStateModel>,
    { header }: ApproveUserAgreementAction
  ) {
    patchState({
      agreementLoading: true,
    });
    return this.definitionService.approveUserAgreement(header)
      .pipe(tap((value) => {
          patchState({
            agreementLoading: false,
            approveUserAgreementSended: value === 'OK' ? true : false,
          });
        }),
        catchError((err) => {
          patchState({
            agreementLoading: false,
          });
          return throwError(err);
        }),
      );
  }
}
