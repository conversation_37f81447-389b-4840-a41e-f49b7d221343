import { AgreementTypeEnum } from "../../enum/agreement-type.enum";

export class GetCountryListAction {
  public static readonly type = '[Definition] Get Country List';
}

export class GetCityListAction {
  public static readonly type = '[Definition] Get City List';

  constructor(public countryId: string) {
  }
}

export class GetAllCountryListAction {
  public static readonly type = '[Definition] Get All Country List';
}

export class GetCountryNameListAction {
  public static readonly type = '[Definition] Get Country Name List';
}

export class GetAgreementsAction {
  public static readonly type = '[Definition] Get Agreements';
  constructor(public agreementType: AgreementTypeEnum, public onlyNotApproved: boolean) {}
}

export class GetUserAgreementsAction {
  public static readonly type = '[Definition] Get Approved Agreements';
  constructor(public position: AgreementTypeEnum | string, public onlyNotApproved: boolean) {}
}

export class ApproveUserAgreementAction {
  public static readonly type = '[Definition] Approve User Agreement';
  constructor(public header: any) {}
}

export class GenerateReferenceNumberAction {
  public static readonly type = '[Definition] Reference Number';
  constructor(public payload: any) {}
}
