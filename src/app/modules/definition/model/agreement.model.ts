export interface AgreementModel {
  name: string;
  description: string;
  descParts: AgreementLink[];
  contentType: number;
  selectable: boolean;
  order: number;
  details: AgreementDetailModel[];
  overrideDescriptions: { position: string, description: string }[],
  positions: number[];
  availableCompanies: {
    countryCode: string;
    companyCode: string;
  }[];
  hasSkip: boolean;
  agreementFormId: string;
  url: string;
}

export interface ApprovalAgreementModel {
  agreementFormId: string;
  hasSkip: boolean;
  url: string;
  agreementCode?: string;
}

export interface ApprovalAgreementUpdatedModel extends ApprovalAgreementModel{
  agreementCode: string
}

export interface AgreementDetailModel {
  name: string;
  url: string;
  order: number;
}

export interface AgreementLink {
  text: string;
  linkText: string;
  url: string;
}

export interface UserAgreementsListModel {
  name: string;
  url: string;
  approvedVersion: number;
  approveDate: string;
  latestVersion: number;
  reapproveNeeded: boolean;
  position: string;
  isApproved: boolean;
}
