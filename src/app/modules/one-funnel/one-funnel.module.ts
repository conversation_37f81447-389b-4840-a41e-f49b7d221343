import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { NgxsModule } from '@ngxs/store';
import { SharedModule } from 'src/app/shared/shared.module';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { OneFunnelRoutingModule } from './one-funnel-routing.module';
import { CoreModule } from 'src/app/core/core.module';
import { OneFunnelListComponent } from './container/one-funnel-list/one-funnel-list.component';
import { OneFunnelDetailComponent } from './container/one-funnel-detail/one-funnel-detail.component';
import { OneFunnelCardComponent } from './components/one-funnel-card/one-funnel-card.component';
import { OneFunnelState } from './state/one-funnel.state';

@NgModule({
  declarations: [OneFunnelListComponent, OneFunnelDetailComponent, OneFunnelCardComponent],
  exports: [OneFunnelListComponent],
  imports: [
    CommonModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    OneFunnelRoutingModule,
    TranslateModule,
    NgxLoadingModule,
    SharedModule,
    CoreModule,
    NgbTooltipModule,
    NgxsModule.forFeature([OneFunnelState]),
  ],
})
export class OneFunnelModule {
  constructor() {
    stopLoadingAnimation();
  }
}
