import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { OneFunnelListComponent } from './container/one-funnel-list/one-funnel-list.component';
import { OneFunnelDetailComponent } from './container/one-funnel-detail/one-funnel-detail.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    redirectTo: 'list',
  },
  {
    path: '',
    children: [
      { path: 'list', component: OneFunnelListComponent },
      { path: 'detail', component: OneFunnelDetailComponent }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class OneFunnelRoutingModule {}
