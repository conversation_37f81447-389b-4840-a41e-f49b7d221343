export enum FlowKeyEnum {
  // Prime Insights
  'Prime Utilization Increase' = 'ONE_PT_001',
  'Prime Rebuild Opportunity' = 'ONE_PT_002',
  'Prime MPSE' = 'ONE_PT_003',
  'Prime Competitive' = 'ONE_PT_004',
  'Prime Opportunity' = 'ONE_PT_005',
  
  // Service Insights
  'DAT Reminder' = 'ONE_ST_001',
  'PCC At Risk' = 'ONE_ST_002',
  'STU At Risk' = 'ONE_ST_003',
  'SMU Reading' = 'ONE_ST_004',
  'Inspection Reminder' = 'ONE_ST_005',
  'Signal Tasks' = 'ONE_ST_006',
  'Service Letters' = 'ONE_ST_007',
  'DNA' = 'ONE_ST_008'
}

export const INSIGHT_TITLE_MAP: { [key: string]: string } = {
  'ONE_PT_001': 'Prime Workload Alert',
  'ONE_PT_002': 'Prime Rebuild Alert',
  'ONE_PT_003': 'Prime Marketing Alert',
  'ONE_PT_004': 'Prime Exchange Alert',
  'ONE_PT_005': 'Prime Opportunity Alert',
  'ONE_ST_001': 'DAT Alert',
  'ONE_ST_002': 'Dormant PCC Alert',
  'ONE_ST_003': 'Dormant STU Alert',
  'ONE_ST_004': 'SMU Alert',
  'ONE_ST_005': 'Inspection Alert',
  'ONE_ST_006': 'CVA Alert',
  'ONE_ST_007': 'Service Letter Alert',
  'ONE_ST_008': 'DNA Alert'
};
