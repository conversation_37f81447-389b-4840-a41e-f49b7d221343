import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';
import { OneFunnelInsightModel, OneFunnelInsightsResponse } from '../model/one-funnel.model';

@Injectable({
  providedIn: 'root'
})
export class OneFunnelService {

  constructor(
    private readonly http: HttpClient,
  ) { }

  getInsights(): Observable<OneFunnelInsightModel[]> {
    return this.http.get<HttpResponse<OneFunnelInsightModel[]>>(`${environment.api}/of/getinsights`).pipe(
      map((response) => {
        if (response.code === 0) {
          return response.data;
        }
        return [];
      })
    );
  }

  updateSmu(serialNumber: string, smu: string): Observable<any> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/of/updatesmu`, {
      serialNumber,
      smu
    }).pipe(
      map((response) => {
        if (response.code === 0) {
          return response.data;
        }
        throw new Error(response.message || 'Failed to update SMU');
      })
    );
  }

}
