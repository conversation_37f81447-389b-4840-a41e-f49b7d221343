import { OneFunnelInsightModel } from '../model/one-funnel.model';

export class LoadOneFunnelInsightsAction {
  public static readonly type = '[OneFunnel] Load Insights';
  constructor() {}
}

export class LoadOneFunnelInsightsSuccessAction {
  public static readonly type = '[OneFunnel] Load Insights Success';
  constructor(public insights: OneFunnelInsightModel[]) {}
}

export class LoadOneFunnelInsightsFailAction {
  public static readonly type = '[OneFunnel] Load Insights Fail';
  constructor(public error: any) {}
}

export class SetOneFunnelDetailAction {
  public static readonly type = '[OneFunnel] Set Detail';
  constructor(public insight: OneFunnelInsightModel) {}
}

export class ClearOneFunnelDetailAction {
  public static readonly type = '[OneFunnel] Clear Detail';
  constructor() {}
}
