import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { tap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { OneFunnelInsightModel } from '../model/one-funnel.model';
import { OneFunnelService } from '../service/one-funnel.service';
import {
  LoadOneFunnelInsightsAction,
  LoadOneFunnelInsightsSuccessAction,
  LoadOneFunnelInsightsFailAction,
  SetOneFunnelDetailAction,
  ClearOneFunnelDetailAction
} from './one-funnel.actions';

export interface OneFunnelStateModel {
  insights: OneFunnelInsightModel[];
  loading: boolean;
  error: any;
  selectedDetail: OneFunnelInsightModel | null;
}

@State<OneFunnelStateModel>({
  name: 'oneFunnel',
  defaults: {
    insights: [],
    loading: false,
    error: null,
    selectedDetail: null
  }
})
@Injectable()
export class OneFunnelState {
  constructor(private readonly oneFunnelService: OneFunnelService) {}

  @Selector()
  public static insights(state: OneFunnelStateModel): OneFunnelInsightModel[] {
    return state.insights;
  }

  @Selector()
  public static loading(state: OneFunnelStateModel): boolean {
    return state.loading;
  }

  @Selector()
  public static error(state: OneFunnelStateModel): any {
    return state.error;
  }

  @Selector()
  public static selectedDetail(state: OneFunnelStateModel): OneFunnelInsightModel | null {
    return state.selectedDetail;
  }

  @Action(LoadOneFunnelInsightsAction)
  loadInsights({ patchState }: StateContext<OneFunnelStateModel>) {
    patchState({
      loading: true,
      error: null
    });

    return this.oneFunnelService.getInsights().pipe(
      tap((insights) => {
        patchState({
          insights,
          loading: false
        });
      }),
      catchError((error) => {
        patchState({
          loading: false,
          error
        });
        return throwError(error);
      })
    );
  }

  @Action(SetOneFunnelDetailAction)
  setDetail({ patchState }: StateContext<OneFunnelStateModel>, { insight }: SetOneFunnelDetailAction) {
    patchState({
      selectedDetail: insight
    });
  }

  @Action(ClearOneFunnelDetailAction)
  clearDetail({ patchState }: StateContext<OneFunnelStateModel>) {
    patchState({
      selectedDetail: null
    });
  }
}
