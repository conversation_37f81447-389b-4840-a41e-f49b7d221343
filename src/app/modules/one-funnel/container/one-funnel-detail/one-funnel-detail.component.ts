import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { TranslatePipe } from '@ngx-translate/core';
import { CustomerModel } from 'src/app/shared/models/customer.model';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';
import { OneFunnelState } from '../../state/one-funnel.state';
import { ClearOneFunnelDetailAction } from '../../state/one-funnel.actions';

@Component({
  selector: 'app-one-funnel-detail',
  templateUrl: './one-funnel-detail.component.html',
  styleUrls: ['./one-funnel-detail.component.scss'],
})
export class OneFunnelDetailComponent implements OnInit, OnDestroy {
  @Select(CustomerState.customer) customer$: Observable<CustomerModel>;
  @Select(OneFunnelState.selectedDetail) selectedDetail$: Observable<OneFunnelInsightModel>;

  customer: CustomerModel;
  insight: OneFunnelInsightModel;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly translatePipe: TranslatePipe
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        closeButton: false,
        hamburgerMenu: true,
        notificationIcon: false,
        title: this.translatePipe.transform('_one_funnel_detail'),
        backButton: true
      })
    );

    this.customer$.pipe(takeUntil(this.subscriptions$)).subscribe((customer) => {
      if (customer) {
        this.customer = customer;
      }
    });

    this.selectedDetail$.pipe(takeUntil(this.subscriptions$)).subscribe((insight) => {
      if (insight) {
        this.insight = insight;
      } else {
        this.router.navigate(['../list'], { relativeTo: this.route });
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.store.dispatch(new ClearOneFunnelDetailAction());
  }

  navigateToBack(): void {
    this.router.navigate(['../list'], { relativeTo: this.route });
  }
}
