import { Component, Input, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { TranslateService } from '@ngx-translate/core';
import { OneFunnelInsightModel } from '../../model/one-funnel.model';
import { CustomerModuleService } from 'src/app/modules/customer/service/customer-module.service';
import { SetOneFunnelDetailAction } from '../../state/one-funnel.actions';
import { OneFunnelService } from '../../service/one-funnel.service';

@Component({
  selector: 'app-one-funnel-card',
  templateUrl: './one-funnel-card.component.html',
  styleUrls: ['./one-funnel-card.component.scss'],
})
export class OneFunnelCardComponent implements OnInit {
  @Input() insight: OneFunnelInsightModel;
  @Input() isDetailView: boolean = false;

  showSmuModal = false;
  smuForm: FormGroup;
  isSubmittingSmu = false;
  smuSubmissionSuccess = false;
  smuSubmissionError = '';

  constructor(
    private readonly customerModuleService: CustomerModuleService,
    private readonly store: Store,
    private readonly router: Router,
    private readonly formBuilder: FormBuilder,
    private readonly oneFunnelService: OneFunnelService,
    private readonly translateService: TranslateService
  ) {
    this.smuForm = this.formBuilder.group({
      smu: ['', [Validators.required, Validators.pattern(/^\d+$/)]],
    });
  }

  ngOnInit() {}

  iconClassMap = new Map([
    ['ONE_PT_001', 'icon-rebuild'],
    ['ONE_PT_002', 'icon-rebuild'],
    ['ONE_PT_003', 'icon-rebuild'],
    ['ONE_PT_004', 'icon-rebuild'],
    ['ONE_PT_005', 'icon-rebuild'],
    ['ONE_ST_001', 'icon-reminder-clock'],
    ['ONE_ST_002', 'icon-pcc-risk'],
    ['ONE_ST_003', 'icon-stu-risk'],
    ['ONE_ST_004', 'icon-smu-reading'],
    ['ONE_ST_005', 'icon-inspection-reminder'],
    ['ONE_ST_006', 'icon-stu-risk'],
    ['ONE_ST_007', 'icon-service-letters'],
    ['ONE_ST_008', 'icon-warning'],
  ]);

  getIconClass(flowKey?: string): string {
    return this.iconClassMap.get(flowKey ?? '') ?? 'icon-smu-reading';
  }

  shouldShowSmuButton(): boolean {
    return this.insight?.flowKey === 'ONE_ST_004';
  }

  shouldShowGoToEquipmentButton(): boolean {
    return !!this.insight?.serialNumber;
  }

  onCardClick(): void {
    if (!this.isDetailView && this.insight) {
      this.store.dispatch(new SetOneFunnelDetailAction(this.insight));
      this.router.navigateByUrl('/one-funnel/detail');
    }
  }

  onGoToEquipment(): void {
    if (this.insight?.equipmentNumber) {
      this.customerModuleService.openEquipmentModule(
        this.insight.customerNumber,
        this.insight.equipmentNumber
      );
    }
  }

  onEnterSmu(): void {
    this.showSmuModal = true;
    this.smuSubmissionSuccess = false;
    this.smuSubmissionError = '';
    this.smuForm.reset();
  }

  onCloseSmuModal(): void {
    this.showSmuModal = false;
    this.smuSubmissionSuccess = false;
    this.smuSubmissionError = '';
    this.smuForm.reset();
  }

  onSubmitSmu(): void {
    if (this.smuForm.valid && !this.isSubmittingSmu) {
      this.isSubmittingSmu = true;
      this.smuSubmissionError = '';

      const smuValue = this.smuForm.get('smu')?.value;
      const serialNumber = this.insight?.serialNumber;

      if (!serialNumber) {
        this.smuSubmissionError = this.translateService.instant(
          '_one_funnel_serial_not_available'
        );
        this.isSubmittingSmu = false;
        return;
      }

      this.oneFunnelService.updateSmu(serialNumber, smuValue).subscribe({
        next: (response) => {
          this.isSubmittingSmu = false;
          this.smuSubmissionSuccess = true;
          // Close modal after 2 seconds
          setTimeout(() => {
            this.onCloseSmuModal();
          }, 2000);
        },
        error: (error) => {
          this.isSubmittingSmu = false;
          this.smuSubmissionError = this.translateService.instant('_one_funnel_smu_update_failed');
        },
      });
    }
  }
}
