.one-funnel-card {
  border: 1px solid #f6f6f6;
  box-shadow: 0px 0px 9.2px 0px #0000003d;
  border-radius: 0.5rem;
  padding: 1rem 20px;

  &.clickable {
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      box-shadow: 0px 0px 15px 0px #00000050;
      transform: translateY(-2px);
    }
  }

  .one-funnel-card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f0;

    .icon::before {
      font-size: 1.5rem;
    }

    .icon-dot3::before {
      font-size: 20px;
    }
  }

  .equipment-detail {
    margin-top: 14px;
    display: flex;
    align-items: center;

    .icon-excavator::before {
      font-size: 24px;
    }
  }

  .one-funnel-date {
    margin-top: 1rem;
    margin-bottom: 1rem;
    font-size: 13px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .escalation-description {
    border-top: 1px solid #f0f0f0;
    border-bottom: 1px solid #f0f0f0;
    margin: 1rem 0;
    padding: 1rem 0;
  }

  .action-btns {
    margin: 0;
    padding-top: 1rem;
    border-top: 1px solid #f0f0f0;
    gap: 0.5rem;
  }
}

.smu-modal-content {
  padding: 0.5rem 0;

  .form-label {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
  }

  .form-control {
    border: 1px solid #ddd;
    border-radius: 0.375rem;
    padding: 0.75rem;
    font-size: 14px;

    &:focus {
      border-color: #007bff;
      box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    &.is-invalid {
      border-color: #dc3545;
    }
  }

  .invalid-feedback {
    display: block;
    font-size: 0.875rem;
    color: #dc3545;
    margin-top: 0.25rem;
  }

  .alert {
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid transparent;

    &.alert-success {
      background-color: #d1edff;
      border-color: #bee5eb;
      color: #0c5460;
    }

    &.alert-danger {
      background-color: #f8d7da;
      border-color: #f5c6cb;
      color: #721c24;
    }

    .icon {
      font-size: 1rem;
    }
  }

  .btn {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 14px;
    font-weight: 500;

    &.btn-primary {
      background-color: #007bff;
      border-color: #007bff;

      &:hover:not(:disabled) {
        background-color: #0056b3;
        border-color: #0056b3;
      }

      &:disabled {
        opacity: 0.65;
      }
    }

    &.btn-secondary {
      background-color: #6c757d;
      border-color: #6c757d;
      color: white;

      &:hover:not(:disabled) {
        background-color: #545b62;
        border-color: #545b62;
      }
    }
  }

  .spinner-border-sm {
    width: 1rem;
    height: 1rem;
  }
}
