<div class="one-funnel-card" *ngIf="insight" [class.clickable]="!isDetailView" (click)="onCardClick()">
  <div class="one-funnel-card-header">
    <div class="d-flex align-items-center">
      <i class="icon" [ngClass]="getIconClass(insight?.flowKey)"></i>
      <span class="ml-2" style="font-weight: 500; font-size: 13px">
        {{ insight.insightTitle }}
      </span>
    </div>
    <button class="btn p-0" style="height: 20px">
      <i class="icon icon-dot3"></i>
    </button>
  </div>
  <div class="equipment-detail">
    <i class="icon icon-excavator"></i>
    <span class="ml-2">{{ insight.serialNumber }}</span>
  </div>

  <div class="one-funnel-date">
    <span>{{ insight.insightDate | date: 'dd.MM.yyyy' }}</span>

    <div
      class="one-funnel-date-badge"
      style="
        background-color: #fff1f1;
        border-radius: 5px;
        padding: 0.15rem 0.25rem;
      "
    >
      <span style="color: #d10000; font-size: 10px"> {{ '_one_funnel_due_in_days' | translate: {days: insight.activeDays} }} </span>
    </div>
  </div>

  <!-- EscalationDescription for detail view -->
  <div *ngIf="isDetailView && insight.escalationDescription" class="escalation-description">
    <p class="text-muted" style="font-size: 13px; margin: 1rem 0;">
      {{ insight.escalationDescription }}
    </p>
  </div>

  <div class="action-btns row">
    <button
      *ngIf="shouldShowGoToEquipmentButton()"
      class="col btn btn-sm btn-warning text-white"
      style="padding: 5px; display: flex; align-items: center; gap: 0.25rem"
      (click)="onGoToEquipment(); $event.stopPropagation()"
    >
      <i class="icon icon-go-to-equipment"></i>
      {{ "_go_to_equipment" | translate }}
    </button>
    <button
      *ngIf="shouldShowSmuButton()"
      class="col btn btn-sm btn-warning text-white"
      style="padding: 5px; display: flex; align-items: center; gap: 0.25rem"
      (click)="onEnterSmu(); $event.stopPropagation()"
    >
      <i class="icon icon-enter-smu"></i>
      {{ "_enter_smu" | translate }}
    </button>
  </div>
</div>

<!-- SMU Modal -->
<app-basic-modal
  [(status)]="showSmuModal"
  [headerText]="'_one_funnel_enter_smu_value' | translate"
  [closeable]="true"
  [backdropClose]="false"
>
  <div class="smu-modal-content">
    <div class="mb-3">
      <p class="text-muted" style="font-size: 14px; line-height: 1.4;">
        {{ '_one_funnel_smu_description' | translate }}
      </p>
    </div>

    <!-- Success Message -->
    <div *ngIf="smuSubmissionSuccess" class="alert alert-success d-flex align-items-center mb-3">
      <i class="icon icon-check-circle me-2"></i>
      <span>{{ '_one_funnel_smu_success' | translate }}</span>
    </div>

    <!-- Error Message -->
    <div *ngIf="smuSubmissionError" class="alert alert-danger mb-3">
      <i class="icon icon-exclamation-triangle me-2"></i>
      <span>{{ smuSubmissionError }}</span>
    </div>

    <!-- Form -->
    <form [formGroup]="smuForm" (ngSubmit)="onSubmitSmu()" *ngIf="!smuSubmissionSuccess">
      <div class="mb-3">
        <label for="smuInput" class="form-label">{{ '_one_funnel_smu_value' | translate }}</label>
        <input
          type="number"
          id="smuInput"
          class="form-control"
          formControlName="smu"
          [placeholder]="'_one_funnel_enter_smu_reading' | translate"
          [class.is-invalid]="smuForm.get('smu')?.invalid && smuForm.get('smu')?.touched"
        />
        <div *ngIf="smuForm.get('smu')?.invalid && smuForm.get('smu')?.touched" class="invalid-feedback">
          <div *ngIf="smuForm.get('smu')?.hasError('required')">{{ '_one_funnel_smu_required' | translate }}</div>
          <div *ngIf="smuForm.get('smu')?.hasError('pattern')">{{ '_one_funnel_smu_invalid_format' | translate }}</div>
        </div>
      </div>

      <div class="d-flex justify-content-center ">

        <button
          class="btn btn-warning btn-gradient text-white"
          type="submit"
          [disabled]="smuForm.invalid || isSubmittingSmu"
        >
          <span *ngIf="isSubmittingSmu" class="spinner-border spinner-border-sm me-2" role="status"></span>
          {{ (isSubmittingSmu ? '_updating' : '_send') | translate}}
        </button>
      </div>
    </form>
  </div>
</app-basic-modal>
