import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class CustomerNotesService {
  constructor(private readonly http: HttpClient) {}

  getCustomerNotes(): Observable<any[]> {
    return this.http
      .post<HttpResponse<any[]>>(
        `${environment.api}/Borusan/customer/notes`,
        {}
      )
      .pipe(
        map((response) => {
          if (response.code === 0) {
            return response.data;
          }
          return [];
        })
      );
  }
}
