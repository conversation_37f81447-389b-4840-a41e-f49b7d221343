<div class="one-funnel-card">
  <div class="title d-flex flex-column align-items-start">
    <div class="d-flex align-items-center">
      <i class="icon icon-description mr-2"></i>
      <div class="one-funnel-date">
        <span class="mr-1" style="font-weight: 500">{{
          "_customer_note" | translate
        }}</span>
        -
        <span class="ml-1">{{
          customerNote.noteDate | date : "dd.MM.yyyy"
        }}</span>
      </div>
    </div>
    <span
      *ngIf="customerNote.opportunitySource"
      class="customer-opportunity-source mt-1"
    >
      {{ "_" + customerNote.opportunitySource | translate }}
    </span>
  </div>

  <p>
    {{ customerNote.note }}
  </p>
</div>
