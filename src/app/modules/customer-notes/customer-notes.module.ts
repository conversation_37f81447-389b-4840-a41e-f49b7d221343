import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { NgSelectModule } from '@ng-select/ng-select';
import { TranslateModule } from '@ngx-translate/core';
import { NgxLoadingModule } from 'ngx-loading';
import { NgxsModule } from '@ngxs/store';
import { SharedModule } from 'src/app/shared/shared.module';
import { stopLoadingAnimation } from 'src/app/util/stop-loading-animation.util';
import { CoreModule } from 'src/app/core/core.module';
import { CustomerNotesListComponent } from './container/customer-notes-list/customer-notes-list.component';
import { CustomerNoteCardComponent } from './components/customer-note-card/customer-note-card.component';
import { CustomerNotesState } from './state/customer-notes.state';
import { CustomerNotesRoutingModule } from './customer-notes-routing.module';

@NgModule({
  declarations: [
    CustomerNotesListComponent,
    CustomerNoteCardComponent,
  ],
  exports: [CustomerNotesListComponent],
  imports: [
    CommonModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    CustomerNotesRoutingModule,
    TranslateModule,
    NgxLoadingModule,
    SharedModule,
    CoreModule,
    NgbTooltipModule,
    NgxsModule.forFeature([CustomerNotesState]),
  ],
})
export class CustomerNotesModule {
  constructor() {
    stopLoadingAnimation();
  }
}
