import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject, timer } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { environment } from '../../../../../environments/environment';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { TranslatePipe } from '@ngx-translate/core';
import { CustomerModel } from 'src/app/shared/models/customer.model';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { CustomerNotesState } from '../../state/customer-notes.state';
import { LoadCustomerNotesAction } from '../../state/customer-notes.actions';

@Component({
  selector: 'app-customer-notes-list',
  templateUrl: './customer-notes-list.component.html',
  styleUrls: ['./customer-notes-list.component.scss'],
})
export class CustomerNotesListComponent implements OnInit, OnDestroy {
  @Select(CustomerState.customer) customer$: Observable<CustomerModel>;
  @Select(CustomerNotesState.customerNotes) customerNotes$: Observable<any>;
  @Select(CustomerNotesState.loading) loading$: Observable<boolean>;

  customer: CustomerModel;
  customerNotes;
  customerNotesTitles = [];
  selectedCustomerNotes = [];
  selectedNoteType = 'All';

  showSubmitModal = false;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly translatePipe: TranslatePipe
  ) {}

  ngOnInit(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: true,
        notificationIcon: false,
        title: this.translatePipe.transform('_customer_notes'),
      })
    );

    this.customer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((customer) => {
        if (customer) {
          console.log('CUST', customer);
          this.customer = customer;
          this.loadCustomerNotes();
        }
      });

    this.customerNotes$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((customerNotes) => {
        const titles = Object.keys(customerNotes)
          .filter((key) => key !== 'partner' && key !== 'name')
          .map((key) => key.charAt(0).toUpperCase() + key.slice(1));
        this.customerNotes = customerNotes;
        this.customerNotesTitles = titles;

        this.customerNoteFilter('All');
      });
  }

  loadCustomerNotes(): void {
    this.store.dispatch(new LoadCustomerNotesAction());
  }

  customerNoteFilter(type: string) {
    //this.selectedNoteType = type;

    if (type.toLowerCase() === 'all') {
       this.selectedCustomerNotes = Object
      .values(this.customerNotes ?? {})
      .reduce<any[]>((acc, item: any) => acc.concat(item?.data ?? []), []);
    } else {
      this.selectedCustomerNotes =
        this.customerNotes[type.toLowerCase()]?.data || [];
    }
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  onClickItem(item: any): void {
    // Navigate to the detail page
    // this.router.navigate([
    //   '/',
    //   ...environment.rootUrl.split('/'),
    //   'boom-guru', 'detail', item.guruRequestId
    // ]);
  }

  navigateToBack() {
    window.history.back();
  }
}
