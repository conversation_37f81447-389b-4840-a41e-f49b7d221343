<div class="px-4 py-3 one-funnel">
  <div class="d-flex align-items-center justify-content-between">
    <h6
      class="mr-2 mb-0"
      style="font-weight: bold; color: #000; font-size: 13px"
    >
      {{ customerNotes?.name }}
    </h6>
    <span style="font-size: 13px">
      {{ customerNotes?.partner }}
    </span>
  </div>

  <ng-select
    class="service-drp mt-2"
    [searchable]="false"
    (change)="customerNoteFilter($event)"
    [placeholder]="'_note_type' | translate"
    [clearable]="false"
    [dropdownPosition]="'bottom'"
  >
    <ng-option *ngFor="let title of customerNotesTitles" [value]="title">{{
      title
    }}</ng-option>
  </ng-select>

  <div class="one-funnel-card-list" *ngIf="!(loading$ | async)">
    <ng-container *ngFor="let note of selectedCustomerNotes">
      <app-customer-note-card *ngIf="note.note" [customerNote]="note">
      </app-customer-note-card>
    </ng-container>
  </div>

  <div *ngIf="selectedCustomerNotes.length === 0" class="text-center py-4">
    <p class="text-muted">{{ "_customerNotes_no_notes" | translate }}</p>
  </div>
</div>

<app-loader [show]="loading$ | async"></app-loader>
