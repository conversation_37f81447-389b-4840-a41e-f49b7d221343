import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { tap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { CustomerNotesService } from '../service/customer-notes.service';
import { LoadCustomerNotesAction } from './customer-notes.actions';

export interface CustomerNotesStateModel {
  customerNotes: any[];
  loading: boolean;
  error: any;
}

@State<CustomerNotesStateModel>({
  name: 'customerNotes',
  defaults: {
    customerNotes: [],
    loading: false,
    error: null,
  },
})
@Injectable()
export class CustomerNotesState {
  constructor(private readonly customerNotesService: CustomerNotesService) {}

  @Selector()
  public static customerNotes(
    state: CustomerNotesStateModel
  ): any[] {
    return state.customerNotes;
  }

  @Selector()
  public static loading(state: CustomerNotesStateModel): boolean {
    return state.loading;
  }

  @Selector()
  public static error(state: CustomerNotesStateModel): any {
    return state.error;
  }

  @Action(LoadCustomerNotesAction)
  loadInsights({ patchState }: StateContext<CustomerNotesStateModel>) {
    patchState({
      loading: true,
      error: null,
    });
    return this.customerNotesService.getCustomerNotes().pipe(
      tap((customerNotes) => {
        patchState({
          customerNotes,
          loading: false,
        });
      }),
      catchError((error) => {
        patchState({
          loading: false,
          error,
        });
        return throwError(error);
      })
    );
  }
}
