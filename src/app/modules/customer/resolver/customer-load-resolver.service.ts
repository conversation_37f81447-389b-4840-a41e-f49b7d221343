import { ActivatedRouteSnapshot, Resolve } from '@angular/router';
import { LoginResponseModel } from '../../authentication/model/login-response.model';
import { Store } from '@ngxs/store';
import { Observable, of } from 'rxjs';
import { Injectable } from '@angular/core';
import { UserState } from '../state/user/user.state';
import { UserAction } from '../state/user/user.actions';

@Injectable()
export class CustomerLoadResolver implements Resolve<LoginResponseModel | boolean> {
  constructor(
    private readonly store: Store,
  ) { }

  resolve(route: ActivatedRouteSnapshot): Observable<any> {
    if (this.store.selectSnapshot(UserState.currentCustomer)) {
      return of(true);
    }

    return this.store.dispatch(new UserAction());
  }

}
