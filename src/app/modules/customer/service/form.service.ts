import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';
import { RfmsFormStatusModel, RfmsIgnoreReasonsFormModel, RfmsIgnoreReasonsFormSendModel } from '../model/form.model';

@Injectable({
  providedIn: 'root'
})
export class FormService {

  constructor(private readonly http: HttpClient) { }

  rfmsIgnoRereasonsFormGet(): Observable<RfmsIgnoreReasonsFormModel[]> {
    return this.http.get<HttpResponse<RfmsIgnoreReasonsFormModel[]>>(`${environment.api}/form/get/rfms/ignorereasons`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  rfmsIgnoRereasonsFormSend(params: RfmsIgnoreReasonsFormSendModel): Observable<any> {
    return this.http.post<any>(`${environment.api}/form/rfms`, params)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getRfmsFormStatus(customerNumber?: string): Observable<RfmsFormStatusModel> {
    return this.http.post<HttpResponse<RfmsFormStatusModel>>(`${environment.api}/form/get/rfms/status`, { customerNumber })
      .pipe(
        map(val => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  sendRequestService(form: any, headers: any = {}): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(`${environment.api}/form/service/create`, form, { headers });
  }
}
