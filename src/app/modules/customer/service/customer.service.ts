import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { MenuModel, MenuTypes } from '../model/menu.model';
import { OttResponse } from '../response/ott.response';
import { CategoryModel, FoundedCatalogModel } from '../model/category.model';
import {
  CatQRModel,
  CustomerContactModel,
  CustomerModel,
  FeedbackModel,
  LeasingModel,
} from 'src/app/shared/models/customer.model';
import {
  EquipmentModel,
  EquipmentRevisionCampaignModel,
} from '../model/equipment.model';
import {
  AccountUsersModel,
  ManageableRolesModel,
  UserRoleModel,
} from '../model/my-users.model';

@Injectable({
  providedIn: 'root',
})
export class CustomerService {
  constructor(private readonly http: HttpClient) {}

  getMenu(publicMenuHeaderCompany: string): Observable<MenuModel[]> {
    const headers: any = {};
    if (publicMenuHeaderCompany) {
      headers.publicMenuHeaderCompany = publicMenuHeaderCompany;
    }

    return this.http
      .get<HttpResponse<MenuModel[]>>(`${environment.api}/catalog/getMenu`, {
        headers,
      })
      .pipe(
        map((val) => {
          // console.log(val);
          if (val.code === 0) {
            // (val.data as any).viewType = (val.data as any).categories ? 'list-view' : 'card-view';
            return val.data;
          }
          return null;
        })
      );
  }

  findMenuCatalog(
    findMenuId: string,
    findProductId: string
  ): Observable<FoundedCatalogModel> {
    return this.http
      .post<HttpResponse<FoundedCatalogModel>>(
        `${environment.api}/catalog/find`,
        {
          findMenuId,
          findProductId,
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getOTT(): Observable<OttResponse> {
    return this.http
      .get<HttpResponse<OttResponse>>(`${environment.api}/user/ott`)
      .pipe(
        map((val) => {
          // console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  fetchCatalog(url: string, publicMenuHeaderCompany = null): Observable<any> {
    const headers: any = {};
    if (publicMenuHeaderCompany) {
      headers.publicMenuHeaderCompany = publicMenuHeaderCompany;
    }
    return this.http
      .get<HttpResponse<any>>(`${environment.api}${url}`, {
        headers,
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            const categories: CategoryModel[] = [];
            if (!val.data?.Data && val.data?.length) {
              val.data.map((item) => {
                categories.push({
                  ...item,
                  title:
                    item.productName ||
                    item.productSubGroupName ||
                    item.productGroupName,
                  imageUrl: item.imageUrl,
                  fetchUrl: null,
                  viewType: MenuTypes.filterable,
                  categories: item.products.map((i) => ({
                    ...i,
                    detail: true,
                    brand: i.brand,
                    model: i.productName,
                    // description: item.LongDescription,
                    title: i.productName,
                    imageUrl: i.imageUrl,
                    webUrl: `${environment.api}${item.FrameAddress}`,
                  })),
                });
              });
            } else if (val.data?.Data?.Categories) {
              val.data.Data.Categories.map((item) => {
                categories.push({
                  title: item.Title,
                  imageUrl: item.Image,
                  fetchUrl: item.Slug,
                });
              });
            } else if (val.data?.Data?.Products) {
              val.data.Data.Products.map((item) => {
                categories.push({
                  detail: true,
                  brand: item.Brand,
                  model: item.ProductModel,
                  description: item.LongDescription,
                  title: item.Title,
                  imageUrl: item.Image,
                  webUrl: `${environment.api}${item.FrameAddress}`,
                });
              });
            }
            return categories;
          }
          return null;
        })
      );
  }

  fetchCampaigns(url: string): Observable<any> {
    return this.http.get<HttpResponse<any>>(`${environment.api}${url}`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  detail(customerNumber: string): Observable<CustomerModel> {
    return this.http
      .post<HttpResponse<CustomerModel>>(`${environment.api}/user/customer`, { customerNumber })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  contacts(companyId: string): Observable<Array<CustomerContactModel>> {
    return this.http
      .get<HttpResponse<Array<CustomerContactModel>>>(
        `${environment.api}/contact/contacts`,
        {
          params: { companyId },
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  sendOrderForm(
    form: any,
    headers = {},
    formUrl: string
  ): Observable<HttpResponse<any>> {
    if (formUrl) {
      return this.http.post<HttpResponse<any>>(
        `${environment.api}/${formUrl}`,
        form,
        {
          headers,
        }
      );
    } else {
      return this.http.post<HttpResponse<any>>(
        `${environment.api}/form/getQuotation`,
        form,
        {
          headers,
        }
      );
    }
  }

  equipmentDetail(
    equipmentNumber: string,
    headers?: any
  ): Observable<EquipmentModel> {
    return this.http
      .post<HttpResponse<EquipmentModel>>(
        `${environment.api}/equipment/detail`,
        { equipmentNumber },
        { headers }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  staticFeedback(): Observable<FeedbackModel> {
    return this.http
      .get<any>(`${environment.api}/feedback/get-mobile-static-feedback`, {
        headers: { SkipError: 'y' },
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  serviceDetail(
    workorderNumber: string,
    customerNumber: string
  ): Observable<any> {
    return this.http
      .post<any>(`${environment.api}/workorder/detail`, {
        workorderNumber, customerNumber,
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  equipmentRevisionCampaign(
    id: string
  ): Observable<EquipmentRevisionCampaignModel> {
    return this.http
      .get<HttpResponse<EquipmentRevisionCampaignModel>>(
        `${environment.api}/equipment/equipmentrevisioncampaign`,
        {
          params: { id },
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  calculateLeasing(body): Observable<LeasingModel> {
    return this.http
      .post<HttpResponse<LeasingModel>>(
        `${environment.api}/leasing/calculate`,
        body
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  sendLeasingCalculateMail(id): Observable<any> {
    return this.http.post<HttpResponse<LeasingModel>>(
      `${environment.api}/leasing/sendEmail?id=${id}`,
      {}
    );
  }

  getCatalogEquipmentDetail(id, lastFetchedId?: string): Observable<any> {
    return this.http
      .post<HttpResponse<any>>(
        `${environment.api}/catalog/getEquipmentDetail`,
        {
          productId: id,
          menuId: lastFetchedId,
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getEquipmentCatQr(value): Observable<CatQRModel> {
    return this.http
      .post<HttpResponse<CatQRModel>>(
        `${environment.api}/equipment/getwithcatqrnumber`,
        {
          catQrNumber: value,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  sendEmailByCart(formBody): Observable<any> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/Leasing/sendEmailByCart`,
      formBody
    );
  }

  getAccountMyUsers(): Observable<AccountUsersModel[]> {
    return this.http
      .get<HttpResponse<AccountUsersModel[]>>(
        `${environment.api}/account/getMyUsers`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getUserRoleDetails(
    referenceId: string,
    portalUserId: string
  ): Observable<UserRoleModel> {
    return this.http
      .get<HttpResponse<UserRoleModel>>(
        `${environment.api}/account/getuserroledetails?referenceid=${referenceId}&portaluserid=${portalUserId}`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getManageableRolesDetail(): Observable<ManageableRolesModel> {
    return this.http
      .get<HttpResponse<ManageableRolesModel>>(
        `${environment.api}/account/getmanageableroles`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  deleteUserFromAccountManager(body: {
    deletedUserId: string;
    description: string;
  }): Observable<any> {
    return this.http
      .delete<any>(`${environment.api}/account/deleteUser`, { body } as any)
      .pipe(
        map((val) => {
          if (val) {
            return val;
          }
          return null;
        })
      );
  }

  editUserRoleDetails(body): Observable<any> {
    return this.http
      .post<HttpResponse<any>>(`${environment.api}/account/editUserRoles`, body)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getActiveApplications(): Observable<any> {
    return this.http
      .get<HttpResponse<any>>(`${environment.api}/account/getapplications`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  approveUser(body: any) {
    return this.http
      .post<HttpResponse<any>>(
        `${environment.api}/account/answerapplication`,
        body
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  generateReferenceNumber(body: any): Observable<any> {
    return this.http
      .post<HttpResponse<any>>(
        `${environment.api}/account/generatereferencenumber`,
        body
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getUserAwaitingAgreements(
    position: string,
    onlyNotApproved: boolean
  ): Observable<any> {
    return this.http
      .get<HttpResponse<any>>(
        `${environment.api}/agreement/get?position=${position}&onlyNotApproved=${onlyNotApproved}`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getUserApprovedAgreements(): Observable<any> {
    return this.http
      .get<HttpResponse<any>>(
        `${environment.api}/agreement/get/appproved/history`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
}
