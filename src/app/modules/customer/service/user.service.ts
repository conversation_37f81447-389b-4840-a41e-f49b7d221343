import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { MeResponse } from '../response/me.response';
import { ChangeCustomerTokenModel, MobileVerifySendModel, MobileVerifyStartModel, PasstwordResetModel } from '../model/user.model';
import { MyUsersModel } from '../model/my-users.model';
import { EditUserRolesModel } from '../model/edit-user-roles.model';
import { DeleteUserModel } from '../model/delete-user.model';
import { UpdateMyUsersModel } from '../model/update-users.model';

@Injectable({
  providedIn: 'root',
})
export class UserService {
  constructor(private readonly http: HttpClient) { }

  getMe(): Observable<MeResponse> {
    return this.http
      .get<HttpResponse<MeResponse>>(`${environment.api}/user/me`, {
        headers: {
          IgnoreHeaders: 'CustomerId'
        }
      })
      .pipe(
        map((val) => {
          // console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  passwordReset(req: PasstwordResetModel): Observable<MeResponse> {
    return this.http
      .post<HttpResponse<MeResponse>>(
        `${environment.api}/user/password/reset`,
        req
      )
      .pipe(
        map((val) => {
          // console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  logout(token): Observable<MeResponse> {
    return this.http
      .post<HttpResponse<MeResponse>>(`${environment.api}/user/logout`, {
        token,
      }, {
        headers: { TOKEN_FREE: 'true' },
      })
      .pipe(
        map((val) => {
          // console.log(val);
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getMyUsers(): Observable<MyUsersModel[]> {
    return this.http
      .get<HttpResponse<MyUsersModel[]>>(`${environment.api}/account/getMyUsers`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  editMyUserRoles(req: Array<EditUserRolesModel>): Observable<MyUsersModel[]> {
    return this.http.post<HttpResponse<UpdateMyUsersModel>>(
      `${environment.api}/account/editUserRoles`, req)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data?.users;
          }
          return null;
        })
      );
  }

  deleteUser(req: DeleteUserModel): Observable<any> {
    return this.http.delete<DeleteUserModel>(
      `${environment.api}/account/deleteUser`, { body: req } as any)
      .pipe(
        map((val) => {
          if (val) {
            return val;
          }
          return null;
        })
      );
  }

  mobileVerifyStart(body: MobileVerifyStartModel): Observable<any> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/user/mobile/verify/start`, body
    )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  mobileVerifySend(body: MobileVerifySendModel): Observable<any> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/user/mobile/verify/code`, body
    )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  changeCustomerToken(body: ChangeCustomerTokenModel): Observable<any> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/notification/register`, body
    )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
}
