import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';
import {
  ContactLocationModel,
  ContactModel,
  FAQModel,
  LeaderCallDetail,
  LeaderUser,
  MobileSupportModel,
  MobileSupportSubjectsModel,
} from '../model/contact';

@Injectable({
  providedIn: 'root',
})
export class ContactService {
  constructor(private readonly http: HttpClient) {}

  callrequest(form: ContactModel): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/form/callrequest/create`,
      form
    );
  }

  locations(companyId: string): Observable<Array<ContactLocationModel>> {
    return this.http
      .get<HttpResponse<Array<ContactLocationModel>>>(
        `${environment.api}/contact/locations`,
        {
          params: { companyId },
        }
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  allLocations(): Observable<Array<ContactLocationModel>> {
    return this.http
      .get<HttpResponse<Array<ContactLocationModel>>>(
        `${environment.api}/contact/alllocations`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  FAQ(): Observable<Array<FAQModel>> {
    return this.http.get<HttpResponse<any>>(`${environment.api}/home/<USER>
      map((val) => {
        if (val.code === 0) {
          return val.data?.rows;
        }
        return null;
      })
    );
  }

  mobileSupportCreate(form: MobileSupportModel): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/form/mobilesupport/create`,
      form
    );
  }
  mobileSupportSubjects(): Observable<Array<MobileSupportSubjectsModel>> {
    return this.http
      .get<HttpResponse<Array<MobileSupportSubjectsModel>>>(
        `${environment.api}/form/mobilesupportsubjects`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
  videoCallLeaders(): Observable<Array<LeaderUser>> {
    return this.http
      .get<HttpResponse<Array<LeaderUser>>>(
        `${environment.api}/videocall/leaders`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  videoCallLeaderDetail(payload: any): Observable<LeaderCallDetail> {
    return this.http
      .post<HttpResponse<LeaderCallDetail>>(
        `${environment.api}/videocall/leadercalldetail`,
        payload
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  videoCallLeaderStatus(payload: { queueCode: string }): Observable<number> {
    return this.http
      .post<HttpResponse<number>>(
        `${environment.api}/videocall/leadercallstatus`,
        payload
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
}
