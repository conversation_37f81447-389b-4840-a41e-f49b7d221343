import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { Store } from '@ngxs/store';
import { FederatedService } from './federated.service';
import { FrameMessageService } from '../../../core/service/frame-message.service';
import { LogService } from '../../../shared/service/log.service';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { SSORedirectKeys } from '../../../shared/enum/sso-pats.enum';

describe('FederatedService', () => {
  let service: FederatedService;
  let httpMock: HttpTestingController;
  let storeMock: jasmine.SpyObj<Store>;
  let frameMessageServiceMock: jasmine.SpyObj<FrameMessageService>;
  let logServiceMock: jasmine.SpyObj<LogService>;

  beforeEach(() => {
    const storeSpyObj = jasmine.createSpyObj('Store', ['dispatch']);
    const frameMessageServiceSpyObj = jasmine.createSpyObj('FrameMessageService', ['startBackgroundFrame', 'stopBackgroundFrame']);
    const logServiceSpyObj = jasmine.createSpyObj('LogService', ['log']);

    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        FederatedService,
        { provide: Store, useValue: storeSpyObj },
        { provide: FrameMessageService, useValue: frameMessageServiceSpyObj },
        { provide: LogService, useValue: logServiceSpyObj }
      ]
    });

    service = TestBed.inject(FederatedService);
    httpMock = TestBed.inject(HttpTestingController);
    storeMock = TestBed.inject(Store) as jasmine.SpyObj<Store>;
    frameMessageServiceMock = TestBed.inject(FrameMessageService) as jasmine.SpyObj<FrameMessageService>;
    logServiceMock = TestBed.inject(LogService) as jasmine.SpyObj<LogService>;
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('getPCCParams', () => {
    it('should fetch PCC parameters successfully', () => {
      const mockResponse: HttpResponse<any> = {
        code: 0,
        data: {
          inApp: 'true',
          dcn: 'S344800',
          locationCode: '01'
        },
        message: null,
        paging: null
      };

      service.getPCCParams().subscribe(result => {
        expect(result).toEqual(mockResponse.data);
      });

      const req = httpMock.expectOne('https://prod.borusancat.com/lgnq/api/catapp/pccparams');
      expect(req.request.method).toBe('POST');
      expect(req.request.body).toEqual({});
      req.flush(mockResponse);
    });

    it('should return null when API returns error code', () => {
      const mockResponse: HttpResponse<any> = {
        code: 1,
        data: null,
        message: 'Error',
        paging: null
      };

      service.getPCCParams().subscribe(result => {
        expect(result).toBeNull();
      });

      const req = httpMock.expectOne('https://prod.borusancat.com/lgnq/api/catapp/pccparams');
      req.flush(mockResponse);
    });
  });

  describe('convertToQueryString', () => {
    it('should convert object to query string', () => {
      const params = {
        inApp: 'true',
        dcn: 'S344800',
        locationCode: '01'
      };

      const result = service.convertToQueryString(params);
      expect(result).toBe('?inApp=true&dcn=S344800&locationCode=01');
    });

    it('should return empty string for null params', () => {
      const result = service.convertToQueryString(null);
      expect(result).toBe('');
    });

    it('should return empty string for empty object', () => {
      const result = service.convertToQueryString({});
      expect(result).toBe('');
    });

    it('should URL encode special characters', () => {
      const params = {
        test: 'value with spaces',
        special: 'value&with=special'
      };

      const result = service.convertToQueryString(params);
      expect(result).toBe('?test=value%20with%20spaces&special=value%26with%3Dspecial');
    });
  });

  describe('appendDynamicParams', () => {
    it('should append parameters to URL without existing query params', () => {
      const baseUrl = 'https://example.com/path';
      const params = {
        inApp: 'true',
        dcn: 'S344800'
      };

      const result = service.appendDynamicParams(baseUrl, params);
      expect(result).toBe('https://example.com/path?inApp=true&dcn=S344800');
    });

    it('should append parameters to URL with existing query params', () => {
      const baseUrl = 'https://example.com/path?existing=param';
      const params = {
        inApp: 'true',
        dcn: 'S344800'
      };

      const result = service.appendDynamicParams(baseUrl, params);
      expect(result).toBe('https://example.com/path?existing=param&inApp=true&dcn=S344800');
    });

    it('should return original URL when params is null', () => {
      const baseUrl = 'https://example.com/path';
      const result = service.appendDynamicParams(baseUrl, null);
      expect(result).toBe(baseUrl);
    });

    it('should return original URL when params is empty', () => {
      const baseUrl = 'https://example.com/path';
      const result = service.appendDynamicParams(baseUrl, {});
      expect(result).toBe(baseUrl);
    });
  });

  describe('getPCCUrlWithDynamicParams', () => {
    it('should return URL with dynamic parameters when cached params exist', () => {
      const baseUrl = SSORedirectKeys.partsCatCom;
      const mockParams = {
        inApp: 'true',
        dcn: 'S344800',
        locationCode: '01'
      };

      spyOn(service, 'appendDynamicParams').and.returnValue(baseUrl + '?inApp=true&dcn=S344800&locationCode=01');
      storeMock.selectSnapshot.and.returnValue(mockParams);

      const result = service.getPCCUrlWithDynamicParams(baseUrl);

      expect(storeMock.selectSnapshot).toHaveBeenCalled();
      expect(service.appendDynamicParams).toHaveBeenCalledWith(baseUrl, mockParams);
      expect(result).toBe(baseUrl + '?inApp=true&dcn=S344800&locationCode=01');
    });

    it('should return original URL when no cached params exist', () => {
      const baseUrl = SSORedirectKeys.partsCatCom;
      storeMock.selectSnapshot.and.returnValue(null);

      const result = service.getPCCUrlWithDynamicParams(baseUrl);

      expect(storeMock.selectSnapshot).toHaveBeenCalled();
      expect(result).toBe(baseUrl);
    });
  });
});
