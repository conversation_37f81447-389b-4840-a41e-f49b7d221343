import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { map } from 'rxjs/operators';
import { SurveyModel } from '../model/survey.model';
import { HttpResponse } from '../../../core/interfaces/http.response';

@Injectable({
  providedIn: 'root',
})
export class SurveyService {
  constructor(private readonly http: HttpClient) {}

  public getList() {
    return this.http
      .get<HttpResponse<SurveyModel[]>>(
        `${environment.api}/survey/activitysurvey`
      )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  public getPulseList() {
    return this.http
      .get<HttpResponse<SurveyModel[]>>(`${environment.api}/survey/pulsesurvey`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  public getPccOrVlinkSurvey(payload: any) {
    return this.http
      .post<HttpResponse<any>>(`${environment.api}/survey/getsurveys`, payload)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data[0];
          }
          return null;
        })
      );
  }

  public sendPccOrVlinkSurveyAnswer(payload: any) {
    return this.http
      .post<HttpResponse<any>>(`${environment.api}/survey/answer`, payload)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }
}
