import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';
import { CompanyCreateModel, CompanyDto } from '../model/company';

@Injectable({
  providedIn: 'root',
})
export class CompanyService {
  data: Observable<Array<CompanyDto>>;

  constructor(private readonly http: HttpClient) {}

  get(): Observable<Array<CompanyDto>> {
    if (!this.data) {
      this.data = this.http
        .get<HttpResponse<Array<CompanyDto>>>(
          `${environment.api}/company/getCompanies`
        )
        .pipe(
          map((val) => {
            if (val.code === 0) {
              return val.data;
            }
            return null;
          }),
          shareReplay(1)
        );
    }
    return this.data;
  }

  create(req: CompanyCreateModel): Observable<HttpResponse<any>> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/applicationform`,
      req
    );
  }
}
