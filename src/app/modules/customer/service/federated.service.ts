import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { SSORedirectKeys } from '../../../shared/enum/sso-pats.enum';
import { FrameMessageService } from '../../../core/service/frame-message.service';
import { Store } from '@ngxs/store';
import { ClearCATSignInCode } from '../../authentication/state/login/login.actions';
import { BackGroundFrameStatusAction } from '../../../shared/state/common/common.actions';
import { LogService } from '../../../shared/service/log.service';
import { HttpResponse } from '../../../core/interfaces/http.response';
import { CustomerState } from '../state/customer/customer.state';

@Injectable({
  providedIn: 'root',
})
export class FederatedService {
  constructor(
    private readonly frameMessageService: FrameMessageService,
    private readonly store: Store,
    private readonly logService: LogService,
    private readonly httpClient: HttpClient,
  ) {}


  startBackgroundFrame(reloginCount) {
    const pccUrlWithParams = this.getPCCUrlWithDynamicParams(SSORedirectKeys.partsCatCom);
    const url = environment.appUrl + '/auth/sso?ru=' + encodeURIComponent(pccUrlWithParams);
    this.frameMessageService.startBackgroundFrame({
      url
    });
    this.store.dispatch(new BackGroundFrameStatusAction('started'));
    this.clearCatSingIn();
    this.logService.log('BACKGROUND_FRAME', 'REQUEST_START', {
      reloginCount,
    }).subscribe();
  }

  stopBackgroundFrame() {
    this.frameMessageService.stopBackgroundFrame();
    this.logService.log('BACKGROUND_FRAME', 'REQUEST_STOP', {}).subscribe();
  }

  clearCatSingIn() {
    this.store.dispatch(new ClearCATSignInCode());
  }

  /**
   * Fetches PCC parameters from the API
   * @returns Observable with the parameters data
   */
  getPCCParams(): Observable<any> {
    return this.httpClient.post<HttpResponse<any>>(`${environment.api}/catapp/pccparams`, {})
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  /**
   * Converts an object to URL query string parameters
   * @param params Object containing key-value pairs
   * @returns URL encoded query string
   */
  convertToQueryString(params: any): string {
    if (!params) {
      return '';
    }

    const queryParams = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');

    return queryParams ? `?${queryParams}` : '';
  }

  /**
   * Appends dynamic parameters to a URL
   * @param baseUrl The base URL to append parameters to
   * @param params The parameters object to append
   * @returns URL with appended parameters
   */
  appendDynamicParams(baseUrl: string, params: any): string {
    if (!params) {
      return baseUrl;
    }

    const queryString = this.convertToQueryString(params);
    if (!queryString) {
      return baseUrl;
    }

    // Check if URL already has query parameters
    const separator = baseUrl.includes('?') ? '&' : '';
    return baseUrl + separator + encodeURIComponent(queryString.substring(1)); // Remove the '?' from queryString
  }

  /**
   * Gets the PCC URL with dynamic parameters from cached state
   * @param baseUrl The base PCC URL
   * @returns URL with appended dynamic parameters if available
   */
  getPCCUrlWithDynamicParams(baseUrl: string): string {
    const cachedParams = this.store.selectSnapshot(CustomerState.pccParams);

    if (cachedParams) {
      return this.appendDynamicParams(baseUrl, cachedParams);
    }

    return baseUrl;
  }
}
