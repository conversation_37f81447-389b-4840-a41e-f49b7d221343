import { LOCALE_ID, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './component/header/header.component';
import { TranslateModule } from '@ngx-translate/core';
import { CompanyModalComponent } from './component/company-modal/company-modal.component';
import { SharedModule } from '../../shared/shared.module';
import { AppListComponent } from './component/app-list/app-list.component';
import { AppDashboardComponent } from './container/app-dashboard/app-dashboard.component';
import { CatalogListComponent } from '../catalog/component/catalog-list/catalog-list.component';
import { SidebarComponent } from './component/sidebar/sidebar.component';
import { IframeViewComponent } from './container/iframe-view/iframe-view.component';
import { CatalogComponent } from './container/catalog/catalog.component';
import { CategoriesComponent } from '../catalog/component/categories/categories.component';
import { RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DefinitionModule } from '../definition/definition.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { CustomerRoutingModule } from './customer-routing.module';
import { PassiveAccountComponent } from './component/passive-account/passive-account.component';
import { ContactModule } from './container/contact/contact.module';
import { SettingsModule } from './container/settings/settings.module';
import { CategoryListViewComponent } from '../catalog/component/categories/category-list-view/category-list-view.component';
import { CategoryCardViewComponent } from '../catalog/component/categories/category-card-view/category-card-view.component';
import { CategoryCampaignViewComponent } from '../catalog/component/categories/category-campaign-view/category-campaign-view.component';
import { CatalogModule } from '../catalog/catalog.module';
import { AgreementApprovalComponent } from './component/approvalable-agreement/agreement-approval.component';
import { RejectedAccountComponent } from './component/rejected-account/rejected-account.component';
import { NewCatalogModule } from '../new-catalog/new-catalog.module';
import { BorusanUserModule } from '../borusan-user/borusan-user.module';
import { IncomingRepeaterComponent } from './container/incoming-repeater/incoming-repeater.component';
import { Store } from '@ngxs/store';
import { LoginState } from '../authentication/state/login/login.state';
import { LeasingCalculationComponent } from './component/leasing-calculation/leasing-calculation.component';
import { HasPermissionsModule } from 'src/app/export/file-upload/permissions/has-permissions.module';
import { OneFunnelModule } from '../one-funnel/one-funnel.module';
import { CustomerNotesModule } from '../customer-notes/customer-notes.module';


@NgModule({
  declarations: [
    HeaderComponent,
    CompanyModalComponent,
    AppListComponent,
    AppDashboardComponent,
    CatalogListComponent,
    SidebarComponent,
    IframeViewComponent,
    CatalogComponent,
    CategoriesComponent,
    PassiveAccountComponent,
    CategoryListViewComponent,
    CategoryCardViewComponent,
    CategoryCampaignViewComponent,
    AgreementApprovalComponent,
    RejectedAccountComponent,
    IncomingRepeaterComponent,
    LeasingCalculationComponent,
  ],
  exports: [
    HeaderComponent,
    CompanyModalComponent,
    SidebarComponent,
    AgreementApprovalComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    SharedModule,
    CustomerRoutingModule,
    RouterModule,
    FormsModule,
    ReactiveFormsModule,
    DefinitionModule,
    NgSelectModule,
    ContactModule,
    OneFunnelModule,
    CustomerNotesModule,
    SettingsModule,
    CatalogModule,
    NewCatalogModule,
    BorusanUserModule,
    CatalogModule,
    HasPermissionsModule
  ],
  providers: [
    {
      provide: LOCALE_ID,
      deps: [Store],
      useFactory: (store: Store) => {
        // console.log('locale lang', store.selectSnapshot(LoginState.language));
        switch (store.selectSnapshot(LoginState.language)) {
          case 'tr':
            return 'tr-TR';
          case 'en':
            return 'en-US';
        }
        return 'tr-TR';
      }
    }
  ]
})
export class CustomerModule {}
