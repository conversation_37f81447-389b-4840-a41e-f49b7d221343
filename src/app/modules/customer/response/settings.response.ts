export interface SettingsResponse {
  passwordPolicies: PasswordPolicies[];
  cdnBaseUrl: string;
  defaults: any;
  defaultTags: SettingsTagItem[];
  emailRegex: string;
  firstUseUrl: string;
  passwordRegex: string;
  twoFactorAuthenticationRegex: string;
  formParametersMaxCharacterLimit: any;
}

export interface PasswordPolicies {
  regex: string;
  description: string;
}

export interface SystemFeature {
  code: string;
  name: string;
  isEnabled: boolean;
}

export interface SocialMediaModel {
  name: string;
  url: string;
}

export interface SettingsTagItem {
  name: string;
  value: string;
}

export interface SettingsTagItemMapped {
  [key: string]: string;
}
