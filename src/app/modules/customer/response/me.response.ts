import { UserModel } from '../../authentication/model/user.model';
import { CustomerRelationModel } from '../model/customer-relation.model';
import { AgreementModel } from '../../definition/model/agreement.model';

export interface MeResponse extends UserModel {
  customerRelationsGrouped: CustomerRelationModel[];
  activeApplications?: ActiveApplicationsModel[];
  customerRelations?: CustomerRelations[];
  agreements?: AgreementModel[];
}

export interface ActiveApplicationsModel {
  id: string;
  customerCompanyName: string;
  countryCode: string;
  cityName: string;
  publicMenuHeaderCompany: string;
}

interface CustomerRelations {
  crmRelatedPersonId: string;
  roleList: string[];
  customer: Customer;
  company: Company;
  modules: Modules[];
}

interface Customer {
  id: string;
  name: string;
  customerNumber: string;
  dataSourceLabel: string;
}

interface Company {
  id: string;
  companyCode: string;
  countryCode: string;
  name: string;
  catDealerCode?: any;
}

interface Modules {
  id: string;
  name: string;
  url: string;
  isAuth: boolean;
  isWebEnabled: boolean;
  isMobileEnabled: boolean;
  thumbnail?: any;
  roles: string[];
  tags?: any;
}

interface CustomerRelationsGrouped {
  groupKey: string;
  groupTitle: string;
  crmRelatedPersonId: string;
  crmPosition: string;
  publicMenuHeaderCompany: string;
  cityName: string;
  customer: Customer2;
  companies: Companies[];
  modules: Modules2[];
}

interface Customer2 {
  id: string;
  name: string;
  customerNumber: string;
  dataSourceLabel: string;
}

interface Companies {
  id: string;
  companyCode: string;
  countryCode: string;
  name: string;
  catDealerCode?: any;
}

interface Modules2 {
  headerCompanies: string;
  companies: Companies2[];
  id: string;
  name: string;
  url: string;
  isAuth: boolean;
  isWebEnabled: boolean;
  isMobileEnabled: boolean;
  thumbnail?: any;
  roles: string[];
  tags?: any;
}

interface Companies2 {
  id: string;
  companyCode: string;
  countryCode: string;
  name: string;
  catDealerCode?: any;
}
