import { Component, OnInit, Input, AfterViewInit, ViewChild, ElementRef, Output, EventEmitter } from '@angular/core';
import { AgreementApprovalBaseComponent } from '../agreement-approval-base/agreement-approval-base.component';
import { ApprovalAgreementUpdatedModel } from 'src/app/modules/definition/model/agreement.model';
import { environment } from 'src/environments/environment';
import { NotificationDeleteAction } from 'src/app/modules/notification/state/notification/notification.actions';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';

@Component({
  selector: 'app-notification-approval-agreement',
  templateUrl: './notification-approval-agreement.component.html',
  styleUrls: ['./notification-approval-agreement.component.scss'],
})
export class NotificationApprovalAgreementComponent  extends AgreementApprovalBaseComponent implements OnInit{
  @ViewChild('target')
  target: ElementRef<HTMLDivElement>;

  @Input() agreement: any;
  @Input() messageId: string;
  @Input() agreementBody: any;
  @Input() hasSkipEnable: boolean;
  @Input() autoShowAgreement: boolean;
  @Output() onBackPressed: EventEmitter<void> = new EventEmitter<void>();
  @Output() agreementApproved: EventEmitter<void> = new EventEmitter<void>();
  @Output() loadingStarted: EventEmitter<any> = new EventEmitter<any>();
  current: ApprovalAgreementUpdatedModel;
  approvedAgreement = false;
  successIcon = `${environment.assets}/success.svg`;
  showAgreementContent = false;
  approveRequiredAgreements = ['CAT_CLOUDSTORAGE_AGREEMENT'];
  approveStatus = false;
  modalContent: any;

  ngOnInit(): void {}

  ngAfterContentChecked() {
    const elements: any = this.target?.nativeElement?.querySelectorAll('a');
    // console.log('element found', elements);
    if (elements?.length > 0) {
      elements.forEach(el => {
        el.onclick = () => {
          this.openFrame(el?.href, el?.innerText);
          return false;
        };
      });
    }
  }

  next() {
    this.text = null;
    const hasSkip: any = this.agreement?.[this.currentIndex]?.hasSkip;
    const approvalAgreementUpdated: ApprovalAgreementUpdatedModel = {
      ...this.agreement[this.currentIndex],
      hasSkip:
        typeof hasSkip === 'string'
          ? JSON.parse(hasSkip?.toLowerCase())
          : hasSkip,
      agreementCode: this.agreement[this.currentIndex]?.userAgreementCode || this.agreement[this.currentIndex]?.name,
    };

    this.current = approvalAgreementUpdated;

    if (!this.current) {
      return;
    }
    this.loading = true;
    this.loadingStarted.emit(true);
    this.definitionService.getAgreementContent(this.current?.url).subscribe(
      (response) => {
        this.text = response;
        this.loading = false;
        this.loadingStarted.emit(false);
      },
      () => {
        this.loadingStarted.emit(false);
        this.loading = false;
      }
    );
  }

  approve(status) {
    this.loading = true;
    let approve;
    console.log('this.current.agreementFormId', this.current?.agreementFormId);

    if (status) {
      approve = this.definitionService.approveUserAgreementFromNotification(
        this?.current?.agreementCode
      );
    }
    approve.subscribe(
      (r) => {
        this.loading = false;
        this.currentIndex++;
        if (this.agreement.length > this.currentIndex) {
          // this.current = this.agreements[this.currentIndex];
          this.next();
        } else {
          this.close();
          this.approvedAgreement = true;
          this.agreementApproved.emit();
          if(this.messageId){
            this.store.dispatch(new NotificationDeleteAction(this.messageId));
          }

          this.logService.log('AGREEMENT_APPROVED', this?.current?.agreementCode, {
            agreement: this.current
          }).subscribe();
        }
      },
      (e) => {
        this.loading = false;
        this.approvedAgreement = false;

        this.modalService.errorModal(
          {
            message: '_general_error_message',
            translate: true,
          },
          this.elementRef.nativeElement
        );
      }
    );
  }

  openFrame(url, title) {
    if (url.slice(0, 4) === 'kvk:') {

      this.definitionService.agreementDetails(url.slice(4))
        .subscribe(data => {
          if (!data.length) {
            return;
          }
          this.modalContent = {
            text: '',
            linkText: '',
            url: data[0]?.url,
          };
        });
    } else {
      this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
        url,
        title
      });
    }
  }

  showAgreement() {
    this.showAgreementContent = true;
  }

  checkApproveRequiredAgreement(agreementCode: string) {
    return this.approveRequiredAgreements.includes(agreementCode);
  }

  onChangeApproveStatus(event) {
    this.approveStatus = event.target.checked;
  }

  navigateBack() {
    if(this.messageId){
      window.history.back();
    }

    this.onBackPressed.emit();
  }
}
