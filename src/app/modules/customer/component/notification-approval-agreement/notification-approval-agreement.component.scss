.modal-btn {
  width: 10em;
}

.agreement {
  height: 20vh;

  display: flex;
  flex-flow: column;

  &-content {
    font-size: 14px;
    overflow: hidden;
    flex-grow: 1;
  }
  &-buttons {
    //height: 3em;
    position: fixed;
    bottom: 0;
    left: 1.5rem;
    right: 1.5rem;
  }
}

.show-more-btn {
  color: #ffa300;
  text-decoration: underline;
  font-size: 15px;
  width: 100%;

  &:focus {
    box-shadow: unset;
  }

  &:hover{
    color: #ffa300 !important;
    text-decoration: underline !important;
  }

  &:active {
    box-shadow: unset;
  }
}

.show-agreement-content {
  height: 75vh;

  .agreement-content {
    overflow: auto;
    padding-bottom: 2rem;
  }
}

.success-agreement-modal {
  position: fixed;
  top: 64px;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  text-align: center;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  background-color: #fff;
  padding: 2rem;
}

[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 4px;
  left: 4px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.success-agreement-modal-without-id{
  position: absolute;
  top: 0;
}
