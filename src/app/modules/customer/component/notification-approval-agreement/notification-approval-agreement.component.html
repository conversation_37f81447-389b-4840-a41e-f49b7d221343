<div
  class="agreement"
  *ngIf="agreement?.length"
  [ngClass]="{ 'show-agreement-content': showAgreementContent || autoShowAgreement}"
>
  <div class="agreement-content" *ngIf="showAgreementContent || autoShowAgreement">
    <div #target [innerHTML]="text | safeHtml" *ngIf="text"></div>

    <ng-container
      *ngIf="checkApproveRequiredAgreement(agreement[0]?.userAgreementCode)"
    >
      <div class="p-2">
        <input
          type="checkbox"
          id="acceptAgreement"
          (change)="onChangeApproveStatus($event)"
        />
        <label for="acceptAgreement">
          {{ "_agree_the_terms" | translate }}
        </label>
      </div>
    </ng-container>
  </div>

  <div class="agreement-buttons">
    <div
      *ngIf="agreement?.length > 1"
      class="d-flex justify-content-center mb-3"
    >
      {{ currentIndex + 1 }} / {{ agreement?.length }}
    </div>
    <div class="d-flex justify-content-between mb-3 mt-2">
      <button
        (click)="navigateBack()"
        class="modal-btn btn btn-secondary btn-sm text-white shadow"
        [disabled]="!current?.hasSkip && !hasSkipEnable"
      >
        {{ "_give_up" | translate }}
      </button>

      <button
        (click)="approve(true)"
        class="modal-btn btn btn-warning btn-sm text-white shadow"
        [disabled]="
          checkApproveRequiredAgreement(agreement[0]?.userAgreementCode) &&
          !approveStatus
        "
      >
        {{ (checkApproveRequiredAgreement(agreement[0]?.userAgreementCode) ? "_save" : "_approve" ) | translate }}
      </button>
    </div>
  </div>
  <button
    class="btn p-0 show-more-btn"
    (click)="showAgreement()"
    *ngIf="!showAgreementContent && !autoShowAgreement"
  >
    {{ "_see_agreement_and_approve" | translate }}
    <i class="icon icon-chevron-down ml-1"></i>
  </button>
</div>

<app-loader [show]="loading"></app-loader>
<div class="success-agreement-modal" [ngClass]="{'success-agreement-modal-without-id': !messageId}" *ngIf="approvedAgreement">
  <img src="{{ successIcon }}" alt="" width="45" height="45" />
  <span>
    {{ "_approve_agreement_message" | translate }}
  </span>
  <div
    class="btn btn-warning btn-gradient btn-block text-white shadow"
    (click)="navigateBack()"
  >
    {{ "_return_back" | translate }}
  </div>
</div>
