import { <PERSON><PERSON>iew<PERSON>nit, Component, <PERSON>ementRef, HostListener, OnDestroy, OnInit, Renderer2 } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { UserState } from '../../state/user/user.state';
import { combineLatest, Observable, Subject } from 'rxjs';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { IframeState, IframeStateModel } from '../../state/iframe/iframe.state';
import { CloseIframeAction, IframeAction } from '../../state/iframe/iframe.actions';
import { CustomerState, HeaderStateModel, } from '../../state/customer/customer.state';
import {
  GetPCCParamsAction,
  HeaderStatusAction,
  HeaderStatusMainAction,
} from '../../state/customer/customer.actions';
import { Location } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { UpdateCatalogAction } from '../../state/catalog/catalog.actions';
import { UserAction } from '../../state/user/user.actions';
import { handleBackButtonUrl } from 'src/app/util/catalog-url.util';
import { environment } from '../../../../../environments/environment';
import { Navigate } from '@ngxs/router-plugin';
import { NotificationState } from '../../../notification/state/notification/notification.state';
import { takeUntil } from 'rxjs/operators';
import { CommonState } from 'src/app/shared/state/common/common.state';
import { UpdateHeaderCompaniesAction } from '../../../authentication/state/login/login.actions';
import { EmitCustomerChanged } from '../../../../shared/state/common/common.actions';
import { DefinitionState } from 'src/app/modules/definition/state/definition/definition.state';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from '../../response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';
import { OneFunnelState } from '../../../one-funnel/state/one-funnel.state';
import { LoadOneFunnelInsightsAction } from '../../../one-funnel/state/one-funnel.actions';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy {
  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(UserState.customers)
  customers$: Observable<SanitizedCustomerModel[]>;

  changeCompanyModal = false;
  sideBar = false;

  @Select(IframeState.getState)
  iframe$: Observable<IframeStateModel>;

  @Select(CustomerState.header)
  status$: Observable<HeaderStateModel>;

  @Select(CustomerState.awaitingAgreements)
  awaitingAgreements$: Observable<any>;

  @Select(CustomerState.surveyList)
  surveyList$: Observable<any>;

  @Select(CustomerState.pulseSurveyList)
  pulseSurveyList$: Observable<any>;

  @Select(DefinitionState.getAgreements)
  getAgreements$: Observable<any>;

  @Select(NotificationState.newIcon)
  newNotification$: Observable<boolean>;


  @Select(UserState.isBorusanUser)
  isBorusanUser$: Observable<boolean>;

  @Select(CommonState.mustChangePassword)
  mustChangePassword$: Observable<boolean>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(IframeState.currentModule)
  currentModule$: Observable<any>;

  @Select(OneFunnelState.insights)
  oneFunnelInsights$: Observable<any[]>;

  iframe: IframeStateModel;
  protected subscriptions$: Subject<boolean> = new Subject();
  isBorusanUser: any;
  cloudStorageAgreement = 'CLOUDSTORAGE_AGREEMENT';
  isUserHaveCloudStorage: boolean;
  showCompanyFullName = false;

  status: HeaderStateModel = {
    company: true,
    backButton: false,
    closeButton: false,
    title: null,
    notificationIcon: true,
    hamburgerMenu: true,
    closeModal: false,
    closeModalMessage: '_close_alert_message',
    bgcolor: 'white',
    img: {
      src: null,
      alt: null,
      altColor: 'black',
    },
  };
  private isCatalogRedirect: boolean;
  systemFeatureMyAgreements: boolean;
  systemFeatureOneFunnel: boolean;
  systemFeatureCustomerNotes: boolean;
  notificationIcon = `${environment.assets}/notifications.svg`;
  borusanIcon = `${environment.assets}/borusan_icon.png`;
  currentModule: any;
  awaitingActionsCount: number;
  funnelInsightsCount: number = 0;

  showCloseModal = false;
  private closeModalFunc: string;
  notToMain: string[] = [];

  constructor(
    private readonly store: Store,
    private readonly activatedRoute: ActivatedRoute,
    private readonly location: Location,
    private readonly router: Router,
    private readonly el: ElementRef,
    private readonly renderer: Renderer2
  ) {
    this.store.dispatch(new UserAction());
    this.router.events.pipe(takeUntil(this.subscriptions$)).subscribe(() => {
      this.changeCompanyModal = false;
    });
  }

  ngOnInit(): void {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customer => {
        if (!customer) {
          return;
        }
        this.store.dispatch(new UpdateHeaderCompaniesAction(
          customer.companies?.map(c => c.id)?.join(';')
        ));

        this.store.dispatch(new EmitCustomerChanged(customer));

        // Load funnel insights when component initializes
        this.store.dispatch(new LoadOneFunnelInsightsAction());

        // Initialize PCC parameters after successful login
        this.store.dispatch(new GetPCCParamsAction());
      });

    this.isBorusanUser$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.isBorusanUser = res;
      });


    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        this.systemFeatureMyAgreements = systemFeature('my_approval_agreements', features, false);
        this.systemFeatureOneFunnel = systemFeature('one_funnel', features, true);
        this.systemFeatureCustomerNotes = systemFeature('customer_note', features, true);
      });

    combineLatest([this.awaitingAgreements$, this.surveyList$, this.pulseSurveyList$])
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any[]) => {
        this.awaitingActionsCount = res.reduce((acc, item) => acc + item?.length, 0);
      });

    this.getAgreements$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.isUserHaveCloudStorage = !!res.some(item => item.name === this.cloudStorageAgreement);
      });
    // console.log('header onInit', status);
    this.status$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((status) => {
        // console.log('header status', status);
        this.status = status;
      });

    this.iframe$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((iframe) => {
        this.iframe = iframe;
        if (iframe.active) {
          // console.log('active frame, header', iframe);

          this.store.dispatch(
            new HeaderStatusAction({
              company: false,
              notificationIcon: false,
              title: null,
              backButton: false,
              closeButton: false,
              hamburgerMenu: true
            })
          );
        }
      });

    this.newNotification$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((status) => {
        this.notificationIcon = status ?
          `${environment.assets}/notification-new.svg` : `${environment.assets}/notifications.svg`;
      });

    this.activatedRoute.queryParams.subscribe(params => {
      let i = 0;
      if (params?.isCatalogRedirect === 'true') {
        this.isCatalogRedirect = true;
        if (i < 1) {
          this.notToMain.push(this.location.path());
        }
        i += 1;
        return;
      }
    });

    this.currentModule$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.currentModule = data;
      });

    // Subscribe to funnel insights count
    this.oneFunnelInsights$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((insights: any[]) => {
        this.funnelInsightsCount = insights?.length || 0;
      });

  }

  changeCompany() {
    this.changeCompanyModal = true;
  }

  openCompanyCreated() {
    this.changeCompanyModal = false;
    this.store.dispatch(new Navigate(['/settings/createaccount']));
  }

  backToHome() {
    if (this.status.closeModal) {
      this.showCloseModal = true;
      this.closeModalFunc = 'backToHome';
      return;
    }

    if(this.status.sendLog){
      this.status.sendLog();
    }

    this.store.dispatch(new HeaderStatusMainAction());
    this.store.dispatch(new Navigate(['/']));
  }

  closeFrame() {
    if (this.status.closeModal) {
      this.showCloseModal = true;
      this.closeModalFunc = 'closeFrame';
      return;
    }
    console.log('closed');
    this.store.dispatch(new CloseIframeAction());
  }

  onClickBack() {

    if (this.status.closeModal) {
      this.showCloseModal = true;
      this.closeModalFunc = 'onClickBack';
      return;
    }
    console.log('back button');
    window.scroll({ top: 0 });

    const url = handleBackButtonUrl(this.location.path());

    if (url === '/') {

      if (this.notToMain.length > 0) {
        if (this.currentModule && this.isCatalogRedirect) {
          this.store.dispatch(new Navigate(['module', this.currentModule?.id]));
          this.store.dispatch(
            new IframeAction(
              {
                url: this.currentModule?.url,
                active: true,
                closeButton: true,
                pageTitle: this.currentModule?.name,
              },
              true
            )
          );
        } else {
          window.history.back();
        }
      } else {
        this.store.dispatch(new HeaderStatusMainAction());

        this.router.navigate([url]).then();
      }
      this.isCatalogRedirect = false;
      return;
    }

    if (this.isCatalogRedirect) {


      console.log('url:: ', url);
      this.router.navigate([url],
        {
          queryParams: {
            isCatalogRedirect: true
          },
          replaceUrl: true
        }).then(() => {
        // this.isCatalogRedirect = false;
        this.store.dispatch(new UpdateCatalogAction());
      });
      return;
    }
    // else if (url === null) {
    //   this.location.back();
    //   return;
    // }
    this.location.back();

    // this.location.go(url);

    // this.store.dispatch(new UpdateCatalogAction());
  }

  openMenu() {
    this.sideBar = true;
  }

  openNotification() {
    this.store.dispatch(new Navigate(['notifications']));
  }

  closeModalYes() {
    this.store.dispatch(new HeaderStatusAction({ closeModal: false }));
    this.showCloseModal = false;
    this[this.closeModalFunc]();
    return;
  }

  closeModalNo() {
    this.showCloseModal = false;
    return;
  }

  handleShowCompanyFullName(){
    this.showCompanyFullName = !this.showCompanyFullName;
  }

  calculateCurrentCustomerNameSize(name: string | null){
    const nameLength = name?.replace(/\s/g, '')?.length;
    if(nameLength > 25){
      return true;
    }
    return false;
  }

  openOneFunnel(){
    this.store.dispatch(new Navigate(['/one-funnel']));
  }

  openCustomerNotes(){
    this.store.dispatch(new Navigate(['/customer-notes']));
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
