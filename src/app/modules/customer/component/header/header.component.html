<app-basic-modal
  [(status)]="changeCompanyModal"
  [headerText]="'_select_company' | translate"
>
  <app-company-modal
    [(status)]="changeCompanyModal"
    (createCompany)="openCompanyCreated()"
  ></app-company-modal>
</app-basic-modal>

<app-sidebar [(visible)]="sideBar"></app-sidebar>

<div
  class="site-header"
  [class.orange-bg]="this.status.bgcolor === 'orange'"
  [class.transparent-bg]="this.status.bgcolor === 'transparent'"
  [class.iframe-header]="iframe.active"
>
  <div *ngIf="status.company" class="company-detail">
    <h1
      class="m-0"
      [ngClass]="{
        wide: showCompanyFullName,
        'calculated-font-size': calculateCurrentCustomerNameSize(
          (currentCustomer$ | async)?.displayName
        )
      }"
      (click)="handleShowCompanyFullName()"
    >
      {{ (currentCustomer$ | async)?.displayName }}
    </h1>
    <button
      class="btn btn-link"
      (click)="changeCompany()"
      appClickLog
      [section]="'APP_HEADER'"
      [subsection]="'CHANGE_COMPANY_CLICK'"
    >
      <img
        *ngIf="isBorusanUser$ | async"
        class="borusanIcon"
        [src]="borusanIcon"
        alt="Borusan"
      />
      {{ "_change_company" | translate }}
      <i class="icon icon-chevron-right font-size-11px"></i>
    </button>
  </div>

  <div
    *ngIf="status.title"
    class="title"
    [ngClass]="{ center: true }"
    [innerHTML]="status.title | safeHtml"
  ></div>

  <div *ngIf="status?.img?.src" class="title" [ngClass]="{ center: true }">
    <div
      class="d-flex flex-column align-items-center title-content"
      [style]="status?.titleContentStyle"
    >
      <img [src]="status.img.src" [alt]="status.img.alt" />
      <div
        *ngIf="status?.img?.alt"
        class="img-alt"
        [class.black-alt-color]="this.status.img.altColor === 'black'"
        [class.gold-alt-color]="this.status.img.altColor === 'gold'"
        [class.blue-alt-color]="this.status.img.altColor === 'blue'"
        [class.orange-alt-color]="this.status.img.altColor === 'orange'"
      >
        {{ status.img.alt | translate }}
      </div>
    </div>
  </div>

  <button
    *ngIf="status.closeButton && !(mustChangePassword$ | async)"
    type="button"
    class="button close"
    (click)="backToHome()"
  >
    <i class="icon icon-x"></i>
  </button>

  <button
    *ngIf="
      (status.backButton || iframe?.backButton) &&
      !(mustChangePassword$ | async)
    "
    type="button"
    class="button back"
    (click)="onClickBack()"
  >
    <i class="icon icon-back"></i>
  </button>

  <ng-container *ngIf="iframe?.active">
    <button
      *ngIf="iframe?.closeButton"
      type="button"
      class="button close"
      (click)="closeFrame()"
    >
      <i class="icon icon-x"></i>
    </button>

    <div *ngIf="iframe?.pageTitle" class="title iframe-title">
      {{ iframe?.pageTitle }}
    </div>
  </ng-container>

  <div class="header-action-btns">
    <button
      *ngIf="status.hamburgerMenu"
      type="button"
      class="button hamburger-menu"
      (click)="openMenu()"
      appClickLog
      [section]="'APP_HEADER'"
      [subsection]="'OPEN_MENU'"
    >
      <i class="icon icon-hamburger"></i>
      <span
        *ngIf="
          awaitingActionsCount > 0 &&
          !isBorusanUser &&
          !isUserHaveCloudStorage &&
          systemFeatureMyAgreements
        "
        class="notification-badge"
        >{{ awaitingActionsCount }}</span
      >
    </button>

    <button
      *ngIf="status.notificationIcon"
      type="button"
      class="button notification"
      (click)="openNotification()"
      appClickLog
      [section]="'APP_HEADER'"
      [subsection]="'NOTIFICATION_CLICK'"
    >
      <img [src]="notificationIcon" alt="Notification" />
    </button>
  </div>

  <div
    class="d-flex align-items-center py-1"
    style="gap: 0.5rem"
    *ngIf="status.company"
  >
    <button
      *ngIf="isBorusanUser && systemFeatureCustomerNotes"
      type="button"
      class="customer-note-btn ml-auto"
      (click)="openCustomerNotes()"
    >
      <i class="icon icon-contract"></i>
    </button>
    <button
      *ngIf="isBorusanUser && systemFeatureOneFunnel"
      type="button"
      class="one-funnel-btn"
      (click)="openOneFunnel()"
    >
      <i class="icon icon-yellow-funnel"></i>
      <span *ngIf="funnelInsightsCount > 0" class="notification-badge">{{
        funnelInsightsCount
      }}</span>
    </button>
  </div>
</div>

<app-basic-modal [(status)]="showCloseModal">
  <div class="mb-3">
    <div class="text-center">
      {{ status.closeModalMessage || "_close_alert_message" | translate }}
    </div>
    <div class="mx-auto text-center mt-4">
      <button
        class="modal-btn btn-sm btn btn-warning btn-gradient text-white shadow col-4 mr-3"
        (click)="closeModalYes()"
      >
        {{ "_close" | translate }}
      </button>
      <button
        class="modal-btn btn-sm btn btn-secondary btn-gradient text-white shadow col-4"
        (click)="closeModalNo()"
      >
        {{ "_cancel" | translate }}
      </button>
    </div>
  </div>
</app-basic-modal>
