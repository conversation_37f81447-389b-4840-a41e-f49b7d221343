.site-header {
  display: inline-block;
  //position: fixed;
  background: #fff;
  top: 0;
  left: 0;
  width: 100%;
  padding: 11px;
  z-index: 50;
  min-height: 57px;

  &.iframe-header {
    background: #f5f4f4;
  }

  &.orange-bg {
    background: #ffa300;
  }

  &.transparent-bg {
    background: transparent;
  }

  .company-detail {
    float: left;
    max-width: 80%;
    margin: 5px 0;
    padding: 0 0 0 11px;

    h1 {
      font-size: 20px;
      line-height: 28px;
      font-weight: 700;
      color: #2c2c2c;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis;
    }

    .btn-link {
      width: inherit;
      height: inherit;
      font-size: 13px;
      line-height: 19px;
      color: #4a8eb0;
      margin-top: 5px;
      padding: 0;

      svg {
        position: relative;
        left: -8px;
        top: 1px;

        path {
          fill: #4a8eb0;
        }
      }
    }
  }

  button {
    border: none;
    background: none;
    width: 40px;
    height: 35px;

    &.back {
      float: left;
      font-size: 19px;
      line-height: 2em;
    }

    &.close {
      float: left;
      position: relative;
      line-height: 1em;
      // top: 3px;
    }

    &.notification {
      float: right;

      img {
        margin-bottom: 1px;
      }
    }

    &.hamburger-menu {
      float: right;
      line-height: 0.8em;
      position: relative;
    }

    svg {
      width: 30px;
    }
  }

  .title {
    float: left;
    font-size: 20px;
    color: #2c2c22;
    font-weight: bold;
    line-height: 35px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    max-width: 70%;

    &.center {
      position: absolute;
      left: 50%;
      transform: translateX(-50%);
    }

    &.iframe-title {
      font-weight: normal;
      font-size: 16px;
    }

    img {
      max-height: 32px;
    }
  }

  .borusanIcon {
    height: 18px;
    border-radius: 4px;
  }
}

.img-alt {
  color: #ffa300;
  border: none;
}

.black-alt-color {
  color: #2c2c22;
}

.gold-alt-color {
  color: rgba(238, 188, 72, 1);
}

.blue-alt-color {
  color: rgba(77, 142, 160, 1);
}

.orange-alt-color {
  color: #ffa300;
}

button .notification-badge {
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: -4px;
  right: -3px;
  background-color: red;
  color: white;
  //padding: 3px 6px;
  font-size: 12px;
  line-height: 12px;

  width: 15px; /* or height */
  aspect-ratio: 1;
  border-radius: 50%;
}

.header-action-btns {
  display: flex;
  align-items: center;
  flex-direction: row-reverse;
}

.wide-company-customer-name {
  font-size: 15px !important;
}

.wide {
  white-space: unset !important;
}

.calculated-font-size {
  font-size: 15px !important;
}

.one-funnel-btn {
  width: 32px !important;
  height: 32px !important;
  border-radius: 4px !important;
  background-color: #f5f5f5 !important;
  font-size: 20px;
  padding: 3px 4px;
  position: relative;


  ::before {
    color: #ffa300;
  }

}
.customer-note-btn {
  width: 32px !important;
  height: 32px !important;
  border-radius: 4px !important;
  background-color: #f5f5f5 !important;
}
