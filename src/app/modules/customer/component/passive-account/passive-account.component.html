<div class="d-flex flex-column align-items-center justify-content-center h-100">
  <div class="app-passive-account">
    <img [src]="warningIcon"/>
    <p [innerHTML]="'_account_passive_message' | translate"></p>
  
  </div>
  <div class="agreement" *ngIf="!(isGdprApproved$ | async) && show">
    <form (submit)="onSubmitForm()" [formGroup]="form">
      <app-agreement-list
        [form]="form"
        [formType]="agreementTypeEnum.PendingApplicationForm"
      ></app-agreement-list>
  
      <div class="text-center">
        <input [disabled]='this.buttonDisabled || this.loading' [ngClass]="{ 'disabled': buttonDisabled }" class="btn btn-warning py-1 text-white" type="submit"
               [value]="'_send' | translate">
      </div>
  
    </form>
  
  </div>
</div>
