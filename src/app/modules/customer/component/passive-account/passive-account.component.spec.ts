import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PassiveAccountComponent } from './passive-account.component';

describe('PassiveAccountComponent', () => {
  let component: PassiveAccountComponent;
  let fixture: ComponentFixture<PassiveAccountComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ PassiveAccountComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(PassiveAccountComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
