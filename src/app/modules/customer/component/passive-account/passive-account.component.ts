import { Component, OnInit } from '@angular/core';
import { environment } from '../../../../../environments/environment';
import { Select, Store } from '@ngxs/store';
import { DefinitionService } from '../../../definition/service/definition.service';
import { AgreementTypeEnum } from '../../../definition/enum/agreement-type.enum';
import { FormGroup } from '@angular/forms';
import { UserState } from '../../state/user/user.state';
import { Observable } from 'rxjs';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { UpdateUserLoadingAction } from '../../state/user/user.actions';
import { ClearTokenAction } from '../../../authentication/state/login/login.actions';
import { LogService } from '../../../../shared/service/log.service';
import { LoggerService } from 'src/app/shared/service/logger.service';
import { GetUserAwaitingAgreements } from '../../state/customer/customer.actions';
import { GetAgreementsAction, GetUserAgreementsAction } from 'src/app/modules/definition/state/definition/definition.actions';

@Component({
  selector: 'app-passive-account',
  templateUrl: './passive-account.component.html',
  styleUrls: ['./passive-account.component.scss'],
})
export class PassiveAccountComponent implements OnInit {
  warningIcon = `${environment.assets}/warning.svg`;
  form: FormGroup = new FormGroup({
    // agreements: new FormControl()
  });
  loading = false;
  show = true;
  @Select(UserState.isGdprApproved)
  isGdprApproved$: Observable<boolean>;
  agreementTypeEnum = AgreementTypeEnum;
  buttonDisabled = true;

  constructor(
    private readonly store: Store,
    private readonly definitionService: DefinitionService,
    private readonly frameMessageService: FrameMessageService,
    private readonly log: LogService,
    private readonly logger: LoggerService,
  ) {}

  ngOnInit(): void {
    this.form.valueChanges.subscribe((items) => {
      console.log('change', items);
      this.buttonDisabled = this.findHeaders().length === 0;
    });
  }

  findHeaders() {
    const entries = Object.entries(this.form.get('agreements').value)
      .filter(([key, value]) => value)
      .map((a) => a[0]);
    return entries;
  }

  onSubmitForm() {
    this.buttonDisabled = true;
    const entries = this.findHeaders();
    if (entries.length === 0) {
      return;
    }
    const headers = { ApprovedAgreementNames: entries.join(';') };
    this.loadingState(true);

    this.store.dispatch(new UpdateUserLoadingAction(true));

    this.log.action('PASSIVE_ACCOUNT', 'APPROVE_AGGREEMENT', {
      agreements: headers,
    }).subscribe();

    this.definitionService.approveAgreement(headers).subscribe(
      (item) => {
        this.frameMessageService.sendMessage(FrameMessageEnum.tokenChanged, {
          tokenResponse: item.tokenResponse,
        });
        if (!item.tokenResponse){
          const logMessage = {
            message: 'Server returned empty token while the agreement was being approved.',
            name: 'Token Empty Error',
            url: window.location.href,
          };
          this.logger.jsErrorLog(logMessage).subscribe();
        }

        this.store.dispatch(new ClearTokenAction());
        //hesap aktifleştirince sözleşmelerin gözükmesi için gerekli actionlar.
        this.store.dispatch(new GetUserAwaitingAgreements('', true));
        // TODO bunlar sonra ortaklanacak.
        this.store.dispatch(new GetUserAgreementsAction(AgreementTypeEnum.PromotionPortal, true));
        this.store.dispatch(new GetAgreementsAction(AgreementTypeEnum.PromotionPortal, true));

        setTimeout(() => this.loadingState(false), 30000);

        this.show = false; // hide agreements
        // window.location.reload();
      },
      (err) => {
        this.loadingState(false);
        this.buttonDisabled = false;
      }
    );
  }

  protected loadingState(value) {
    this.store.dispatch(new UpdateUserLoadingAction(value));
    this.loading = value;
  }
}
