import { AfterViewInit, Component, ElementRef, Input, OnInit } from '@angular/core';
import { ApprovalAgreementModel } from '../../../definition/model/agreement.model';
import { Select } from '@ngxs/store';
import { UserState } from '../../state/user/user.state';
import { Observable } from 'rxjs';
import { DefinitionService } from '../../../definition/service/definition.service';
import { ModalService } from '../../../../shared/service/modal.service';
import { disableBack } from '../../../../util/disable-back.util';

@Component({
  selector: 'app-agreement-approval',
  templateUrl: './agreement-approval.component.html',
  styleUrls: ['./agreement-approval.component.scss']
})
export class AgreementApprovalComponent implements OnInit, AfterViewInit {

  @Input() notification: any;

  @Select(UserState.approvalAgreements)
  approvalAgreements$: Observable<ApprovalAgreementModel[]>;

  agreements: ApprovalAgreementModel[] = [];
  loading: boolean;
  status = true;

  constructor() { }

  ngOnInit(): void {
    this.approvalAgreements$.subscribe(agreements => {
      if (!agreements?.length) {
        return;
      }
      this.agreements = agreements || [];
    });
  }

  popupClose() {
    this.status = false;
  }

  ngAfterViewInit(): void {

  }
}
