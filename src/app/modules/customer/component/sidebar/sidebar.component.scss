@import "variable/bootstrap-variable";

.app-sidebar {
  position: fixed;
  width: 100vw;
  height: 100vh;
  overflow: auto;
  top: 0;
  left: 0;
  z-index: 100;
  background: #fff;
  transform: translateX(100%);

  &.show {
    transform: translateX(0);
  }

  .app-sidebar-area {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
      position: sticky;
      top: 0;
      z-index: 1;
      background: #ffa300;
      padding: 22px;
      line-height: 1em;

      .close {
        font-size: 20px;
        color: #ffffff;
      }

      .info {
        width: 100%;
        display: inline-block;
        margin-top: 24px;

        .top-left {
          float: left;
          width: 62%;

          .name {
            color: #fff;
            font-size: 18px;
            font-weight: 700;
            line-height: 21px;
            width: 100%;
            white-space: nowrap;
            overflow: hidden !important;
            text-overflow: ellipsis;
          }

          .company-name {
            color: #fff;
            font-size: 14px;
            font-weight: 600;
            line-height: 21px;
            width: 100%;
            white-space: nowrap;
            overflow: hidden !important;
            text-overflow: ellipsis;
          }
        }

        .top-right {
          float: right;

          .icon {
            font-size: 2.5rem;
          }
        }
      }

      .borusanIcon {
        height: 20px;
        border-radius: 4px;
        border: 1px solid #e4e8ebad;
      }
    }

    .sidebar-content {
      .app-modules {
        margin: 6px 22px;

        .app-list {
          height: inherit;
          overflow: inherit;

          ul {
            padding: 0;
            margin: 0;

            li {
              display: inline-block;
              width: 100%;
              border-radius: 0;
              text-align: left;
              flexpccparams: none;
              margin: 7.5px 0 !important;
              min-height: inherit;

              .image-area {
                float: left;
                width: 40px;
                height: 40px;
                border-radius: 100%;
                background: #ebebeb;

                img {
                  bottom: 0;
                  transform: translateX(-50%) translateY(-50%);
                  left: 50%;
                  top: 50%;
                  max-width: 75%;
                }
              }

              .title {
                float: left;
                font-size: 16px;
                line-height: 24px;
                font-weight: 400;
                margin-left: 20px;
              }
            }
          }
        }
      }

      .nav-links {
        border-top: 1px solid #dbdbdb;
        //margin: 22px 0;
        padding: 16px 35px;
        padding-bottom: 0;

        &-icon {
          font-size: 20px;
        }

        ul {
          list-style: none;
          padding: 0;

          li {
            display: block;
            margin: 8px 0;
            color: #505050;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            padding: 8px 0;

            svg {
              float: left;
              margin-top: 4px;
            }

            span {
              display: inline-block;
              margin-left: 18px;
            }
          }
        }
      }
    }

    .bottom-area {
      margin-top: auto;
      padding-bottom: 18px;

      .borusan-cat-logo {
        display: flex;
        justify-content: center;
      }

      .version {
        font-size: 13px;
        text-align: center;
      }
    }
  }
}

.btn-main {
  background: #ffffff;
  border: 1px solid #bababa;
  border-radius: 4px;
  font-size: 13px;
  line-height: 27px;
  color: #505050;
  padding: 0 10px;
  font-weight: 600;
}

.static-feedback {
  color: $warning;
}
.survey {
  //color: $warning;
}

.coin-history-icon {
  width: 24px;
  height: 24px;
}

.diamond-history-icon {
  width: 24px;
  height: 21px;
}
.boom-guru-icon {
  width: 24px;
  height: 24px;
}

.gold-history-icon {
  width: 24px;
  height: 21px;
}

.coin-icon {
  width: 40px;
  height: 40px;
  margin-left: 5px;
}

.coin-number {
  color: white;
  font-size: 20px;
  position: absolute;
  right: 70px;
  top: 77px;
  font-weight: 600;
}

.coin-number-diamond{
  color: white;
  font-size: 20px;
  position: absolute;
  right: 90px;
  top: 77px;
  font-weight: 600;
}

.coin-number-gold{
  color: white;
  font-size: 20px;
  position: absolute;
  right: 105px;
  top: 77px;
  font-weight: 600;
}

.version-info{
  padding: 1rem 35px;
  border-top: 1px solid #DBDBDB;
  border-bottom: 1px solid #DBDBDB;
  margin-bottom: 2rem;

  .event-link{
    text-decoration: underline;
    color: #437DA9;
  }
}

.notification-badge {
  background-color: red;
  color: white;
  border-radius: 50%;
  padding: 0px 8px;
  margin-left: 8px !important;
  font-size: 15px;
}
