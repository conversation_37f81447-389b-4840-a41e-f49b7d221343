import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { UserState, UserStateModel } from '../../state/user/user.state';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { LogoutProcessAction } from '../../../authentication/state/login/login.actions';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { Navigate } from '@ngxs/router-plugin';
import { CommonState } from '../../../../shared/state/common/common.state';
import { environment } from 'src/environments/environment';
import {
  CustomerDetailAction,
  CustomerDetailResetAction,
  GetPulseSurveysAction,
  GetSurveysAction,
  SidebarStatusClearAction,
  StaticFeedbackAction
} from '../../state/customer/customer.actions';
import { CustomerState } from '../../state/customer/customer.state';
import { FeedbackModel } from '../../../../shared/models/customer.model';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { map, takeUntil } from 'rxjs/operators';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import {
  ContactWorkingHoursAction,
  SystemFeatureAction
} from '../../../../shared/state/settings/settings.actions';
import { SystemFeature } from '../../response/settings.response';
import { systemFeature } from '../../../../util/system-feature.util';
import { BorusanUserLdapData } from 'src/app/modules/authentication/model/user.model';
import { PromotionState } from 'src/app/modules/promotion/state/promotion.state';
import { DefinitionState } from 'src/app/modules/definition/state/definition/definition.state';
import { UserAgreementsListModel } from 'src/app/modules/definition/model/agreement.model';
import {
  GetAllCountryListAction,
  GetUserAgreementsAction
} from 'src/app/modules/definition/state/definition/definition.actions';
import { AgreementTypeEnum } from 'src/app/modules/definition/enum/agreement-type.enum';
import { Router } from '@angular/router';
import { PromotionAction, PssrAccountAction } from 'src/app/modules/promotion/state/promotion.action';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { PssrAccountModel } from 'src/app/modules/promotion/model/promotion.model';
import { PermissionEnum } from 'src/app/modules/definition/enum/permission.enum';
import { CatalogService } from '../../../catalog/service/catalog.service';
import { CustomerModuleService } from '../../service/customer-module.service';
import { SurveyModel } from '../../model/survey.model';
import { LoginState } from '../../../authentication/state/login/login.state';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit, OnDestroy {
  @Input()
  visible = false;

  @Output()
  visibleChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Select(CommonState.version)
  version$: Observable<string>;

  @Select(UserState.getState)
  user$: Observable<UserStateModel>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(CustomerState.staticFeedback)
  staticFeedback$: Observable<FeedbackModel>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(PromotionState.getTotalPoints)
  totalPoint$: Observable<number>;

  @Select(DefinitionState.getForceAprovalAgreement)
  forceAgreement$: Observable<UserAgreementsListModel[]>;
  hasForceAgreement = false;

  faqShow = true;
  boomCoinShow = false;
  boomClubAccessible = false;
  surveyShow: boolean;
  pulseSurveyShow: boolean;

  @Select(UserState.borusanUserData)
  borusanUserData$: Observable<BorusanUserLdapData>;

  @Select(UserState.isBorusanUser)
  isBorusanUser$: Observable<boolean>;

  @Select(CustomerState.sidebarStatus)
  sidebarStatus$: Observable<boolean>;

  @Select(UserState.isGdprApproved)
  isGdprApproved$: Observable<boolean>;

  @Select(CustomerState.awaitingAgreements)
  awaitingAgreements$: Observable<any>;

  @Select(CustomerState.surveyList)
  surveyList$: Observable<SurveyModel[]>;

  @Select(CustomerState.pulseSurveyList)
  pulseSurveyList$: Observable<SurveyModel[]>;

  isGdprApproved: boolean;

  isPassiveAccount: boolean;
  boomClubShow: boolean;
  boomclubBorusanUser: boolean;
  awaitingApprovalsNumber: any;
  user: UserStateModel;
  systemFeaturescanQr: boolean;
  systemFeatureOfflineMode: boolean;
  systemFeatureAccountManager: boolean;
  systemFeatureMyAgreements: boolean;
  offlineAndQrVersionController: boolean;
  protected subscriptions$: Subject<boolean> = new Subject();

  borusanCatLogo = `${environment.assets}/borusan-cat-logo.svg`;
  borusanIcon = `${environment.assets}/borusan_icon.png`;
  qr = `${environment.assets}/qr.png`;
  pssrAccount: PssrAccountModel;
  boomDiamondSystemFeature: boolean;
  boomGoldSystemFeature: boolean;
  boomGuruSystemFeature: boolean;
  isUserHaveCloudStorage: boolean;
  cloudStorageAgreement = 'CLOUDSTORAGE_AGREEMENT';

  @Select(PromotionState.getPssrAccount)
  pssrAccount$: Observable<PssrAccountModel>;


  @Select(SettingsState.borusanBlockedActions)
  borusanBlockedActions$: Observable<string[]>;

  @Select(DefinitionState.getAgreements)
  getAgreements$: Observable<any>;

  @Select(CommonState.backGroundFrameStatus)
  backGroundFrameStatus$: Observable<any>;

  @Select(CommonState.backGroundFrameLastUrl)
  backGroundFrameLastUrl$: Observable<any>;

  @Select(LoginState.CATSignInCode)
  CATSignInCode$: Observable<any>;

  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  PermissionEnum = PermissionEnum;
  showWhatIsNew = false;
  showJustAClick = false;
  backgroundLoginLogs = false;
  awaitingSurveyCount: number = 0;
  awaitingPulseSurveyCount: number = 0;

  constructor(
    private readonly store: Store,
    private readonly messageService: FrameMessageService,
    private readonly router: Router,
    private readonly frameMessageService: FrameMessageService,
    private readonly catalogService: CatalogService,
    private readonly customerModuleService: CustomerModuleService,
  ) { }

  ngOnInit(): void {
    this.awaitingAgreements$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.awaitingApprovalsNumber = res?.length;
      });
    this.awaitingAgreements$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.awaitingApprovalsNumber = res?.length;
      });

    this.user$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res) => {
        this.user = res;
      });
    this.sidebarStatus$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res) => {
        if (!res) {
          this.close();
          this.store.dispatch(new SidebarStatusClearAction(true));
        }
      });
    this.getAgreements$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.isUserHaveCloudStorage = !!res.some(item => item.name === this.cloudStorageAgreement);
      });

    this.surveyList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.awaitingSurveyCount = res?.length;
      });
    this.pulseSurveyList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res: any) => {
        this.awaitingPulseSurveyCount = res?.length;
      });

    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res) => {
        if (res) {
          if (this.store.selectSnapshot(CustomerState.customer)?.customerNumber !== res.customer?.customerNumber) {
            if (res.customer?.customerNumber) {
              this.store.dispatch(new CustomerDetailAction(res.customer?.customerNumber));
            } else {
              this.store.dispatch(new CustomerDetailResetAction());
            }
          }

          if (!this.store.selectSnapshot(SettingsState.contactWorkingHours)?.length) {
            this.store.dispatch(new ContactWorkingHoursAction());
          }
          this.isGdprApproved = this.store.selectSnapshot(UserState.isGdprApproved);
          this.isPassiveAccount = res?.passive;

          this.store.dispatch(new GetAllCountryListAction());
          // ? SystemFeatureAction & UserAgreementsAction
          this.store.dispatch([
            new SystemFeatureAction(),
            new GetUserAgreementsAction(AgreementTypeEnum.PromotionPortal, true),
          ]).pipe(map(() => this.store.selectSnapshot(DefinitionState.getForceAprovalAgreement)))
            .subscribe((forceAgreements) => {
              this.hasForceAgreement = !!forceAgreements?.length;

              const features = this.store.selectSnapshot(SettingsState.systemFeatures);
              const isBorusanUser = this.store.selectSnapshot(UserState.isBorusanUser);

              const boomCoinSystemFeature = systemFeature('loyality_point', features, false);
              const boomCoinBorusanUser = systemFeature('loyality_point_borusan_user', features, false);
              const boomCoinShow = systemFeature('loyality_point', features, false);
              this.systemFeaturescanQr = systemFeature('scan_qr', features, false);
              this.systemFeatureOfflineMode = systemFeature('offline_mode', features, false);
              this.systemFeatureAccountManager = systemFeature('account_manager', features, false);
              this.systemFeatureMyAgreements = systemFeature('my_approval_agreements', features, false);
              this.boomClubShow = systemFeature('boomclub', features, false);
              this.boomDiamondSystemFeature = systemFeature('boom_diamond', features, false);
              this.boomGoldSystemFeature = systemFeature('boom_gold', features, false);
              this.boomGuruSystemFeature = systemFeature('boom_guru', features, false);
              this.showWhatIsNew = systemFeature('whats_new', features, true);
              this.showJustAClick = systemFeature('just_a_click', features, false);
              this.backgroundLoginLogs = systemFeature('background_login_logs', features, false);
              this.surveyShow = systemFeature('survey_forms', features, false);
              this.pulseSurveyShow = systemFeature('pulse_survey_forms', features, false);

              if (this.boomClubShow) {
                this.boomClubAccessible = true;
                if (isBorusanUser) {
                  this.boomclubBorusanUser = systemFeature('boomclub_borusan_user', features, true);
                  this.boomClubAccessible = this.boomclubBorusanUser;
                }
              } else {
                this.boomClubAccessible = false;
              }

              this.boomCoinShow = boomCoinSystemFeature && (
                boomCoinBorusanUser || (!isBorusanUser && !boomCoinBorusanUser)
              ) && !this.isPassiveAccount;

              if (isBorusanUser && boomCoinBorusanUser && !this.hasForceAgreement) {
                this.store.dispatch(new PromotionAction());
              } else if (boomCoinShow && !isBorusanUser && !this.hasForceAgreement) {
                this.store.dispatch(new PromotionAction());
              }

              if (this.surveyShow) {
                this.store.dispatch(new GetSurveysAction());
              }
              if (this.pulseSurveyShow) {
                this.store.dispatch(new GetPulseSurveysAction());
              }
            });
        }
      });

    this.offlineAndQrVersionController = appVersionController(this.store.selectSnapshot(CommonState.version), 'offlineAndQr');
    this.forceAgreement$
      .subscribe(forceAgreements => {
        this.hasForceAgreement = !!forceAgreements?.length;
      });

    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.faqShow = systemFeature('faq', features, true);

      }
    });
    this.store.dispatch(new PssrAccountAction());
    this.pssrAccount$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.pssrAccount = data;
      });
    // feedback
    this.store.dispatch(new StaticFeedbackAction());
  }

  close() {
    this.visible = false;
    this.visibleChange.emit();
  }

  goToLink(s: string) {
    this.store.dispatch(new Navigate([s]));
    this.close();
  }

  goToPromotionPage() {
    if (!this.hasForceAgreement || this.pssrAccount?.isPssrUser || this.pssrAccount?.isPssrManagerUser) {
      this.router.navigate(['promotion', 'coin']);
      this.close();
      return;
    }
    this.router.navigate(['promotion']);
    this.close();
    return;
  }

  goToBoomGuruPage() {
    this.customerModuleService.openBoomGuruForm();

    this.close();
  }

  offlineMode() {
    this.frameMessageService.sendMessage(FrameMessageEnum.openOfflineMenu);
  }

  goToScanQr() {
    this.close();
    this.frameMessageService.sendMessage(FrameMessageEnum.scanQr);
    // this.customerModuleService.openModuleInner({
    //   relativeUrl: '/customer/work-order/********/**********',
    //   pageTitle: 'Servis Detay',
    // })
  }

  goBoomClubPage() {
    this.router.navigate(['promotion', 'boom-club']);
    this.close();
    return;
  }


  handleLogOut() {
    this.store.dispatch(new LogoutProcessAction());
  }

  onClickFeedback(feedback) {
    this.messageService.sendMessage(FrameMessageEnum.staticFeedback, feedback);
    this.close();
  }

  onClickSurvey() {
    this.customerModuleService.openSurveyForm();
    this.close();
  }

  onClickPulseSurvey() {
    this.customerModuleService.openSurveyForm({
      link: '/pulse-survey',
    });
    this.close();
  }

  handleWhatIsNew() {
    this.catalogService.navigateTaggedCatalog('whatIsNew');
    this.close();
    return;
  }


  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
