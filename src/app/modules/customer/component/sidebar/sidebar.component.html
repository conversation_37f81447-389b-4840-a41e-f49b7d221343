<div class="app-sidebar" [ngStyle]="{ transition: visible ? 'all 0.3s ease' : 'all 0.3s ease;' }"
     [ngClass]="{ show: visible }">
  <div class="app-sidebar-area">
    <div class="top">
      <div class="d-flex flex-row flex-nowrap justify-content-between">
        <i class="icon icon-x close" (click)="close()" appClickLog [section]="'MENU'" [subsection]="'CLOSE'"></i>

        <small *ngIf="(borusanUserData$ | async)?.ldapUsername || (borusanUserData$ | async)?.isLdapLogin"
               class="text-white">
          {{ (borusanUserData$ | async)?.ldapUsername }}
          <img class="borusanIcon" [src]="borusanIcon" alt="Borusan">
        </small>
      </div>

      <div class="info">
        <div class="top-left">
          <div class="name">
            {{ user?.isLdapLogin ? user?.ldapFullname : user?.firstName + ' ' + user?.lastName }}
            <!-- {{ (user$ | async).firstName }} {{ (user$ | async).lastName }} -->
          </div>
          <div class="company-name">
            {{ (user$ | async).currentCustomer?.displayName }}
          </div>
        </div>
        <div [hasPermission]="PermissionEnum.MenuBoomCoin"
             *ngIf="boomCoinShow  && ((!pssrAccount?.isPssrUser && !pssrAccount?.isPssrManagerUser) || !(boomDiamondSystemFeature && boomGoldSystemFeature)) && !((borusanBlockedActions$ | async) && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.ViewBoomCoinPage) >= 0)"
             class="top-right" (click)="goToPromotionPage()">
          <span *ngIf="!hasForceAgreement" class="coin-number">{{ totalPoint$ | async }}</span>
          <img src="assets/boom-coin-single.png" class="coin-icon"/>
          <!-- <i class="icon icon-boom-logo"></i> -->
          <!--        <button *ngIf="!(currentCustomer$ | async)?.passive" class="btn btn-main">{{'_manage_account' | translate}}</button>-->
          <!--        <button *ngIf="(currentCustomer$ | async)?.passive" class="btn btn-main">{{'_waiting_approval' | translate}}</button>-->
        </div>
        <div
          *ngIf="boomDiamondSystemFeature && pssrAccount?.isPssrManagerUser && !((borusanBlockedActions$ | async) && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.ViewBoomCoinPage) >= 0) "
          class="top-right" (click)="goToPromotionPage()">
          <span class="coin-number-diamond">{{ pssrAccount?.boomCoin }}</span>
          <img src="assets/boom-diamond.png" class=""/>
        </div>
        <div
          *ngIf="boomGoldSystemFeature && pssrAccount?.isPssrUser && !((borusanBlockedActions$ | async) && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.ViewBoomCoinPage) >= 0) "
          class="top-right" (click)="goToPromotionPage()">
          <span class="coin-number-gold">{{ pssrAccount?.boomCoin }}</span>
          <img src="assets/kulcealtin.svg" class=""/>
        </div>
        <div
          *ngIf="((borusanUserData$ | async)?.isLdapLogin && (boomDiamondSystemFeature || boomGoldSystemFeature || boomCoinShow)) || (!(borusanUserData$ | async)?.isLdapLogin && boomCoinShow) else boomLogo"></div>
        <ng-template #boomLogo>
          <div class="top-right">
            <i class="icon icon-boom-logo"></i>
          </div>
        </ng-template>

      </div>
    </div>
    <div class="sidebar-content">
      <div *ngIf="!(currentCustomer$ | async)?.passive" class="app-modules">
        <app-app-list [mini]="true" (onSelect)="close()"></app-app-list>
      </div>
      <div class="nav-links">
        <ul>
          <li *ngIf="surveyShow"
              (click)="onClickSurvey()"
              appClickLog [section]="'CONTACT'" [subsection]="'SURVEY_FORM_CLICK'">
            <div class="float-left survey">
              <i class="icon icon-contract nav-links-icon"></i>
            </div>
            <span class="survey">{{ "_surveys" | translate }}</span>
            <span *ngIf="awaitingSurveyCount > 0" class="notification-badge">{{ awaitingSurveyCount }}</span>
          </li>
          <li *ngIf="pulseSurveyShow"
              (click)="onClickPulseSurvey()"
              appClickLog [section]="'CONTACT'" [subsection]="'PULSE_SURVEY_FORM_CLICK'">
            <div class="float-left survey">
              <i class="icon icon-contract nav-links-icon"></i>
            </div>
            <span class="survey">{{ "_pulse_surveys" | translate }}</span>
            <span *ngIf="awaitingPulseSurveyCount > 0" class="notification-badge">{{ awaitingPulseSurveyCount }}</span>
          </li>

          <li *ngIf="showJustAClick"
              [hasPermission]="PermissionEnum.VideoCallBirTikMesafedeyiz"
              (click)="goToLink('/settings/click-away')"
              appClickLog [section]="'MENU'" [subsection]="'ACCOUNT_JUST_A_CLICK_AWAY'">
            <div class="float-left">
              <i class="icon icon-videocall nav-links-icon"></i>
            </div>
            <span [innerHTML]="'_just_a_click_away_menu' | translate | safeHtml"></span>
            <!--        <span style="color:#FFA300">BİR TIK</span> Mesafedeyiz-->
            <!--        <span style="color:#FFA300">Just</span> a <span style="color:#FFA300">Click</span> Away-->
          </li>
          <li (click)="goToLink('/contact/menu')" appClickLog [section]="'MENU'" [subsection]="'CONTACT_CLICK'">
            <div class="float-left">
              <i class="icon icon-contact nav-links-icon"></i>
            </div>
            <span>{{ "_contact_us" | translate }}</span>
          </li>
          <li (click)="goToLink('/settings/menu')" appClickLog [section]="'MENU'" [subsection]="'SETTINGS_CLICK'">
            <div class="float-left">
              <i class="icon icon-settings nav-links-icon"></i>
            </div>
            <span>{{ "_settings" | translate }}</span>
          </li>
          <li *ngIf="systemFeatureAccountManager" [hasPermission]="PermissionEnum.AccountManagement"
              (click)="goToLink('/settings/account-manager/customers')" appClickLog [section]="'MENU'"
              [subsection]="'ACCOUNT_MANAGER_CLICK'" class="d-flex align-items-center">
            <div class="float-left">
              <i class="icon icon-persons nav-links-icon"></i>
            </div>
            <span>{{ "_account_manager" | translate }}</span>
          </li>
          <li *ngIf="!user.isLdapLogin && systemFeatureMyAgreements && !isUserHaveCloudStorage"
              (click)="goToLink('/settings/my-agreements/awaiting-approvals')" appClickLog [section]="'MENU'"
              [subsection]="'ACCOUNT_MY_AGREEMENTS_CLICK'">
            <div class="float-left">
              <i class="icon icon-contract nav-links-icon"></i>
            </div>
            <span>{{ "_my_agreements" | translate }}</span>
            <span *ngIf="awaitingApprovalsNumber > 0" class="notification-badge">{{ awaitingApprovalsNumber }}</span>
          </li>
          <li [hasPermission]="PermissionEnum.RequestsApplicationSupport" (click)="goToLink('/settings/app-support')"
              appClickLog [section]="'CONTACT'" [subsection]="'APP_SUPPORT_CLICK'">
            <div class="float-left">
              <i class="icon icon-headset nav-links-icon"></i>
            </div>
            <span>{{ "_app_support" | translate }}</span>
          </li>
          <li [hasPermission]="PermissionEnum.MenuQr" *ngIf="systemFeaturescanQr && offlineAndQrVersionController"
              (click)="goToScanQr()" appClickLog [section]="'MENU'" [subsection]="'QR_CLICK'">
            <div class="float-left">
              <img class="coin-history-icon" [src]="qr"/>
            </div>
            <span style="margin-left: 14px">{{ "_qr_scan" | translate }}</span>
          </li>
          <li [hasPermission]="PermissionEnum.MenuBoomCoin"
              *ngIf="boomCoinShow && ((!pssrAccount?.isPssrUser && !pssrAccount?.isPssrManagerUser) || !(boomDiamondSystemFeature && boomGoldSystemFeature))"
              (click)="goToPromotionPage()" appClickLog [section]="'BOOM_COIN'" [subsection]="'BOOM_COIN_CLICK'">
            <div class="float-left">
              <img class="coin-history-icon" src="assets/boom-coin-single.png"/>
            </div>
            <span style="margin-left: 14px">{{ "_boom_coin" | translate }}</span>
          </li>
          <li *ngIf="boomDiamondSystemFeature && pssrAccount?.isPssrManagerUser" (click)="goToPromotionPage()"
              appClickLog [section]="'BOOM_COIN'" [subsection]="'BOOM_COIN_CLICK'">
            <div class="float-left">
              <img class="diamond-history-icon" src="assets/boom-diamond.png"/>
            </div>
            <span style="margin-left: 14px">{{ pssrAccount?.boomCoinTypeName }}</span>
          </li>

          <li *ngIf="boomGuruSystemFeature" (click)="goToBoomGuruPage()" appClickLog
              [section]="'BOOM_GURU'" [subsection]="'BOOM_GURU_CLICK'">
            <div class="float-left">
              <img class="boom-guru-icon" src="assets/mag_star.svg"/>
            </div>
            <img style="margin-left: 14px; height: 22px" class="my-2" class="" src="assets/boom_guru.svg"/>
          </li>

          <li *ngIf="boomGoldSystemFeature && pssrAccount?.isPssrUser" (click)="goToPromotionPage()" appClickLog
              [section]="'BOOM_COIN'" [subsection]="'BOOM_COIN_CLICK'">
            <div class="float-left">
              <img class="" src="assets/sidebar_kulcealtin.svg"/>
            </div>
            <span style="margin-left: 14px">{{ pssrAccount?.boomCoinTypeName }}</span>
          </li>
          <li [hasPermission]="PermissionEnum.BoomClub" *ngIf="boomClubAccessible && !((borusanBlockedActions$ | async)
              && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.UseBoomClub) >= 0)"
              (click)="goBoomClubPage()">
            <div class="float-left">
              <img class="coin-history-icon" src="assets/boom_club.svg"/>
            </div>
            <span style="margin-left: 14px; color: #ffa300;">Boom Club</span>
          </li>
          <li *ngIf="systemFeatureOfflineMode && offlineAndQrVersionController" (click)="offlineMode()" appClickLog
              [section]="'MENU'" [subsection]="'OFFLINE_MODE_CLICK'">
            <div class="float-left">
              <img class="coin-history-icon" src="assets/offline_mode.svg"/>
            </div>
            <span style="margin-left: 14px">{{ "_offline_mode" | translate }}</span>
          </li>
          <li *ngIf="faqShow" (click)="goToLink('/contact/faq')" appClickLog [section]="'CONTACT'"
              [subsection]="'FAQ_CLICK'">
            <div class="float-left">
              <i class="icon icon-question nav-links-icon"></i>
            </div>
            <span>{{ "_faq" | translate }}</span>
          </li>
          <ng-container *ngIf="(staticFeedback$ | async) as feedback">
            <li [hasPermission]="PermissionEnum.MenuFeedback" *ngIf="!feedback.hasAnswered
                && !((borusanBlockedActions$ | async) && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.SendFeedback) >= 0)"
                (click)="onClickFeedback(feedback)"
                appClickLog
                [section]="'CONTACT'"
                [subsection]="'FEEDBACK_CLICK'">
              <div class="float-left static-feedback">
                <i class="icon icon-rate font-weight-bold nav-links-icon"></i>
              </div>
              <span class="static-feedback">{{ "_rate_us" | translate }}</span>
            </li>
          </ng-container>


          <li (click)="handleLogOut()" appClickLog [section]="'MENU'" [subsection]="'LOGOUT'">
            <div class="float-left" style="width: 20px">
              <i class="icon icon-exit nav-links-icon" style="font-size: 19px"></i>
            </div>
            <span>{{ "_log_out" | translate }}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="bottom-area">
      <div class="version-info py-3 d-flex align-items-center justify-content-between">
        <span class="font-weight-semi-bold">
          {{ '_version' | translate }} {{ version$ | async }}
        </span>
        <a
          *ngIf="showWhatIsNew"
          appClickLog
          [section]="'MENU'"
          [subsection]="'WHAT_IS_NEW'"
          (click)="handleWhatIsNew()" class="font-size-14px event-link">
          {{ '_what_is_new' | translate }}
        </a>

        <!--  version güncelse ? Yenilikleri gör : Güncel Sürüm Mevcut -->
      </div>
      <div *ngIf="backgroundLoginLogs" class="px-3" style="word-wrap: break-word">
        <div *ngIf="CATSignInCode$| async">
          CAT Signin OK
        </div>
        {{ backGroundFrameStatus$| async }}
      </div>
      <div class="borusan-cat-logo">

        <img [src]="borusanCatLogo"/>
      </div>
      <!-- <div class="version">{{ version$ | async }}</div> -->
    </div>
  </div>
</div>
