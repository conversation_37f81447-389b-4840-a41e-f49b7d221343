import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { ChangeCustomerAction } from '../../state/user/user.actions';
import { Observable, Subject } from 'rxjs';
import { UserState } from '../../state/user/user.state';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { HeaderStatusMainAction } from '../../state/customer/customer.actions';
import { Navigate } from '@ngxs/router-plugin';
import { LogService } from '../../../../shared/service/log.service';
import { LogSubSectionEnum } from '../../../definition/enum/log-sub-section.enum';
import { UpdateAppStorageAction } from '../../../../shared/state/common/common.actions';
import { takeUntil } from 'rxjs/operators';
import { SystemFeatureAction } from 'src/app/shared/state/settings/settings.actions';
import {SettingsState} from '../../../../shared/state/settings/settings.state';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { ResetCampaignAction } from '../../state/catalog/catalog.actions';

@Component({
  selector: 'app-company-modal',
  templateUrl: './company-modal.component.html',
  styleUrls: ['./company-modal.component.scss'],
})
export class CompanyModalComponent implements OnInit, OnDestroy, OnChanges {
  @Select(UserState.isGdprApproved)
  isGdprApproved$: Observable<boolean>;
  @Select(UserState.activeApplications)
  activeApplications$: Observable<any[]>;
  @Select(UserState.customers)
  customers$: Observable<SanitizedCustomerModel[]>;

  @Select(UserState.isBorusanUser)
  isBorusanUser$: Observable<boolean>;
  isBorusanUser = false;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;
  @Output()
  createCompany: EventEmitter<null> = new EventEmitter<null>();

  @Output()
  statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input()
  status: boolean;
  currentCustomer: SanitizedCustomerModel;
  customers: SanitizedCustomerModel[];
  searchTerm = '';
  keyDownOnSearch = false;

  @Select(SettingsState.borusanBlockedActions)
  borusanBlockedActions$: Observable<string[]>;
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly log: LogService
  ) { }

  ngOnInit(): void {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((customer) => {
        this.currentCustomer = customer;
        this.customers$
          .pipe(takeUntil(this.subscriptions$))
          .subscribe(data => {
            this.customers = data.slice().sort(this.compareNamesAndPassive);
          });
      });

    this.isBorusanUser$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(isBorusan => {
        if (isBorusan) {
          this.isBorusanUser = isBorusan;
        }
      });
  }

  compareNamesAndPassive(a, b) {
    if (a.passive && b.passive) {
      if (a.name < b.name) {
        return -1;
      }
      if (a.name > b.name) {
        return 1;
      }
      return 0;
    }
    if (a.passive) {
      return 1;
    }
    if (b.passive) {
      return -1;
    }
    if (a.name < b.name) {
      return -1;
    }
    if (a.name > b.name) {
      return 1;
    }
    return 0;
  }
  searchCompany(customers): SanitizedCustomerModel[] {
    if (this.searchTerm) {
      const search = this.searchTerm.toLowerCase();
      return customers
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          return 0;
        });
    }
    return customers.sort(this.compareNamesAndPassive);
  }
  changeCompany(customer: SanitizedCustomerModel) {
    this.store.dispatch(new ChangeCustomerAction(customer));
    this.status = false;
    this.store.dispatch(new HeaderStatusMainAction());
    this.store.dispatch(new Navigate(['dashboard']));
    this.store.dispatch(new UpdateAppStorageAction({
      currentCustomerTitle: customer.groupTitle
    }));

    this.statusChange.emit();
    this.log
      .action(LogSubSectionEnum.HEADER, 'COMPANY_CHANGED', {
        customerNumber: customer?.customer?.customerNumber,
        displayName: customer?.displayName,
      })
      .subscribe();
    this.store.dispatch(new SystemFeatureAction());
    this.searchTerm = '';
    this.store.dispatch(new ResetCampaignAction());
  }

  openNewCompanyModal() {
    this.createCompany.emit();
  }

  goBorusanPage() {
    this.store.dispatch(new Navigate(['borusan']));
  }

  ngOnChanges(): void {
    this.searchTerm = '';
  }


  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  keypress(){
    this.keyDownOnSearch = true;
    setTimeout(() => {
      this.keyDownOnSearch = false;
    }, 500);
  }

  goToTop(){
    if (this.keyDownOnSearch){
      document.getElementsByClassName('company-list')[0].scrollTop = 0;
      console.log('scroll');
    }
  }
}
