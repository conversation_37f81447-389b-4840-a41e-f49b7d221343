<div class="company-popup">
  <div *ngIf="customers.length > 7" class="search-area position-relative mb-4">
    <input #search [placeholder]="'_search' | translate" class="form-control search-input" type="text"
           [(ngModel)]="searchTerm" placement="bottom" (keydown)="keypress()"/>
    <i class="icon icon-search"></i>
  </div>
  <ul class="company-list" (scroll)="goToTop()">
    <li *ngFor="let customer of searchCompany(customers)| search:'name':searchTerm; index as currentIndex" [ngClass]="{'block-click': customer.displayName == currentCustomer?.displayName}">
      <div>
        <input
          type="radio"
          [id]="'company' + currentIndex"
          name="groupName"
          [checked]="customer.displayName == currentCustomer?.displayName"
          (click)="changeCompany(customer)"
        />
        <label [for]="'company' + currentIndex">
          {{ customer.displayName }}
        </label>
      </div>
    </li>
  </ul>
  <div class="company-add-button">
      <button
        class="btn btn-link text-info"
        (click)="openNewCompanyModal()"
        appClickLog
        [section]="'COMPANY_MODAL'"
        [subsection]="'ADD_COMPANY_CLICK'"
        *ngIf="
          !(
            !(isGdprApproved$ | async) && (activeApplications$ | async)?.length > 0
          ) && !((borusanBlockedActions$ | async) && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.AddAccount) >= 0)
        "
      >
        <i class="icon icon-plus"></i>
        {{ "_add_account" | translate }}
      </button>
      <button *ngIf="isBorusanUser" class="btn btn-link text-info" (click)="goBorusanPage()">
        <i class="icon icon-chevron-right font-size-11px"></i>
        {{ "_company_search" | translate }}
      </button>
  </div>
</div>
