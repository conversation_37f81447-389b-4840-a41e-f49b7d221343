import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ModuleCodeEnum } from 'src/app/shared/enum/module-code.enum';

import { environment } from '../../../../../environments/environment';
import { getOS } from '../../../../util/os.util';
import { UpdateHeaderCompaniesAction } from '../../../authentication/state/login/login.actions';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { UserState } from '../../state/user/user.state';
import { ModalService } from '../../../../shared/service/modal.service';
import { TranslatePipe } from '@ngx-translate/core';
import { VideocallState } from '../../state/videocall/videocall.state';
import { VideocallAvailableAgentModel } from '../../model/videocal.model';
import { VideoCallManagerService } from '../../../../shared/service/video-call-manager.service';
import { CatalogState } from '../../state/catalog/catalog.state';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from '../../response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';
import { GetModuleAction } from '../../state/iframe/iframe.actions';
import { LogService } from 'src/app/shared/service/log.service';
import { CustomerModuleService } from '../../service/customer-module.service';

@Component({
  selector: 'app-app-list',
  templateUrl: './app-list.component.html',
  styleUrls: ['./app-list.component.scss'],
})
export class AppListComponent implements OnInit, OnDestroy {
  protected subscriptions$: Subject<boolean> = new Subject();

  @Input()
  mini = false;

  // tslint:disable-next-line:no-output-on-prefix
  @Output()
  onSelect: EventEmitter<boolean> = new EventEmitter<boolean>();

  modules: any[];

  @Select(UserState.customers)
  customers$: Observable<SanitizedCustomerModel[]>;
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;
  @Select(VideocallState.getVideocallAvailableAgent)
  videocallAvailableAgent$: Observable<VideocallAvailableAgentModel>;
  videocallLoading = false;
  systemFeatureCatPcc = false;
  systemFeatureCatSis = false;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly modalService: ModalService,
    private readonly translate: TranslatePipe,
    private readonly videoCallManagerService: VideoCallManagerService,
    private readonly log: LogService,
    private readonly customerModuleService: CustomerModuleService
  ) { }


  ngOnInit(): void {

    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        if (features) {
          this.systemFeatureCatPcc = systemFeature('cat_pcc', features, false);
          this.systemFeatureCatSis = systemFeature('cat_sis', features, false);
        }
      });


    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(customer => {
        if (customer) {
          // this.store.dispatch(new UpdateHeaderCompaniesAction(
          //   customer.companies?.map(c => c.id)?.join(';')
          // )); MOVED to header

          this.modules = customer.modules.map(item => {
            return {
              ...item,
              imageUrl: item?.imageUrl ? item.imageUrl : `${environment.imageApi}/image/module?moduleid=${item.id}`
            };
          });
        }
      });
  }

  showModule(code: string) {
    if (code === ModuleCodeEnum.PCC) {
      return this.systemFeatureCatPcc;
    } else if (code === ModuleCodeEnum.SIS) {
      return this.systemFeatureCatSis;
    }

    return true;
  }

  handleRedirect(module: any) {
    if (module?.isMaintenance) {
      this.modalService.errorModal({
        message: module?.maintenanceMessage || this.translate.transform('_module_maintenance_message')
      });
      return;
    }
    this.onSelect.emit(true);
    this.store.dispatch(new UpdateHeaderCompaniesAction(module.headerCompanies));

    if (module.code === ModuleCodeEnum.OpportunityProducts) {
      this.log.action('OpportunityProduct', 'OPEN').subscribe();
      const catalog = this.store.selectSnapshot(CatalogState.catalog);
      const constructionEquipments = catalog.find((c) => c.tagList?.includes('constructionEquipments'));
      catalog.forEach((category) => {
        category?.categories.forEach((subCategory) => {
          if (subCategory?.tagList?.includes('opportunityProducts')) {
            this.router.navigate(
              [
                'catalog',
                constructionEquipments?.id,
                'categories',
                subCategory?.id,
              ],
              { queryParams: { opportunityProducts: '/catalog/getEquipmentV2ByIsPrice' } }
            );
            return;
          }
        });
      });
      return;
    }
    // if (module.code === ModuleCodeEnum.PCC) {
    //   this.store.dispatch(new Navigate(['auth/sso'], {
    //     ru: SSORedirectKeys.partsCatCom
    //   }));
    //   return;
    // }

    if (module?.tags?.find(tag => tag === 'redirect')) {
      window.open(module.url, '_self');
      return;
    }

    if (module?.tags?.find(tag => tag === 'externalview')) {
      this.openExternalModule(module);
      return;
    }


    if (module.code === ModuleCodeEnum.LiveSupport) {
      this.videocallSubs('LiveSupport');
      return;
    }


    // some history navigation not working on IOS, hack
    if (this.router.url.startsWith('/module') && getOS() === 'IOS') {
      this.router.navigate(['dashboard']);
      setTimeout(() =>
        this.router.navigate(['module', module.id], {
          state: { module }
        }), 0);
      return;
    }
    this.store.dispatch(new GetModuleAction(module));
    this.router.navigate(['module', module.id], { state: { module } });
  }

  videocallSubs(queueName) {
    this.videoCallManagerService.startVideoCall(queueName);

  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  private openExternalModule(module: any) {
    this.customerModuleService.openExternalModule(module);
  }
}
