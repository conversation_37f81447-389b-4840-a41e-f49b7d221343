<app-category-campaign-view *ngIf="!mini"></app-category-campaign-view>
<div class="app-list" [ngClass]="{ 'app-list-mini': mini }">
  <ul *ngIf="modules?.length else moduleNull">
    <ng-container *ngFor="let module of modules">
      <li (click)="handleRedirect(module)"
          appClickLog [section]="mini ? 'MENU' : 'DASHBOARD'"
          [subsection]="'MODULE_CLICK_' + module.code | uppercase" [data]="{moduleId:module.id}"
          *ngIf="showModule(module?.code)"
          [attr.disabled]="module?.disabled"
      >
        <div class="module-card">
          <div class="image-area menu-image-only-ios">
            <img [src]="module.imageUrl"/>
          </div>
          <div class="title">{{ module.name }}</div>
        </div>
      </li>
    </ng-container>
  </ul>
  <ng-template #moduleNull>
    <!-- TODO Modul yet<PERSON><PERSON> u<PERSON>ı<PERSON>ı eklenecek -->
  </ng-template>
</div>
<app-loader [show]="videocallLoading"></app-loader>
