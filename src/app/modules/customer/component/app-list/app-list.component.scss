.app-list {
  //margin-top: 14px;
  //position: relative;
  //left: -5%;
  //width: 110%;
  //padding: 0 10px;
  // min-height: calc(100vh - 420px);
  // overflow: auto;
  padding: 0 17px;

  ul {
     padding: 0;
    list-style: none;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
    margin-top: 1rem;
    margin-bottom: 0;

    li {
      //flex: 0 0 calc(50% - 10px);
      //margin-bottom: 20px;
      position: relative;

      &:nth-child(odd) {
        //margin-right: 20px;
      }

      .module-card {
        //min-height: 120px;
        display: flex;
        flex-direction: column;
        /* min-height: 134px; */
        height: 100px;
        min-height: max-content;
        border-radius: 8px;
        background: #fff;
        //border: 3px solid #fff;
        box-shadow: 0px 4px 8.800000190734863px 0px #00000029;

        /* Safari 10.1 */
        @media not all and (min-resolution: 0.001dpcm) {
          @supports (-webkit-appearance: none) and
            (not (stroke-color: transparent)) {
            .menu-image-only-ios {
              min-height: 70px;
            }
          }
        }

        .image-area {
          min-height: 60px;
          /* height: min(max(14vw,70px),120px); */
          //background: linear-gradient(91.31deg,#ffa300,#ffc45a);
          border-top-left-radius: 8px;
          border-top-right-radius: 8px;
          position: relative;
          border-bottom: 1px solid rgba(#ffa300, .4);

          img {
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            max-width: 65%;
            max-height: 100%;
          }
        }

        .title {
          //text-align: center;
        }
      }

      .title {
        max-height: 100%;
        /* padding: 8px; */
        font-size: 12px;
        /* color: #505050; */
        /* line-height: 21px; */
        /* overflow: hidden; */
        /* text-overflow: ellipsis; */
        /* display: -webkit-box; */
        /* -webkit-line-clamp: 1; */
        -webkit-box-orient: vertical;
        flex: 1;
        /* height: 100%; */
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
      }
    }
  }
}

.app-list-mini {
  height: inherit;
  overflow: inherit;
  padding: 0;
  min-height: 0;

  ul {
    padding: 0;
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    gap: 0;

    li {
      display: inline-block;
      flex: none;
      margin: 7.5px 0 !important;
      width: 100%;

      .module-card {
        border-radius: 0;
        box-shadow: none;
        border: none;
        text-align: left;
        flex: none;
        margin: 7.5px 0 !important;
        height: unset;
        min-height: inherit;
        flex-direction: row;

        /* Safari 10.1 */
        @media not all and (min-resolution: 0.001dpcm) {
          @supports (-webkit-appearance: none) and
            (not (stroke-color: transparent)) {
            .menu-image-only-ios {
              min-height: 40px;
            }
          }
        }

        .image-area {
          float: left;
          width: 40px;
          height: 40px;
          min-height: 40px;
          border-radius: 100%;
          background: #ebebeb;

          img {
            bottom: 0;
            transform: translateX(-50%) translateY(-50%);
            left: 50%;
            top: 50%;
            max-width: 75%;
          }

          .title {
            text-align: left;
          }
        }
      }

      .title {
        float: left;
        font-size: 16px;
        line-height: 24px;
        font-weight: 400;
        margin: 10px;
        margin-left: 20px;
        justify-content: flex-start;
      }
    }
  }
}