<div *ngIf="!rejectLoading && !(borusanUserData$ | async)?.isLdapLogin" class="app-rejected-account text-center">
  <div class="font-size-14px">
    <img [src]="warningIcon" />
    <p class="rejected-warning">
      {{ "_rejected_account_message" | translate }}
    </p>
  </div>
  <br />
  <div class="font-size-16px mt-3">
      {{ "_add_account_message" | translate }}
    <div class="mx-auto row mt-3 justify-content-center">
      <button
      routerLink="/settings/createaccount"
      class="btn btn-warning btn-sm text-white shadow"
      >
        {{ "_add_account" | translate }}
      </button>
    </div>
  </div>
</div>
