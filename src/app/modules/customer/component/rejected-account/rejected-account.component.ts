import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { combineLatest, Observable } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { UserAction } from '../../state/user/user.actions';
import { UserState } from '../../state/user/user.state';
import { BorusanUserLdapData } from 'src/app/modules/authentication/model/user.model';

@Component({
  selector: 'app-rejected-account',
  templateUrl: './rejected-account.component.html',
  styleUrls: ['./rejected-account.component.scss']
})


export class RejectedAccountComponent implements OnInit {

  @Select(UserState.userLoading)
  userLoading$: Observable<boolean>;

  @Select(UserState.borusanUserData)
  borusanUserData$: Observable<BorusanUserLdapData>;

  warningIcon = `${environment.assets}/warning.svg`;
  rejectLoading = true;
  constructor(
    private readonly store: Store,
  ) { }

  ngOnInit() {

    this.userLoading$
    .subscribe((value) => {
      this.rejectLoading = value;
    });

  }

}
