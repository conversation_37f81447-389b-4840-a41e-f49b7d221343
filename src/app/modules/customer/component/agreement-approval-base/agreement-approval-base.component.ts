import { Component, ElementRef, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { ApprovalAgreementModel } from '../../../definition/model/agreement.model';
import { Store } from '@ngxs/store';
import { DefinitionService } from '../../../definition/service/definition.service';
import { ModalService } from '../../../../shared/service/modal.service';
import { disableBack } from '../../../../util/disable-back.util';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { LogService } from 'src/app/shared/service/log.service';


@Component({
  selector: 'app-agreement-approval-base',
  templateUrl: './agreement-approval-base.component.html',
  styleUrls: ['./agreement-approval-base.component.scss']
})
export class AgreementApprovalBaseComponent implements OnInit {
  @Input() agreement: ApprovalAgreementModel[];
  @Output() visibleStatus = new EventEmitter();
  current: ApprovalAgreementModel;
  currentIndex = 0;
  loading: boolean;
  status = true;
  private disableBack: { remove: any };
  text: string;

  constructor(
    protected readonly definitionService: DefinitionService,
    protected readonly modalService: ModalService,
    protected readonly elementRef: ElementRef,
    protected readonly store: Store,
    protected readonly frameMessageService: FrameMessageService,
    protected readonly logService: LogService
  ) { }

  ngOnInit(): void {
    this.disableBack = disableBack(i => console.log('back prevent'));
  }

  ngAfterViewInit(): void {
    this.currentIndex = 0;
    this.next();
  }

  next() {
    this.text = null;
    this.current = this.agreement[this.currentIndex];
    // console.log('this.current',this.current);

    if (!this.current) {
      return;
    }
    this.loading = true;
    this.definitionService.getAgreementContent(this.current?.url).subscribe(response => {
      this.text = response;
      this.loading = false;
    }, () => {
      this.loading = false;
    });
  }

  onApprove() {
    this.approve(true);
  }

  onSkip() {
    this.approve(false);
  }

  approve(status) {
    this.loading = true;
    let approve;

    if (status) {
      approve = this.definitionService
        .approveUserRequiredAgreement(this.current.agreementFormId);
    } else {
      approve = this.definitionService
        .skipUserRequiredAgreement(this.current.agreementFormId);

    }
    approve
      .subscribe(r => {
        this.loading = false;
        this.currentIndex++;
        if (this.agreement.length > this.currentIndex) {
          // this.current = this.agreements[this.currentIndex];
          this.next();
        } else {
          this.close();
        }

      }, e => {
        this.loading = false;

        this.modalService.errorModal({
          message: '_general_error_message',
          translate: true
        }, this.elementRef.nativeElement);
      });
  }

  protected close() {
    this.status = false;
    this.visibleStatus.emit();
    this.disableBack?.remove();
  }
}
