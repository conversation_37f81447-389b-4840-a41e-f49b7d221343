<div class="agreement" *ngIf="agreement?.length">
    <div class="agreement-content" #target [innerHTML]="text | safeHtml">
    </div>
    <div class="agreement-buttons">
        <div *ngIf="agreement?.length > 1" class="d-flex justify-content-center mb-3">
            {{currentIndex + 1}} / {{agreement?.length}}
        </div>
        <div class="d-flex justify-content-between mb-3 mt-2">
            <button (click)="onSkip()" class="modal-btn btn btn-secondary btn-sm text-white shadow "
                [disabled]="!current?.hasSkip || loading">

                {{ "_skip" | translate }}
            </button>

            <button (click)="onApprove()" class="modal-btn btn btn-warning btn-sm text-white shadow "
                    [disabled]="loading">
            >
                {{ "_approve" | translate }}
            </button>
        </div>
    </div>

</div>
