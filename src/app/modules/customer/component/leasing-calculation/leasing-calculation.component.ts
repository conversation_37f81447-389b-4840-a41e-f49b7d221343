import { Component, OnInit } from '@angular/core';
import { HeaderStatusAction } from '../../state/customer/customer.actions';
import { Select, Store } from '@ngxs/store';
import { CommonState } from 'src/app/shared/state/common/common.state';
import { Observable } from 'rxjs';
import { ActivatedRoute, Route, Router } from '@angular/router';
import { UserAction } from '../../state/user/user.actions';
HeaderStatusAction

@Component({
  selector: 'app-leasing-calculation',
  templateUrl: './leasing-calculation.component.html',
  styleUrls: ['./leasing-calculation.component.scss']
})
export class LeasingCalculationComponent implements OnInit {

  @Select(CommonState.leasingPlanData)
  leasingPlanData$: Observable<any>;

  data: any;
  formLeasingStatus = 0
  leasingStatus = 0
  
  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute
  ) { }

  ngOnInit() {    
    this.leasingPlanData$.subscribe(data => {
      this.data = data;
    }),

    this.route.queryParams.subscribe(q => {
      this.formLeasingStatus = 'formLeasingStatus' in q ? parseInt(q.formLeasingStatus, 2) : 0;
      this.leasingStatus = 'leasingStatus' in q ? parseInt(q.leasingStatus, 2) : 0;
    });
  }
}
