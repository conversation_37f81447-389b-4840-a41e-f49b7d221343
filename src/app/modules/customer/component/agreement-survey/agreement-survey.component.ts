import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Select, Store } from '@ngxs/store';
import { AgreementTypeEnum } from 'src/app/modules/definition/enum/agreement-type.enum';
import { DefinitionService } from 'src/app/modules/definition/service/definition.service';
import { NotificationDeleteAction } from 'src/app/modules/notification/state/notification/notification.actions';
import { LogService } from 'src/app/shared/service/log.service';
import { ModalService } from 'src/app/shared/service/modal.service';
import { getFormErrorMessage, isShowFormError, validateAllFormFields } from 'src/app/util/form-error.util';
import { environment } from 'src/environments/environment';
import { UserState } from '../../state/user/user.state';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-agreement-survey',
  templateUrl: './agreement-survey.component.html',
  styleUrls: ['./agreement-survey.component.scss'],
})
export class AgreementSurveyComponent implements OnInit {
  @Input() notification: any;

  form: FormGroup = new FormGroup({
    Answer: new FormControl('', [Validators.required]),
    // SelectedAnswer: new FormControl(null, [Validators.required]),
  });
  agreementTypeEnum = AgreementTypeEnum;
  answers = ['Boom360', '_phone', 'SMS', '_email'];
  isShowFormError = isShowFormError;
  getFormErrorMessage = getFormErrorMessage;
  loading = false;
  successIcon = `${environment.assets}/success.svg`;
  approvedAgreement = false;
  countryCode: string;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  constructor(
    private readonly log: LogService,
    private readonly definitionService: DefinitionService,
    private readonly modalService: ModalService,
    private readonly store: Store
  ) {}

  ngOnInit() {
    this.currentCustomer$
      .subscribe(currentCustomer => {
        if (currentCustomer) {
          this.countryCode = currentCustomer.countryCode;
        }
      });
  }

  getFormStatus() {
    if (this.form.value.agreements) {
      return Object.values(this?.form?.value?.agreements).some(
        (value) => value === false
      );
    }
    return true;
  }

  sendForm() {
    if (!this.form.valid) {
      return validateAllFormFields(this.form);
    }

    this.loading = true;

    this.log.action('AGREEMENTS_SURVEY_CAMPAIGN', 'SURVEY_CAMPAIGN_APPROVE', {
      agreement: 'SURVEY_CAMPAIGN',
      answer: this.form.value.Answer,
    }).subscribe();

    this.definitionService
      .approveUserAgreementFromNotification('CAT_CLOUDSTORAGE_AGREEMENT')
      .subscribe(
        () => {
          this.loading = false;
          this.approvedAgreement = true;
          this.store.dispatch(new NotificationDeleteAction(this.notification?.messageId));
        },
        (e) => {
          this.loading = false;
          this.approvedAgreement = false;
          this.modalService.errorModal(
            {
              message: '_general_error_message',
              translate: true,
            },
          );
        }
      );
  }

  navigateBack() {
    window.history.back();
  }
}
