<div class="survey-campaign-agreement pt-4 d-flex flex-column justify-content-between" *ngIf="!approvedAgreement">
  <div>
    <div class="h5 text-warning text-center">
      {{ notification?.title }}
    </div>
    <p class="mt-3">
      {{ (this.countryCode === "KZ" ? "_survey_campaign_body_kz" : "_survey_campaign_body") | translate }}
    </p>
    <form [formGroup]="form">
      <!-- <div class="form-group">
        <ng-select
          class="service-drp"
          [searchable]="false"
          [placeholder]="'_type_your_answer' | translate"
          [clearable]="false"
          formControlName="SelectedAnswer"
        >
          <ng-option
            *ngFor="let answer of answers"
            [value]="answer | translate"
            >{{ answer | translate }}</ng-option
          >
        </ng-select>
        <div
          [ngClass]="{ 'd-block': isShowFormError(form.controls.SelectedAnswer) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.SelectedAnswer) | translate }}
        </div>
      </div> -->
      <div class="form-group">
        <input
          [placeholder]="'_type_your_answer' | translate"
          class="form-control form-control text-form"
          formControlName="Answer"
          type="text"
        />
        <div
          [ngClass]="{ 'd-block': isShowFormError(form.controls.Answer) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Answer) | translate }}
        </div>
      </div>
    </form>
  </div>
  <app-agreement-list
    [form]="form"
    [formType]="agreementTypeEnum.SurveyCampaign"
    [forceAgreement]="true"
  ></app-agreement-list>

  <button
    class="btn btn-warning text-white send-btn"
    [disabled]="getFormStatus()"
    (click)="sendForm()"
  >
    {{ "_send" | translate }}
  </button>
</div>
<app-loader [show]="loading"></app-loader>
<div class="success-agreement-modal" *ngIf="approvedAgreement">
  <img src="{{ successIcon }}" alt="" width="45" height="45"/>
  <span>
    {{ "_approve_agreement_message" | translate }}
  </span>
  <div
    class="btn btn-warning btn-gradient btn-block text-white shadow"
    (click)="navigateBack()"
  >
    {{ "_return_back" | translate }}
  </div>
