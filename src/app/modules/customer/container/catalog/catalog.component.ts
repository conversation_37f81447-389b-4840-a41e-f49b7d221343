import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { MenuModel } from '../../model/menu.model';
import { CatalogState } from '../../state/catalog/catalog.state';
import { CatalogAction } from '../../state/catalog/catalog.actions';
import { LoginState } from '../../../authentication/state/login/login.state';

@Component({
  selector: 'app-catalog',
  templateUrl: './catalog.component.html',
  styleUrls: ['./catalog.component.scss'],
})
export class CatalogComponent implements OnInit {

  @Select(CatalogState.catalog)
  catalog$: Observable<MenuModel[]>;

  activeCatalog: MenuModel;


  constructor(
    private readonly route: ActivatedRoute,
    private readonly store: Store,
  ) { }

  ngOnInit(): void {
    const { id } = this.route.snapshot.params;

    if (!this.store.selectSnapshot(CatalogState.catalog)?.length) {
      this.store.dispatch(new CatalogAction(
        this.store.selectSnapshot(LoginState.headerCompanies), true
      ));
    }
    // console.log('tst', this.store.selectSnapshot(CustomerState.menuItems));
    // if (this.store.selectSnapshot(CustomerState.menuItems)) {
    //   this.store.dispatch(new CustomerMenuAction(
    //     this.store.selectSnapshot(UserState.currentCustomer)?.publicMenuHeaderCompany));
    // }

    this.catalog$.subscribe(items => {
      if (items) {
        this.activeCatalog = items[id];
      }
    });

    // this.activeCatalog.
  }

}
