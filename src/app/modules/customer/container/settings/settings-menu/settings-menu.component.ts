import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CustomerModel } from '../../../../../shared/models/customer.model';
import { SanitizedCustomerModel } from '../../../model/sanitized-customer.model';
import { CustomerDetailAction, HeaderStatusAction, } from '../../../state/customer/customer.actions';
import { CustomerState } from '../../../state/customer/customer.state';
import { UserState, UserStateModel } from '../../../state/user/user.state';
import { takeUntil } from 'rxjs/operators';
import { CompanyRoleEnum } from '../../../../definition/enum/company-role.enum';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from '../../../response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';
import { DefinitionState } from 'src/app/modules/definition/state/definition/definition.state';
import { Country } from 'src/app/modules/definition/model/country.model';
import {BorusanBlockedActionsEnum} from '../../../../definition/enum/borusan-blocked-actions.enum';
import { PermissionEnum } from 'src/app/modules/definition/enum/permission.enum';

@Component({
  selector: 'app-settings-menu',
  templateUrl: './settings-menu.component.html',
  styleUrls: ['./settings-menu.component.scss'],
  providers: [TranslatePipe],
})
export class SettingsMenuComponent implements OnInit, OnDestroy {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;

  @Select(UserState.isGdprApproved)
  isGdprApproved$: Observable<boolean>;
  @Select(UserState.activeApplications)
  activeApplications$: Observable<any[]>;
  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  @Select(UserState.getState)
  userState$: Observable<UserStateModel>;
  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;
  currentCustomer: SanitizedCustomerModel;
  @Select(UserState.isBorusanUser)
  isBorusanUser$: Observable<boolean>;
  @Select(UserState.isMobileVerified)
  isMobileVerified$: Observable<boolean>;
  showUserManagement = false;
  enableUsersManagement = false;
  borusanCatLogo = `${environment.assets}/borusan-cat-logo.svg`;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;
  hasPhoneCode: boolean;

  showUserInfo = true;

  @Select(SettingsState.borusanBlockedActions)
  borusanBlockedActions$: Observable<string[]>;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  PermissionEnum = PermissionEnum;

  protected subscriptions$: Subject<boolean> = new Subject();
  companies: {
    id: string;
    name: string;
    position: string;
    address: string;
    customerNumber?: string;
  }[] = [];
  isGdprApproved: boolean;

  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe
  ) {

  }

  ngOnInit(): void {
    this.userState$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(userState => {
        console.log('some data', userState);
        if (!userState) {
          return;
        }
        this.firstName = userState.firstName;
        this.lastName = userState.lastName;
        this.email = userState.email.toLowerCase();
        this.phone = userState.mobile;
        this.isGdprApproved = userState.isGdprApproved;
      });

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_settings'),
      })
    );
    this.currentCustomer = this.store.selectSnapshot(UserState.currentCustomer);
    this.showUserManagement = this.currentCustomer?.roleList
      && this.currentCustomer.roleList?.indexOf(CompanyRoleEnum.ManageAccount) !== -1;

    this.showUserInfo = ['RU', 'KZ'].indexOf(this.currentCustomer?.groupKey) < 0;

    this.createGroupList();

    this.countryList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.hasPhoneCode = (data?.filter(x => x?.phoneCode)?.length > 0);
        }
      });

    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        if (features) {
          this.enableUsersManagement = systemFeature('users_management', features, true);
          // this.isEnableMobileVerify = systemFeature('enable_mobile_verify', features,
          //   ['TR'].indexOf(this.currentCustomer?.groupKey) >= 0 ? true : false);
        }
      });
    this.borusanBlockedActions$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => this.borusanBlockedActions = x);
  }

  createGroupList() {
    const companies = this.store.selectSnapshot(UserState.customers);
    companies
      .map((c) => {
        return c.customer
          ? {
            id: c.customer.id,
            name: c.customer.name,
            position: c.crmPosition,
            customerNumber: c.customer?.customerNumber,
            address: '',
          }
          : {
            id: c.id,
            name: c.displayName,
            position: c.crmPosition,
            address: '',
          };
      })
      .sort((f) => (this.isActive(f.id) ? -1 : 1))
      .forEach((c) => {
        if (!this.companies.some((cm) => cm.id === c.id)) {
          this.companies.push(c);
        }
      });

    const activeCompany = this.companies.find((c) => this.isActive(c.id));
    if (activeCompany?.customerNumber) {
      this.store.dispatch(
        new CustomerDetailAction(activeCompany?.customerNumber)
      );
      this.customer$.subscribe((res: CustomerModel) => {
        if (res && res.customerNumber === activeCompany?.customerNumber) {
          const defAddress = res.details.adresses.find(
            (a) => a.adressKind === 'XXDEFAULT' || a.adressKind === 'M'
          );
          activeCompany.address = defAddress?.adressInfo || '';
        }
      });
    }
  }

  isActive(companyid: string): boolean {
    return (
      companyid === this.currentCustomer?.customer?.id ||
      companyid === this.currentCustomer?.id
    );
  }

  isPassive() {
    return this.currentCustomer.passive;
  }

  getLogo(name: string): string {
    return name.slice(0, 1).toUpperCase();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
