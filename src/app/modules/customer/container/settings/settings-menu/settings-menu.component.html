<div class="settings-area">
  <div class="user-info">
    <div class="user-info-detail">{{ firstName }} {{ lastName }}</div>
    <div class="user-info-subdetail" *ngIf="email">
      <div class="user-info-subdetail-title">{{ "_email" | translate }}:</div>
      <div class="user-info-subdetail-value">
        {{ email }}
      </div>
    </div>
    <div class="user-info-subdetail mb-3" *ngIf="phone">
      <div class="user-info-subdetail-title">{{ "_phone" | translate }}:</div>
      <div class="user-info-subdetail-value">
        <i *ngIf="!(isBorusanUser$ | async) && (isMobileVerified$ | async)" class="icon icon icon-success text-success"></i>
        {{ phone }}
      </div>
    </div>
    <ul class="user-info-company">
      <li class="user-info-company-item" *ngFor="let company of companies">
        <div
          class="user-info-company-item-logo"
          [class.active]="isActive(company.id)"
        >
          {{ getLogo(company.name) }}
        </div>
        <div class="user-info-company-item-info">
          <div
            class="user-info-company-item-info-detail user-info-company-item-info-name"
            [class.active]="isActive(company.id)"
          >
            {{ company?.name }}
          </div>
          <ng-container *ngIf="showUserInfo">
            <div
              class="user-info-company-item-info-detail user-info-company-item-info-joptitle"
              [class.active]="isActive(company.id)"
            >
              {{ company?.position }}
            </div>
            <div
              class="user-info-company-item-info-detail user-info-company-item-info-address"
              [class.active]="isActive(company.id)"
              *ngIf="isActive(company.id) && !isPassive()"
            >{{ "_address" | translate }}: {{ company?.address }}
            </div>
          </ng-container>
          <div
            class="user-info-company-item-info-detail user-info-company-item-info-address"
            [class.active]="isActive(company.id)"
            *ngIf="isActive(company.id) && !isPassive()"
          >{{ "_customer_number" | translate }}:
            {{ company?.customerNumber | serialFormat }}
          </div>
        </div>
      </li>
    </ul>
  </div>

  <div class="settings-menu settings-menu-only-ios">
    <div class="scroll-area">
      <ul class="settings-menu-list">
        <li
          class="settings-menu-list-item"
          *ngIf="
            !(
              !(isGdprApproved$ | async) &&
              (activeApplications$ | async)?.length > 0
            ) && !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.AddAccount) >= 0)
          "
        >
          <i class="icon icon-plus"></i>
          <a
            routerLink="/settings/createaccount"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'ADD_ACCOUNT_CLICK'"
          >
            {{ "_add_account" | translate }}
          </a>
        </li>
        <li class="settings-menu-list-item" *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitDeleteAccountForm) >= 0)">
          <img alt="Delete User" src="assets/delete_user.svg" class="icon"/>
          <a
            routerLink="/settings/deleteaccount"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'DELETE_ACCOUNT_CLICK'"
          >
            {{ "_delete_user_me" | translate }}
          </a>
        </li>
        <li class="settings-menu-list-item" *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ChangePassword) >= 0)">
          <i class="icon icon-padlock"></i>
          <a
            routerLink="/settings/passwordchange"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'PASSWORD_CHANGE_CLICK'"
          >
            {{ "_password_change" | translate }}
          </a>
        </li>
        <li class="settings-menu-list-item"
            *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ConfirmPhoneNumber) >= 0)
                  && hasPhoneCode && isGdprApproved">
          <i class="icon icon-mobile-verify text-center"></i>
          <a
            routerLink="/settings/mobile-verify"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'MOBILE_VERIFY_CLICK'"
          >
            {{ "_mobile_no_verify" | translate }}
          </a>
        </li>
        <li
          *ngIf="showUserManagement && enableUsersManagement && !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ViewUserManagementList) >= 0)"
          class="settings-menu-list-item"
        >
          <i class="icon icon-user-permission"></i>
          <a
            routerLink="/settings/usermanagement"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'USER_MANAGEMENT_CLICK'"
          >
            {{ "_user_management" | translate }}
          </a>
        </li>
        <li
          [hasPermission]="PermissionEnum.MenuSettingsAddresses"
          class="settings-menu-list-item"
          *ngIf="currentCustomer && !currentCustomer?.passive && !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.ViewAddresses) >= 0)"
        >
          <i class="icon icon-location"></i>
          <a
            routerLink="/settings/address"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'LANGUAGE_CHANGE_CLICK'"
          >
            {{ "_my_addresses" | translate }}
          </a>
        </li>
        <li class="settings-menu-list-item">
          <i class="icon icon-language"></i>
          <a
            routerLink="/settings/language"
            appClickLog
            [section]="'SETTINGS'"
            [subsection]="'LANGUAGE_CHANGE_CLICK'"
          >
            {{ "_language" | translate }}
          </a>
        </li>
      </ul>
    </div>
    <div class="bottom-area">
      <div class="borusan-cat-logo">
        <img alt="BorusanCat" [src]="borusanCatLogo" />
      </div>
    </div>
  </div>
</div>
