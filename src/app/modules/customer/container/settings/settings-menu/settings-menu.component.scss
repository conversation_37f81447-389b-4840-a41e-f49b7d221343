.settings-area {
  height: calc(100vh - 64px);
  position: relative;
  overflow: hidden;
  overflow-y: scroll;
  .user-info {
    padding: 22px;
    padding-right: 0;
    position: relative;
    height: 50%;
    border-radius: 0 0 0 10%;
    background-color: white;
    &::before {
      content: "";
      width: 100vw;
      height: 100%;
      z-index: -1;
      background-color: #eef2f4;
      position: absolute;
      top: 0;
      left: 0;
    }
    &-detail {
      font-weight: normal;
      font-size: 16px;
      line-height: 24px;
      color: #4a4a4a;
    }
    &-subdetail {
      font-weight: normal;
      font-size: 12px;
      line-height: 16px;
      color: #4a4a4a;
      display: flex;
      flex-direction: row;
      word-break: break-all;
      margin-bottom: 7px;
      &-title {
        width: 5rem;
        margin-right: 5px;
      }
      &-value {
        align-self: center !important;
      }
    }
    &-company {
      margin: 0;
      padding: 0;
      height: calc(100% - 66px);
      overflow-y: scroll;
      padding-right: 8px;

      &-item {
        display: flex;
        flex-direction: row;
        margin-bottom: 15px;
        &-logo {
          width: 40px;
          height: 40px;
          background: #d7e5ea;
          border: 2px solid #d7e5ea;
          border-radius: 50%;
          display: inline-flex;
          justify-content: center;
          align-items: center;

          color: #8c8c8c;
          font-weight: normal;
          font-size: 20px;
          line-height: 30px;

          margin-right: 19px;
          &.active {
            border-color: #ffa300;
            color: #ffa300;
            background-color: #f4e9d4;
          }
        }
        &-info {
          display: flex;
          flex-direction: column;
          width: calc(100% - 59px);
          &-detail {
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden !important;
            font-weight: 400;
            &.active {
              color: #2c2c2c;
            }
          }
          &-name {
            font-size: 16px;
            line-height: 24px;
            color: #505050;
            &.active {
              font-weight: 500;
            }
          }
          &-joptitle {
            font-size: 12px;
            line-height: 18px;
            color: #8c8c8c;
          }
          &-address {
            white-space: pre-wrap;
            font-size: 12px;
            line-height: 18px;
            color: #4a4a4a;
          }
        }
      }
    }
  }

  .settings-menu {
    display: flex;
    flex-direction: column;
    padding: 15px 0;
    background-color: #eef2f4;
    position: relative;
    height: 50%;
    min-height: 360px;
    border-radius: 0 10% 0 0;
    &::before {
      content: "";
      width: 100vw;
      height: 100%;
      z-index: -1;
      background-color: white;
      position: absolute;
      top: 0;
      left: 0;
    }
    &-list {
      line-height: 3em;
      list-style: none;
      margin: 0;
      padding-left: 10px;

      &-item {
        font-weight: 400;
        font-size: 16px;
        display: flex;
        flex-direction: row;
        align-items: center;
        .icon {
          font-size: 1.25rem;
          margin-right: 1.5rem;
          width: 20px;
          height: 20px;
        }
      }
    }
    .scroll-area {
      padding: 0 15px;
      margin-bottom: 0.5rem;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .bottom-area {
      margin-top: auto;
      margin-bottom: 1rem;
      .borusan-cat-logo {
        display: flex;
        justify-content: center;
      }
    }
  }
}

::-webkit-scrollbar {
  -webkit-appearance: none;
}

::-webkit-scrollbar:vertical {
  width: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 6px;
  border: 2px solid #eee;
}

::-webkit-scrollbar-track {
  background-color: #eee;
  border-radius: 3px;
}

/* Safari 10.1 */
@media not all and (min-resolution: 0.001dpcm) {
  @supports (-webkit-appearance: none) and (not (stroke-color: transparent)) {
    .settings-menu-only-ios {
      min-height: 300px;
    }
  }
}
