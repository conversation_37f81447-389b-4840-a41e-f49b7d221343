import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SettingsComponent } from './settings.component';
import { SettingsMenuComponent } from './settings-menu/settings-menu.component';
import { PasswordChangeComponent } from './password-change/password-change.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule, TranslatePipe } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { SettingsRoutingModule } from './settings-routing.module';
import { CreateAccountComponent } from './create-account/create-account.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { LanguageComponent } from './language/language.component';
import { AddressComponent } from './address/address.component';
import { AppSupportComponent } from './app-support/app-support.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { NgbAccordionModule, NgbDropdownModule, } from '@ng-bootstrap/ng-bootstrap';
import { UserManagementDetailComponent } from './user-management-detail/user-management-detail.component';
import { UserManagementAllDetailComponent } from './user-management-all-detail/user-management-all-detail.component';
import { DeleteAccountComponent } from './delete-account/delete-account.component';
import { MobileVerifyComponent } from './mobile-verify/mobile-verify.component';
import { HasPermissionsModule } from 'src/app/export/file-upload/permissions/has-permissions.module';
import { AccountManagerLayoutComponent } from './account-manager/account-manager-layout/account-manager-layout.component';
import { AccountManagerCustomersComponent } from './account-manager/account-manager-customers/account-manager-customers.component';
import { AccountManagerCustomerComponent } from './account-manager/account-manager-customer/account-manager-customer.component';
import { AccountManagerAvatarComponent } from './account-manager/account-manager-avatar/account-manager-avatar.component';
import {
  AccountManagerAwaitingApprovalsComponent
} from './account-manager/account-manager-awaiting-approvals/account-manager-awaiting-approvals.component';
import { AccountManagerNavbarComponent } from './account-manager/account-manager-navbar/account-manager-navbar.component';
import { AccountManagerRoleLabelComponent } from './account-manager/account-manager-role-label/account-manager-role-label.component';
import {
  AccountManagerRoleToggleBtnComponent
} from './account-manager/account-manager-role-toggle-btn/account-manager-role-toggle-btn.component';
import { AccountManagerUserListComponent } from './account-manager/account-manager-user-list/account-manager-user-list.component';
import { QRCodeModule } from 'angularx-qrcode';
import { AccountManagerQrComponent } from './account-manager/account-manager-qr/account-manager-qr.component';
import { MyAgreementsComponent } from './my-agreements/my-agreements.component';
import { MyAgreementsApprovedComponent } from './my-agreements/my-agreements-approved/my-agreements-approved.component';
import {
  MyAgreementsAwaitingApprovalsComponent
} from './my-agreements/my-agreements-awaiting-approvals/my-agreements-awaiting-approvals.component';
import { CustomerLoadResolver } from '../../resolver/customer-load-resolver.service';

@NgModule({
  declarations: [
    SettingsComponent,
    SettingsMenuComponent,
    PasswordChangeComponent,
    CreateAccountComponent,
    LanguageComponent,
    AddressComponent,
    AppSupportComponent,
    UserManagementComponent,
    UserManagementDetailComponent,
    UserManagementAllDetailComponent,
    DeleteAccountComponent,
    MobileVerifyComponent,
    AccountManagerLayoutComponent,
    AccountManagerCustomersComponent,
    AccountManagerCustomerComponent,
    AccountManagerAvatarComponent,
    AccountManagerAwaitingApprovalsComponent,
    AccountManagerNavbarComponent,
    AccountManagerRoleLabelComponent,
    AccountManagerRoleToggleBtnComponent,
    AccountManagerUserListComponent,
    AccountManagerQrComponent,
    MyAgreementsComponent,
    MyAgreementsApprovedComponent,
    MyAgreementsAwaitingApprovalsComponent,
  ],
  imports: [
    CommonModule,
    SettingsRoutingModule,
    TranslateModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    NgSelectModule,
    NgbAccordionModule,
    NgbDropdownModule,
    HasPermissionsModule,
    QRCodeModule,
  ],
  providers: [TranslatePipe, CustomerLoadResolver],
  exports: [AccountManagerCustomerComponent],
})
export class SettingsModule {}
