@import "variable/icon-variable";

.user-management-menu {
  overflow: hidden;
  flex: 1 1 auto;
  padding-bottom: 1rem;
  font-size: 16px;
  align-items: center;
  min-height: 360px;

  .align-middle {
    align-items: center;
  }

  .circular-data {
    margin: 2.5px;
    width: 40px;
    height: 40px;
    background: #d7e5ea;
    border: 2px solid #d7e5ea;
    border-radius: 50%;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: #8c8c8c;
    font-weight: normal;
    font-size: 20px;
    line-height: 30px;
    margin-right: 19px;
  }
  .dropdown-toggle::after {
    display: none;
  }
  .dropup .dropdown-toggle::after {
    display: none;
  }
}

.user-list {
  line-height: 2em;
}

.dropdown-menu.show {
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15), 0 10px 20px rgba(0, 0, 0, 0.15);;

}

.wrap-container {
  overflow: scroll;
  height: calc(100vh - 57px);
}

.text-size-12px {
  font-size: 12px;
}

.text-size-18px {
  font-size: 18px;
}

.h-container{
  height: calc(100vh - 135px);
}
