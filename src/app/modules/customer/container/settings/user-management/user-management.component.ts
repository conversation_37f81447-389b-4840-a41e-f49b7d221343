import {Component, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation} from '@angular/core';
import {Select, Store} from '@ngxs/store';
import {HeaderStatusAction} from '../../../state/customer/customer.actions';
import {TranslateService} from '@ngx-translate/core';
import {UserService} from '../../../service/user.service';
import {GetMyUsersAction} from 'src/app/shared/state/settings/settings.actions';
import {SettingsState} from 'src/app/shared/state/settings/settings.state';
import {MyUsersModel} from '../../../model/my-users.model';
import {Observable, Subject} from 'rxjs';
import {SanitizedCustomerModel} from '../../../model/sanitized-customer.model';
import {UserState} from '../../../state/user/user.state';
import {takeUntil} from 'rxjs/operators';
import {BorusanBlockedActionsEnum} from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';


@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.scss'],
  encapsulation: ViewEncapsulation.None,

})
export class UserManagementComponent implements OnInit, OnDestroy {

  @Select(SettingsState.myUsers)
  myUsers$: Observable<MyUsersModel[]>;

  @Select(SettingsState.myUsersLoading)
  myUsersLoading$: Observable<boolean>;

  removeUser: boolean;
  removeUserLoading: boolean;
  selectedUserId: string;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  myUsers: MyUsersModel[];
  constructor(
    private readonly store: Store,
    private readonly translateService: TranslateService,
    private readonly userService: UserService,
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translateService.instant('_user_management'),
      })
    );
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(currentCustomer => {
        if (currentCustomer) {
          this.store.dispatch(new GetMyUsersAction());
        }
      });
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
    this.myUsers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(myUsers => {
        if (myUsers) {
          this.myUsers = myUsers;
        }
      });
  }

  removeUserFunc() {
    const deleteUserObj = {
      DeletedUserId: this.selectedUserId,
      Description: ' ',
    };

    this.removeUser = false;
    this.removeUserLoading = true;
    this.userService
      .deleteUser(deleteUserObj)
      .subscribe(() => {
          // console.log('Delete data: ' + data);
          this.store.dispatch(new GetMyUsersAction());
          this.removeUserLoading = false;
        },
        () => {
          this.removeUserLoading = false;
        });
  }

  removeUserClick(userId: any) {
    this.removeUser = true;
    this.selectedUserId = userId;
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}

