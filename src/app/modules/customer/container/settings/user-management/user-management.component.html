<div class="px-2 pb-4 wrap-container d-flex flex-column">
  <div class="text-center mb-2 text-size-12px">
    {{ "_user_management_info" | translate }}
  </div>
  <div class="vertical-middle flex-grow-1">
    <!-- <div class="w-100 mb-2 text-right">
      <a class="text-right text-primary">
        <i class="icon icon-plus"></i>
        {{ "_invite_users" | translate }}
        </a>
    </div> -->
    <div class="user-management-menu h-100">
      <ng-container *ngIf="myUsers">
        <div class="user-list" *ngFor="let user of myUsers">
          <div
            class="
            d-inline-flex
            col
            justify-content-between
            text-decoration-none
            user-list
          "
          >
            <div class="d-flex flex-wrap align-middle">
            <div
              appClickLog
              [section]="'USER_MANAGEMENT'"
              [subsection]="'PERMISSION_CLICK'"
              [data]="{ userId: this.user?.userId }"
              [routerLink]="user.userId"
              class="align-middle circular-data text-uppercase"
            >
              {{ user?.firstName.charAt(0) }}
            </div>
          </div>
          <div
            appClickLog
            [section]="'USER_MANAGEMENT'"
            [subsection]="'PERMISSION_CLICK'"
            [data]="{ userId: this.user?.userId }"
            [routerLink]="user.userId"
            class="
              d-flex
              mr-auto
              flex-wrap
              align-self-stretch align-items-center
            "
          >
            {{ user?.firstName + " " + user?.lastName }}
          </div>
          <div class="d-flex flex-wrap align-middle pr-2">
            <i
              [ngClass]="{
                'icon icon-warning text-danger': user?.isUserDeleteRequested
              }"
            ></i>
          </div>
          <div class="d-flex flex-wrap align-middle">
            <div
              class="btn-group"
              ngbDropdown
              role="group"
              aria-label="Button group with nested dropdown"
            >
              <button
                class="
                  btn btn-sm
                  dropdown-toggle-split
                  equipment-menu-button
                  py-0
                  px-1
                "
                ngbDropdownToggle
              >
                <i class="icon icon-dot3 text-size-18px"></i>
              </button>
              <div class="equipment-menu mt-0 dropdown-menu" ngbDropdownMenu>
                <a
                  appClickLog
                  [section]="'USER_MANAGEMENT'"
                  [subsection]="'PERMISSION_CLICK'"
                  [data]="{ userId: this.user?.userId }"
                  [routerLink]="user.userId"
                  ngbDropdownItem
                >
                  <i class="icon icon-edit mr-1"></i>
                  {{ "_user_permissions" | translate }}
                </a>
                <a
                  ngbDropdownItem
                  (click)="removeUserClick(user?.userId)"
                  class="equipment-menu-element text-danger"
                  [class]="{ 'd-none': user?.isUserDeleteRequested }"
                  *ngIf="!(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.DeleteUserManagementList) >= 0)"
                >
                  <i class="icon icon-x mr-1"></i>
                  {{ "_user_remove" | translate }}
                </a>
              </div>
            </div>
          </div>
          </div>
        </div>
      </ng-container>
      <app-basic-modal
        *ngIf="removeUser"
        [(status)]="removeUser"
        [headerText]="'_remove_user' | translate"
      >
        <div class="mb-3">
          <div>{{ "_remove_user_text" | translate }}</div>

          <div class="mx-auto text-center mt-4">
            <button
              class="
                modal-btn
                btn-sm btn btn-secondary btn-gradient
                text-white
                shadow
                col-4
                mr-3
              "
              (click)="removeUser = false"
              [disabled]="this.removeUserLoading"
            >
              {{ "_cancel" | translate }}
            </button>
            <button
              class="
                modal-btn
                btn-sm btn btn-warning btn-gradient
                text-white
                shadow
                col-4
              "
              appClickLog
              [section]="'USER_MANAGEMENT'"
              [subsection]="'REMOVE_USER'"
              [data]="{ userId: selectedUserId }"
              (click)="removeUserFunc()"
              [disabled]="this.removeUserLoading"
            >
              {{ "_approve" | translate }}
            </button>
          </div>
        </div>
      </app-basic-modal>
    </div>
  </div>
</div>
<app-loader [show]="(myUsersLoading$ | async) || removeUserLoading"></app-loader>
