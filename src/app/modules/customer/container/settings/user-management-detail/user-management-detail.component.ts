import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, ViewEncapsulation } from '@angular/core';
import { Activated<PERSON>oute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { GetMyUsersAction, UpdateMyUsersStateAction } from 'src/app/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { EditUserRolesModel } from '../../../model/edit-user-roles.model';
import { MyUsersModel } from '../../../model/my-users.model';
import { SanitizedCustomerModel } from '../../../model/sanitized-customer.model';
import { UserService } from '../../../service/user.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { UserState } from '../../../state/user/user.state';
import { takeUntil } from 'rxjs/operators';
import { LogService } from 'src/app/shared/service/log.service';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { disableBack } from '../../../../../util/disable-back.util';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';

@Component({
  selector: 'app-user-management-detail',
  templateUrl: './user-management-detail.component.html',
  styleUrls: ['./user-management-detail.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class UserManagementDetailComponent implements OnInit, OnDestroy {

  paramUserId: string;
  user: MyUsersModel;
  form: FormGroup;

  userDeletedRequest: boolean;
  checkboxSpecial: {
    manageAccount: boolean,
    manageEquipments: boolean,
    manageService: boolean,
    manageFinance: boolean,
    manageOffers: boolean,
    manageSpareParts: boolean,
  } | any;

  sendPermissionsData: EditUserRolesModel;

  countRelationsData: any = {};
  isDirty: boolean;
  saveModal: boolean;
  changePermissionsLoading: boolean;

  @Select(SettingsState.myUsers)
  readonly myUsers$: Observable<MyUsersModel[]>;

  @Select(UserState.currentCustomer)
  readonly currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(SettingsState.myUsersLoading)
  myUsersLoading$: Observable<boolean>;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  protected subscriptions$: Subject<boolean> = new Subject();
  protected disableBack: { remove: () => void };
  protected backClicked: boolean;

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    private userService: UserService,
    private readonly loggerService: LogService,
    private formBuilder: FormBuilder,
  ) {

  }

  ngOnInit() {
    this.route.params.subscribe(param => {
      this.paramUserId = param.userId;
    });

    this.myUsers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(users => {
        if (users) {
          console.log('USER', users);
          this.user = users.find(i => {
            return i.userId === this.paramUserId;
          });

          this.sendPermissionsData = {
            userId: this.user.userId,
            relations: this.user.relations.map(data => ({ ...data }))
          };
          this.store.dispatch(
            new HeaderStatusAction({
              company: false,
              backButton: true,
              closeButton: false,
              hamburgerMenu: false,
              notificationIcon: false,
              title: this.user.firstName + ' ' + this.user.lastName,
            })
          );

          this.userDeletedRequest = this.user.isUserDeleteRequested;
          this.countRelationsData = this.permissionsCount(this.user.relations);
          this.checkboxSpecial = this.checkboxSpecialFunc(this.countRelationsData, this.user?.relations?.length);
          this.form = this.formBuilder.group({
            manageAccount: new FormControl(this.countRelationsData.manageAccount === this.user.relations.length),
            manageEquipments: new FormControl(this.countRelationsData.manageEquipments === this.user.relations.length),
            manageService: new FormControl(this.countRelationsData.manageService === this.user.relations.length),
            manageFinance: new FormControl(this.countRelationsData.manageFinance === this.user.relations.length),
            manageOffers: new FormControl(this.countRelationsData.manageOffers === this.user.relations.length),
            manageSpareParts: new FormControl(this.countRelationsData.manageSpareParts === this.user.relations.length),
          });

        }
      });
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(currentCustomer => {
        if (currentCustomer) {
          this.store.dispatch(new GetMyUsersAction());
        }
      });

  }

  permissionsCount(rel: any) {
    const midArray = {
      manageAccount: 0,
      manageEquipments: 0,
      manageService: 0,
      manageFinance: 0,
      manageOffers: 0,
      manageSpareParts: 0
    };
    rel.map(relData => {
      Object.entries(relData).map(([key, value]: any) => {
        if (key.startsWith('manage') && value) {
          midArray[key]++;
        }
      });
    });
    return midArray;
  }

  checkboxSpecialFunc(midData: any, relLenght: number) {
    const returnData = {};
    Object.entries(midData).map(([key, value]) => {
      returnData[key] = value < relLenght && value > 0;
    });

    return returnData;
  }

  onItemClick(event) {
    this.disableBackButton();
    this.isDirty = true;
    const elementId: string = (event.target as Element).id;
    this.checkboxSpecial[elementId] = false;
    this.sendPermissionsData.relations.map((data) => {
      data[elementId] = event.target.checked;
    });
  }

  changePermissionsApproved() {
    this.loggerService
      .action('USER_MANAGEMENT', 'PERMISSION_CHANGE', {
        userId: this.user.userId,
      })
      .subscribe();

    this.store.dispatch(new UpdateMyUsersStateAction({
      myUsersLoading: true
    }));

    this.userService.editMyUserRoles([this.sendPermissionsData])
      .subscribe(data => {
        this.store.dispatch(new UpdateMyUsersStateAction({
          myUsers: data,
          myUsersLoading: false
        }));
        if (this.backClicked) {
          this.forceBack();
        } else {
          this.removePreventBack(-1);
        }

      }, () => {
        this.store.dispatch(new UpdateMyUsersStateAction({
          myUsersLoading: false
        }));
      });

    this.saveModal = false;
    this.isDirty = false;
  }

  changePermissionsCancel() {
    this.saveModal = false;
    if (this.backClicked) {
      this.forceBack();
    }
  }

  backButtonClick() {
    console.log('back', this.saveModal);
    // if (this.isDirty) {
    this.saveModal = true;
    // }
  }

  ngOnDestroy(): void {
    this.removePreventBack();

    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  protected forceBack() {
    console.log('force backed');
    this.removePreventBack(-2);
  }

  protected disableBackButton() {
    if (this.disableBack) {
      return;
    }
    this.disableBack = disableBack(() => {
      console.log('back prevent');
      this.backClicked = true;
      this.backButtonClick();
    });
  }

  protected removePreventBack(goBack = 0) {
    if (this.disableBack) {
      console.log('removed');
      this.disableBack.remove();
      this.disableBack = null;
      if (goBack) {
        window.history.go(goBack);
      }
    }

  }

  goToEnhanced() {
    const extras: any = { relativeTo: this.route };
    extras.replaceUrl = !!this.disableBack;
    // DO NOT MOVE, order is important
    this.removePreventBack();
    this.router.navigate(['all'], extras);
  }
}
