<div class="px-2 pb-2 wrap-container">
  <div class="user-management-detail-menu">
    <app-error-box
      *ngIf="user?.isUserDeleteRequested"
      [title]="'_warning' | translate"
    >
      <div class="text-danger">
        {{ "_delete_request_info_text" | translate }}
      </div>
    </app-error-box>
    <div *ngIf="form" class="answer fit-body">
      <form [formGroup]="form">
        <ng-container>
          <div class="css-table permission-list-container container">
            <div class="css-table-body permission-list">
              <div class="css-table-row dflex flex-row d-none">
                <div>{{ "_account_management" | translate }}</div>
                <div class="float-right vertical-middle">
                  <input
                    id="manageAccount"
                    class="checkbox-class"
                    type="checkbox"
                    formControlName="manageAccount"
                    [class]="{
                      'checkbox-special': checkboxSpecial?.manageAccount
                    }"
                    (click)="onItemClick($event)"
                    [attr.disabled]="(userDeletedRequest === true ? true : null)
                      || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
                  />
                  <label for="manageAccount"></label>
                </div>
              </div>
              <div class="css-table-row dflex flex-row">
                <div>{{ "_equipments_management" | translate }}</div>
                <div class="float-right vertical-middle">
                  <input
                    id="manageEquipments"
                    class="checkbox-class"
                    type="checkbox"
                    formControlName="manageEquipments"
                    [class.special]="checkboxSpecial?.manageEquipments"
                    (click)="onItemClick($event)"
                    [attr.disabled]="(userDeletedRequest === true ? true : null)
                      || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
                  />
                  <label for="manageEquipments"></label>
                </div>
              </div>
              <div class="css-table-row dflex flex-row">
                <div>{{ "_service_management" | translate }}</div>
                <div class="float-right vertical-middle">
                  <input
                    id="manageService"
                    class="checkbox-class"
                    type="checkbox"
                    formControlName="manageService"
                    [class.special]="checkboxSpecial?.manageService"
                    (click)="onItemClick($event)"
                    [attr.disabled]="(userDeletedRequest === true ? true : null)
                      || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
                  />
                  <label for="manageService"></label>
                </div>
              </div>
              <div class="css-table-row dflex flex-row">
                <div>{{ "_financial_management" | translate }}</div>
                <div class="float-right vertical-middle">
                  <input
                    id="manageFinance"
                    class="checkbox-class"
                    type="checkbox"
                    formControlName="manageFinance"
                    [class.special]="checkboxSpecial?.manageFinance"
                    (click)="onItemClick($event)"
                    [attr.disabled]="(userDeletedRequest === true ? true : null)
                      || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
                  />
                  <label for="manageFinance"></label>
                </div>
              </div>
              <div class="css-table-row dflex flex-row">
                <div>{{ "_offer_management" | translate }}</div>
                <div class="float-right vertical-middle">
                  <input
                    id="manageOffers"
                    class="checkbox-class"
                    type="checkbox"
                    formControlName="manageOffers"
                    [class.special]="checkboxSpecial?.manageOffers"
                    (click)="onItemClick($event)"
                    [attr.disabled]="(userDeletedRequest === true ? true : null)
                      || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
                  />
                  <label for="manageOffers"></label>
                </div>
              </div>
              <div class="css-table-row dflex flex-row">
                <div>{{ "_spare_part_management" | translate }}</div>
                <div class="float-right vertical-middle">
                  <input
                    id="manageSpareParts"
                    class="checkbox-class"
                    type="checkbox"
                    formControlName="manageSpareParts"
                    [class.special]="checkboxSpecial?.manageSpareParts"
                    (click)="onItemClick($event)"
                    [attr.disabled]="(userDeletedRequest === true ? true : null)
                      || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
                  />
                  <label for="manageSpareParts"></label>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </form>
    </div>
    <div class="row text-center">
      <div class="col-7 p-2 text-left ml-2">
        <a
          appClickLog
          [section]="'USER_MANAGEMENT'"
          [subsection]="'PERMISSION_DETAIL'"
          [data]="{ userId: this.user?.userId }"
          class="font-size-14px"
          (click)="goToEnhanced()"
        >
          {{ "_user_permissions_all_detail" | translate }}
          <i class="icon icon-chevron-right font-size-11px"></i>
        </a>
      </div>
      <div class="ml-auto col-4">
        <button
          class="
            modal-btn
            btn-sm btn btn-warning btn-gradient
            text-white
            shadow
            w-100
            min-90px
          "
          [attr.disabled]="(isDirty === true ? null : true)
          || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
          [class]="{ 'd-none': userDeletedRequest }"
          (click)="saveModal = true"
        >
          {{ "_save" | translate }}
        </button>
      </div>
    </div>
  </div>
</div>
<app-basic-modal
  *ngIf="user"
  [(status)]="saveModal"
  [headerText]="'_change_permissions_messagebox_header' | translate"
>
  <div class="mb-3">
    <div>{{ "_change_permissions_messagebox_text" | translate }}</div>

    <div class="mx-auto text-center mt-4">
      <button
        class="
          modal-btn
          btn-sm btn btn-secondary btn-gradient
          text-white
          shadow
          col-4
          mr-3
        "
        (click)="changePermissionsCancel()"
      >
        {{ "_cancel" | translate }}
      </button>
      <button
        class="
          modal-btn
          btn-sm btn btn-warning btn-gradient
          text-white
          shadow
          col-4
        "
        (click)="changePermissionsApproved()"
      >
      {{ "_save" | translate }}
      </button>
    </div>
  </div>
</app-basic-modal>
<app-loader [show]="myUsersLoading$ | async"></app-loader>
