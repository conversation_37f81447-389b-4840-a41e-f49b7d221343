@import "variable/icon-variable";
.wrap-container {
  overflow: scroll;
  height: calc(100vh - 57px);
}
.user-management-detail-menu {
  padding-bottom: 10px;

  .css-table {
    display: table;
    padding: 1rem;

    &-header {
      display: table-header-group;
      font-weight: bold;
    }

    &-body {
      display: table-row-group;
      font-size: 16px;
    }

    &-row {
      display: table-row;
      vertical-align: middle;
    }

    &-header div,
    &-row div {
      display: table-cell;
      vertical-align: middle;
      padding: 0 6px;
    }

    &-header div {
      text-align: center;
    }

    .vertical-middle {
      display: inline-block;
      vertical-align: middle;
    }
  }

  .permission-list {
    line-height: 3em;

    &-container {
      border-radius: 6px;
      background-color: #f9f9f9;
      border: 1px solid rgba(0, 0, 0, 0.125);
      margin-bottom: 10px;
    }
    .read-only-input {
      input:focus,
      textarea:focus,
      select:focus {
        outline: none;
      }
    }
  }
}
.text-size-16 {
  font-size: 16px;
}
.min-90px{
  min-width: 90px;
}

[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
