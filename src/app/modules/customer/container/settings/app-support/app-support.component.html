<div class="px-4 pb-5 tablet-form-container" *ngIf="!formSendStatus">
  <app-info-box [title]="'_info' | translate">
    {{'_app_support_form_info' | translate}}
  </app-info-box>

  <form (submit)="onSubmitForm()" [formGroup]="form" class="tablet-form">

    <div class="form-group">
      <input
        appInputMaxLength
        [name]="'NameSurname'"
        [placeholder]="'_name_surname' | translate"
        class="form-control form-control"
        formControlName="Name"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>


    <div class="form-group">
      <input
        appInputMaxLength
        [name]="'Phone'"
        [placeholder]="'_phone' | translate"
        class="form-control form-control"
        formControlName="PhoneNumber"
        (input)="onInputPhone($event)"
        minlength="7"
        type="tel"
      />
      <div
        [ngClass]="{ 'd-block': isShowFormErrorTouched(form.controls.PhoneNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.PhoneNumber) | translate }}
      </div>
    </div>

    <div class="form-group">
      <input
        appInputMaxLength
        [name]="'Email'"
        [placeholder]="'_email' | translate"
        class="form-control form-control"
        formControlName="Email"
        type="text"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Email) | translate }}
      </div>
    </div>

    <div class="form-group">

      <ng-select
        #select
        *ngIf="(mobileSupportSubjects$ | async).length > 0"
        class="service-drp"
        [placeholder]="'_category' | translate"
        formControlName="FormSubjectCode"
        [searchable]="false"
        [clearable]="false"
        [dropdownPosition]="'bottom'"
        (close)="select.blur()"
      >
        <ng-option
          *ngFor="let subject of mobileSupportSubjects$ | async"
          [value]="subject.code"
        >{{ subject.title }}</ng-option>
      </ng-select>
    </div>

    <div class="form-group textarea-form-element-container">
      <textarea
        appInputMaxLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control textarea-form-element"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <input
      [value]="'_send' | translate"
      class="btn btn-gradient btn-block text-white shadow"
      [disabled]="!form.valid"
      [ngClass]="{ 'btn-secondary': !form.valid, 'btn-warning': form.valid }"
      type="submit"
    />
  </form>
</div>

<app-success-modal
  *ngIf="formSendStatus"
  message="_successfully_send_form"
></app-success-modal>

<app-loader [show]="loading"></app-loader>
