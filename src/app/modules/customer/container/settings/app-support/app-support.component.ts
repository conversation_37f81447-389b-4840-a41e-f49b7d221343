import { Component, OnInit } from '@angular/core';
import { getFormErrorMessage, isShowFormError, isShowFormErrorTouched, validateAllFormFields } from '../../../../../util/form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { Select, Store } from '@ngxs/store';
import { TranslatePipe } from '@ngx-translate/core';
import { ContactService } from '../../../service/contact.service';
import { LogService } from '../../../../../shared/service/log.service';
import { UserState } from '../../../state/user/user.state';
import { MobileSupportSubjectsAction } from '../../../state/contact/contact.actions';
import { MobileSupportSubjectsModel } from '../../../model/contact';
import { Observable } from 'rxjs';
import { ContactState } from '../../../state/contact/contact.state';
import { CustomValidator } from '../../../../../util/custom-validator';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';

@Component({
  selector: 'app-app-support',
  templateUrl: './app-support.component.html',
  styleUrls: ['./app-support.component.scss']
})
export class AppSupportComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isShowFormErrorTouched = isShowFormErrorTouched;
  loading = false;
  formSendStatus = false;
  form: FormGroup;

  @Select(ContactState.mobileSupportSubjects)
  mobileSupportSubjects$: Observable<MobileSupportSubjectsModel[]>;


  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe,
    private readonly service: ContactService,
    private readonly log: LogService
  ) {}

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_app_support'),
      })
    );
    const userState = this.store.selectSnapshot(UserState.getState);

    console.log('userState', userState);
    this.form = new FormGroup({
      Name: new FormControl(userState.firstName ?
        (userState.firstName + ' ' + userState.lastName) : null, [Validators.required]),
      Email: new FormControl(userState.email, [Validators.required, CustomValidator.mailFormat]),
      PhoneNumber: new FormControl(userState.mobile, [Validators.required, Validators.minLength(7)]),
      Description: new FormControl(null, [Validators.required]),
      FormSubjectCode: new FormControl(null, [Validators.required]),
    });

    this.store.dispatch(new MobileSupportSubjectsAction());

  }

  onSubmitForm() {
    if (this.form.valid) {
      const { value } = this.form;
      this.formSendStatus = false;
      this.loading = true;
      const current = this.store.selectSnapshot(UserState.currentCustomer);
      this.log.action('APP_SUPPORT', 'SEND_FORM').subscribe();

      this.service
        .mobileSupportCreate({
          PhoneNumber: value.PhoneNumber,
          Description: value.Description,
          Name: value.Name,
          Email: value.Email.toLowerCase(),
          FormSubjectCode: value.FormSubjectCode,
          CountryCode: current?.groupKey,
          CompanyName: current?.name,
          CompanyId: current?.publicMenuHeaderCompany,
        })
        .subscribe(
          (val) => {
            if (val.code === 0) {
              this.formSendStatus = true;
            }
            this.loading = false;
          },
          () => {
            this.formSendStatus = false;
            this.loading = false;
          }
        );
    } else {
      validateAllFormFields(this.form);
    }
  }


  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }
}
