import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { Store } from '@ngxs/store';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';

@Component({
  selector: 'app-my-agreements',
  templateUrl: './my-agreements.component.html',
  styleUrls: ['./my-agreements.component.scss']
})
export class MyAgreementsComponent implements OnInit {

  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe,
    private readonly router: Router
  ) {}

  ngOnInit() {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_my_agreements'),
      })
    );
  }

}
