import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GetUserApprovedAgreements } from 'src/app/modules/customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';

@Component({
  selector: 'app-my-agreements-approved',
  templateUrl: './my-agreements-approved.component.html',
  styleUrls: ['./my-agreements-approved.component.scss']
})
export class MyAgreementsApprovedComponent implements OnInit {
  @Select(CustomerState.approvedAgreementsLoading)
  loading$: Observable<boolean>;

  @Select(CustomerState.approvedAggreements)
  approvedAgreements$: Observable<any>;

  approvedAgreements: any;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private store: Store,
    private router: Router
  ) { }

  ngOnInit() {
    this.store.dispatch(new GetUserApprovedAgreements());

    this.approvedAgreements$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(data => {
      if(data){
        this.approvedAgreements = data;
      }
    })
  }

  goToWaitingApprovals() {
    this.router.navigate(['settings/my-agreements/awaiting-approvals']);
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

}
