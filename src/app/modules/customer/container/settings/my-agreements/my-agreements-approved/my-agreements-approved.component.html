<div class="approved-agreements">
  <div class="agreement-area">
    <div *ngFor="let agreement of approvedAgreements" class="agreement d-flex align-items-center p-3 mb-2 border">
      <i class="icon icon-contract"></i>
      <div class="d-flex flex-column ml-2">
        <p class="mb-0 font-size-14px ml-2">{{ agreement.agreementFormName | translate }}</p>
        <div class="d-flex align-items-center ml-2">
          <div class="font-weight-bold" style="font-size: 14px;">
            {{ '_approve_date' | translate }}:
          </div>
          <div class="font-weight-bold ml-2" style="font-size: 14px;">
            <div class="d-flex align-items-center">
              {{ agreement.history[0].approveDate | date: "dd.MM.yyyy" }}
              <div *ngIf="!agreement.hasApprovedLatestVersion" class="new-update" (click)="goToWaitingApprovals()">
                {{ '_new_update' | translate }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="h6 py-3 px-4 text-center pt-5" *ngIf="!approvedAgreements?.length && !(loading$ | async)">
    {{ '_empty_approved_agreements' | translate }}
  </div>
</div>
<app-loader [show]="loading$ | async"></app-loader>
