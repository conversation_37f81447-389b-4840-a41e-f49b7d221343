import { Component, OnDestroy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GetUserAwaitingAgreements } from 'src/app/modules/customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { AgreementTypeEnum } from 'src/app/modules/definition/enum/agreement-type.enum';
import { GetAgreementsAction, GetUserAgreementsAction } from 'src/app/modules/definition/state/definition/definition.actions';

@Component({
  selector: 'app-my-agreements-awaiting-approvals',
  templateUrl: './my-agreements-awaiting-approvals.component.html',
  styleUrls: ['./my-agreements-awaiting-approvals.component.scss'],
})
export class MyAgreementsAwaitingApprovalsComponent implements OnInit, OnDestroy {

  @Select(CustomerState.awaitingAggrementsLoading)
  loading$: Observable<boolean>;

  @Select(CustomerState.awaitingAgreements)
  awaitingAgreements$: Observable<any>;

  awaitingAgreements: any;
  showAgreement = false;
  agreement: any;
  loadingStarted: boolean;
  modalSize: string;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(private store: Store) {}

  ngOnInit() {
    this.awaitingAgreements$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        if (data) {
          this.awaitingAgreements = data;
        }
      });
      this.store.dispatch(new GetUserAwaitingAgreements('', true));
  }

  handleShowAgreement(agreement: any) {
    this.agreement = agreement?.details.map((details) => ({
      ...details,
      agreementFormId: agreement.id,
      agreementCode: agreement?.name,
    }));
    this.modalSize = '75vh';
    this.showAgreement = !this.showAgreement;
  }

  handleBackPressed() {
    this.modalSize = '75vh';
    this.showAgreement = false;
  }

  setLoading(data) {
    this.loadingStarted = data;
  }


  reloadAgreements() {
    this.modalSize = '255px';
    this.store.dispatch(new GetUserAwaitingAgreements('', true));
    this.store.dispatch(new GetUserAgreementsAction(AgreementTypeEnum.PromotionPortal, true));
    this.store.dispatch(new GetAgreementsAction(AgreementTypeEnum.PromotionPortal, true));
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
