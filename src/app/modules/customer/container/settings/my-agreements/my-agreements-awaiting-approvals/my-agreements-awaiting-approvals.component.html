<div class="awaiting-approvals">
  <div
    (click)="handleShowAgreement(agreement)"
    *ngFor="let agreement of awaitingAgreements"
    class="agreement d-flex align-items-center p-3 mb-2 border"
  >
    <i class="icon icon-contract"></i>
    <p class="mb-0 font-size-14px ml-2">{{ agreement?.name | translate }}</p>
  </div>
  <div class="h6 py-3 px-4 text-center pt-5" *ngIf="!awaitingAgreements?.length && !(loading$ | async)">
    {{ '_empty_active_approvals' | translate }}
  </div>
</div>


<div class="awaiting-approvals-agreement-area">

  <app-big-modal [(status)]="showAgreement" style="--size:{{ modalSize }}">
    <app-loader [show]="(loading$ | async) || loadingStarted"></app-loader>
    <app-notification-approval-agreement *ngIf="showAgreement" [agreement]="agreement"
      (onBackPressed)="handleBackPressed()"
      (agreementApproved)="reloadAgreements()"
      (loadingStarted)="setLoading($event)"
      [hasSkipEnable]="true"
      [autoShowAgreement]="true"
    ></app-notification-approval-agreement>
  </app-big-modal>

</div>
<app-loader [show]="(loading$ | async)"></app-loader>
