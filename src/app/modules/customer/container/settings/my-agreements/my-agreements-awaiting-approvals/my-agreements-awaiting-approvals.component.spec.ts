/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { MyAgreementsAwaitingApprovalsComponent } from './my-agreements-awaiting-approvals.component';

describe('MyAgreementsAwaitingApprovalsComponent', () => {
  let component: MyAgreementsAwaitingApprovalsComponent;
  let fixture: ComponentFixture<MyAgreementsAwaitingApprovalsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ MyAgreementsAwaitingApprovalsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(MyAgreementsAwaitingApprovalsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
