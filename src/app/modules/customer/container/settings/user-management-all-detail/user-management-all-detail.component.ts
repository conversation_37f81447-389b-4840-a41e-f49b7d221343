import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { <PERSON><PERSON>rray, FormBuilder, FormGroup } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LogService } from 'src/app/shared/service/log.service';
import { GetMyUsersAction, UpdateMyUsersStateAction } from 'src/app/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { EditUserRolesModel, EditUsersRelations } from '../../../model/edit-user-roles.model';
import { MyUsersModel } from '../../../model/my-users.model';
import { SanitizedCustomerModel } from '../../../model/sanitized-customer.model';
import { UserService } from '../../../service/user.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { UserState } from '../../../state/user/user.state';
import { disableBack } from '../../../../../util/disable-back.util';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';

@Component({
  selector: 'app-user-management-all-detail',
  templateUrl: './user-management-all-detail.component.html',
  styleUrls: ['./user-management-all-detail.component.scss']
})
export class UserManagementAllDetailComponent implements OnInit, OnDestroy {

  paramUserId: string;
  user: MyUsersModel;
  editUserRoleForm: FormGroup;
  userDeletedRequest: boolean;
  changedPermissionsControl: boolean;
  changePermissionsMessagebox: boolean;
  changePermissionsLoading: boolean;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  relations: Array<EditUsersRelations> = [{
    companyId: null,
    manageAccount: null,
    manageEquipments: null,
    manageService: null,
    manageFinance: null,
    manageOffers: null,
    manageSpareParts: null,
  }];
  permissionsData: EditUserRolesModel = {
    userId: null,
    relations: []
  };
  protected disableBack: { remove: () => void };
  protected backClicked: boolean;

  @Select(SettingsState.myUsers)
  readonly myUsers$: Observable<MyUsersModel[]>;

  @Select(SettingsState.myUsersLoading)
  myUsersLoading$: Observable<boolean>;

  @Select(UserState.currentCustomer)
  readonly currentCustomer$: Observable<SanitizedCustomerModel>;

  constructor(
    private readonly store: Store,
    private readonly route: ActivatedRoute,
    private formBuilder: FormBuilder,
    private userService: UserService,
    private readonly loggerService: LogService,
  ) { }

  get relationsArray(): FormArray {
    return this.editUserRoleForm.get('relationsArray') as FormArray;
  }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {

    this.route.params.subscribe(param => {
      this.paramUserId = param.userId;
    });

    this.myUsers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(users => {
        if (users) {
          this.user = users.find(i => {
            return i.userId === this.paramUserId;
          });
          this.store.dispatch(
            new HeaderStatusAction({
              company: false,
              backButton: true,
              closeButton: false,
              hamburgerMenu: false,
              notificationIcon: false,
              title: this.user.firstName + ' ' + this.user.lastName,
            })
          );

          this.userDeletedRequest = this.user.isUserDeleteRequested;

          this.editUserRoleForm = this.formBuilder.group({
            userId: ([] = this.user.userId),
            relationsArray: this.formBuilder.array([]),
          });

          this.user.relations.map(relation => {
            this.relationsArray.push(
              this.formBuilder.group({
                companyId: relation.companyId,
                companyName: relation.companyName,
                manageAccount: relation.manageAccount,
                manageEquipments: relation.manageEquipments,
                manageService: relation.manageService,
                manageFinance: relation.manageFinance,
                manageOffers: relation.manageOffers,
                manageSpareParts: relation.manageSpareParts,
              })
            );
          });
        }
      });
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(currentCustomer => {
        if (currentCustomer) {
          this.store.dispatch(new GetMyUsersAction());
        }
      });

  }

  onSubmit() {
    this.disableBackButton();
    this.changedPermissionsControl = true;
    this.permissionsData.userId = this.editUserRoleForm.value.userId;
  }

  changePermissionsApproved() {
    this.permissionsData.userId = this.editUserRoleForm.value.userId;
    this.loggerService
      .action('USER_MANAGEMENT', 'PERMISSION_CHANGE', {
        // companyId,
        // customerId: this.customer.id,
        userId: this.editUserRoleForm.value.userId,
      })
      .subscribe();
    this.permissionsData.relations = this.editUserRoleForm.value.relationsArray;

    this.userService.editMyUserRoles([this.permissionsData])
      .subscribe(data => {
        this.store.dispatch(new UpdateMyUsersStateAction({
          myUsers: data,
          myUsersLoading: false
        }));
        if (this.backClicked) {
          this.forceBack();
        } else {
          this.removePreventBack(-1);
        }


      }, () => {
        this.store.dispatch(new UpdateMyUsersStateAction({
          myUsersLoading: false
        }));
      });

    this.changedPermissionsControl = false;
    this.changePermissionsMessagebox = false;
    this.changePermissionsLoading = false;
  }

  changePermissionsCancel() {
    this.changePermissionsMessagebox = false;
    if (this.backClicked) {
      this.forceBack();
    }
  }

  backButtonClick() {
    if (this.changedPermissionsControl) {
      this.changePermissionsMessagebox = true;
    }
  }

  ngOnDestroy(): void {
    this.removePreventBack();

    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  protected forceBack() {
    this.removePreventBack(-2);
  }

  protected disableBackButton() {
    if (this.disableBack) {
      return;
    }
    this.disableBack = disableBack(() => {
      console.log('back prevent');
      this.backClicked = true;
      this.backButtonClick();
    });
  }

  protected removePreventBack(goBack = 0) {
    if (this.disableBack) {
      console.log('removed');
      this.disableBack.remove();
      this.disableBack = null;
      if (goBack) {
        window.history.go(goBack);
      }
    }
  }
}
