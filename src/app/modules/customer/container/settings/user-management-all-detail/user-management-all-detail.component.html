<div class="wrap-container d-flex flex-column mb-2">
  <div class="user-management-detail-menu flex-grow-1">
    <div class="px-2">
      <app-error-box
        *ngIf="user?.isUserDeleteRequested"
        [title]="'_warning' | translate"
      >
        <div class="text-danger">
          {{ "_delete_request_info_text" | translate }}
        </div>
      </app-error-box>

      <div *ngIf="user" class="answer fit-body" style="width: 100%">
        <form [formGroup]="editUserRoleForm">
          <ng-container formArrayName="relationsArray">
            <ng-container
              *ngFor="let rel of relationsArray.controls; index as i"
              class="pb-2"
            >
              <ng-container [formGroupName]="i">
                <div class="css-table permission-list-container container">
                  <div class="css-table-header font-size-18px">
                    <div class="read-only-input col">
                      <input
                        style="
                          border: 0 none;
                          width: 100%;
                          background-color: transparent;
                          text-align: center;
                          font-weight: bold;
                          color: black;
                        "
                        formControlName="companyName"
                        readonly
                      />
                    </div>
                  </div>
                  <div class="css-table-body permission-list">
                    <div class="css-table-row dflex flex-row d-none">
                      <div>{{ "_account_management" | translate }}</div>
                      <div class="float-right vertical-middle">
                        <input
                          [id]="'manageAccount' + i"
                          (ngModelChange)="onSubmit()"
                          formControlName="manageAccount"
                          class="checkbox-class"
                          type="checkbox"
                        />
                        <label [for]="'manageAccount' + i"></label>
                      </div>
                    </div>
                    <div class="css-table-row dflex flex-row">
                      <div>{{ "_equipments_management" | translate }}</div>
                      <div class="float-right">
                        <input
                          [id]="'manageEquipments' + i"
                          (ngModelChange)="onSubmit()"
                          formControlName="manageEquipments"
                          class="checkbox-class"
                          type="checkbox"
                          [attr.disabled]="
                            (userDeletedRequest === true ? true : null)
                            || !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)
                          "
                        />
                        <label [for]="'manageEquipments' + i"></label>
                      </div>
                    </div>
                    <div class="css-table-row dflex flex-row">
                      <div>{{ "_service_management" | translate }}</div>
                      <div class="float-right">
                        <input
                          [id]="'manageService' + i"
                          (ngModelChange)="onSubmit()"
                          formControlName="manageService"
                          class="checkbox-class"
                          type="checkbox"
                          [attr.disabled]="
                            (userDeletedRequest === true ? true : null)
                            || !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)
                          "
                        />
                        <label [for]="'manageService' + i"></label>
                      </div>
                    </div>
                    <div class="css-table-row dflex flex-row">
                      <div>{{ "_financial_management" | translate }}</div>
                      <div class="float-right">
                        <input
                          [id]="'manageFinance' + i"
                          (ngModelChange)="onSubmit()"
                          formControlName="manageFinance"
                          class="checkbox-class"
                          type="checkbox"
                          [attr.disabled]="
                            (userDeletedRequest === true ? true : null)
                            || !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)
                          "
                        />
                        <label [for]="'manageFinance' + i"></label>
                      </div>
                    </div>
                    <div class="css-table-row dflex flex-row">
                      <div>{{ "_offer_management" | translate }}</div>
                      <div class="float-right">
                        <input
                          [id]="'manageOffers' + i"
                          (ngModelChange)="onSubmit()"
                          formControlName="manageOffers"
                          class="checkbox-class"
                          type="checkbox"
                          [attr.disabled]="
                            (userDeletedRequest === true ? true : null)
                            || !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)
                          "
                        />
                        <label [for]="'manageOffers' + i"></label>
                      </div>
                    </div>
                    <div class="css-table-row dflex flex-row">
                      <div>{{ "_spare_part_management" | translate }}</div>
                      <div class="float-right">
                        <input
                          [id]="'manageSpareParts' + i"
                          (ngModelChange)="onSubmit()"
                          formControlName="manageSpareParts"
                          class="checkbox-class"
                          type="checkbox"
                          [attr.disabled]="
                            (userDeletedRequest === true ? true : null)
                            || !(borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)
                          "
                        />
                        <label [for]="'manageSpareParts' + i"></label>
                      </div>
                    </div>
                  </div>
                </div>
              </ng-container>
            </ng-container>
          </ng-container>
        </form>
      </div>
    </div>
  </div>
  <div *ngIf="!userDeletedRequest" class="save-cancel-button">
    <div class="mx-auto text-center bg-white p-3">
      <button
        class="
          modal-btn
          btn-sm btn btn-warning btn-gradient
          text-white
          shadow
          col-6
        "
        [attr.disabled]="(changedPermissionsControl === true ? null : true)
        || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.EditUserManagementList) >= 0)"
        (click)="changePermissionsMessagebox = true"
      >
        {{ "_save" | translate }}
      </button>
    </div>
  </div>
</div>
<app-basic-modal
  *ngIf="user"
  [(status)]="changePermissionsMessagebox"
  [headerText]="'_change_permissions_messagebox_header' | translate"
>
  <div class="mb-3">
    <div>{{ "_change_permissions_messagebox_text" | translate }}</div>

    <div class="mx-auto text-center mt-4">
      <button
        class="
          modal-btn
          btn-sm btn btn-secondary btn-gradient
          text-white
          shadow
          col-4
          mr-3
        "
        (click)="changePermissionsCancel()"
        [disabled]="this.changePermissionsLoading"
      >
        {{ "_cancel" | translate }}
      </button>
      <button
        class="
          modal-btn
          btn-sm btn btn-warning btn-gradient
          text-white
          shadow
          col-4
        "
        (click)="changePermissionsApproved()"
        [disabled]="this.changePermissionsLoading"
      >
        {{ "_save" | translate }}
      </button>
    </div>
  </div>
</app-basic-modal>
<app-loader [show]="myUsersLoading$ | async"></app-loader>
