import { HttpClient, HttpResponse } from '@angular/common/http';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LogoutProcessAction } from 'src/app/modules/authentication/state/login/login.actions';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { LogService } from 'src/app/shared/service/log.service';
import { CommonState } from 'src/app/shared/state/common/common.state';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { CustomValidator } from 'src/app/util/custom-validator';
import {
  getFormErrorMessage,
  isShowFormError,
  validateAllFormFields,
} from 'src/app/util/form-error.util';
import { environment } from 'src/environments/environment';
import { SettingsResponse } from '../../../response/settings.response';
import { ContactService } from '../../../service/contact.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { UserState, UserStateModel } from '../../../state/user/user.state';

@Component({
  selector: 'app-delete-account',
  templateUrl: './delete-account.component.html',
  styleUrls: ['./delete-account.component.scss'],
})
export class DeleteAccountComponent implements OnInit, OnDestroy {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Select(UserState.getState)
  userState$: Observable<UserStateModel>;

  @Select(SettingsState.basic)
  basicSettings$: Observable<SettingsResponse>;

  email: string;
  loading = false;
  approve = false;
  userstate: UserStateModel;

  formSendStatus = false;
  isDeleteAccount = false;
  acceptanceLast = false;

  descMinLength = 2;
  descMaxLength = 500;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  protected subscriptions$: Subject<boolean> = new Subject();

  form: FormGroup = new FormGroup({
    Email: new FormControl(null, [
      CustomValidator.mailFormat,
    ]),
    Description: new FormControl(null, [
      Validators.required,
      Validators.minLength(this.descMinLength)
    ]),
    Acceptance: new FormControl(null, [Validators.required]),
  });

  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe,
    private readonly service: ContactService,
    private readonly log: LogService,
    private readonly http: HttpClient
  ) { }

  ngOnInit() {
    this.userState$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(userState => {
        if (!userState) {
          return;
        }
        this.userstate = userState;
        this.email = userState.email.toLowerCase();
      });
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_delete_user_me'),
      })
    );
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);

    // Settings
    this.store.dispatch(new GetBasisSettingsAction());
    this.basicSettings$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(settings => {
        if (settings && settings.formParametersMaxCharacterLimit) {
          const description = settings?.formParametersMaxCharacterLimit
          .find(data => data.formName === 'DefaultForm')?.parameters
          .find(data => data.name === 'Description');
          this.descMaxLength = description.maxLength;
          this.descMinLength = description.minLength;
        }
      });
  }
  sendDeleteRequestForm() {
    if (this.form.valid) {
      const { value } = this.form;
      this.formSendStatus = false;
      this.loading = true;
      const company = this.store.selectSnapshot(LoginState.company);
      const countryCodeCommon = this.store.selectSnapshot(CommonState.countryCode);
      const body = {
        CountryCode: this.userstate?.currentCustomer?.countryCode || countryCodeCommon,
        Description: this.form.value.Description,
        Email: this.form.value.Email || this.email,
      };
      this.log.action('CUSTOMER', 'DELETE_ACCOUNT_SEND');
      console.log('FORM: ', body);
      this.http.post<HttpResponse<any>>(
        `${environment.api}/form/account/delete`,
        body
      ).pipe(takeUntil(this.subscriptions$)).subscribe(data => {
        this.store.dispatch(new LogoutProcessAction());
      });
    } else {
      validateAllFormFields(this.form);
    }
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
