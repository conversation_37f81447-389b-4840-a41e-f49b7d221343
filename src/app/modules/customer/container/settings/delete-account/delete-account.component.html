<div class="px-4 pb-5">
  <app-error-box [title]="'_warning' | translate">
    <div class="text-danger">
      {{ "_delete_user_infobox_text" | translate }}
    </div>
  </app-error-box>
  <form (submit)="isDeleteAccount = true" [formGroup]="form">
    <div class="form-group">
      {{ "_delete_user_warning_text" | translate }}
    </div>
    <div class="form-group">
      <input
        [ngModel]="email"
        [placeholder]="'_email' | translate"
        class="form-control form-control"
        formControlName="Email"
        type="email"
        minlength="3"
        [attr.disabled]="true"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Email) | translate }}
      </div>
    </div>
    <div class="form-group">
      <textarea
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control"
        formControlName="Description"
        [maxLength]="descMaxLength"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        <!-- {{ getFormErrorMessage(form.controls.Description) | translate }} -->
        {{ getFormErrorMessage(form.controls.Description, {
          required : '_required',
          minlength: '_min_length'
        }) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        type="checkbox"
        name="Acceptance"
        id="acceptance"
        formControlName="Acceptance"
        [(ngModel)]="approve"
      />
      <label for="acceptance"></label>
      {{ "_delete_user_acceptance_text" | translate }}
    </div>
    <div class="form-group">
      <input
        [value]="'_send' | translate"
        class="btn btn-gradient btn-block text-white shadow"
        [ngClass]="{ 'btn-secondary': (!form.valid || !approve), 'btn-info': (form.valid && approve) }"
        [disabled]="!form.valid || !approve || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitDeleteAccountForm) >= 0)"
        type="submit"
      />
    </div>
  </form>
</div>
<app-basic-modal
  *ngIf="isDeleteAccount"
  [(status)]="isDeleteAccount"
  [headerText]="'_delete_user_me' | translate"
>
  <div class="mb-3">
    <div>{{ "_delete_account_acceptance_modal_text" | translate }}</div>
    <div class="mx-auto text-center mt-4">
      <button
        class="modal-btn btn-sm btn btn-secondary btn-gradient text-white shadow col-4 mr-3"
        (click)="isDeleteAccount = false"
      >
        {{ "_cancel" | translate }}
      </button>
      <button
        class="modal-btn btn-sm btn btn-danger btn-gradient text-white shadow col-4"
        (click)="sendDeleteRequestForm()"
      >
        {{ "_send" | translate }}
      </button>
    </div>
  </div>
</app-basic-modal>
