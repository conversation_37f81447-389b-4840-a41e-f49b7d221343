<div class="w-100 search-area position-relative mb-3 px-3">
  <input
    #search
    [placeholder]="'_search' | translate"
    class="form-control search-input"
    type="text"
    [(ngModel)]="searchText"
  />
  <i class="icon icon-search"></i>
</div>
<div class="px-3 user-list">
  <ng-container *ngFor="let myUser of filteredUsers">
    <button
      (click)="navigate === 'customer' ? navigateToCustomer(myUser?.roleReferenceId, myUser?.portalUserId) : navigateToAprrove(myUser)"
      class="btn w-100 user-box d-flex align-items-center justify-content-between px-4"
    >
      <div class="d-flex align-items-center">
        <app-account-manager-avatar
          [firstName]="myUser?.name || myUser?.firstName"
          [lastName]="myUser?.lastname || myUser?.lastName"
        ></app-account-manager-avatar>
        <div class="d-flex flex-column text-left ml-4">
          <span
            >{{ myUser?.name || myUser?.firstName }}
            {{ myUser?.lastname || myUser?.lastName }}</span
          >
          <!-- <span
            *ngIf="badge === 'default'"
            class="text-warning font-weight-bold font-size-14px"
          >
            {{ "_related_person_number" | translate }}:
            {{ myUser.relatedPersonNumber }}
          </span> -->
          <app-account-manager-role-label
            *ngIf="badge === 'awaiting'"
            label="_awaiting_approve"
            variant="warning"
          ></app-account-manager-role-label>
        </div>
      </div>
      <div class="d-flex">
        <i class="text-decoration-none icon icon-chevron-right"></i>
      </div>
    </button>
  </ng-container>
</div>
