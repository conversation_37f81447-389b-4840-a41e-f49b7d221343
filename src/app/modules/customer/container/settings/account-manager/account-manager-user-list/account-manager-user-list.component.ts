import { Component, Input, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';

@Component({
  selector: 'app-account-manager-user-list',
  templateUrl: './account-manager-user-list.component.html',
  styleUrls: ['./account-manager-user-list.component.scss']
})
export class AccountManagerUserListComponent implements OnInit {
  @Input()
  users: any[];

  @Input()
  badge: 'default' | 'awaiting' = 'default';

  @Input()
  navigate: 'customer' | 'approve' = 'customer';

  searchText = '';

  constructor(
    private readonly router: Router
  ) { }

  ngOnInit() {

  }

  get filteredUsers() {
    const searchText = this.searchText?.trim().toLocaleLowerCase();

    if (!searchText) {
      return this.users;
    }

    return this.users.filter(user => {
      const nameMatch = user?.name?.toLocaleLowerCase().includes(searchText);
      const firstNameMatch = user?.firstName?.toLocaleLowerCase().includes(searchText);
      const lastNameMatch = user?.lastName?.toLocaleLowerCase().includes(searchText) || user?.lastname?.toLocaleLowerCase().includes(searchText);

      return nameMatch || firstNameMatch || lastNameMatch;
    });
  }


  navigateToCustomer(referenceId: string, portalUserId: string){
    this.router.navigate(['/settings/account-manager/customer'], {
      queryParams: { referenceId, portalUserId },
      queryParamsHandling: 'merge',
    }).then();
  }

  navigateToAprrove(user){
    this.router.navigate(['/settings/account-manager/customer'], {
      queryParams: { approve: true },
      queryParamsHandling: 'merge',
      state: { user }
    }).then();
  }
}
