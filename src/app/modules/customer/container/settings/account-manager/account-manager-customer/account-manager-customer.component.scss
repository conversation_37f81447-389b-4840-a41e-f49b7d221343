.account-manager-customer {
  height: calc(100vh - 64px);
  overflow: auto;

  .customer-detail-title {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    place-items: flex-start;

    .back-btn {
      &:focus {
        box-shadow: unset;
      }
    }
  }

  .manage-roles-header {
    // display: grid;
    // grid-template-columns: repeat(2, 1fr);
    // gap: 1.5rem;

    .tab {
      opacity: 0.8;
      height: 48px;
      border-radius: 0;
      font-weight: bold;

      &.active {
        border-bottom: 3px solid #ffa300;
        opacity: 1;
        color: #ffa300;
      }

      &:focus {
        box-shadow: unset;
      }
    }
  }

  .customer-detail-card {
    border-radius: 0.5rem;
    background-color: #fff;
  }

  .add-roles-list {
    padding: 0;
    display: flex;
    flex-direction: column;
    gap: 1rem;

    .role-info {
      list-style: none;
      background-color: #f3f5f7;
      border-radius: 6px;

      &-odd {
        //border-radius: 0.5rem;
      }
    }
  }
}
:host ::ng-deep ngb-accordion {
  .card {
    border-radius: 2px;
    border: none;
    border: 1px solid rgba(0, 0, 0, 0.125);
  }

  .card-body {
    padding: 1rem !important;
  }

  .card-header {
    border-radius: 0;
    padding: 0;
    border: none;

    .btn {
      width: 100%;
      float: left;
      text-align: left !important;
      box-shadow: none;
      border-radius: 0;
      //border: none;
    }

    .btn:focus .btn:hover {
      box-shadow: none;
    }
  }

  .btn-link {
    //color: #4a8eb0;
    background-color: #fff;
    padding: 0;
    border: 0;
  }

  .btn-link:hover {
    box-shadow: none;
    text-decoration: none;
    //color: #4a8eb0;
  }

  button:not(.collapsed) {
    //font-weight: 600;
    //background-color: #F5F4F4;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.075);

    color: #4a8eb0;

    .icon-chevron-right {
      transform: rotate(90deg);
    }
  }

  .icon-chevron-right {
    line-height: 1em;
    height: 1em;
    transition: all 0.4s ease;
  }
}

ngb-accordion .btn:focus .btn:hover {
  box-shadow: none;
}

.role-options {
  &-title {
    text-decoration: underline;
  }
}

[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 24px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  // font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

[type="text"] {
  height: 40px;
}

.icon-plus {
  font-size: 20px;
  font-weight: bold;
}

.info-badge {
  font-size: 10px;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid #6c6c6c;
}

.approve-card{
  background-color: #fff;
  border-radius: 6px;
}


.btn-gray{
  background-color:#B0B0B0;
}
