import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Navigate } from '@ngxs/router-plugin';
import { Select, Store } from '@ngxs/store';
import { combineLatest, forkJoin, Observable, Subject } from 'rxjs';
import { filter, take, takeUntil } from 'rxjs/operators';
import {
  AccountUsersModel,
  CompaniesModel,
  ManageableRoleModel,
  ManageableRolesModel,
  RoleOptionModel,
  RoleOptionsEnum,
  UserRoleDetailModel,
  UserRoleModel,
} from 'src/app/modules/customer/model/my-users.model';
import { CustomerService } from 'src/app/modules/customer/service/customer.service';
import {
  GetManageableRolesDetailsAction,
  GetUserRoleDetailsAction,
} from 'src/app/modules/customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import {
  getFormErrorMessage,
  isShowFormError,
  validateAllFormFields,
} from 'src/app/util/form-error.util';

@Component({
  selector: 'app-account-manager-customer',
  templateUrl: './account-manager-customer.component.html',
  styleUrls: ['./account-manager-customer.component.scss'],
})
export class AccountManagerCustomerComponent implements OnInit, OnDestroy {

  @Input()
  fromNotification: any;

  @Select(CustomerState.userRoleDetails)
  userRoleDetails$: Observable<any>;

  @Select(CustomerState.manageableRoleDetails)
  manageableRoleDetails$: Observable<ManageableRolesModel>;

  @Select(CustomerState.userRoleDetailsLoading)
  userRoleDetailsLoading$: Observable<boolean>;

  isShowError = isShowFormError;
  getFormErrorMessage = getFormErrorMessage;
  userDetail: any;
  manageableRoles: ManageableRoleModel[];
  roleOptions: RoleOptionModel[];
  companies: CompaniesModel[];
  activeTab = 0;
  activeRoleToggle = false;
  countryCodeList = ['TR', 'KZ', 'AZ', 'GE', 'KG'];
  roleOptionsEnum = RoleOptionsEnum;
  userRoleDetail: any;
  referenceId: string;
  editLoading = false;
  portalUserId: string;
  currentRoles = [];
  editModalStatus = false;
  approveCustomer = false;
  approveLoading = false;
  approveComment = '';
  successSendForm = false;
  editableRole = null;
  authEquipmentList = null;
  unAuthEquipmentList = null;
  countryList = null;
  successUpdateRole = false;
  loading = false;
  deactiveAccountModal = false;
  successDeactiveAccount = false;
  selectedCompany = null;
  currentCompanyUserRoleDetail = null;
  updateForm: FormGroup = new FormGroup({
    AuthEquipmentText: new FormControl('', []),
    UnAuthEquipmentText: new FormControl('', []),
  });

  constructor(
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly router: Router
  ) {}

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit(): void {
    const { referenceId, portalUserId } = this.route.snapshot.queryParams;
    this.referenceId = referenceId;
    this.portalUserId = portalUserId;
    const myUsers = this.store.selectSnapshot(CustomerState.myUsers);
    this.userDetail = myUsers.find((users) => users.portalUserId === portalUserId);
    const approveCustomer = this.route.snapshot.queryParams['approve'] || !!this.fromNotification;
    if (approveCustomer) {
      this.userDetail = window.history.state.user || this.fromNotification;
      this.approveCustomer = true;
    } else if(!this.fromNotification){
      this.store.dispatch(
        new GetUserRoleDetailsAction(referenceId, portalUserId)
      );
      this.store.dispatch(new GetManageableRolesDetailsAction());

      combineLatest([this.userRoleDetails$, this.manageableRoleDetails$])
        .pipe(takeUntil(this.subscriptions$))
        .subscribe({
          next: ([userRoleDetail, manageableRoleDetails]) => {
            if (userRoleDetail && manageableRoleDetails) {
              this.userRoleDetail = userRoleDetail;
              this.manageableRoles = manageableRoleDetails.manageableRoles;
              this.roleOptions = manageableRoleDetails.roleOptions;
              this.companies = manageableRoleDetails?.companies;
              this.userRoleDetailByCompany()
            }
          },
          error: (error) => {
            // Hata yönetimi
          },
        });

        this.manageableRoleDetails$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(data => {
          if(data){
            this.selectedCompany = data?.companies[0];
            this.userRoleDetailByCompany()
          }
        })
    }
  }

  userRoleDetailByCompany() {
    const currentCompanyUserRoleDetail = this.userRoleDetail?.find((u) => u.companyId === this.selectedCompany?.id);

    this.currentCompanyUserRoleDetail = currentCompanyUserRoleDetail;

    const currentRolesInUserDetail = currentCompanyUserRoleDetail?.roleDetails ?
    this.userRoleDetail.map(a => (
      a.roleDetails.map((role) => ({
        RoleOptions: role.roleOptions,
        RoleId: role?.roleId,
        CompanyId: currentCompanyUserRoleDetail?.companyId,
      }))
    )): [];

  const allRoleOptions = this.userRoleDetail?.flatMap((item) =>
  item.roleDetails.map((role) => ({
    RoleOptions: role.roleOptions,
    RoleId: role.roleId,
    CompanyId: item?.companyId,
  }))
);


    this.currentRoles = allRoleOptions;
  }

  navigateBack() {
    window.history.back();
  }

  navigateTab(tab: number) {
    this.activeTab = tab;
  }

  checkRole(roleId: string) {
    if(this.currentCompanyUserRoleDetail){
      return this.currentCompanyUserRoleDetail?.roleDetails.some(
        (role) => role.roleId === roleId
      );
    }
  }

  changeRoleStatus(roleId: string) {
    this.editLoading = true;
    const role = {
      RoleId: roleId,
      CompanyId: this.selectedCompany.id,
      RoleOptions: this.roleOptions,
    };
    if (this.currentRoles.find((role) => role.RoleId === roleId && role.CompanyId === this.selectedCompany?.id)) {
      this.currentRoles = this.currentRoles.filter(a => a.CompanyId === this.selectedCompany?.id && a.RoleId !== roleId)
    } else {
      this.currentRoles = [...this.currentRoles, role];
    }

    const body = [
      {
        roleReferenceId: this.userDetail.roleReferenceId,
        referenceUserId: this.userDetail.portalUserId,
        Roles: this.currentRoles,
      },
    ];

    this.customerService.editUserRoleDetails(body).subscribe(
      () => {
        this.store.dispatch(
          new GetUserRoleDetailsAction(this.referenceId, this.portalUserId)
        );
        this.editLoading = false;
      },
      () => {
        this.editLoading = false;
      }
    );
  }

  openEditModal(roleId) {
    const role = this.currentRoles.find((r) => r.RoleId === roleId);
    const authEquipmentList = role.RoleOptions.find(
      (option) => option.roleOption === RoleOptionsEnum.AuthEquipments
    );
    const unAuthEquipmentList = role.RoleOptions.find(
      (option) => option.roleOption === RoleOptionsEnum.UnauthEquipments
    );
    const countryList = role.RoleOptions.find(
      (option) => option.roleOption === RoleOptionsEnum.Country
    );
    this.editableRole = role;
    this.authEquipmentList = authEquipmentList;
    this.unAuthEquipmentList = unAuthEquipmentList;
    this.countryList = countryList;
    this.editModalStatus = !this.editModalStatus;
  }

  approveUser(approve) {
    this.approveLoading = true;
    const formBody = {
      applicationId: this.userDetail?.applicationId,
      approved: approve,
      comment: this.approveComment,
    };

    this.customerService.approveUser(formBody).subscribe(
      () => {
        this.approveLoading = false;
        this.successSendForm = true;
      },
      () => {
        this.approveLoading = false;
      }
    );
  }

  backToUserList() {
    this.router.navigate(['settings', 'account-manager', 'customers']);
  }

  countryControl(value: string) {
    return this.countryList?.values?.includes(value);
  }

  addCountry(country) {
    if (this.countryList.values.find((c) => c === country)) {
      this.countryList = {
        ...this.countryList,
        values: this.countryList.values.filter((c) => c !== country),
      };
    } else {
      this.countryList = {
        ...this.countryList,
        values: [...this.countryList, country],
      };
    }
  }

  addAuthEquipments(remove = false, serialNumber?: string) {
    if (remove) {
      this.authEquipmentList = {
        ...this.authEquipmentList,
        values: this.authEquipmentList.values.filter(
          (equipment) => equipment !== serialNumber
        ),
      };
    } else {
      this.authEquipmentList = {
        ...this.authEquipmentList,
        values: [
          ...this.authEquipmentList.values,
          this.updateForm.value.AuthEquipmentText,
        ],
      };
      this.updateForm.patchValue({ AuthEquipmentText: '' });
    }
  }

  updateRoleContent(roleId: string) {
    this.editLoading = true;

    const updateRole = this.currentRoles.find((r) => r.RoleId === roleId);
    this.currentRoles = this.currentRoles.filter((r) => r.RoleId !== roleId);

    const body = [
      {
        roleReferenceId: this.userDetail.roleReferenceId,
        referenceUserId: this.userDetail.portalUserId,
        Roles: [
          ...this.currentRoles,
          {
            ...updateRole,
            RoleOptions: [
              this.authEquipmentList,
              this.unAuthEquipmentList,
              this.countryList,
            ],
          },
        ],
      },
    ];

    this.customerService.editUserRoleDetails(body).subscribe(
      () => {
        this.store.dispatch(
          new GetUserRoleDetailsAction(this.referenceId, this.portalUserId)
        );
        this.editLoading = false;
        this.successUpdateRole = true;
        this.editModalStatus = false;
      },
      () => {
        this.editLoading = false;
      }
    );
  }

  closeEditModal() {
    this.editModalStatus = false;
    this.successUpdateRole = false;
  }

  addUnAuthEquipments(remove = false, serialNumber?: string) {
    if (remove) {
      this.unAuthEquipmentList = {
        ...this.unAuthEquipmentList,
        values: this.unAuthEquipmentList.values.filter(
          (equipment) => equipment !== serialNumber
        ),
      };
    } else {
      this.unAuthEquipmentList = {
        ...this.unAuthEquipmentList,
        values: [
          ...this.unAuthEquipmentList.values,
          this.updateForm.value.UnAuthEquipmentText,
        ],
      };
      this.updateForm.patchValue({ UnAuthEquipmentText: '' });
    }
  }

  disabledAddBtn(value: string) {
    return !value.trim();
  }

  openDeactiveAccountModal() {
    this.deactiveAccountModal = !this.deactiveAccountModal;
  }

  deactiveAccount() {
    this.loading = true;

    const body = {
      deletedUserId: this.userDetail?.portalUserId,
      description: '',
    };

    this.customerService.deleteUserFromAccountManager(body).subscribe(
      () => {
        this.loading = false;
        this.successDeactiveAccount = true;
        this.openDeactiveAccountModal();
      },
      () => {
        this.loading = false;
      }
    );
  }

  changeSelectedCompany(company: CompaniesModel) {
    this.selectedCompany = company;
    this.userRoleDetailByCompany()
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
