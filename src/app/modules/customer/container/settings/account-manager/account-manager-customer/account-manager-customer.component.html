<ng-container
  *ngIf="!successSendForm && !successUpdateRole && !successDeactiveAccount"
>
  <div class="account-manager-customer p-3">
    <div class="h5 font-weight-bold mb-4 customer-detail-title text-nowrap">
      <button class="btn p-0 back-btn" (click)="navigateBack()">
        <i class="icon icon-back font-weight-bold"></i>
      </button>
      <span style="place-self: center">
        {{ "_user" | translate }}
      </span>
    </div>
    <div class="customer-detail-card px-3 py-3 d-flex align-items-center">
      <app-account-manager-avatar
        [firstName]="userDetail?.name || userDetail?.firstName"
        [lastName]="userDetail?.lastname || userDetail?.lastName"
      ></app-account-manager-avatar>
      <div class="ml-4">
        <div
          class="d-flex align-items-center mb-2 font-size-14px font-weight-semi-bold"
        >
          <span class=""
            >{{ userDetail?.name || userDetail?.firstName }}
            {{ userDetail?.lastname || userDetail?.lastName }}</span
          >
        </div>
        <div
          *ngIf="userRoleDetail?.roleDetails"
          class="d-flex align-items-center flex-wrap mb-3"
          style="gap: 0.5rem"
        >
          <app-account-manager-role-label
            *ngFor="let role of userRoleDetail?.roleDetails"
            [label]="role?.title"
          ></app-account-manager-role-label>
        </div>
        <div class="d-flex align-items-center mb-2 font-size-14px">
          <i class="icon icon-contact"></i>
          <span class="ml-2 font-size-12px">{{ userDetail?.email }}</span>
        </div>
        <!-- <div class="d-flex align-items-center mb-2 font-size-14px">
          <i class="icon icon-avatar"></i>
          <span class="ml-2 font-size-12px">{{
            userDetail?.relatedPersonNumber || userDetail?.applicationId
          }}</span>
        </div> -->
        <div
          *ngIf="approveCustomer"
          class="d-flex align-items-center mb-2 font-size-14px"
        >
          <span class="info-badge">i</span>
          <span class="ml-2" style="color: red">
            {{ "_awaiting_approve" | translate }}
          </span>
        </div>
        <button
          *ngIf="!approveCustomer"
          class="btn btn-sm p-0"
          style="color: #da3a3c; text-decoration: underline"
          (click)="openDeactiveAccountModal()"
        >
          {{ "_deactive_account" | translate }}
        </button>
      </div>
    </div>
    <ng-container *ngIf="!approveCustomer">
      <div class="company-select-container mb-3">
        <div class="manage-roles-header mt-2">
          <div class="h6">
            {{ "_select_company" | translate }}
          </div>
        </div>
        <ng-select
          class="service-drp"
          [searchable]="false"
          [placeholder]="'_select' | translate"
          [clearable]="false"
          [(ngModel)]="selectedCompany"

          (ngModelChange)="changeSelectedCompany($event)"
        >
          <ng-option
            *ngFor="let company of companies"
            [value]="company"
          >
            {{company.name}}
          </ng-option>
        </ng-select>
      </div>
      <div class="manage-roles-header mt-2">
        <div class="h6">
          {{ "_add_role" | translate }}
        </div>
      </div>
      <ul class="add-roles-list mt-3" *ngIf="activeTab === 0">
        <div *ngFor="let manageableRole of manageableRoles">
          <li class="role-info font-size-14px p-3 d-flex align-items-center">
            <app-account-manager-role-toggle-btn
              [activeRole]="checkRole(manageableRole.roleId)"
              (onChangeRoleStatus)="changeRoleStatus(manageableRole.roleId)"
            ></app-account-manager-role-toggle-btn>
            <div class="d-flex flex-column flex-grow-1 ml-4">
              <span class="font-weight-bold">
                {{ manageableRole?.name }}
              </span>
              <span>
                {{ manageableRole?.description }}
              </span>
            </div>
            <!-- <button
              (click)="openEditModal(manageableRole.roleId)"
              class="btn p-0"
              *ngIf="checkRole(manageableRole.roleId)"
            >
              <i
                class="text-decoration-none icon icon-edit font-weight-bold"
                style="font-size: 20px"
              ></i>
            </button> -->
          </li>
        </div>
      </ul>
    </ng-container>

    <ng-container *ngIf="approveCustomer">
      <div class="approve-card p-3 mt-2">
        <div class="h6">
          {{ "_user_approve" | translate }}
        </div>
        <p class="font-size-14px">{{ "_approve_info" | translate }}</p>
        <textarea
          appInputMaxLength
          [placeholder]="'_description' | translate"
          [rows]="5"
          class="form-control"
          minlength="3"
          [(ngModel)]="approveComment"
          style="resize: none;"
        ></textarea>
        <div class="row m-0 mt-2">
          <button
            class="col mr-2 btn btn-sm btn-warning text-white"
            (click)="approveUser(true)"
          >
            {{ "_approve" | translate }}
          </button>
          <button
            class="col btn btn-sm btn-gray text-white"
            (click)="approveUser(false)"
          >
            {{ "_reject" | translate }}
          </button>
        </div>
      </div>
    </ng-container>
  </div>
</ng-container>
<app-loader
  [show]="
    (userRoleDetailsLoading$ | async) ||
    editLoading ||
    approveLoading ||
    loading
  "
></app-loader>
<app-success-modal
  *ngIf="successSendForm"
  message="_successfully_send_account_approve"
>
  <div
    class="btn btn-warning btn-gradient btn-block text-white shadow"
    (click)="backToUserList()"
  >
    {{ "_return_back" | translate }}
  </div>
</app-success-modal>
<app-big-modal [(status)]="editModalStatus">
  <div class="edit-modal p-3">
    <div>
      <div
        class="role-options"
        *ngFor="let roleOption of editableRole?.RoleOptions"
      >
        <div class="h6 role-options-title mb-3">
          {{ roleOption?.roleOptionDescription }}
        </div>
        <form [formGroup]="updateForm">
          <ng-container
            *ngIf="roleOption.roleOption === roleOptionsEnum.AuthEquipments"
          >
            <div class="h6">{{ "_auth_equipments" | translate }}</div>
            <div class="mb-4 auth-equipment-list">
              <div class="input-container d-flex align-items-center">
                <input
                  [placeholder]="'_serial_number' | translate"
                  class="form-control"
                  type="text"
                  [name]="'AuthEquipmentText'"
                  formControlName="AuthEquipmentText"
                />
                <button
                  type="button"
                  class="btn p-0 ml-3"
                  (click)="addAuthEquipments()"
                  [disabled]="
                    disabledAddBtn(updateForm.value.AuthEquipmentText)
                  "
                >
                  <i class="icon icon-plus font-size-20px"></i>
                </button>
              </div>
              <!-- <div
                [ngClass]="{
                  'd-block': isShowError(updateForm.controls.AuthEquipmentText)
                }"
                class="invalid-feedback pl-3"
              >
                {{
                  getFormErrorMessage(updateForm.controls.AuthEquipmentText)
                    | translate
                }}
              </div> -->
              <div
                class="serial-number-list d-flex flex-wrap align-items-center mt-2"
                style="gap: 0.5rem"
              >
                <ng-container *ngFor="let item of authEquipmentList.values">
                  <app-account-manager-role-label
                    [label]="item"
                    [removeBtn]="true"
                    (onRemoveLabel)="addAuthEquipments(true, item)"
                  >
                  </app-account-manager-role-label>
                </ng-container>
              </div>
            </div>
          </ng-container>

          <ng-container
            *ngIf="roleOption.roleOption === roleOptionsEnum.UnauthEquipments"
          >
            <div class="mb-4 unauth-equipment-list">
              <div class="h6">{{ "_unauth_equipments" | translate }}</div>
              <div class="input-container d-flex align-items-center">
                <input
                  [placeholder]="'_serial_number' | translate"
                  class="form-control"
                  type="text"
                  [name]="'UnAuthEquipmentText'"
                  formControlName="UnAuthEquipmentText"
                />
                <button
                  type="button"
                  class="btn p-0 ml-3"
                  (click)="addUnAuthEquipments()"
                  [disabled]="
                    disabledAddBtn(updateForm.value.UnAuthEquipmentText)
                  "
                >
                  <i class="icon icon-plus font-size-20px"></i>
                </button>
              </div>
              <!-- <div
                [ngClass]="{
                  'd-block': isShowError(
                    updateForm.controls.UnAuthEquipmentText
                  )
                }"
                class="invalid-feedback pl-3"
              >
                {{
                  getFormErrorMessage(updateForm.controls.UnAuthEquipmentText)
                    | translate
                }}
              </div> -->
              <div
                class="serial-number-list d-flex flex-wrap align-items-center mt-2"
                style="gap: 0.5rem"
              >
                <ng-container *ngFor="let item of unAuthEquipmentList.values">
                  <app-account-manager-role-label
                    [label]="item"
                    [removeBtn]="true"
                    (onRemoveLabel)="addUnAuthEquipments(true, item)"
                  >
                  </app-account-manager-role-label>
                </ng-container>
              </div>
            </div>
          </ng-container>

          <ng-container
            *ngIf="roleOption.roleOption === roleOptionsEnum.Country"
          >
            <div class="h6">{{ "_country" | translate }}</div>
            <div
              class="country-list d-flex align-items-center justify-content-between"
            >
              <div *ngFor="let country of countryCodeList">
                <input
                  type="checkbox"
                  [id]="country"
                  [checked]="countryControl(country)"
                  (click)="addCountry(country)"
                />
                <label [for]="country">{{ country }}</label>
              </div>
            </div>
          </ng-container>
        </form>
      </div>
    </div>
    <button
      class="btn btn-warning text-white w-100 btn-sm mt-1"
      (click)="updateRoleContent(editableRole.RoleId)"
    >
      {{ "_update" | translate }}
    </button>
  </div>
</app-big-modal>
<app-success-modal
  *ngIf="successUpdateRole"
  message="_successfully_send_account_approve"
>
  <div
    class="btn btn-warning btn-gradient btn-block text-white shadow"
    (click)="closeEditModal()"
  >
    {{ "_return_back" | translate }}
  </div>
</app-success-modal>
<app-big-modal [(status)]="deactiveAccountModal">
  <div class="p-3">
    <p class="text-center">
      {{ "_deactive_account_text" | translate }}
    </p>
    <div class="row w-100 m-0">
      <button
        class="btn-gray btn btn-sm text-white col"
        (click)="openDeactiveAccountModal()"
      >
        {{ "_cancel" | translate }}
      </button>
      <button
        class="btn-gray btn btn-sm text-white col ml-3"
        style="background-color: #da3a3c"
        (click)="deactiveAccount()"
      >
        {{ "_approve" | translate }}
      </button>
    </div>
  </div>
</app-big-modal>
<app-success-modal
  *ngIf="successDeactiveAccount"
  message="_successfully_send_deactive-account"
>
  <div
    class="btn btn-warning btn-gradient btn-block text-white shadow"
    (click)="backToUserList()"
  >
    {{ "_return_back" | translate }}
  </div>
</app-success-modal>
