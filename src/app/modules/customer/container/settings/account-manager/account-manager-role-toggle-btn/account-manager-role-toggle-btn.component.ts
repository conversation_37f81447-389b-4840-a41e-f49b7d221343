import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-account-manager-role-toggle-btn',
  templateUrl: './account-manager-role-toggle-btn.component.html',
  styleUrls: ['./account-manager-role-toggle-btn.component.scss']
})
export class AccountManagerRoleToggleBtnComponent implements OnInit {
  @Input() 
  activeRole = false;
  
  @Output()
  onChangeRoleStatus : EventEmitter<void> = new EventEmitter<void>();

  constructor() { }

  ngOnInit() {}


  changeRoleStatus(){
    this.onChangeRoleStatus.emit();
  }
}
