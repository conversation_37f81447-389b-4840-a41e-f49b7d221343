<nav class="account-manager-navbar">
  <div class="border-bottom navbar-container" [ngClass]="{
    'with-qr': accountManagementShareQrSystemFeature,
    'without-qr': !accountManagementShareQrSystemFeature
  }">
    <a
      routerLink="/settings/account-manager/customers"
      routerLinkActive="active-link"
      class="link p-0 d-flex flex-column align-items-center nav-btn ml-1 text-nowrap"
    >
      {{ "_customers" | translate }}
    </a>
    <a
      routerLink="/settings/account-manager/awaiting-approvals"
      routerLinkActive="active-link"
      class="link p-0 d-flex flex-column align-items-center nav-btn text-nowrap"
    >
      {{ "_awaiting_approvals" | translate }}
    </a>
    <a
      *ngIf="accountManagementShareQrSystemFeature"
      routerLink="/settings/account-manager/share-qr"
      routerLinkActive="active-link"
      class="link p-0 d-flex flex-column align-items-center nav-btn mr-1 text-nowrap"
    >
    <div>
      <img class="coin-history-icon" [src]="qr" />
    </div>
  </a>
  </div>
</nav>
