import { Component, OnInit } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/util/system-feature.util';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-account-manager-navbar',
  templateUrl: './account-manager-navbar.component.html',
  styleUrls: ['./account-manager-navbar.component.scss']
})
export class AccountManagerNavbarComponent implements OnInit {

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  protected subscriptions$: Subject<boolean> = new Subject();

  accountManagementShareQrSystemFeature: boolean;

  qr = `${environment.assets}/qr.png`;

  constructor() { }

  ngOnInit() {
    this.systemFeatures$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(features => {
      this.accountManagementShareQrSystemFeature = systemFeature('account_manager_share_qr', features, true);
    });
  }
  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }


}
