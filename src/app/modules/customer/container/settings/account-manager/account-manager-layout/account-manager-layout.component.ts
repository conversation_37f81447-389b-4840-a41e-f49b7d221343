import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslatePipe } from '@ngx-translate/core';
import { Store } from '@ngxs/store';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';

@Component({
  selector: 'app-account-manager-layout',
  templateUrl: './account-manager-layout.component.html',
  styleUrls: ['./account-manager-layout.component.scss']
})
export class AccountManagerLayoutComponent implements OnInit {

  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe,
    private readonly router: Router
  ) {}

  ngOnInit() {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_account_manager'),
      })
    );
  }


  // navigateToProfile(){
  //   this.router.navigate(['settings', 'account-manager', 'profile']);
  // }

  // navigateToCustomers(){
  //   this.router.navigate(['settings', 'account-manager', 'customers']);
  // }
}
