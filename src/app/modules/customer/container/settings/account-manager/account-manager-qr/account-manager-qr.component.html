<app-account-manager-navbar></app-account-manager-navbar>
<div *ngIf="!(myUsersLoading$ | async)">
  <div class="share-qr-title text-center mt-4 h5">
    {{ "_share_qr_title" | translate }}
  </div>

  <div class="share-qr-title text-center mt-5">
    {{ "_share_qr_description" | translate }}
  </div>

  <div class="qrcodeImage text-center mt-5">
    <div class="share-qr-center">
      <qrcode
        *ngIf="shareLinkText"
        [qrdata]="shareLinkText"
        [colorDark]="'#000000'"
        [colorLight]="'#ffa300'"
        [elementType]="'url'"
        [width]="200"
      ></qrcode>
      <div class="share-qr-link" (click)="shareLink()">
        {{ "_share_qr_link" | translate }}
        <i class="icon icon-share"></i>
      </div>

    </div>
  </div>
</div>
<app-loader [show]="myUsersLoading$ | async"></app-loader>
