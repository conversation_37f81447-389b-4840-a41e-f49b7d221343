
.qrcodeImage {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  // background: linear-gradient(180deg, rgba(2,0,36,1) 0%, rgba(255,163,0,1) 0%, rgba(2,0,36,1) 100%);
  height: 100vh;
}

.share-qr-center {
  display: flex;
  gap: 1rem;
  flex-direction: column;
  justify-content: center;
}

.share-qr-link {
  text-decoration: underline;
}

.share-qr-area {
  display: flex;
  justify-content: space-between;
}

::ng-deep .share-qr-center .qrcode img {
  border: 1px solid #ffa300;
  padding: 10px;
  border-radius: 35px;
}

.icon {
  font-size: 20px;
}
