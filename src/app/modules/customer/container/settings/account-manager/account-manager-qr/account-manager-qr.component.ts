import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { GenerateReferenceNumberAction } from 'src/app/modules/definition/state/definition/definition.actions';

@Component({
  selector: 'app-account-manager-qr',
  templateUrl: './account-manager-qr.component.html',
  styleUrls: ['./account-manager-qr.component.scss']
})
export class AccountManagerQrComponent implements OnInit {

  @Select(CustomerState.referenceNumberLoading)
  myUsersLoading$: Observable<boolean>;

  shareLinkText: string;
  customerId: string;

  constructor(
    private readonly frameMessageService: FrameMessageService,
    private readonly store: Store
  ) { }

  ngOnInit() {
    this.store.dispatch(new GenerateReferenceNumberAction({hasUsageLimit: false}))
    .pipe(map(() => this.store.selectSnapshot(CustomerState.referenceNumber)))
    .subscribe((state: any) => {
      this.shareLinkText = state?.url;
    });
  }

  shareLink() {
    this.frameMessageService.sendMessage(FrameMessageEnum.shareContent, { url: this.shareLinkText });
  }
}
