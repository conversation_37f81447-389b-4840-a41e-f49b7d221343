/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { AccountManagerAwaitingApprovalsComponent } from './account-manager-awaiting-approvals.component';

describe('AccountManagerAwaitingApprovalsComponent', () => {
  let component: AccountManagerAwaitingApprovalsComponent;
  let fixture: ComponentFixture<AccountManagerAwaitingApprovalsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AccountManagerAwaitingApprovalsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AccountManagerAwaitingApprovalsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
