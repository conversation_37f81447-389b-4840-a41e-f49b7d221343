import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { GetActiveApplicationsAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';

@Component({
  selector: 'app-account-manager-awaiting-approvals',
  templateUrl: './account-manager-awaiting-approvals.component.html',
  styleUrls: ['./account-manager-awaiting-approvals.component.scss']
})
export class AccountManagerAwaitingApprovalsComponent implements OnInit, OnDestroy {
  @Select(CustomerState.activeApplications)
  activeApplications$: Observable<any>

  @Select(CustomerState.activeApplicationsLoading)
  activeApplicationsLoading$: Observable<boolean>

  searchText: string;
  activeApplications: any;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store
  ) {}

  ngOnInit() {
    this.store.dispatch(new GetActiveApplicationsAction());
    this.activeApplications$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.activeApplications = data?.applications;
    })
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
