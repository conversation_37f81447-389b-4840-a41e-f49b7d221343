.account-manager-customers{
    height: calc(100vh - 120px);
}

:host ::ng-deep ngb-accordion {
    .card{
        border-radius: 2px;
        border: none;
        border: 1px solid rgba(0, 0, 0, 0.125);
    }

    .card-body {
        padding: 1.5rem 2rem !important;
    }

    .card-header {
        border-radius: 0;
        padding: 0;
        border: none;

        .btn {
        width: 100%;
        float: left;
        text-align: left !important;
        box-shadow: none;
        border-radius: 0;
        //border: none;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    }

    .btn:focus .btn:hover {
        box-shadow: none;
    }
}

    .btn-link {
    //color: #4a8eb0;
    background-color: #fff;
    padding: 1rem 2rem;
}

.btn-link:hover {
    box-shadow: none;
    text-decoration: none;
    //color: #4a8eb0;
}

    button:not(.collapsed) {
    //font-weight: 600;
    //background-color: #F5F4F4;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.075);

    color: #4a8eb0;

    .icon-chevron-right {
      transform: rotate(90deg);
    }
  }

    .icon-chevron-right {
        line-height: 1em;
        height: 1em;
        transition: all 0.4s ease;
    }
}

ngb-accordion .btn:focus .btn:hover {
  box-shadow: none;
}

.user-box{
    background-color: #fff;
    border-radius: .5rem;
}

.user-list{
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.icon-circle-avatar{
    font-size: 48px;
}

.account-manager-navbar {
    padding-top: 0.5rem;
    padding-bottom: 0.25rem;
    background-color: #fff;

    .icon-persons {
      font-size: 20px;
      font-weight: 700;
    }
  }

  .link {
    text-decoration: none;
    padding: 0.75rem !important;
    border-bottom: 4px solid transparent;
    font-weight: bold;
    color: #8C8C8C;
  }


.active-link {
  color: #4a8eb0 !important;
  border-bottom-color: #4a8eb0 !important;
}

.navbar-container{
    display: grid;
    grid-template-columns: 1fr 1fr;
    border-bottom: 1px solid #CFCFCF;
    gap: 1.5rem;
}
