import { Component, OnDestroy, OnInit } from '@angular/core';
import { Route, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { AccountUsersModel } from 'src/app/modules/customer/model/my-users.model';
import { GetMyUsersAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';

@Component({
  selector: 'app-account-manager-customers',
  templateUrl: './account-manager-customers.component.html',
  styleUrls: ['./account-manager-customers.component.scss']
})
export class AccountManagerCustomersComponent implements OnInit, OnDestroy {
  @Select(CustomerState.myUsers)
  myUsers$: Observable<AccountUsersModel[]>;

  @Select(CustomerState.myUsersLoading)
  myUsersLoading$: Observable<boolean>;

  @Select(LoginState.language)
  lang$: Observable<string>;

  myUsers: AccountUsersModel[];
  searchText = '';
  lang: string;

  constructor(
    private readonly store: Store,
    private readonly router: Router
  ) { }

  protected subscriptions$: Subject<boolean> = new Subject();

  ngOnInit() {
    this.store.dispatch(new GetMyUsersAction())

    this.myUsers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if(data){
          this.myUsers = data;
        }
      })
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
