import { Component, Input, OnInit } from '@angular/core';

@Component({
  selector: 'app-account-manager-avatar',
  templateUrl: './account-manager-avatar.component.html',
  styleUrls: ['./account-manager-avatar.component.scss']
})
export class AccountManagerAvatarComponent implements OnInit {
  @Input()
  firstName: string;

  @Input()
  lastName: string;

  constructor() { }

  ngOnInit() {
  }

  getAvatarName(){
    return `${this.firstName?.slice(0,1)} ${this.lastName?.slice(0,1)}`
  }
}
