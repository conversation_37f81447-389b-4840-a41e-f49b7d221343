import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'app-account-manager-role-label',
  templateUrl: './account-manager-role-label.component.html',
  styleUrls: ['./account-manager-role-label.component.scss']
})
export class AccountManagerRoleLabelComponent implements OnInit {
  @Input()
  label: string;

  @Input()
  removeBtn = false;

  @Input()
  variant: 'default' | 'warning' = 'default';

  @Output()
  onRemoveLabel: EventEmitter<void> = new EventEmitter<void>();

  constructor() { }

  ngOnInit() {
  }

  removeLabel(){
    this.onRemoveLabel.emit();
  }

}
