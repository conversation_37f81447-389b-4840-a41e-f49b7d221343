import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormGroup, FormControl, Validators } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, of, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { Country } from 'src/app/modules/definition/model/country.model';
import {
  GetCountryListAction,
  GetCityListAction,
} from 'src/app/modules/definition/state/definition/definition.actions';
import { DefinitionState } from 'src/app/modules/definition/state/definition/definition.state';
import { LogService } from 'src/app/shared/service/log.service';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';
import {
  getFormErrorMessage,
  isShowFormError,
  isShowFormErrorTouched,
  validateAllFormFields,
} from 'src/app/util/form-error.util';
import { CompanyService } from '../../../service/company.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';

@Component({
  selector: 'app-create-account',
  templateUrl: './create-account.component.html',
  styleUrls: ['./create-account.component.scss'],
  providers: [TranslatePipe],
})
export class CreateAccountComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isShowFormErrorTouched = isShowFormErrorTouched;
  formSendStatus: boolean = false;
  form: FormGroup = new FormGroup({
    CompanyName: new FormControl('', [Validators.required]),
    Phone: new FormControl(null, [Validators.required, Validators.minLength(7)]),
    CountryId: new FormControl(null, [Validators.required]),
    CityId: new FormControl(null, [Validators.required]),
    Description: new FormControl(null, [Validators.required]),
  });
  protected subscriptions$: Subject<boolean> = new Subject();
  loading: Observable<boolean> = of(false);
  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;
  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;
  @Select(DefinitionState.countryList) countryList$: Observable<Country[]>;
  @Select(DefinitionState.cityList) cityList$: Observable<Country[]>;
  cityList: Country[];
  constructor(
    private readonly store: Store,
    private readonly service: CompanyService,
    private readonly log: LogService,
    private readonly translate: TranslatePipe
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_add_account'),
      })
    );
    this.store.dispatch(new GetCountryListAction());
    this.cityList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.cityList = data;
      });
  }

  isActiveCountry(countryId: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.id === countryId);

    return country?.isActive;
  }

  onSelectCountry(id: string) {
    const selectedCountry = this.getCountryByCode(id);

    this.form.patchValue({ CityId: null });
    if (this.isActiveCountry(id)) {
      this.form.controls.CityId.setValidators([Validators.required]);
      this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.CityId.clearValidators();
      this.form.controls.CityId.updateValueAndValidity();
    }
  }

  getCountryByCode(id: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.id === id);
  }

  onSubmitForm() {
    if (this.form.valid) {
      const { value } = this.form;
      this.formSendStatus = false;
      this.loading = of(true);
      this.service
        .create({
          customerCompanyName: value.CompanyName,
          countryId: value.CountryId,
          cityId: value.CityId,
          Mobile: value.Phone,
          Description: value.Description,
        })
        .subscribe(
          () => {
            this.formSendStatus = true;
            this.loading = of(false);
            this.log.action('COMPANY_MODAL', 'COMPANY_ADDED', {
              customerCompanyName: value.CompanyName,
            }).subscribe();
          },
          (err) => {
            this.formSendStatus = false;
            this.loading = of(false);
          }
        );
    } else {
      validateAllFormFields(this.form);
    }
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal.toLowerCase();
      return data
        .filter(data => data.name.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name.toLowerCase().indexOf(search) > b.name.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name.toLowerCase().indexOf(search) < b.name.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
