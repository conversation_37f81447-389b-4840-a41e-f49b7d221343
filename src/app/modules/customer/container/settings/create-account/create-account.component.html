<div class="px-4 createAcount" *ngIf="!formSendStatus">
  <form (submit)="onSubmitForm()" [formGroup]="form">
    <div class="form-group mt-2">
      <input
        appInputMaxLength
        [name]="'CompanyName'"
        [placeholder]="'_company_name' | translate"
        class="form-control"
        formControlName="CompanyName"
        type="text"
        minlength="3"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        appInputMaxLength
        [name]="'Phone'"
        [placeholder]="'_phone' | translate"
        class="form-control"
        formControlName="Phone"
        (input)="onInputPhone($event)"
        type="tel"
        minlength="7"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Phone) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Phone) | translate }}
      </div>
    </div>
    <div class="form-group">
      <ng-select
        class="service-drp"
        [searchable]="false"
        [placeholder]="'_country' | translate"
        formControlName="CountryId"
        (ngModelChange)="onSelectCountry($event)"
        [dropdownPosition]="'bottom'"
      >
        <ng-option
          *ngFor="let country of countryList$ | async"
          [value]="country.id"
        >
          {{ country.name }}
        </ng-option>
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CountryId) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CountryId) | translate }}
      </div>
    </div>
    <div
      class="form-group"
      *ngIf="
        (!!form.controls['CountryId'].value ||
          (cityList$ | async)?.length > 0) &&
        isActiveCountry(form.controls['CountryId'].value)
      "
    >
      <ng-select
        class="service-drp"
        [searchable]="true"
        [placeholder]="'_city' | translate"
        formControlName="CityId"
        dropdownPosition="bottom"
        (keypress)="scrollTop()"
        #city
      >
        <ng-option
          *ngFor="let city of searchCity(cityList, city.searchTerm)"
          [value]="city.id"
        >
          {{ city.name }}
        </ng-option>
      </ng-select>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.CityId) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.CityId) | translate }}
      </div>
    </div>
    <div class="form-group">
      <textarea
        appInputMaxLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <input
      [value]="'_create' | translate"
      class="btn btn-gradient btn-block text-white shadow"
      [disabled]="!form.valid"
      [ngClass]="{ 'btn-secondary': !form.valid, 'btn-info': form.valid }"
      type="submit"
    />
  </form>
</div>

<app-success-modal
  *ngIf="formSendStatus"
  message="_successfully_get_register"
></app-success-modal>

<app-loader
  [show]="
    (loading | async) ||
    (countryListLoading$ | async) ||
    (cityListLoading$ | async)
  "
></app-loader>
