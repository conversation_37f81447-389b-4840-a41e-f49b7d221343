import { Location } from '@angular/common';
import { Component, On<PERSON>estroy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { FrameMessageEnum } from '../../../../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../../../../core/service/frame-message.service';
import { LogService } from '../../../../../shared/service/log.service';
import { CommonState } from '../../../../../shared/state/common/common.state';
import { CountryNameModel } from '../../../../definition/model/country.model';
import { GetCountryNameListAction } from '../../../../definition/state/definition/definition.actions';
import { DefinitionState } from '../../../../definition/state/definition/definition.state';
import { SanitizedCustomerModel } from '../../../model/sanitized-customer.model';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { UserState } from '../../../state/user/user.state';
import { takeUntil } from 'rxjs/operators';
import { UpdateLoadingAction } from '../../../../authentication/state/login/login.actions';

@Component({
  selector: 'app-language',
  templateUrl: './language.component.html',
  styleUrls: ['./language.component.scss'],
})
export class LanguageComponent implements OnInit, OnDestroy {
  @Select(DefinitionState.countryNameList)
  countryNames$: Observable<CountryNameModel[]>;
  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;
  @Select(CommonState.languageOtt)
  languageOtt$: Observable<string>;
  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;
  languages: string[] = [];
  loading: boolean = false;
  langLoading: boolean = true;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly log: LogService,
    private readonly trans: TranslateService,
    private readonly frameMEssage: FrameMessageService,
    private readonly location: Location
  ) { }

  ngOnInit(): void {
    this.trans.get('_language').subscribe((trans) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trans,
        })
      );
    });
    this.store.dispatch(new GetCountryNameListAction());

    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((currentCustomer) => {
        if (currentCustomer) {
          this.countryNames$.subscribe((res) => {
            if (res.length > 0) {
              const currentCountry = res.find(
                (c) => c.code === currentCustomer?.groupKey || c.code === (currentCustomer as any)?.countryCode
              );
              this.languages = currentCountry?.languages;
            }
          });
        } else {
          this.countryNames$.subscribe((res) => {
            if (res.length > 0) {
              const currentCountry = res.find(
                (c) => c.code === 'TR'
              );
              this.languages = currentCountry?.languages;
            }
          });
        }
      });

    this.countryListLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(loadingList => {
        this.loading = loadingList;
      });
  }

  onSelectLanguage(code: string) {
    this.loading = true;
    setTimeout(() => {
      this.loading = false;
    }, 1000 * 30);
    this.frameMEssage.sendMessage(FrameMessageEnum.changeLanguage, {
      lang: code,
    });

    // TODO - Bildirimden gelindiğinde hata oluyor incelenecek
    if (navigator.userAgent.includes('Android')) {
      history.go(1 - history.length);
      this.store.dispatch(new UpdateLoadingAction(true));
    }

    this.languageOtt$.subscribe((ott) => {
      if (ott) {
        this.location.replaceState(this.location.path(), `ott=${ott}`);
        location.reload();
      }
    });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
