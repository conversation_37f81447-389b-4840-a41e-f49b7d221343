<div class="container">
  <div class="px-2">
    <ng-select
      #type
      class="search-select"
      [dropdownPosition]="'bottom'"
      [placeholder]="'_select_address_type' | translate"
      [searchable]="false"
      [multiple]="false"
      [(ngModel)]="selectedAddressType"
      (ngModelChange)="onSelectAddressType($event)"
      (close)="type.blur()"
      [notFoundText]="'_no_items_found' | translate"
    >
      <ng-option *ngFor="let item of addressTypes" [value]="item">
        {{ '_address_type_' + item | translate}}
      </ng-option>
    </ng-select>
  </div>

  <ul class="address-list">
    <li
      class="address-list-item"
      *ngFor="let address of addresses"
    >
      <i class="icon icon-location mr-4"></i>
      <p>
        {{ address.adressInfo }}
      </p>
    </li>
  </ul>
</div>
