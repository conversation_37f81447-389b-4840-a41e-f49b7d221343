import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { Adresses, CustomerModel } from '../../../../../shared/models/customer.model';
import { CustomerDetailAction, HeaderStatusAction, } from '../../../state/customer/customer.actions';
import { CustomerState } from '../../../state/customer/customer.state';
import { UserState } from '../../../state/user/user.state';

@Component({
  selector: 'app-address',
  templateUrl: './address.component.html',
  styleUrls: ['./address.component.scss'],
})
export class AddressComponent implements OnInit {
  @Select(CustomerState.customer) customer$: Observable<CustomerModel>;
  customer: CustomerModel;
  selectedAddressType = 'XXDEFAULT';
  addresses: Adresses[];
  addressTypes: string[];

  constructor(
    private readonly store: Store,
    private readonly trans: TranslateService
  ) {}

  ngOnInit(): void {
    this.trans.get('_my_addresses').subscribe((trans) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trans,
        })
      );
    });

    const currentCustomer = this.store.selectSnapshot(
      UserState.currentCustomer
    );

    if (currentCustomer && currentCustomer.customer) {
      this.store.dispatch(
        new CustomerDetailAction(currentCustomer.customer?.customerNumber)
      );
    }

    this.customer$.subscribe((res) => {
      if (res) {
        this.customer = res;
        this.addressTypes = this.findAddressTypes(res.details?.adresses);
        this.determineDefaultAddress();
        this.filterAddresses(res.details?.adresses);
      }

    });
  }

  onSelectAddressType($event: any) {
    this.filterAddresses(this.customer?.details?.adresses || []);
  }

  filterAddresses(addresses: Adresses[]) {
    // console.log(addresses, this.selectedAddressType);
    this.addresses = addresses.filter(i => i.adressKind === this.selectedAddressType);
  }

  findAddressTypes(addresses: Adresses[]) {
    return Array.from(new Set(addresses.map(i => i.adressKind)));
  }

  private determineDefaultAddress() {
    if (this.addressTypes.length && this.addressTypes.indexOf(this.selectedAddressType) === -1) {
      this.selectedAddressType = this.addressTypes?.[0];
    }
  }
}
