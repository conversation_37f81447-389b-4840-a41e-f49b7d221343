import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { getFormErrorMessage, isShowFormError, validateAllFormFields, } from 'src/app/util/form-error.util';
import { LogService } from '../../../../../shared/service/log.service';
import { CommonStoreAction } from '../../../../../shared/state/common/common.actions';
import { CommonState } from '../../../../../shared/state/common/common.state';
import { PasswordPolicies } from '../../../response/settings.response';
import { SettingsService } from '../../../../../shared/service/settings.service';
import { UserService } from '../../../service/user.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { LogoutProcessAction } from '../../../../authentication/state/login/login.actions';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';
import { UserState } from '../../../state/user/user.state';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-password-change',
  templateUrl: './password-change.component.html',
  styleUrls: ['./password-change.component.scss'],
})
export class PasswordChangeComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  loading = false;
  formSendStatus: boolean = false;
  confirmStatus: boolean = false;
  form: FormGroup = new FormGroup({
    oldPassword: new FormControl('', [Validators.required]),
    newPassword: new FormControl('', [Validators.required]),
    confirmPassword: new FormControl('', [Validators.required]),
  });
  passwordPolicies: PasswordPolicies[];
  passwordType: boolean;
  passwordConfirmationType: boolean;
  oldPasswordType: boolean;

  @Select(UserState.isBorusanUser)
  isBorusanUser$: Observable<boolean>;

  constructor(
    private readonly store: Store,
    private readonly service: UserService,
    private readonly settingsService: SettingsService,
    private readonly router: Router,
    private readonly log: LogService,
    private readonly trans: TranslateService
  ) {}

  ngOnInit(): void {
    this.store.dispatch(new GetBasisSettingsAction());
    const mustChangePassword = this.store.selectSnapshot(
      CommonState.mustChangePassword
    );
    // console.log(lcode'mustChangePassword', mustChangePassword);

    if (mustChangePassword) {
      this.disableBack();
    }
    this.trans.get('_password_change').subscribe((trans) => {

      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: !mustChangePassword,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trans,
        })
      );
    });
    this.settingsService.getBasic().subscribe((res) => {
      this.passwordPolicies = res.passwordPolicies;
    });
  }

  togglePasswordType() {
    this.passwordType = !this.passwordType;
  }
  togglePasswordConfirmationType() {
    this.passwordConfirmationType = !this.passwordConfirmationType;
  }

  toggleNewPasswordType() {
    this.oldPasswordType = !this.oldPasswordType;
  }

  isPassValid(pol: PasswordPolicies) {
    return new RegExp(pol.regex).test(this.form.controls.newPassword.value);
  }

  get canSave() {
    let val = true;
    val = this.passwordPolicies?.reduce(
      (p, c) => p && this.isPassValid(c),
      val
    );
    const { valid } = this.form;
    val = val && valid;
    return val;
  }

  onSubmitForm() {
    if (this.form.valid) {


      const { value } = this.form;
      if (value.newPassword === value.confirmPassword) {
        this.confirmStatus = false;
        this.formSendStatus = false;

        this.loading = true;
        this.service
          .passwordReset({
            confirmPassword: value.confirmPassword,
            newPassword: value.newPassword,
            oldPassword: value.oldPassword,
          })
          .subscribe(
            () => {
              this.formSendStatus = true;
              this.loading = false;
              this.store.dispatch(
                new CommonStoreAction({
                  mustChangePassword: false,
                })
              );
              this.log.action('SETTINGS', 'PASSWORD_CHANGED').subscribe();
              this.disableBack(() => {
                this.logOut();
              });

              setTimeout(() => {
                // this.router.navigate(['/'], { replaceUrl: true });
                this.logOut();

              }, 4000);
            },
            (err) => {
              this.formSendStatus = false;
              this.loading = false;
            }
          );
      } else {
        this.confirmStatus = true;
      }
    } else {
      validateAllFormFields(this.form);
    }
  }

  logOut() {
    this.store.dispatch(new LogoutProcessAction(false));
  }

  backHome() {
    history.back();
  }

  private disableBack(onBack = null) {
    history.pushState(null, document.title);
    window.addEventListener('popstate', () => {
      history.pushState(null, document.title);
      if (onBack) {
        onBack();
      }
    });
  }
}
