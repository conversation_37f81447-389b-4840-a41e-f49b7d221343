<div class="px-4 pb-5" *ngIf="!formSendStatus && !(isBorusanUser$ | async)">
  <form (submit)="onSubmitForm()" [formGroup]="form">
    <div class="form-group">
      <div class="input-group">
        <input
          appInputMaxLength
          [name]="'oldPassword'"
          [type]="oldPasswordType ? 'text' : 'password'"
          [placeholder]="'_old_password' | translate"
          class="form-control form-control"
          formControlName="oldPassword"
          type="password"
        />

        <div class="input-group-append">
          <span class="input-group-text">
            <i
              class="icon font-size-18px"
              [ngClass]="{
                'icon-eye': oldPasswordType,
                'icon-eye-slash': !oldPasswordType
              }"
              (click)="toggleNewPasswordType()"
            ></i>
          </span>
        </div>
      </div>

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.oldPassword) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.oldPassword) | translate }}
      </div>
    </div>
    <div class="form-group">
      <div class="input-group">
        <input
          appInputMaxLength
          [name]="'Password'"
          [type]="passwordType ? 'text' : 'password'"
          [placeholder]="'_new_password' | translate"
          class="form-control form-control"
          formControlName="newPassword"
          type="password"
          autocomplete="off"
        />

        <div class="input-group-append">
          <span class="input-group-text">
            <i
              class="icon font-size-18px"
              [ngClass]="{
                'icon-eye': passwordType,
                'icon-eye-slash': !passwordType
              }"
              (click)="togglePasswordType()"
            ></i>
          </span>
        </div>
      </div>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.newPassword) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.newPassword) | translate }}
      </div>
    </div>
    <div class="password-condition-area">
      <ul class="password-conditions">
        <li
          class="password-condition"
          *ngFor="let pol of passwordPolicies"
          [class.text-danger]="
            !isPassValid(pol) && form.controls.newPassword.dirty
          "
          [class.text-success]="
            isPassValid(pol) && form.controls.newPassword.dirty
          "
        >
          {{ pol.description }}
        </li>
      </ul>
    </div>
    <div class="form-group">
      <div class="input-group">
        <input
          appInputMaxLength
          [name]="'Password'"
          [type]="passwordConfirmationType ? 'text' : 'password'"
          [placeholder]="'_confirm_password' | translate"
          class="form-control form-control"
          formControlName="confirmPassword"
          type="password"
          autocomplete="off"
        />

        <div class="input-group-append">
          <span class="input-group-text">
            <i
              class="icon font-size-18px"
              [ngClass]="{
                'icon-eye': passwordConfirmationType,
                'icon-eye-slash': !passwordConfirmationType
              }"
              (click)="togglePasswordConfirmationType()"
            ></i>
          </span>
        </div>
      </div>

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.confirmPassword) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.confirmPassword) | translate }}
      </div>
      <div
        [ngClass]="{ 'd-block': confirmStatus }"
        class="invalid-feedback pl-3"
      >
        {{ "_not_equal_password" | translate }}
      </div>
    </div>

    <input
      [value]="'_save' | translate"
      class="btn btn-gradient btn-block text-white shadow"
      [disabled]="!canSave"
      [ngClass]="{ 'btn-secondary': !canSave, 'btn-info': canSave }"
      type="submit"
    />
    <div class="mt-2 font-size-13px">
      * {{ "_after_password_change_text" | translate }}
    </div>
  </form>
</div>

<!-- ? Is Borusan User -->
<div class="text-center" *ngIf="(isBorusanUser$ | async)">
  <div class="empty-content d-flex align-items-center flex-column justify-content-center">
    <div class="empty-content-logo mb-2">
      <i class="icon icon-x-bold text-warning"></i>
    </div>
    <div class="empty-content-message text-center ">
      {{ '_unauthorised' | translate }}
    </div>
    <button class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="backHome()">
      {{ "_close" | translate }}
    </button>
  </div>
</div>

<app-success-modal
  *ngIf="formSendStatus"
  message="_successfully_change_password"
  detail="_login_required_with_new_password"
></app-success-modal>

<!-- <app-basic-modal *ngIf="formErrorStatus" [(status)]="formErrorStatus">
  <div class="after-form-send">
    <div class="after-form-send-content text-center px-4">
      <img [src]="warningIcon" />
      <div
        class="h3 my-4 text-center"
        [ngClass]="{ 'service-error': !!formErrorMessage }"
      >
        {{ formErrorMessage || ("_some_error_form" | translate) }}
      </div>
    </div>
  </div>
</app-basic-modal> -->

<app-loader [show]="loading"></app-loader>
