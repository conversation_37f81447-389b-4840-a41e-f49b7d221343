<div *ngIf="!verifySuccessModal && !verifySendLinkModal" class="px-4 pt-4 pb-5">
  <div class="card">
    <div class="card-body">
      <ng-container *ngIf="!codeVerifyModal else showVerifyCode">
        <form [formGroup]="mobileVerifyForm">
          <div class="form-group d-flex flex-row">
            <div class="col-4 p-0 mr-2">
              <ng-select [searchable]="false"
                         [placeholder]="'+' + selectedPhoneCode"
                         (change)="getSetCountryPhoneCode($event)"
                         formControlName="phoneCode">
                <ng-option *ngFor="let country of countryList" [value]="country.phoneCode">
                  +{{ country.phoneCode }}
                </ng-option>
              </ng-select>
            </div>
            <div>
              <input appInputMaxLength [name]="'Phone'" [placeholder]="'_phone' | translate" class="form-control form-control"
                     formControlName="phoneNumber" (input)="onInputPhone($event)" minlength="7" type="tel" />
            </div>
          </div>
          <div [ngClass]="{
            'd-block': isShowError(mobileVerifyForm.controls.phoneNumber || mobileVerifyForm.controls.countryId)
          }" class="invalid-feedback pl-3">
            {{ getFormErrorMessage(mobileVerifyForm.controls.phoneNumber || mobileVerifyForm.controls.countryId) | translate }}
          </div>
          <div class="form-group">
            <div class="mb-2">
              {{ '_mobile_verify_code_type' | translate }}
            </div>
            <div class="form-check btn-group w-100 row radio-button pl-3">
              <label class="col-4 btn btn-sm btn-outline-success radio-btn-special" for="verifyWithCodeTrue" [ngClass]="{
                selected: mobileVerifyForm.value.verifyWithCode
              }">{{ "_with_code" | translate }}</label>
              <input type="radio" id="verifyWithCodeTrue" name="verifyWithCode" class="btn-check d-none" formControlName="verifyWithCode"
                     [value]="true" />
              <input type="radio" id="verifyWithCodeFalse" name="verifyWithCode" class="btn-check d-none" formControlName="verifyWithCode"
                     [value]="false" />
              <label class="col-4 btn btn-sm btn-outline-secondary radio-btn-special" for="verifyWithCodeFalse" [ngClass]="{
                  selected: !mobileVerifyForm.value.verifyWithCode && mobileVerifyForm.value.verifyWithCode !== null
                }">{{ "_with_link" | translate }}</label>
            </div>
            <div [ngClass]="{
            'd-block': isShowError(mobileVerifyForm.controls.verifyWithCode)
          }" class="invalid-feedback pl-3">
              {{ getFormErrorMessage(mobileVerifyForm.controls.verifyWithCode) | translate }}
            </div>

            <div class="d-flex flex-row justify-content-center mt-4">
              <div class="btn btn-warning text-white" (click)="verifyStart()">
                {{ "_send" | translate }}
              </div>
            </div>
          </div>
        </form>
      </ng-container>
      <ng-template #showVerifyCode>
        <div class="d-flex flex-column mb-4">
          <b class="h4 text-center mb-2">
            +{{ selectedPhoneCode }}{{ mobileVerifyForm?.value?.phoneNumber }}
          </b>
          <div class="mb-2">
            {{ '_mobile_verify_code_text' | translate }}
          </div>
          <form (submit)="verifyConfirmationCodeSend()" [formGroup]="mobileVerifySendForm">
            <div class="form-group">
              <input appInputMaxLength [name]="'ConfirmationCode'" [placeholder]="'_mobile_verify_code_label' | translate"
                     class="form-control form-control" formControlName="confirmationCode" (input)="onInputCode($event)" minlength="6" maxlength="6"
                     type="tel" autofocus />
              <div [ngClass]="{'d-block': isShowFormErrorTouched(mobileVerifySendForm.controls.confirmationCode)}" class="invalid-feedback pl-3">
                {{ getFormErrorMessage(mobileVerifySendForm.controls.confirmationCode) | translate }}
              </div>
            </div>
            <input [value]="'_send' | translate" class="btn btn-gradient btn-block text-white shadow" [disabled]="!mobileVerifySendForm.valid"
                   [ngClass]="{
          'btn-secondary': !mobileVerifySendForm.valid,
          'btn-info': mobileVerifySendForm.valid
        }" type="submit" />
          </form>
          <hr class="m-4">
          <div class="d-flex flex-row justify-content-center">
            <div *ngIf="showReSendButton else showTimer" class="btn btn-secondary btn-block text-white shadow" (click)="sendVerifyCode()">
              {{ "_resend_verify_code" | translate }}
            </div>
            <ng-template #showTimer>
              <div class="btn btn-secondary btn-block text-white shadow">
                {{ reSendTime }}
              </div>
            </ng-template>
          </div>
        </div>
      </ng-template>
    </div>
  </div>
</div>

<app-success-modal *ngIf="verifySuccessModal" message="_mobile_verify_success_message">
  <div class="btn btn-warning btn-gradient btn-block text-white shadow" (click)="back()">
    {{ "_close" | translate }}
  </div>
</app-success-modal>

<app-success-modal *ngIf="verifySendLinkModal" message="_mobile_verify_link_send_message">
  <div class="btn btn-warning btn-gradient btn-block text-white shadow" (click)="back()">
    {{ "_close" | translate }}
  </div>
</app-success-modal>

<app-loader [show]="formLoading$ | async"></app-loader>
