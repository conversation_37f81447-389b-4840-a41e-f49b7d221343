import {Component, OnInit} from '@angular/core';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {TranslateService} from '@ngx-translate/core';
import {Navigate} from '@ngxs/router-plugin';
import {Select, Store} from '@ngxs/store';
import {Observable, Subject} from 'rxjs';
import {takeUntil} from 'rxjs/operators';
import {Country} from 'src/app/modules/definition/model/country.model';
import {GetAllCountryListAction} from 'src/app/modules/definition/state/definition/definition.actions';
import {DefinitionState} from 'src/app/modules/definition/state/definition/definition.state';
import {getFormErrorMessage, isShowFormError, isShowFormErrorTouched, validateAllFormFields} from 'src/app/util/form-error.util';
import {MobileVerifySendModel, MobileVerifyStartModel} from '../../../model/user.model';
import {HeaderStatusAction} from '../../../state/customer/customer.actions';
import {MobileVerifyClearAction, MobileVerifySendAction, MobileVerifyStartAction} from '../../../state/form/form.actions';
import {FormState} from '../../../state/form/form.state';
import {ModalService} from '../../../../../shared/service/modal.service';

@Component({
  selector: 'app-mobile-verify',
  templateUrl: './mobile-verify.component.html',
  styleUrls: ['./mobile-verify.component.scss']
})
export class MobileVerifyComponent implements OnInit {

  constructor(
    private readonly trans: TranslateService,
    private readonly store: Store,
    private readonly modalService: ModalService,
  ) { }

  mobileVerifyForm: FormGroup = new FormGroup({
    phoneCode: new FormControl(null),
    countryId: new FormControl(null, [Validators.required]),
    phoneNumber: new FormControl(null, [Validators.required, Validators.minLength(7)]),
    verifyWithCode: new FormControl(true, [Validators.required]),
  });

  mobileVerifySendForm: FormGroup = new FormGroup({
    confirmationCode: new FormControl(null, [Validators.required, Validators.minLength(6)]),
  });

  @Select(FormState.mobileVerifyStart)
  mobileVerifyStart$: Observable<any>;
  @Select(FormState.mobileVerifySend)
  mobileVerifySend$: Observable<any>;
  @Select(FormState.formLoading)
  formLoading$: Observable<boolean>;
  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;
  countryList: Country[];
  selectedCountry: Country;
  selectedPhoneCode: string;

  codeVerifyModal = false;
  verifySuccessModal = false;
  verifySendLinkModal = false;
  verifyCodeSended = false;
  verifyWithCode = false;

  showReSendButton = false;
  reSendTime = 60;
  interval: any;

  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  isShowFormErrorTouched = isShowFormErrorTouched;

  protected subscriptions$: Subject<boolean> = new Subject();
  ngOnInit() {
    this.setHeader();
    this.getCountryList();
    this.mobileVerifyForm.statusChanges.subscribe(() => {
      this.verifyCodeSended = false;
    });
  }

  setHeader() {
    this.trans.get('_mobile_no_verify').subscribe((trans) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: false,
          closeButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trans,
        })
      );
    });
  }

  getCountryList() {
    this.store.dispatch(new GetAllCountryListAction());
    this.countryList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data?.length) {
          this.countryList = data?.filter(x => x.phoneCode !== null);
          this.selectedCountry = data[0];
          this.selectedPhoneCode = data[0].phoneCode;
          this.getSetCountryPhoneCode(this.selectedPhoneCode);
        }
      });
  }

  getSetCountryPhoneCode(e: any) {
    this.selectedPhoneCode = e;
    this.selectedCountry = this.countryList.find(x => x.phoneCode === this.selectedPhoneCode);
    this.mobileVerifyForm.patchValue({
      phoneCode: this.selectedCountry.phoneCode,
      countryId: this.selectedCountry.id,
    });
  }

  verifyStart() {
    if (this.mobileVerifyForm.valid) {
      this.verifyWithCode = this.mobileVerifyForm.value.verifyWithCode;
      this.sendVerifyCode();
    } else {
      validateAllFormFields(this.mobileVerifyForm);
    }
  }

  sendVerifyCode() {
    const mobileData: MobileVerifyStartModel = {
      countryId: this.mobileVerifyForm.value.countryId,
      phoneNumber: this.mobileVerifyForm.value.phoneNumber,
      verifyWithCode: this.verifyWithCode
    };
    this.pauseTimer();
    this.store.dispatch(new MobileVerifyStartAction(mobileData));
    this.mobileVerifyStart$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data && !data?.errorMessage) {
          if (this.verifyWithCode) {
            this.startTimer();
            this.codeVerifyModal = true;
            this.verifyCodeSended = true;
          } else {
            this.verifySendLinkModal = true;
          }
          return;
        }
        if (data?.errorMessage) {
          this.modalService.errorModal({message: data?.errorMessage});
        }
      }, () => {
        this.codeVerifyModal = false;
        this.store.dispatch(new MobileVerifyClearAction());
      });
  }

  startTimer() {
    this.reSendTime = 60;
    this.showReSendButton = false;
    clearInterval(this.interval);
    this.interval = setInterval(() => {
      this.intervalInit();
    }, 1000);
  }

  intervalInit() {
    this.reSendTime--;

    if (this.reSendTime === 0) {
      this.showReSendButton = true;
      this.pauseTimer();
    }
  }

  pauseTimer() {
    clearInterval(this.interval);
  }

  verifyConfirmationCodeSend() {
    if (this.mobileVerifySendForm.valid) {
      console.log('form value::: ', this.mobileVerifySendForm.value);
      const verifyData: MobileVerifySendModel = {
        confirmationCode: this.mobileVerifySendForm.value.confirmationCode
      };
      this.store.dispatch(new MobileVerifySendAction(verifyData));
      this.mobileVerifySend$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(data => {
          if (data) {
            this.codeVerifyModal = false;
            this.verifySuccessModal = true;
            this.store.dispatch(new MobileVerifyClearAction());
          }
        });
    } else {
      validateAllFormFields(this.mobileVerifySendForm);
    }
  }

  verifyModalChange() {
    this.mobileVerifySendForm.setValue({
      confirmationCode: null,
    });
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  onInputCode(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
    e.target.value = e.target.value.slice(0, 6);
    this.mobileVerifySendForm.setValue({
      confirmationCode: e.target.value
    });
  }

  back() {
    this.store.dispatch(new MobileVerifyClearAction());
    this.store.dispatch(new Navigate(['/']));
  }
}
