import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';
import { AddressComponent } from './address/address.component';
import { CreateAccountComponent } from './create-account/create-account.component';
import { LanguageComponent } from './language/language.component';
import { PasswordChangeComponent } from './password-change/password-change.component';
import { SettingsMenuComponent } from './settings-menu/settings-menu.component';
import { SettingsComponent } from './settings.component';
import { AppSupportComponent } from './app-support/app-support.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { UserManagementDetailComponent } from './user-management-detail/user-management-detail.component';
import { UserManagementAllDetailComponent } from './user-management-all-detail/user-management-all-detail.component';
import { DeleteAccountComponent } from './delete-account/delete-account.component';
import { MobileVerifyComponent } from './mobile-verify/mobile-verify.component';
import { AccountManagerLayoutComponent } from './account-manager/account-manager-layout/account-manager-layout.component';
import { AccountManagerCustomersComponent } from './account-manager/account-manager-customers/account-manager-customers.component';
import { AccountManagerCustomerComponent } from './account-manager/account-manager-customer/account-manager-customer.component';
import {
  AccountManagerAwaitingApprovalsComponent
} from './account-manager/account-manager-awaiting-approvals/account-manager-awaiting-approvals.component';
import { AccountManagerQrComponent } from './account-manager/account-manager-qr/account-manager-qr.component';
import { MyAgreementsComponent } from './my-agreements/my-agreements.component';
import { MyAgreementsApprovedComponent } from './my-agreements/my-agreements-approved/my-agreements-approved.component';
import {
  MyAgreementsAwaitingApprovalsComponent
} from './my-agreements/my-agreements-awaiting-approvals/my-agreements-awaiting-approvals.component';

const routes: Routes = [
  {
    path: '',
    component: SettingsComponent,

    canActivate: [PermissionGuard],
    children: [
      { path: 'menu', component: SettingsMenuComponent },
      { path: 'passwordchange', component: PasswordChangeComponent },
      { path: 'createaccount', component: CreateAccountComponent },
      { path: 'language', component: LanguageComponent },
      { path: 'address', component: AddressComponent },
      { path: 'app-support', component: AppSupportComponent },
      { path: 'usermanagement', component: UserManagementComponent },
      { path: 'usermanagement/:userId', component: UserManagementDetailComponent },
      { path: 'usermanagement/:userId/all', component: UserManagementAllDetailComponent },
      { path: 'deleteaccount', component: DeleteAccountComponent },
      { path: 'mobile-verify', component: MobileVerifyComponent },
      {
        path: 'account-manager', component: AccountManagerLayoutComponent, children: [
          { path: 'customers', component: AccountManagerCustomersComponent },
          { path: 'customer', component: AccountManagerCustomerComponent },
          { path: 'awaiting-approvals', component: AccountManagerAwaitingApprovalsComponent },
          { path: 'share-qr', component: AccountManagerQrComponent }
        ]
      },
      {
        path: 'my-agreements', component: MyAgreementsComponent, children: [
          { path: 'approved', component: MyAgreementsApprovedComponent },
          { path: 'awaiting-approvals', component: MyAgreementsAwaitingApprovalsComponent },
        ]
      },
      {
        path: 'click-away',
        loadChildren: () => import('../../../click-away/click-away.module').then(m => m.ClickAwayModule)
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class SettingsRoutingModule {}
