import { Component, OnInit } from '@angular/core';
import {LogService} from '../../../../shared/service/log.service';
import {ActivatedRoute, Router} from '@angular/router';
import {IncomingMessageEnum} from '../../../../core/enum/incoming-message.enum';
import {IncomingMessageService} from '../../../../core/service/incoming-message.service';

@Component({
  selector: 'app-incoming-repeater',
  template: `<app-loader [show]="loading"></app-loader>`,
  styles: [``]
})
export class IncomingRepeaterComponent implements OnInit {

  loading = true;
  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly log: LogService,
    private readonly incomingMessageService: IncomingMessageService,
    ) { }

  ngOnInit(): void {
    const search = location.search;
    const url = location.pathname.split('/incoming-repeater/')[1];

    try {
      const message = {
        function: url,
        data: this.router?.parseUrl(search)?.queryParams
      };
      console.log('message: ', message);

      // @ts-ignore
      if (message?.function && message.function.indexOf(IncomingMessageEnum)) {
        this.log.action('DEEPLINK', 'REDIRECT-INCOMING', {
          function: message.function,
          data: message?.data
        });
        this.incomingMessageService[message?.function](message?.data);
      }
    } catch (err) {}
    this.router.navigate(['dashboard']).then();
    this.loading = false;
  }

}
