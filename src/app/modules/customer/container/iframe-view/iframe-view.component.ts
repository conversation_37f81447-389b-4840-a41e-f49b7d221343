import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostL<PERSON>ener, OnDestroy, OnInit, ViewChild, } from '@angular/core';
import { IframeState, IframeStateModel } from '../../state/iframe/iframe.state';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import { IframeAction, IframeCloseHeaderUrlAction, PostMessageAction, } from '../../state/iframe/iframe.actions';
import { WindowScrollingService } from 'src/app/core/service/window-scrolling.service';
import { ActivatedRoute, Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { IncomingMessageEnum } from '../../../../core/enum/incoming-message.enum';
import { CommonState } from '../../../../shared/state/common/common.state';
import { NotificationState } from '../../../notification/state/notification/notification.state';
import { UserState } from '../../state/user/user.state';
import { UpdateLoadingAction } from '../../../authentication/state/login/login.actions';
import jsonSafeParse from '../../../../util/json-safe-parse';
import { SettingsState } from '../../../../shared/state/settings/settings.state';
import {HeaderStatusAction} from '../../state/customer/customer.actions';
import { LoginState } from '../../../authentication/state/login/login.state';

declare global {
  interface Window {
    Borusan: any;
  }
}

@Component({
  selector: 'app-iframe-view',
  templateUrl: './iframe-view.component.html',
  styleUrls: ['./iframe-view.component.scss'],
})
export class IframeViewComponent implements OnInit, OnDestroy {
  @ViewChild('moduleIframe') moduleIframe: ElementRef;

  protected subscriptions$: Subject<boolean> = new Subject();

  @Select(IframeState.getState)
  iframe$: Observable<IframeStateModel>;
  @Select(IframeState.postMessage)
  postMessage$: Observable<IframeStateModel>;

  iframe: IframeStateModel;
  private loadingTimout: any;

  constructor(
    private readonly store: Store,
    private readonly frameMessageService: FrameMessageService,
    private readonly scrollingService: WindowScrollingService,
    private readonly router: Router,
    route: ActivatedRoute
  ) {
    // this.router.routeReuseStrategy.shouldReuseRoute = () => {
    //   return false;
    // };
    route.params.subscribe(() => {
      this.moduleChange();
    });
  }

  ngOnInit(): void {

    this.iframe$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((iframe) => {
        this.iframe = iframe;
        // console.log('iframe', iframe);
        if (iframe.active && !this.loadingTimout) {
          this.loadingTimout = setTimeout(() => this.endLoading(), 20000);
        }
      });
    this.postMessage$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((res) => {
        if (this.iframe && this.iframe.active && this.moduleIframe) {
          // console.log('SENDING MESSAGE to IFRAME');
          this.moduleIframe.nativeElement.contentWindow.postMessage(JSON.stringify({ ...res }), '*');
        }
      });

    this.scrollingService.disable();
  }

  ngOnDestroy(): void {
    this.store.dispatch([
      new IframeCloseHeaderUrlAction(),
      new HeaderStatusAction({closeModal: false})
    ]);
    this.scrollingService.enable();

    this.endLoading();
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

  @HostListener('window:message', ['$event'])
  onMessage(e) {
    console.log('MAINUI received message from iframe', e.data);
    // this.frameMessageService.emitMessage(e.data);
    this.frameMessageService.handleMessageFromIframe(e.data);
    if (jsonSafeParse(e.data)?.function === 'moduleStarted') {
      this.sendInitMessages();
    }
  }

  protected moduleChange() {
    const iframeAction = this.router.getCurrentNavigation().extras?.state?.iframeAction;

    if (iframeAction) {
      const withOtt = typeof iframeAction.withOtt !== 'undefined' ? iframeAction.withOtt : true;
      this.store.dispatch(new IframeAction(iframeAction, withOtt));
      return;
    }

    const module = this.router.getCurrentNavigation().extras?.state?.module;

    if (!module) {
      return;
    }

    this.store.dispatch(
      new IframeAction({
        url: module.url,
        active: true,
        closeButton: true,
        pageTitle: module.name,
      }, true)
    );
  }

  onLoadFrame() {
    // console.log('on load FRAME', this.moduleIframe?.nativeElement?.src);
    // it calls multiple times; about:blank
    if (this.moduleIframe?.nativeElement?.src?.startsWith('http')) {
      this.endLoading();
      // this.sendInitMessages();
    }
  }

  private endLoading() {
    if (this.loadingTimout) {
      clearTimeout(this.loadingTimout);
      this.loadingTimout = null;
    }
    this.store.dispatch(new UpdateLoadingAction(false));
  }

  private sendInitMessages() {
    // console.log('SENT Mobile info ',this.moduleIframe?.nativeElement?.contentWindow.postMessage);

    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.mobileInfo, {
      version: this.store.selectSnapshot(CommonState.version),
      timezone: this.store.selectSnapshot(CommonState.timezoneId),
      userAgent: this.store.selectSnapshot(CommonState.userAgent),
      trackerUser: this.store.selectSnapshot(CommonState.trackerUser),
      firebaseToken: this.store.selectSnapshot(NotificationState.deviceToken),
      currentCustomer: this.store.selectSnapshot(UserState.currentCustomer),
      loginType: this.store.selectSnapshot(LoginState.loginType),
    }));

    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.appStorage,
      this.store.selectSnapshot(CommonState.appStorage)
    ));
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.systemFeatures,
      this.store.selectSnapshot(SettingsState.systemFeatures)
    ));
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.borusanBlockedAction,
      this.store.selectSnapshot(SettingsState.borusanBlockedActions)
    ));
  }
}
