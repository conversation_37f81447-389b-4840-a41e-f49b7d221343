<div *ngIf="iframe && iframe.active" class="app-iframe-view" [ngClass]="{'show':!iframe.loading}">
  <div class="iframe-loader" [ngClass]="{'show' :iframe.loading}">
    <!--<Loader />-->
  </div>
  <iframe #moduleIframe id="moduleIframe"
    *ngIf="iframe.url"
    [ngClass]="{ 'iframe-content' :iframe.loading, 'iframe-loader show':!iframe.loading}"
    [src]="iframe.url"
      (load)="onLoadFrame()"
  ></iframe>

</div>
