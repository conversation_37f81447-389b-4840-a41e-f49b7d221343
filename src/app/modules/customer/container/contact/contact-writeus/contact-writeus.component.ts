import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslatePipe } from '@ngx-translate/core';
import { Store } from '@ngxs/store';
import {
  getFormErrorMessage,
  isShowFormError,
  validateAllFormFields,
} from 'src/app/util/form-error.util';
import { ContactService } from '../../../service/contact.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { UserState } from '../../../state/user/user.state';
import { LogService } from '../../../../../shared/service/log.service';
import { GetBasisSettingsAction } from 'src/app/shared/state/settings/settings.actions';
import { SettingsState } from '../../../../../shared/state/settings/settings.state';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { FileModel, FormType } from 'src/app/export/file-upload/model/file.model';
import uuidv4 from 'src/app/util/uuidv4';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { PermissionEnum } from 'src/app/modules/definition/enum/permission.enum';

@Component({
  selector: 'app-contact-writeus',
  templateUrl: './contact-writeus.component.html',
  styleUrls: ['./contact-writeus.component.scss'],
  providers: [TranslatePipe],
})
export class ContactWriteusComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  // isShowFormErrorTouched = isShowFormErrorTouched;
  loading = false;
  formSendStatus = false;
  FormType = FormType;
  attachId = uuidv4();
  form: FormGroup = new FormGroup({
    PhoneNumber: new FormControl(null, [Validators.required, Validators.minLength(7)]),
    Description: new FormControl(null, [Validators.required]),
    Attachments: new FormControl([]),
    AttachmentIdList: new FormControl([]),
  });

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  constructor(
    private readonly store: Store,
    private readonly translate: TranslatePipe,
    private readonly service: ContactService,
    private readonly log: LogService,
    private readonly frameMessageService: FrameMessageService,
  ) {}

  ngOnInit(): void {
    this.frameMessageService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);
    this.store.dispatch(new GetBasisSettingsAction());
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translate.transform('_write_us'),
      })
    );
    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  onSubmitForm() {
    if (this.form.valid) {
      const { value } = this.form;
      this.formSendStatus = false;
      this.loading = true;
      const current = this.store.selectSnapshot(UserState.currentCustomer);
      // const mobile = this.store.selectSnapshot(UserState.mobile);
      const email = this.store.selectSnapshot(UserState.email);
      const userBasic = this.store.selectSnapshot(UserState.basics);
      this.log.action('WRITE_US', 'SEND_FORM').subscribe();

      this.service
        .callrequest({
          PhoneNumber: value.PhoneNumber,
          Description: value.Description,
          Name: userBasic?.firstName + ' ' + userBasic?.lastName,
          Email: email.toLowerCase(),
          CountryCode: current?.groupKey ? current?.groupKey : 'TR',
          CompanyName: current?.name,
          CompanyId: current?.publicMenuHeaderCompany,
          // CompanyPhoneNumber: mobile,
          AttachmentIdList: this.form.value.Attachments.map((item) => item.id),
        })
        .subscribe(
          (val) => {
            if (val.code === 0) {
              this.formSendStatus = true;
            }
            this.loading = false;
          },
          () => {
            this.formSendStatus = false;
            this.loading = false;
          }
        );
    } else {
      validateAllFormFields(this.form);
    }
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  onDeleteAttachmentFile(file: FileModel) {
    this.form.patchValue({
      Attachments: this.form.value.Attachments.filter(
        (f: FileModel) => !(f.id === file.id)
      ),
    });
  }
}
