<div class="px-4 pb-5 write-us" *ngIf="!formSendStatus">
  <app-info-box [title]="'_info' | translate">
    {{'_write_us_form_info' | translate}}
  </app-info-box>
  <form (submit)="onSubmitForm()" [formGroup]="form">
    <div class="form-group">
      <input
        appInputMaxLength
        [name]="'Phone'"
        [placeholder]="'_phone' | translate"
        class="form-control form-control"
        formControlName="PhoneNumber"
        (input)="onInputPhone($event)"
        minlength="7"
        type="tel"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.PhoneNumber) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.PhoneNumber) | translate }}
      </div>
    </div>
    <div class="form-group">
      <textarea
        appInputMaxLength
        [name]="'Description'"
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control"
        formControlName="Description"
        minlength="3"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description) | translate }}
      </div>
    </div>
    <div class="form-group">
      <app-file-upload
        formControlName="Attachments"
        [id]="attachId"
        [formType]="FormType.CallRequest"
      ></app-file-upload>
    </div>
    <div class="mb-3">
      <app-file-upload-preview
        (deleteFile)="onDeleteAttachmentFile($event)"
        [deleteButtonStatus]="true"
        [files]="form.get('Attachments').value"
      ></app-file-upload-preview>
    </div>
    <input
      [value]="'_send' | translate"
      class="btn btn-gradient btn-block text-white shadow"
      [disabled]="!form.valid || (borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitWriteUsForm) >= 0)"
      [ngClass]="{ 'btn-secondary': !form.valid, 'btn-warning': form.valid }"
      type="submit"
    />
  </form>
  <app-loader [show]="loading"></app-loader>
</div>

<app-success-modal
  *ngIf="formSendStatus"
  message="_successfully_send_form"
></app-success-modal>


