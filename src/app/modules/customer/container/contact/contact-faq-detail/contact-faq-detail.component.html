<div class="pb-4">

  <div class="w-100 search-area position-relative mb-3 px-3">
    <input
      #search
      [placeholder]="'_search' | translate"
      class="form-control search-input"
      type="text"
      [(ngModel)]="searchText"
    />
    <i class="icon icon-search"></i>
  </div>
  
  <div class="faq-menu" #target>
    <ngb-accordion [closeOthers]="true">
      <ngb-panel id="id-{{i}}" *ngFor="let faq of faqQuestions | search:'question,answer':searchText; let i = index">
        <ng-template ngbPanelTitle>
          <div class="d-flex align-items-center justify-content-between">
            <div class="d-flex">{{faq.question}}</div>
            <div class="d-flex"><i class="text-decoration-none icon icon-chevron-right"></i></div>
          </div>
        </ng-template>
        <ng-template ngbPanelContent>
          <div class="position-relative">
            <div class="left-bar" *ngIf="getParagraph(faq.answer)?.length > 1"></div>
            <ng-container *ngFor="let paragraph of getParagraph(faq.answer); let i = index">
              <div class="position-relative">
                <div [ngClass]="{'pl-4': getParagraph(faq.answer)?.length > 1}" class="pl-2 d-flex align-items-center fit-body" style="width: 100%">
                  <div class="status" *ngIf="getParagraph(faq.answer)?.length > 1">
                    {{i + 1}}
                  </div>
                  <div class="answer" [innerHTML]="paragraph | safeHtml"></div>
                </div>
              </div>
            </ng-container>
          </div>
        </ng-template>
      </ngb-panel>
    </ngb-accordion>
  </div>
</div>