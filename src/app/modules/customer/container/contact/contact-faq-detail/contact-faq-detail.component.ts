import {
  Component,
  OnInit,
  AfterContentChe<PERSON>,
  ElementRef,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { environment } from 'src/environments/environment';
import { Location } from '@angular/common';
import { CustomerState } from '../../../state/customer/customer.state';
import uuidv4 from 'src/app/util/uuidv4';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { CustomerModuleService } from '../../../service/customer-module.service';
import { UserState } from '../../../state/user/user.state';
import { ContactState } from '../../../state/contact/contact.state';
import { Observable } from 'rxjs';
import { FAQModel } from '../../../model/contact';
import { FAQAction } from '../../../state/contact/contact.actions';

@Component({
  selector: 'app-contact-faq-detail',
  templateUrl: './contact-faq-detail.component.html',
  styleUrls: ['./contact-faq-detail.component.scss'],
  encapsulation: ViewEncapsulation.None,
})
export class ContactFaqDetailComponent implements OnInit, AfterContentChecked {
  @ViewChild('target')
  target: ElementRef<HTMLDivElement>;

  @Select(ContactState.faqList)
  faqList$: Observable<FAQModel[]>;

  customerNumber: string;
  searchText: any;
  faqQuestions: any;

  constructor(
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly store: Store,
    private readonly translateService: TranslateService,
    private readonly translate: TranslatePipe,
    private readonly location: Location,
    private readonly frameMessageService: FrameMessageService,
    private readonly customerModuleService: CustomerModuleService
  ) {}

  ngOnInit() {
    const { topic } = this.activatedRoute.snapshot.params;

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translateService.instant(topic),
      })
    );

    this.faqList$.subscribe(faq => {
      this.faqQuestions = faq.filter(f => f.category === topic);
    });

    this.customerNumber = this.store.selectSnapshot(UserState.currentCustomer)?.customer?.customerNumber;
  }

  ngAfterContentChecked() {
    const elements: any =
      this.target?.nativeElement?.querySelectorAll('a');
    if (elements?.length > 0) {
      elements.forEach((el) => {
        el.onclick = () => {
          this.linkSeperator(el);
          // this.openFrame(el?.href, el?.innerText);
          return false;
        };
      });
    }
  }

  iframeAction(url) {
    if (url.search('mobileview') === 0) {
      url = url.slice(10, url.length);
    }
    let pageTitle: string;
    switch (url) {
      case '/form/request-service':
        pageTitle = '_service_request';
        break;
      case '/form/request-mda':
        pageTitle = '_mda_request';
        break;
      case '/form/request-equipment-part':
        pageTitle = '_spare_part_request';
        break;
      case '/expertise/init':
        pageTitle = '_expertise_form';
        break;
      default:
        pageTitle = null;
        break;
    }
    if (pageTitle == null) {
      return null;
    }
    const iframeAction = {
      url: environment.frameUrl + url,
      params: {
        showHeader: false,
        navigatedPage: 'FAQ',
      },
      active: true,
      closeButton: false,
      backButton: true,
      pageTitle: this.translate.transform(pageTitle),
      previous: {
        headerStatus: this.store.selectSnapshot(CustomerState.header),
        navigate: this.location.path(),
      },
    };
    return iframeAction;
  }

  linkSeperator(element) {
    let link360 = '';
    let linkParams: any;
    // const urlParams = this.linkParamsSeperator(element.href);
    // this.incomingService.open_module(element.href);
    if (element.href.search('borusancat360://') === 0) {
      link360 = element.href.slice(16, element.href.lenght);
      if (link360.search('cat_open_form') === 0) {
        linkParams = this.linkParamsSeperator(element.href);
        if (linkParams.find((o) => o.key === 'url')) {
          const iframeAction = this.iframeAction(
            linkParams.find((o) => o.key === 'url').value
          );
          if (iframeAction === null) {
            return;
          }
          this.router.navigate(['module', uuidv4()], {
            state: { iframeAction },
          });
        }
      }
      if (link360.search('open_module') === 0) {
        linkParams = this.linkParamsSeperator(element.href);
        if (linkParams.find((o) => o.key === 'moduleCode')) {
          this.customerModuleService.findAndNavigate(
            this.customerNumber,
            linkParams.find((o) => o.key === 'moduleCode').value
          );
        }
      }
    }
    if (
      element.href.search('tel://') === 0 ||
      element.href.search('tel:') === 0
    ) {
      const phone =
        element.href.search('tel://') === 0
          ? element.href.slice(6, element.href.length)
          : element.href.slice(4, element.href.length);
      this.frameMessageService.sendMessage(FrameMessageEnum.call_phone, {
        phone,
      });
    }
    if (element.href.search('https://wa.me/') === 0) {
      const phone = element.href.slice(14, element.href.lenght);
      this.frameMessageService.sendMessage(FrameMessageEnum.open_whatsapp, {
        phone,
      });
    }
  }

  linkParamsSeperator(url) {
    const paramString = url.split('?')[1];
    const paramsArray = paramString.split('&');
    const params = [];
    paramsArray.forEach((param) => {
      const pair = param.split('=');
      const p = {
        key: pair[0],
        value: pair[1],
      };
      params.push(p);
    });
    return params;
  }

  getParagraph(text) {
    const matchesText = text.match(/<p>(.*?)<\/p>/gs);
    return matchesText?.map(match => match.trim()) || [text];
  }
}
