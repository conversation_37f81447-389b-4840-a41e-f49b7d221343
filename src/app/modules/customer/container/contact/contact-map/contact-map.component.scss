.orange-bg {
  height: 34px;
  width: 100%;
  background-color: orange;
  position: absolute;
  top: 56px;
}

.search-content {
  z-index: 1;
  position: absolute;
  top: 64px;
  display: flex;
  justify-content: center;
  width: 100%;
  .search-select-area {
    background: #ffffff;
    border: 1px solid #d7e5ea;
    border-radius: 6px;
    position: relative;
    .filter-button{
      position: absolute;
      left: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
    }
    ng-select{
      margin-left: 50px;
    }
    .search-select ::ng-deep.ng-select-container {
      background-color: transparent;
      border: none;
      width: 100%;
    }
  }
}

.filter-content {
  position: absolute;
  top: 155px;
  .filter-items {
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: nowrap;
    list-style: none;
    padding: 0;
    margin: 0;
    overflow: auto;
    .filter-item {
      height: 30px;
      background: #ffffff;
      border: 1px solid #bababa;
      box-sizing: border-box;
      border-radius: 4px;
      margin-left: 0.5rem;
      margin-right: 0.5rem;
      font-weight: 600;
      font-size: 13px;
      line-height: 19px;
      padding: 0.3rem;
      white-space: nowrap;
    }
  }
}
.map-content {
  margin-top: 26px;
  height: calc(100vh - (64px + 26px));
  width: 100vw;
  position: relative;
}

::ng-deep google-map .map-container {
  height: 100% !important;
  width: 100vw !important;
  .gmnoprint {
    display: none;
  }
}

.location-info-content {
  position: absolute;
  z-index: 1;
  bottom: 0;
  width: 100%;
  .location-info-body {
    background-color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 0.5rem;
    .location-info-title {
      font-weight: 700;
      font-size: 16px;
      text-align: center;
    }
    .location-info-address {
      font-weight: 400;
      font-size: 14px;
    }
    .location-info-email {
      font-weight: 400;
      font-size: 14px;
    }
    .location-info-phone {
      padding: 1rem;
    }
    .location-info-navigate {
      position: absolute;
      bottom: 0;
      right: 0;
      padding: 1rem;
      .icon {
        font-size: 1.5rem;
      }
    }
  }
}

.service-person-item-detail {
  margin: 0;
  margin-bottom: 1.5rem;
  padding: 0;
  list-style: none;
  &-item {
    font-weight: normal;
    font-size: 16px;
    line-height: 24px;
    display: flex;
    align-items: center;
    color: #505050;
    margin-top: 1rem;
    .icon-area {
      margin-right: 1rem;
      border-radius: 50%;
      background-color: #ebebeb;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0.625rem;
    }
  }
}

::ng-deep .ng-input input {
  color: black !important;
}