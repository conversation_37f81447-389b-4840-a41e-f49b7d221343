<div class="orange-bg"></div>
<div class="search-content">
  <div class="search-select-area col-9 px-0">
    <button class="btn filter-button" (click)="onClickFilter()">
      <i class="icon icon-filter" [ngClass]="{'text-warning': isFiltered || selectedLocation}"></i>
    </button>
    <ng-select
      #searchSelect
      [dropdownPosition]="'bottom'"
      class="search-select"
      [placeholder]="'_choose_office' | translate"
      [searchable]="true"
      [items]="locations"
      bindLabel="name"
      groupBy="locationTypeText"
      [multiple]="false"
      [(ngModel)]="selectedLocation"
      (ngModelChange)="onSelectLocation($event)"
      (focus)="onClickMarker(null)"
      (close)="searchSelect.blur()"
    >
    </ng-select>
  </div>
</div>

<div class="map-content">
  <google-map  *ngIf="apiLoaded | async" [center]="center" [zoom]="zoom" (mapClick)="onClickMarker(null)">
    <map-marker
      *ngFor="let markerPosition of locations"
      [position]="markerPosition"
      [options]="markerOptions"
      (mapClick)="onClickMarker(markerPosition)"
    ></map-marker>
  </google-map>
</div>

<div class="location-info-content" *ngIf="selectedLocation">
  <div class="location-info-body">
    <div class="location-info-title">{{ selectedLocation.name }}</div>
    <div class="location-info-address">{{ selectedLocation.adress }}</div>
    <div
      *ngIf="selectedLocation.email"
      class="location-info-email text-secondary"
    >
      {{ selectedLocation.email }}
    </div>
    <div
      *ngIf="selectedLocation.fax"
      class="location-info-phone text-secondary"
    >
      {{ selectedLocation.fax }}
    </div>
    <div
      *ngIf="selectedLocation.phone"
      class="col-12 text-center location-info-phone text-info"
      (click)="onClickPhone(selectedLocation.phone)"
    >
      {{ selectedLocation.phone }}
    </div>
    <div
      class="location-info-navigate"
      appClickLog
      [section]="'FIND_ON_MAP'"
      [subsection]="'DIRECTION_CLICK'"
      [data]="{
        lat: selectedLocation.lat,
        lng: selectedLocation.lng,
        name: selectedLocation.name
      }"
      (click)="onNavigate()"
    >
      <i class="icon icon-direction"></i>
    </div>
  </div>
</div>

<app-basic-modal
  *ngIf="isShowCallModal"
  [(status)]="isShowCallModal"
  [headerText]="'_select_phone' | translate"
>
  <ul class="service-person-item-detail">
    <li
      class="service-person-item-detail-item"
      *ngFor="let phone of selectedLocation.phoneNumbers"
      (click)="onCallMessage(phone)"
    >
      <div class="icon-area">
        <i class="icon icon-phone-call"></i>
      </div>
      {{ phone }}
    </li>
  </ul>
</app-basic-modal>

<app-basic-modal
  *ngIf="isShowFilterModal"
  [(status)]="isShowFilterModal"
  [headerText]="'_filters' | translate"
  (statusChange)="onFilterModalClose($event)"
>
  <div class="mb-2">
    <ng-select
      #countrySelect
      class="search-select"
      [dropdownPosition]="'bottom'"
      [placeholder]="'_select_country' | translate"
      [searchable]="true"
      [multiple]="false"
      [(ngModel)]="selectedCountryCode"
      (close)="countrySelect.blur()"
    >
      <ng-option *ngFor="let item of countries$ | async" [value]="item.code">
        {{ item.name }}
      </ng-option>
    </ng-select>
  </div>
  <div class="mb-5">
    <ng-select
      #locationSelect
      class="search-select"
      [dropdownPosition]="'bottom'"
      [placeholder]="'_select_office_type' | translate"
      [searchable]="true"
      [multiple]="false"
      [(ngModel)]="selectedLocationType"
      (ngModelChange)="onSelectLocationType($event)"
      (close)="locationSelect.blur()"
    >
      <ng-option *ngFor="let item of locationTypes" [value]="item.id">
        {{ item.name }}
      </ng-option>
    </ng-select>
  </div>
  <div class="d-flex justify-content-around mb-3">
    <button
      (click)="onClearFilter()"
      class="modal-btn btn btn-secondary btn-gradient text-white shadow"
    >
      {{ "_clear" | translate }}
    </button>

    <button
      (click)="onApplyFilter()"
      class="modal-btn btn btn-warning btn-gradient text-white shadow"
    >
      {{ "_apply" | translate }}
    </button>
  </div>
</app-basic-modal>
