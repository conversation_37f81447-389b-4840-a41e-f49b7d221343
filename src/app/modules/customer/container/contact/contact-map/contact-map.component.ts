import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { ContactAllLocationAction, } from '../../../state/contact/contact.actions';
import { ContactState } from '../../../state/contact/contact.state';
import { ContactLocationModel } from '../../../model/contact';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from '../../../../../core/enum/frame-message.enum';
import { LogService } from '../../../../../shared/service/log.service';
import { environment } from 'src/environments/environment';
import { DefinitionState } from '../../../../definition/state/definition/definition.state';
import { Country } from '../../../../definition/model/country.model';
import { GetCountryListAction } from '../../../../definition/state/definition/definition.actions';

@Component({
  selector: 'app-contact-map',
  templateUrl: './contact-map.component.html',
  styleUrls: ['./contact-map.component.scss'],
  providers: [TranslatePipe],
})
export class ContactMapComponent implements OnInit, OnDestroy {
  apiLoaded: Observable<boolean>;
  // markers: any[] = [];
  center: google.maps.LatLngLiteral = { lng: 29.172098, lat: 40.918099 };
  zoom = 8;
  markerOptions: google.maps.MarkerOptions = {
    draggable: false,
  };
  // markerPositions: google.maps.LatLngLiteral[] = [];
  selectedLocation: ContactLocationModel;
  isShowCallModal = false;
  isShowFilterModal = false;

  @Select(ContactState.allLocations)
  companyLocations$: Observable<ContactLocationModel[]>;
  locations: ContactLocationModel[];

  @Select(DefinitionState.countryList)
  countries$: Observable<Country[]>;
  selectedCountryCode: string;

  locationTypes: { id: number; name: string }[] = [];
  selectedLocationType: number;
  isFiltered = false;
  appliedValues: any = {};

  constructor(
    private readonly store: Store,
    private readonly translate: TranslateService,
    private readonly messageService: FrameMessageService,
    httpClient: HttpClient,
    private readonly log: LogService
  ) {
    this.apiLoaded = httpClient
      .jsonp(
        'https://maps.googleapis.com/maps/api/js?key=AIzaSyBAIRBC-ckhE8xKezngRiaPMD3R42EvVC8',
        'callback'
      )
      .pipe(
        map(() => {
          this.markerOptions = {
            draggable: false,
            icon: {
              url: environment.assets + '/map_pin-01.svg',
              scaledSize: new google.maps.Size(40, 40),
            },
          };
          return true;
        }),
        catchError(() => of(false))
      );
  }

  ngOnInit(): void {
    this.translate.get('_find_on_map').subscribe((lng) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: lng,
          bgcolor: 'orange',
        })
      );
    });
    this.store.dispatch(new ContactAllLocationAction());
    this.store.dispatch(new GetCountryListAction());
    this.companyLocations$.subscribe(() => this.onInitLocations());
  }

  ngOnDestroy(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        bgcolor: 'white',
      })
    );
  }

  onSelectCountry(countryCode) {
    this.onClearFilter();
    this.selectedCountryCode = countryCode;
    this.onInitLocations();
  }

  onSelectLocationType(type) {
    this.selectedLocationType = type;
    this.selectedLocation = null;
  }

  onSelectLocation(loc: ContactLocationModel) {
    this.center = { lat: loc.lat, lng: loc.lng };
    this.selectedLocation = loc;
    this.log
      .action('FIND_ON_MAP', 'SELECT_COMPANY', {
        locationName: loc.name,
      })
      .subscribe();
  }

  onInitLocations() {
    this.onFilterOffices();
    this.onFilterLocationTypes();
  }

  onFilterLocationTypes() {
    this.locationTypes = [];
    this.locations.forEach((l) => {
      if (!this.locationTypes.some((lt) => lt.id === l.locationType)) {
        const item = {
          id: l.locationType,
          name: l.locationTypeText,
        };
        this.locationTypes.push(item);
      }
    });
  }

  onFilterOffices() {
    const isLocations = this.store.selectSnapshot(ContactState.allLocations);
    this.locations = isLocations
      .filter(
        (l) =>
          !this.selectedCountryCode ||
          l.countryCode.toLowerCase() === this.selectedCountryCode.toLowerCase()
      )
      .filter(
        (l) =>
          !this.selectedLocationType ||
          l.locationType === this.selectedLocationType
      );
    if (this.selectedCountryCode || this.selectedLocationType) {
      this.isFiltered = true;
    }
  }

  onClickFilter() {
    this.isShowFilterModal = true;
  }

  onApplyFilter() {
    this.isShowFilterModal = false;
    this.selectedLocation = null;
    this.onFilterOffices();
    this.appliedValues = {
      selectedLocationType: this.selectedLocationType,
      selectedCountryCode: this.selectedCountryCode
    };
    console.log('appliedValues',this.appliedValues);


    if (this.locations.length > 0 && (this.selectedLocationType || this.selectedCountryCode)) {
      const { lat, lng } = this.locations[0];
      this.center = { lat, lng };
    }
  }

  onClearFilter() {
    this.isFiltered = false;
    this.selectedLocationType = null;
    this.selectedCountryCode = null;
    this.selectedLocation = null;
    this.appliedValues = { };
    this.onInitLocations();
  }

  onClickMarker(marker: ContactLocationModel) {
    if (marker) {
      this.onClearFilter();
      this.center = { lat: marker.lat, lng: marker.lng };
      this.selectedLocation = marker;
      this.log
        .action('FIND_ON_MAP', 'MARKER_CLICK', {
          name: marker.name,
          lng: marker.lng,
          lat: marker.lat,
        })
        .subscribe();
      // this.selectedLocationType = marker?.locationType;
      // this.selectedCountryCode = marker?.countryCode.toUpperCase();
    }
    this.selectedLocation = marker;
  }

  onClickPhone(phone: string) {
    if (this.selectedLocation.phoneNumbers.length > 1) {
      this.isShowCallModal = true;
    } else {
      this.onCallMessage(phone);
    }
  }

  onCallMessage(phone: string) {
    this.log.action('FIND_ON_MAP', 'PHONE_CLICK', { phone }).subscribe();
    this.messageService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onNavigate() {
    this.messageService.sendMessage(FrameMessageEnum.directionToMap, {
      lng: this.selectedLocation.lng,
      lat: this.selectedLocation.lat,
    });
  }

  onFilterModalClose($event: boolean) {
    console.log('appliedValues',this.appliedValues);
      this.selectedCountryCode = this.appliedValues?.selectedCountryCode;
      this.selectedLocationType = this.appliedValues?.selectedLocationType;
  }
}
