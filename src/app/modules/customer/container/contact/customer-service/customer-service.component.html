<div *ngIf="pssrList || bankoList">
  <div class="px-2 font-size-12px" *ngIf="!pssrList[0]?.workingHour?.isWorkingHour && !bankoList[0]?.workingHour?.isWorkingHour">
    <app-warning-box [title]="'_warning' | translate">
      {{'_customer_may_not_be_available' | translate}}
      <div class="text-center mt-1">
        <button class="action-button btn btn-info btn-sm"
                [routerLink]="'/contact/writeus'">{{'_write_us' | translate}}</button>
      </div>
    </app-warning-box>

  </div>

  <ul class="service-person">
    <!-- ? PSSR List -->
    <li
      class="service-person-item"
      *ngFor="let persons of pssrList"
    >
      <div class="service-person-item-name">
        {{ persons.pssrName }}
      </div>
      <div class="pssr-title">
        <ng-container *ngFor="let title of persons.titles; let i = index">
          {{('_customer_title_' + title) | translate}} {{ i !== persons.titles.length - 1 ? '-' : ''}}
        </ng-container>
      </div>

      <ul class="service-person-item-detail">
        <li
          class="service-person-item-detail-item"
          *ngFor="let phones of persons.telephoneList"
          (click)="onCall(phones.telephoneNumber)"
        >
          <div class="icon-area">
            <i class="icon icon-phone-call"></i>
          </div>
          {{ phones.telephoneNumberShort || phones.telephoneNumber}}
        </li>
      </ul>

      <ul class="service-person-item-detail">
        <li
          class="service-person-item-detail-item"
          *ngFor="let mail of persons.mailList"
          (click)="onMail(mail.mailAdress)"
        >
          <div class="icon-area">
            <i class="icon icon-contact"></i>
          </div>
          {{ mail.mailAdress }}
        </li>
      </ul>
    </li>
    <!-- ? Banko List -->
    <li
    class="service-person-item"
    *ngFor="let banko of bankoList"
  >
    <div class="service-person-item-name">
      {{ banko.bankoName }}
    </div>
    <div class="pssr-title">
        {{('_customer_title_BANKO') | translate}}
    </div>

    <ul class="service-person-item-detail">
      <li
        class="service-person-item-detail-item"
        *ngFor="let phones of banko.telephoneList"
        (click)="onCall(phones.telephoneNumber)"
      >
        <div class="icon-area">
          <i class="icon icon-phone-call"></i>
        </div>
        {{ phones.telephoneNumberShort || phones.telephoneNumber}}
      </li>
    </ul>

    <ul class="service-person-item-detail">
      <li
        class="service-person-item-detail-item"
        *ngFor="let mail of banko.mailList"
        (click)="onMail(mail.mailAdress)"
      >
        <div class="icon-area">
          <i class="icon icon-contact"></i>
        </div>
        {{ mail.mailAdress }}
      </li>
    </ul>
  </li>
  </ul>

</div>
