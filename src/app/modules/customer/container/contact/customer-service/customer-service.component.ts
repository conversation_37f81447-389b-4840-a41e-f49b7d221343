import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { FrameMessageEnum } from '../../../../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../../../../core/service/frame-message.service';
import { BankoList, CustomerModel, PssrList } from '../../../../../shared/models/customer.model';
import { LogService } from '../../../../../shared/service/log.service';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { CustomerState } from '../../../state/customer/customer.state';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-customer-service',
  templateUrl: './customer-service.component.html',
  styleUrls: ['./customer-service.component.scss'],
})
export class CustomerServiceComponent implements OnInit {
  customer: CustomerModel;
  pssrList: PssrList[] | any;
  bankoList: BankoList[];

  @Select(CustomerState.customer)
  customer$: Observable<CustomerModel>;

  constructor(
    private readonly store: Store,
    private readonly translate: TranslateService,
    private readonly messageService: FrameMessageService,
    private readonly log: LogService
  ) {}

  ngOnInit(): void {
    this.translate.get('_contact_with_customer_service').subscribe((trns) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: true,
          closeButton: false,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trns,
        })
      );
    });

    this.customer$.subscribe(customer => {
      if (customer) {
        this.pssrList = customer.details?.pssrList.filter(pssr => {
          return pssr.mailList.length || pssr.telephoneList.length;
        });
        this.bankoList = customer.details?.bankoList.filter(banko => {
          return banko.mailList.length || banko.telephoneList.length;
        });
      }
    });

    // console.log(this.customer.details.pssrList);
  }

  onCall(phone: string) {
    this.log
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_PHONE_CLICK', { phone })
      .subscribe();
    this.messageService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onMail(mail: string) {
    this.log
      .action('CONTACT_REPRESENTATIVE', 'REPRESENTATIVE_MAIL_CLICK', { mail })
      .subscribe();
    this.messageService.sendMessage(FrameMessageEnum.open_mail, {
      mail,
    });
  }
}
