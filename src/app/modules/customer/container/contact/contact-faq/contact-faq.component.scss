@import "variable/icon-variable";

ngb-accordion .card {
  border-radius: 2px;
  border: none;
  border: 1px solid rgba(0, 0, 0, 0.125);
}

ngb-accordion .card-body {
  padding: 0.7em;
}

ngb-accordion .card-header {
  border-radius: 0;
  padding: 0;
  border: none;

  .btn {
    width: 100%;
    float: left;
    text-align: left !important;
    box-shadow: none;
    border-radius: 0;
    //border: none;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);

  }

  .btn-link {
    //color: #4a8eb0;
    background-color: #fff;
    padding: 20px 20px 20px 40px;
  }

  .btn-link:hover {
    box-shadow: none;
    text-decoration: none;
    //color: #4a8eb0;
  }


  button:not(.collapsed) {
    //font-weight: 600;
    //background-color: #F5F4F4;
    //border-bottom: 1px solid rgba(0, 0, 0, 0.075);

    color: #4a8eb0;

    .icon-chevron-right {
      transform: rotate(90deg);
    }
  }

  .icon-chevron-right {
    line-height: 1em;
    height: 1em;
    transition: all 0.4s ease;
  }

}

ngb-accordion .btn:focus .btn:hover {
  box-shadow: none;
}

//[data-toggle="collapse"]:after {
//  display: inline-block;
//  //font: normal normal normal 14px/1 FontAwesome;
//  font-family: "#{$cat-icon-font-family}" !important;
//
//  font-size: inherit;
//  text-rendering: auto;
//  -webkit-font-smoothing: antialiased;
//  -moz-osx-font-smoothing: grayscale;
//  content: $icon-filter;
//  transform: rotate(90deg);
//  transition: all linear 0.25s;
//  float: right;
//}
//
//[data-toggle="collapse"].collapsed:after {
//  transform: rotate(0deg);
//}

.faq-menu ngb-accordion:first-child .card {
  //border-top: 1px solid rgba(0, 0, 0, 0.125);
}

.faq-menu {
  overflow: auto;
  height: 100%;
}

.search-area {
  input {
    padding-left: 47px;
    box-shadow: none !important;
  }

  i {
    position: absolute;
    left: 32px;
    //top: 50%;
    top: 24px;
    font-size: 18px;
    line-height: 18px;
    height: 18px;
    margin-top: -9px;
    font-weight: bold;
  }
}

.search-input:focus {
  box-shadow: none;
}

.faq-title{
  padding: 20px 20px 20px 40px !important;
}
