import { Component, ElementRef, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { FAQAction } from '../../../state/contact/contact.actions';
import { ContactState } from '../../../state/contact/contact.state';
import { Observable } from 'rxjs';
import { FAQModel } from '../../../model/contact';
import { HeaderStatusAction } from '../../../state/customer/customer.actions';
import { FrameMessageEnum } from '../../../../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../../../../core/service/frame-message.service';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { environment } from 'src/environments/environment';
import { CustomerModuleService } from '../../../service/customer-module.service';
import { UserState } from '../../../state/user/user.state';
import { CustomerState } from '../../../state/customer/customer.state';
import { Location } from '@angular/common';
import { Router } from '@angular/router';
import { IncomingMessageService } from 'src/app/core/service/incoming-message.service';

@Component({
  selector: 'app-contact-faq',
  templateUrl: './contact-faq.component.html',
  styleUrls: ['./contact-faq.component.scss'],
  encapsulation: ViewEncapsulation.None

})
export class ContactFaqComponent implements OnInit {

  @ViewChild('target')
  target: ElementRef<HTMLDivElement>;

  @Select(ContactState.faqList)
  faqList$: Observable<FAQModel[]>;

  @Select(ContactState.loading)
  loading$: Observable<boolean>;
  faqs: FAQModel[];
  searchText: any;
  faqCategories: string[];

  frameMessage: FrameMessageEnum;
  customerNumber: string;

  constructor(
    private readonly store: Store,
    private readonly translateService: TranslateService,
    private readonly frameMessageService: FrameMessageService,
    private readonly customerModuleService: CustomerModuleService,
    private readonly translate: TranslatePipe,
    private readonly location: Location,
    private readonly router: Router,
    private readonly incomingService: IncomingMessageService,
  ) { }

  ngOnInit(): void {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: true,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translateService.instant('_faq'),
      })
    );
    this.customerNumber = this.store.selectSnapshot(UserState.currentCustomer)?.customer?.customerNumber;
    this.store.dispatch(new FAQAction());
    this.faqList$.subscribe(faqs => {
      const categories =  [...new Set(faqs.map(f => f.category))]?.filter(c => typeof c === 'string');
      this.faqCategories = categories;
    });
  }

  navigateFaqQuestion(topic){
    this.router.navigate(['contact', 'faq', topic]);
  }
}
