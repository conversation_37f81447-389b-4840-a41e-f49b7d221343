import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ContactComponent } from './contact.component';
import { ContactRoutingModule } from './contact-routing.module';
import { ContactWriteusComponent } from './contact-writeus/contact-writeus.component';
import { TranslateModule, TranslatePipe } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ContactMenuComponent } from './contact-menu/contact-menu.component';
import { ContactMapComponent } from './contact-map/contact-map.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { HttpClientJsonpModule, HttpClientModule } from '@angular/common/http';
import { GoogleMapsModule } from '@angular/google-maps';
import { CoreModule } from '../../../../core/core.module';
import { CustomerServiceComponent } from './customer-service/customer-service.component';
import { ContactFaqComponent } from './contact-faq/contact-faq.component';
import { ContactFaqDetailComponent } from './contact-faq-detail/contact-faq-detail.component';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { FileUploadModule } from 'src/app/export/file-upload/file-upload.module';
import { HasPermissionsModule } from 'src/app/export/file-upload/permissions/has-permissions.module';

@NgModule({
  declarations: [
    ContactComponent,
    ContactWriteusComponent,
    ContactMenuComponent,
    ContactMapComponent,
    CustomerServiceComponent,
    ContactFaqComponent,
    ContactFaqDetailComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    ContactRoutingModule,
    FormsModule,
    ReactiveFormsModule,
    SharedModule,
    NgSelectModule,
    GoogleMapsModule,
    HttpClientModule,
    HttpClientJsonpModule,
    CoreModule,
    NgbAccordionModule,
    FileUploadModule,
    HasPermissionsModule
  ],
  exports:[
    CustomerServiceComponent
  ],
  providers: [TranslatePipe],
})
export class ContactModule {}
