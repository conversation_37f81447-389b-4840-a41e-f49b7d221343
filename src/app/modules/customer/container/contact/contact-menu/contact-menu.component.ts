import { Component, ElementRef, OnInit } from '@angular/core';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { CustomerModel } from 'src/app/shared/models/customer.model';
import { CustomerDetailAction, HeaderStatusAction, } from '../../../state/customer/customer.actions';
import { CustomerState } from '../../../state/customer/customer.state';
import { UserState } from '../../../state/user/user.state';
import { FrameMessageEnum } from '../../../../../core/enum/frame-message.enum';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { Location } from '@angular/common';
import { ContactWorkingHoursAction, SocialMediaAccountsAction } from '../../../../../shared/state/settings/settings.actions';
import { SettingsState } from '../../../../../shared/state/settings/settings.state';
import { ContactWorkingHoursModel } from '../../../../../shared/models/contact-working-hours.model';
import * as moment from 'moment-timezone';
import { ModalService } from '../../../../../shared/service/modal.service';
import { SocialMediaModel, SystemFeature } from '../../../response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';
import { VideoCallService } from '../../../service/video-call.service';
import { LogService } from '../../../../../shared/service/log.service';
import { SanitizedCustomerModel } from '../../../model/sanitized-customer.model';
import { CustomerModuleService } from '../../../service/customer-module.service';
import { BorusanBlockedActionsEnum } from 'src/app/modules/definition/enum/borusan-blocked-actions.enum';
import { VideoCallManagerService } from '../../../../../shared/service/video-call-manager.service';
import { PermissionEnum } from 'src/app/modules/definition/enum/permission.enum';

@Component({
  selector: 'app-contact-menu',
  templateUrl: './contact-menu.component.html',
  styleUrls: ['./contact-menu.component.scss'],
})
export class ContactMenuComponent implements OnInit {
  @Select(CustomerState.customerLoading) customerLoading$: Observable<boolean>;
  @Select(CustomerState.contactLoading) contactLoading$: Observable<boolean>;
  @Select(CustomerState.customer) customer$: Observable<CustomerModel>;
  @Select(SettingsState.contactWorkingHours) contactWorkingHours$: Observable<ContactWorkingHoursModel[]>;
  @Select(SettingsState.socialMedia) socialMediaAccounts$: Observable<SocialMediaModel[]>;
  @Select(SettingsState.systemFeatures) systemFeatures$: Observable<SystemFeature[]>;

  customer: CustomerModel;
  currentCustomer: SanitizedCustomerModel;
  isShowCallCenterModal: boolean;
  isShowWhatsAppModal: boolean;
  borusanCatLogo = `${environment.assets}/borusan-cat-logo.svg`;
  contactWorkingHours: ContactWorkingHoursModel[];
  isShowCallService: boolean;
  isShowDigitalBanko = true;
  socials: SocialMediaModel[];
  socialsLogo = {
    Instagram: `${environment.assets}/socials/Instagram.svg`,
    Facebook: `${environment.assets}/socials/Facebook.svg`,
    Linkedin: `${environment.assets}/socials/Linkedin.svg`,
    Twitter: `${environment.assets}/socials/Twitter.svg`,
    Youtube: `${environment.assets}/socials/Youtube.svg`,
    Odnoklassniki: `${environment.assets}/socials/Odnoklassniki.svg`,
    VK: `${environment.assets}/socials/VK.svg`,
    Telegram: `${environment.assets}/socials/Telegram.svg`,
  };
  videocallLoading = false;

  expertiseShow: boolean;
  surveyShow: boolean;

  showCallCenterLabel2Phone: boolean;
  @Select(SettingsState.borusanBlockedActions)
  borusanBlockedActions$: Observable<string[]>;
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  PermissionEnum = PermissionEnum;

  constructor(
    private readonly store: Store,
    private readonly translateService: TranslateService,
    private readonly translate: TranslatePipe,
    private readonly messageService: FrameMessageService,
    private readonly router: Router,
    private readonly modalService: ModalService,
    private readonly elementRef: ElementRef,
    private readonly customerModuleService: CustomerModuleService,
    private readonly videoCallManagerService: VideoCallManagerService,

  ) { }

  ngOnInit(): void {
    this.currentCustomer = this.store.selectSnapshot(
      UserState.currentCustomer
    );

    this.store.dispatch(new ContactWorkingHoursAction());
    this.store.dispatch(new SocialMediaAccountsAction());

    this.translateService.get('_contact_us').subscribe((trns) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: false,
          closeButton: true,
          closeModal: false,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trns,
        })
      );
    });


    if (this.currentCustomer && this.currentCustomer.customer) {
      this.store.dispatch(
        new CustomerDetailAction(this.currentCustomer.customer?.customerNumber)
      );
      // this.store.dispatch(
      //   new CustomerContactAction(currentCustomer.publicMenuHeaderCompany)
      // );
    }

    this.customer$.subscribe((customer) => {
      this.customer = customer;
      this.isShowCallService = !!this.customer?.details?.pssrList
        ?.some(pssr => pssr.telephoneList.length > 0 || pssr.mailList.length > 0);

    });
    // this.contacts$.subscribe((res) => {
    //   this.contacts = res;
    // });

    this.contactWorkingHours$.subscribe((res) => {
      this.contactWorkingHours = res;
    });

    this.socialMediaAccounts$.subscribe((res) => {
      this.socials = res;
    });

    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.expertiseShow = systemFeature('inspection_forms', features, true);
        this.surveyShow = systemFeature('survey_forms', features, false);
        this.isShowDigitalBanko = systemFeature('digital_banko_videocall', features, true);
      }
    });
  }

  get isShowCallCenter() {
    return this.findElement('CONTACTUS');
  }

  get isShowWhatsApp() {
    return this.currentCustomer?.countryCode === 'RU'
    || this.currentCustomer?.groupKey === 'RU'
      ? false
      : this.findElement('CONTACT_WHATSAPP');
  }

  onClickCallCenter() {
    if (!this.checkWorkingHours('CONTACTUS')) {
      return;
    }

    const values = this.findElement('CONTACTUS').phoneNumbers;
    if (values.length > 1) {
      this.isShowCallCenterModal = true;
    } else {
      this.onCall(values[0]);
    }
  }

  onClickWhatsApp() {
    if (!this.checkWorkingHours('CONTACT_WHATSAPP')) {
      return;
    }
    const values = this.findElement('CONTACT_WHATSAPP').phoneNumbers;
    if (values.length > 1) {
      this.isShowWhatsAppModal = true;
    } else {
      this.messageService.sendMessage(FrameMessageEnum.open_whatsapp, {
        phone: values[0],
      });
    }
  }

  onCall(phone: string) {
    this.messageService.sendMessage(FrameMessageEnum.call_phone, { phone });
  }

  onClickServiceRequest() {
    this.customerModuleService.openServiceForm({
      showHeader: false,
      navigatedPage: 'Contact',
      source: 'Service',
      sourceRoot: 'Contact'
    });
  }

  onClickMDARequest() {
    this.customerModuleService.openMDAForm();
  }

  onClickSparePartRequest() {
    this.customerModuleService.openSparePartRequestForm();
  }

  onClickDigitalBanko() {
    this.videoCallManagerService.startVideoCall('DijitalBanko');
  }

  onClickCompanyInformations() {
    this.customerModuleService.openCompanyInformations();
  }

  private checkWorkingHours(key) {
    const hour = this.findElement(key);
    if (hour) {
      const time = moment.tz(hour.workTimezoneCode || 'UTC').format('HH:mm');
      console.log('time', time);
      if (!(time > hour.workStartTime && time < hour.workEndTime)) {
        const modal = this.modalService.errorModal({
          message: this.translateService.instant('_working_hours_error'),
          button: this.translateService.instant('_write_us'),
          buttonClick: () => {
            this.modalService.clearErrorModals();
            modal?.close(true);
            this.router.navigate(['contact', 'writeus']).then();
          }
        }, this.elementRef.nativeElement);

        return false;
      }
    }
    return true;
  }

  findElement(code) {
    if (this.contactWorkingHours?.length > 0) {
      return this.contactWorkingHours.find((c) => c.code === code);
    }
    return null;
  }

  contactValues(code) {
    return this.findElement(code).phoneNumbers;
  }

  onClickSocial(socialUrl) {
    this.messageService.sendMessage(FrameMessageEnum.openStore, { url: socialUrl });
  }

  onClickExpertise() {
    // this.store.dispatch(new HeaderStatusAction({closeButton: true}));
    this.customerModuleService.openExpertiseForm();
  }

  getCallCenterText() {
    // Only RU Region Show Call Center Label is Phone Number
    return this.currentCustomer?.countryCode === 'RU'
      ? this.findElement('CONTACTUS').phoneNumbers : this.translate.transform('_call_center');
  }
}
