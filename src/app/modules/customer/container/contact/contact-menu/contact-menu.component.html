<div class="contact-list">
  <ul class="contact-list-items">
    <li
      class="contact-list-item"
      *ngIf="isShowCallService"
      routerLink="/contact/customerservice"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'CONTACT_REPRESENTATIVE_CLICK'"
    >
      <i class="icon icon-phone-call"></i>
      {{ "_contact_with_customer_service" | translate }}
    </li>
    <li
      [hasPermission]="PermissionEnum.RequestsService"
      class="contact-list-item"
      (click)="onClickServiceRequest()"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'REQUEST_SERVICE_CLICK'"
    >
      <i class="icon icon-contact"></i>
      <div class="contact-list-title">
        {{ "_service_request" | translate }}
      </div>
    </li>
    <li
      [hasPermission]="PermissionEnum.RequestsMda"
      class="contact-list-item"
      (click)="onClickMDARequest()"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'REQUEST_MDA_CLICK'"
    >
      <i class="icon icon-contact"></i>
      <div class="contact-list-title">
        {{ "_mda_request" | translate }}
      </div>
    </li>
    <li
      [hasPermission]="PermissionEnum.RequestsSparePart"
      class="contact-list-item"
      (click)="onClickSparePartRequest()"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'REQUEST_EQUIPMENT_PART'"
    >
      <i class="icon icon-contact"></i>
      <div class="contact-list-title">
        {{ "_spare_part_request" | translate }}
      </div>
    </li>
    <li
      [hasPermission]="PermissionEnum.RequestsInspection"
      class="contact-list-item"
      (click)="onClickExpertise()"
      *ngIf="expertiseShow"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'EXPERTISE_FORM_CLICK'"
    >
      <i class="icon icon-contact"></i>
      <div class="contact-list-title">
        {{ "_expertise_form" | translate }}
      </div>
    </li>
<!--
    <li
      class="contact-list-item"
      (click)="onClickSurvey()"
      *ngIf="surveyShow"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'SURVEY_FORM_CLICK'"
    >
      <i class="icon icon-contact"></i>
      <div class="contact-list-title">
        {{ "_surveys" | translate }}
      </div>
    </li>
-->

    <li
      class="contact-list-item"
      routerLink="/contact/map"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'FIND_ON_MAP_CLICK'"
    >
      <i class="icon icon-pin"></i>
      {{ "_find_on_map" | translate }}
    </li>
    <li
      class="contact-list-item"
      routerLink="/contact/writeus"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'WRITE_US_CLICK'"
    >
      <i class="icon icon-edit"></i>
      {{ "_write_us" | translate }}
    </li>
    <li
      class="contact-list-item"
      *ngIf="isShowCallCenter"
      (click)="onClickCallCenter()"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'CALL_CENTER_CLICK'"
    >
      <i class="icon icon-headset"></i>
        {{ getCallCenterText() }}
    </li>
    <li
      [hasPermission]="PermissionEnum.VideoCallBanko"
      class="contact-list-item"
      *ngIf="isShowDigitalBanko && !((borusanBlockedActions$ | async) && (borusanBlockedActions$ | async)?.indexOf(BorusanBlockedActionsEnum.DigitalBankoVideoCall) >= 0)"
      (click)="onClickDigitalBanko()"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'CLICK_DIGITAL_BANKO'"
    >
      <i class="icon icon-videocall"></i>
        {{ '_videocall_digital_banko' | translate }}
    </li>
        <!-- TODO appClickLog eklenecek -->
        <li
        class="contact-list-item"
        (click)="onClickCompanyInformations()"

      >
        <i class="icon icon-company"></i>
        <div class="contact-list-title">
          {{ "_company_informations" | translate }}
        </div>
      </li>
    <li
      class="contact-list-item"
      *ngIf="isShowWhatsApp"
      (click)="onClickWhatsApp()"
      appClickLog
      [section]="'CONTACT'"
      [subsection]="'WHATSAPP_CLICK'"
      [data]="{phoneNumber: findElement('CONTACT_WHATSAPP').phoneNumbers}"
    >
      <i class="icon icon-whatsapp font-weight-bold"></i>
      {{ "_whatsapp" | translate }}
    </li>
<!--    <li-->
<!--      class="contact-list-item"-->
<!--      routerLink="/auth/login"-->
<!--    >-->
<!--      <i class="icon icon-phone-call"></i>-->
<!--      saml login-->
<!--    </li>-->
  </ul>
  <div class="bottom-area">
    <div class="row d-flex justify-content-center mb-2">
      <div
        *ngFor="let social of socials"
        class="contact-list-item"
        (click)="onClickSocial(social.url)"
        appClickLog
        [section]="'CONTACT'"
        [subsection]="'SOCIAL_CLICK'"
        [data]="{ socialName: this.social?.name }"
      >
        <img class="social-icon" [src]="socialsLogo[social?.name]" />
      </div>
    </div>
    <div class="borusan-cat-logo">
      <img [src]="borusanCatLogo" />
    </div>
  </div>
</div>

<app-basic-modal
  *ngIf="isShowCallCenterModal"
  [(status)]="isShowCallCenterModal"
  [headerText]="'_call_center' | translate"
>
  <div class="service-content">
    <ul class="service-person-phones">
      <li
        class="service-person-phone"
        *ngFor="let number of contactValues('CONTACTUS')"
        (click)="onCall(number)"
      >
        <i class="icon icon-phone"></i>
        {{ number }}
      </li>
    </ul>
  </div>
</app-basic-modal>

<app-basic-modal
  *ngIf="isShowWhatsAppModal"
  [(status)]="isShowWhatsAppModal"
  [headerText]="'_whatsapp' | translate"
>
  <div class="service-content">
    <ul class="service-person-phones">
      <li
        class="service-person-phones"
        *ngFor="let number of contactValues('CONTACT_WHATSAPP')"
        (click)="onCall(number)"
      >
        <i class="icon icon-phone"></i>
        {{ number }}
      </li>
    </ul>
  </div>
</app-basic-modal>
<app-loader [show]="(customerLoading$ || contactLoading$ | async) || videocallLoading"></app-loader>
