import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';
import { ContactMapComponent } from './contact-map/contact-map.component';
import { ContactMenuComponent } from './contact-menu/contact-menu.component';
import { ContactWriteusComponent } from './contact-writeus/contact-writeus.component';
import { ContactComponent } from './contact.component';
import { CustomerServiceComponent } from './customer-service/customer-service.component';
import { ContactFaqComponent } from './contact-faq/contact-faq.component';
import { ContactFaqDetailComponent } from './contact-faq-detail/contact-faq-detail.component';

const routes: Routes = [
  {
    path: '',
    component: ContactComponent,
    canActivate: [PermissionGuard],
    children: [
      { path: 'menu', component: ContactMenuComponent },
      { path: 'writeus', component: ContactWriteusComponent },
      { path: 'map', component: ContactMapComponent },
      { path: 'customerservice', component: CustomerServiceComponent },
      { path: 'faq', component: ContactFaqComponent },
      { path: 'faq/:topic', component: ContactFaqDetailComponent}
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class ContactRoutingModule {}
