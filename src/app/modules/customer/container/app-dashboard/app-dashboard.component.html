<div class="app-dashboard-wrapper">
  <div class="app-dashboard app-background"
       [ngClass]="{'passive-background': !!(currentCustomer$ | async)?.passive}">
    <app-rejected-account *ngIf="!(currentCustomer$ | async)"></app-rejected-account>
    <app-app-list *ngIf="(currentCustomer$ | async) && !(currentCustomer$ | async)?.passive "></app-app-list>
    <app-passive-account *ngIf="!!(currentCustomer$ | async)?.passive"></app-passive-account>
  </div>
  <app-catalog-list></app-catalog-list>
</div>
<app-update-modal></app-update-modal>