import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusMainAction } from '../../state/customer/customer.actions';
import { UserState } from '../../state/user/user.state';
import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';
import { Observable, Subject } from 'rxjs';
import { environment } from 'src/environments/environment';
import { VersionCheckService } from 'src/app/shared/service/version-check.service';
import { UserAction } from '../../state/user/user.actions';
import { LogService } from '../../../../shared/service/log.service';
import { takeUntil } from 'rxjs/operators';
import {CommonStoreAction, PageOpenedAction} from 'src/app/shared/state/common/common.actions';
import { PagesEnum } from 'src/app/shared/enum/pages.enum';
import {LoginState} from '../../../authentication/state/login/login.state';

@Component({
  selector: 'app-app-dashboard',
  templateUrl: './app-dashboard.component.html',
  styleUrls: ['./app-dashboard.component.scss'],
})
export class AppDashboardComponent implements OnInit, OnDestroy {
  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly versionCheckService: VersionCheckService,
    private readonly log: LogService
  ) { }

  ngOnInit(): void {
    this.store.dispatch(new HeaderStatusMainAction());
    this.store.dispatch(new UserAction(false)); // user/me without loading
    this.store.dispatch(new PageOpenedAction(PagesEnum.dashBoardPage));
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((customer) => {
        console.log('customer', customer);
        this.log.action('DASHBOARD', 'OPEN').subscribe();
      });
    if (environment.production) {
      this.versionCheckService.checkVersion(environment.versionCheckURL);
    }

    const tracker = this.store.selectSnapshot(LoginState.trackerUser);
    if (tracker) {
      this.setTrackerUser(tracker);
    }
  }

  setTrackerUser(data) {
    this.store.dispatch(
      new CommonStoreAction({
        trackerUser: data,
      })
    );
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
