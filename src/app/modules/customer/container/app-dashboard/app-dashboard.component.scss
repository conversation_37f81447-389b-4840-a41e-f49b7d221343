@import "environment.scss";

.app-dashboard-wrapper {
  margin-top: -9px;
  height: calc(100vh - 85px);
  overflow: auto;

  &:before {
    content: "";
    width: 100%;
    height: 50vh;
    position: fixed;
    bottom: 0;
    left: 0;
    background: #F9F5EF;
    background-size: cover;
    z-index: -1;
  }

  &::-webkit-scrollbar{
    display: none;
  }
}

.app-dashboard {
    // min-height: calc(100vh - 350px);
    position: relative;
    background-color: #F9F5EF;
    z-index: 1;
    min-height: 50%;
    padding-bottom: 1.5rem;
    
    &::before{
        content: " ";
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        border-bottom-left-radius: 2.5rem;
        background-color: #FFFFFF;
    }

    h1 {
      font-weight: bold;
      font-size: 26px;
      color: #2c2c2c;
      margin: 0;
      padding: 0;
      white-space: nowrap;
      overflow: hidden !important;
      text-overflow: ellipsis;
    }
}

.app-wide-dashboard{
  // height: 80%;
}

.app-background {
  // &:before {
  //   content: "";
  //   width: 100vw;
  //   height: calc(100vh - 120px);
  //   position: absolute;
  //   bottom: 0;
  //   left: 0;
  //   background: url("#{$assets}/bg.svg") no-repeat;
  //   background-size: cover;
  //   z-index: -1;
  // }
}

.passive-background {
  &::before{
    background-color: #FFFFFF !important;
  }
}

@media screen and (min-width: 750px) {
  // .app-background {
  //   &:before {
  //     background-position: right 50px;
  //   }
  // }
}
