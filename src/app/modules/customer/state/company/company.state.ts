import { Action, Selector, State, StateContext } from '@ngxs/store';
import { Injectable } from '@angular/core';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { CompanyDto } from '../../model/company';
import { CompanyService } from '../../service/company.service';
import { CompanyAction } from './company.actions';

export interface CompanyStateModel {
  loading: boolean;
  companies: CompanyDto[];
}

@State<CompanyStateModel>({
  name: 'company',
  defaults: {
    loading: false,
    companies: [],
  },
})
@Injectable()
export class CompanyState {
  constructor(private readonly service: CompanyService) {}

  @Selector()
  public static loading({ loading }: CompanyStateModel) {
    return loading;
  }

  @Selector()
  public static getState(state: CompanyStateModel) {
    return state;
  }

  @Selector()
  public static companies({ companies }: CompanyStateModel) {
    return companies;
  }

  @Action(CompanyAction)
  getCompanies({ patchState }: StateContext<CompanyStateModel>) {
    patchState({
      loading: true,
    });
    return this.service.get().pipe(
      tap((value) => {
        patchState({
          companies: value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }
}
