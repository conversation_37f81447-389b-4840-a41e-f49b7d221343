import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import {
  CatQRAction,
  CustomerContactAction,
  CustomerDetailAction,
  CustomerDetailResetAction,
  CustomerLeasingAction,
  CustomerLoadingAction,
  GetActiveApplicationsAction,
  GetManageableRolesDetailsAction,
  GetMyUsersAction,
  GetOTTAction,
  GetPccOrVlinkSurvey,
  GetPCCParamsAction,
  GetPulseSurveysAction,
  GetSurveysAction,
  GetUserApprovedAgreements,
  GetUserAwaitingAgreements,
  GetUserRoleDetailsAction,
  HeaderStatusAction,
  HeaderStatusMainAction,
  ResetLeasingAction,
  SidebarStatusAction,
  SidebarStatusClearAction,
  StaticFeedbackAction,
} from './customer.actions';
import { Injectable } from '@angular/core';
import { IframeAction } from '../iframe/iframe.actions';
import {
  CatQRModel,
  CustomerContactModel,
  CustomerModel,
  FeedbackModel,
  LeasingModel,
} from 'src/app/shared/models/customer.model';
import { CustomerService } from '../../service/customer.service';
import { FederatedService } from '../../service/federated.service';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { OttResponse } from '../../response/ott.response';
import {
  AccountUsersModel,
  ManageableRolesModel,
  UserRoleModel,
} from '../../model/my-users.model';
import { GenerateReferenceNumberAction } from 'src/app/modules/definition/state/definition/definition.actions';
import { SurveyModel } from '../../model/survey.model';
import { SurveyService } from '../../service/survey.service';

export interface HeaderStateModel {
  company: boolean;
  backButton: boolean;
  closeButton: boolean;
  title: string;
  // centeredTitle: boolean;
  notificationIcon: boolean;
  hamburgerMenu: boolean;
  closeModal: boolean;
  closeModalMessage: string;
  bgcolor: 'orange' | 'white' | 'transparent';
  img: {
    src: string;
    alt: string;
    altColor?: 'black' | 'gold' | 'blue' | 'orange';
  };
  titleContentStyle?: any;
  sendLog?: () => void
}

export interface CustomerStateModel {
  customerLoading: boolean;
  contactLoading: boolean;
  customer: CustomerModel;
  contacts: CustomerContactModel[];
  staticFeedback: FeedbackModel;
  header: HeaderStateModel;
  leasing: LeasingModel;
  leasingLoading: boolean;
  sidebarStatus: boolean;
  CatQR: CatQRModel;
  ott: OttResponse;
  ottLoading: boolean;
  myUsers: AccountUsersModel[];
  myUsersLoading: boolean;
  userRoleDetails: UserRoleModel;
  userRoleDetailsLoading: boolean;
  manageableRoleDetails: ManageableRolesModel;
  activeApplications: any;
  activeApplicationsLoading: boolean;
  referenceNumber: any;
  referenceNumberLoading: boolean;
  awaitingAggrements: any;
  approvedAgreements: any;
  awaitingAggrementsLoading: boolean;
  approvedAgreementsLoading: boolean;
  surveyList: SurveyModel[];
  pulseSurveyList: SurveyModel[];
  surveyLoading: boolean;
  pccOrVlinkSurvey: any;
  pccOrVlinkSurveyLoading: boolean;
  pccParams: {
    data: any | null;
    loading: boolean;
    error: string | null;
  };
}

@State<CustomerStateModel>({
  name: 'customer',
  defaults: {
    customerLoading: false,
    contactLoading: false,
    customer: null,
    contacts: [],
    staticFeedback: null,
    sidebarStatus: true,
    CatQR: null,
    header: {
      company: true,
      backButton: false,
      closeButton: false,
      title: null,
      // centeredTitle: true,
      notificationIcon: true,
      hamburgerMenu: true,
      closeModal: false,
      closeModalMessage: null,
      bgcolor: 'white',
      img: {
        src: null,
        alt: null,
        altColor: 'black',
      },
      titleContentStyle: '',
    },
    leasing: null,
    leasingLoading: false,
    ott: null,
    ottLoading: false,
    myUsers: [],
    myUsersLoading: false,
    userRoleDetails: null,
    userRoleDetailsLoading: false,
    manageableRoleDetails: null,
    activeApplications: [],
    activeApplicationsLoading: false,
    referenceNumber: null,
    referenceNumberLoading: false,
    awaitingAggrements: null,
    approvedAgreements: null,
    awaitingAggrementsLoading: false,
    approvedAgreementsLoading: false,
    surveyList: [],
    pulseSurveyList: [],
    surveyLoading: false,
    pccOrVlinkSurvey: null,
    pccOrVlinkSurveyLoading: false,
    pccParams: {
      data: null,
      loading: false,
      error: null,
    },
  },
})
@Injectable()
export class CustomerState {
  constructor(
    private readonly store: Store,
    private readonly customerService: CustomerService,
    private readonly surveyService: SurveyService,
    private readonly federatedService: FederatedService
  ) {}

  @Selector()
  public static customerLoading({ customerLoading }: CustomerStateModel) {
    return customerLoading;
  }

  @Selector()
  public static contactLoading({ contactLoading }: CustomerStateModel) {
    return contactLoading;
  }

  @Selector()
  public static getState(state: CustomerStateModel) {
    return state;
  }

  @Selector()
  public static header({ header }: CustomerStateModel) {
    return header;
  }

  @Selector()
  public static customer({ customer }: CustomerStateModel): CustomerModel {
    return customer;
  }

  @Selector()
  public static staticFeedback({
    staticFeedback,
  }: CustomerStateModel): FeedbackModel {
    return staticFeedback;
  }

  @Selector()
  public static pccOrVlinkSurvey({ pccOrVlinkSurvey }: CustomerStateModel) {
    return pccOrVlinkSurvey;
  }

  @Selector()
  public static pccOrVlinkSurveyLoading({ pccOrVlinkSurveyLoading }: CustomerStateModel) {
    return pccOrVlinkSurveyLoading;
  }

  @Selector()
  public static pccParams({ pccParams }: CustomerStateModel) {
    return pccParams.data;
  }

  @Selector()
  public static pccParamsLoading({ pccParams }: CustomerStateModel) {
    return pccParams.loading;
  }

  @Selector()
  public static pccParamsError({ pccParams }: CustomerStateModel) {
    return pccParams.error;
  }

  @Selector()
  public static contacts({
    contacts,
  }: CustomerStateModel): CustomerContactModel[] {
    return contacts;
  }

  @Selector()
  public static leasing({ leasing }: CustomerStateModel) {
    return leasing;
  }

  @Selector()
  public static leasingLoading({ leasingLoading }: CustomerStateModel) {
    return leasingLoading;
  }

  @Selector()
  public static sidebarStatus({ sidebarStatus }: CustomerStateModel) {
    return sidebarStatus;
  }

  @Selector()
  public static catQr({ CatQR }: CustomerStateModel) {
    return CatQR;
  }

  @Selector()
  public static ott({ ott }: CustomerStateModel): OttResponse {
    return ott;
  }

  @Selector()
  public static myUsers({ myUsers }: CustomerStateModel) {
    return myUsers;
  }

  @Selector()
  public static myUsersLoading({ myUsersLoading }: CustomerStateModel) {
    return myUsersLoading;
  }

  @Selector()
  public static userRoleDetails({ userRoleDetails }: CustomerStateModel) {
    return userRoleDetails;
  }

  @Selector()
  public static userRoleDetailsLoading({
    userRoleDetailsLoading,
  }: CustomerStateModel) {
    return userRoleDetailsLoading;
  }

  @Selector()
  public static manageableRoleDetails({
    manageableRoleDetails,
  }: CustomerStateModel) {
    return manageableRoleDetails;
  }

  @Selector()
  public static activeApplications({ activeApplications }: CustomerStateModel) {
    return activeApplications;
  }

  @Selector()
  public static activeApplicationsLoading({
    activeApplicationsLoading,
  }: CustomerStateModel) {
    return activeApplicationsLoading;
  }

  @Selector()
  public static referenceNumber({
    referenceNumber,
  }: CustomerStateModel): string {
    return referenceNumber;
  }

  @Selector()
  public static referenceNumberLoading({
    referenceNumberLoading,
  }: CustomerStateModel): boolean {
    return referenceNumberLoading;
  }

  @Selector()
  public static awaitingAgreements({
    awaitingAggrements,
  }: CustomerStateModel): boolean {
    return awaitingAggrements;
  }

  @Selector()
  public static approvedAggreements({
    approvedAgreements,
  }: CustomerStateModel): boolean {
    return approvedAgreements;
  }

  @Selector()
  public static awaitingAggrementsLoading({
    awaitingAggrementsLoading,
  }: CustomerStateModel): boolean {
    return awaitingAggrementsLoading;
  }

  @Selector()
  public static approvedAgreementsLoading({
    approvedAgreementsLoading,
  }: CustomerStateModel): boolean {
    return approvedAgreementsLoading;
  }

  @Selector()
  public static isShowCallService({ customer }: CustomerStateModel): boolean {
    return !!customer?.details?.pssrList?.some(
      (pssr) => pssr.telephoneList.length > 0 || pssr.mailList.length > 0
    );
  }

  @Selector()
  public static surveyList({ surveyList }: CustomerStateModel): SurveyModel[] {
    return surveyList;
  }

  @Selector()
  public static pulseSurveyList({
    pulseSurveyList,
  }: CustomerStateModel): SurveyModel[] {
    return pulseSurveyList;
  }

  @Action(HeaderStatusAction)
  public headerStatusAction(
    { getState, patchState }: StateContext<CustomerStateModel>,
    { headerStatus }: HeaderStatusAction
  ) {
    if (getState().header?.img && !headerStatus.img) {
      headerStatus.img = null;
    }
    patchState({
      header: {
        ...getState().header,
        closeButton: false,
        ...headerStatus,
      },
    });
  }

  @Action(HeaderStatusMainAction)
  public headerStatusMainAction(
    { getState, patchState }: StateContext<CustomerStateModel>,
    { headerStatus }: HeaderStatusMainAction
  ) {
    patchState({
      header: {
        ...getState().header,
        ...headerStatus,
      },
    });

    this.store.dispatch(
      new IframeAction({
        active: false,
        pageTitle: null,
        closeButton: false,
      })
    );
  }

  @Action(CustomerDetailAction)
  customerDetailAction(
    { patchState, getState }: StateContext<CustomerStateModel>,
    { customerNumber }: CustomerDetailAction
  ) {
    // const state = getState();
    // if (!state.customer || state.customer.customerNumber !== customerNumber) {

    // patchState({
    //   customerLoading: true, // no need
    // });
    return this.customerService.detail(customerNumber).pipe(
      tap((value) => {
        patchState({
          customer: value,
          customerLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          customerLoading: false,
        });
        return throwError(err);
      })
    );
    // }
    // return of(state.customer);
  }

  @Action(CustomerDetailResetAction)
  customerDetailResetAction({ patchState }: StateContext<CustomerStateModel>) {
    patchState({
      customer: null,
    });
  }

  @Action(CustomerContactAction)
  customerContactAction(
    { patchState, getState }: StateContext<CustomerStateModel>,
    { companyId }: CustomerContactAction
  ) {
    // const state = getState();
    // if (state.contacts.length === 0) {

    // patchState({
    //   contactLoading: true,
    // });
    return this.customerService.contacts(companyId).pipe(
      tap((value) => {
        patchState({
          contacts: value,
          contactLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          contactLoading: false,
        });
        return throwError(err);
      })
    );
    // }
    // return of(state.contacts);
  }

  @Action(StaticFeedbackAction)
  staticFeedBackAction({ patchState }: StateContext<CustomerStateModel>) {
    // const state = getState();
    // if (state.staticFeedback) {
    //   return of(state.staticFeedback);
    // }

    return this.customerService.staticFeedback().pipe(
      tap((value) => {
        patchState({
          staticFeedback: value,
        });
      }),
      catchError((err) => {
        // patchState({
        //   contactLoading: false,
        // });
        return throwError(err);
      })
    );
  }

  @Action(CustomerLoadingAction)
  contactLoadingAction(
    { patchState }: StateContext<CustomerStateModel>,
    { loading }: CustomerLoadingAction
  ) {
    patchState({
      customerLoading: loading,
    });
  }

  @Action(CustomerLeasingAction)
  customerLeasingAction(
    { patchState }: StateContext<CustomerStateModel>,
    { leasingFormBody, leasingData }: CustomerLeasingAction
  ) {
    patchState({
      leasingLoading: true,
    });
    return this.customerService.calculateLeasing(leasingFormBody).pipe(
      tap((value) => {
        patchState({
          leasing: { ...value, leasingData: leasingData },
          leasingLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          leasingLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(ResetLeasingAction)
  resetLeasingAction({ patchState }: StateContext<CustomerStateModel>) {
    patchState({
      leasing: null,
    });
  }

  @Action(SidebarStatusAction)
  public sidebarStatusAction(
    { patchState }: StateContext<CustomerStateModel>,
    { toggle }: SidebarStatusAction
  ) {
    patchState({
      sidebarStatus: toggle,
    });
  }

  @Action(SidebarStatusClearAction)
  public sidebarStatusClearAction(
    { patchState }: StateContext<CustomerStateModel>,
    { toggle }: SidebarStatusClearAction
  ) {
    patchState({
      sidebarStatus: toggle,
    });
  }

  @Action(GetOTTAction)
  public getOTTAction(
    { patchState }: StateContext<CustomerStateModel>,
    action: GetOTTAction
  ) {
    patchState({
      ottLoading: false,
    });
    return this.customerService.getOTT().pipe(
      tap((value) => {
        patchState({
          ott: value,
          ottLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          ottLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(CatQRAction)
  public CatQRAction(
    { patchState }: StateContext<CustomerStateModel>,
    { value }: CatQRAction
  ) {
    return this.customerService.getEquipmentCatQr(value).pipe(
      tap((value) => {
        patchState({
          CatQR: value,
        });
      }),
      catchError((err) => {
        return throwError(err);
      })
    );
  }

  @Action(GetMyUsersAction)
  public GetMyUsersAction({ patchState }: StateContext<CustomerStateModel>) {
    patchState({
      myUsersLoading: true,
    });

    return this.customerService.getAccountMyUsers().pipe(
      tap((value) => {
        patchState({
          myUsers: value,
          myUsersLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          myUsersLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetUserRoleDetailsAction)
  public GetUserRoleDetailsAction(
    { patchState }: StateContext<CustomerStateModel>,
    { referenceId, portalUserId }: GetUserRoleDetailsAction
  ) {
    patchState({
      userRoleDetailsLoading: true,
    });

    return this.customerService
      .getUserRoleDetails(referenceId, portalUserId)
      .pipe(
        tap((value) => {
          patchState({
            userRoleDetails: value,
            userRoleDetailsLoading: false,
          });
        }),
        catchError((err) => {
          patchState({
            userRoleDetailsLoading: false,
          });
          return throwError(err);
        })
      );
  }

  @Action(GetManageableRolesDetailsAction)
  public GetManageableRolesDetailsAction({
    patchState,
  }: StateContext<CustomerStateModel>) {
    patchState({
      userRoleDetailsLoading: true,
    });

    return this.customerService.getManageableRolesDetail().pipe(
      tap((value) => {
        patchState({
          manageableRoleDetails: value,
          userRoleDetailsLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          userRoleDetailsLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetActiveApplicationsAction)
  public GetActiveApplicationsAction({
    patchState,
  }: StateContext<CustomerStateModel>) {
    patchState({
      activeApplicationsLoading: true,
    });

    return this.customerService.getActiveApplications().pipe(
      tap((value) => {
        patchState({
          activeApplications: value,
          activeApplicationsLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          activeApplicationsLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GenerateReferenceNumberAction)
  generateReferenceNumberState(
    { patchState }: StateContext<CustomerStateModel>,
    { payload }: GenerateReferenceNumberAction
  ) {
    patchState({
      referenceNumberLoading: true,
    });
    return this.customerService.generateReferenceNumber(payload).pipe(
      tap((value) => {
        patchState({
          referenceNumberLoading: false,
          referenceNumber: value,
        });
      }),
      catchError((err) => {
        patchState({
          referenceNumberLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetUserApprovedAgreements)
  public GetUserApprovedAgreements({
    patchState,
  }: StateContext<CustomerStateModel>) {
    patchState({
      approvedAgreementsLoading: true,
    });

    return this.customerService.getUserApprovedAgreements().pipe(
      tap((value) => {
        patchState({
          approvedAgreements: value,
          approvedAgreementsLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          approvedAgreementsLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetUserAwaitingAgreements)
  public GetUserAwaitingAgreements(
    { patchState }: StateContext<CustomerStateModel>,
    { position, onlyNotApproved }: GetUserAwaitingAgreements
  ) {
    patchState({
      awaitingAggrementsLoading: true,
    });

    return this.customerService
      .getUserAwaitingAgreements(position, onlyNotApproved)
      .pipe(
        tap((value) => {
          patchState({
            awaitingAggrements: value,
            awaitingAggrementsLoading: false,
          });
        }),
        catchError((err) => {
          patchState({
            awaitingAggrementsLoading: false,
          });
          return throwError(err);
        })
      );
  }

  @Action(GetSurveysAction)
  getSurveysAction(
    { patchState }: StateContext<CustomerStateModel>,
    action: GetSurveysAction
  ) {
    patchState({ surveyLoading: true });
    return this.surveyService.getList().pipe(
      tap((list) => {
        patchState({
          surveyList: list,
          surveyLoading: false,
        });
      }),
      catchError((err) => {
        patchState({ surveyLoading: true });
        return throwError(err);
      })
    );
  }

  @Action(GetPulseSurveysAction)
  getPulseSurveysAction(
    { patchState }: StateContext<CustomerStateModel>,
    action: GetPulseSurveysAction
  ) {
    patchState({ surveyLoading: true });
    return this.surveyService.getPulseList().pipe(
      tap((list) => {
        patchState({
          pulseSurveyList: list,
          surveyLoading: false,
        });
      }),
      catchError((err) => {
        patchState({ surveyLoading: true });
        return throwError(err);
      })
    );
  }

  @Action(GetPccOrVlinkSurvey)
  getPccOrVlinkSurvey(
    { patchState }: StateContext<CustomerStateModel>,
    action: GetPccOrVlinkSurvey
  ) {
    patchState({ pccOrVlinkSurveyLoading: true });
    return this.surveyService.getPccOrVlinkSurvey(action.payload).pipe(
      tap((list) => {
        patchState({
          pccOrVlinkSurvey: list,
          pccOrVlinkSurveyLoading: false,
        });
      }),
      catchError((err) => {
        patchState({ pccOrVlinkSurveyLoading: false });
        return throwError(err);
      })
    );
  }

  @Action(GetPCCParamsAction)
  getPCCParams({ patchState }: StateContext<CustomerStateModel>) {
    patchState({
      pccParams: {
        data: null,
        loading: true,
        error: null
      }
    });

    return this.federatedService.getPCCParams().pipe(
      tap((data) => {
        patchState({
          pccParams: {
            data,
            loading: false,
            error: null,
          },
        });
      }),
      catchError((err) => {
        patchState({
          pccParams: {
            data: null,
            loading: false,
            error: err.message || 'Failed to fetch PCC parameters',
          },
        });
        return throwError(err);
      })
    );
  }
}
