import { HeaderStateModel } from './customer.state';

export class HeaderStatusAction {
  public static readonly type = '[Customer] header status';

  constructor(public headerStatus: Partial<HeaderStateModel>) {}
}

export class HeaderStatusMainAction {
  public static readonly type = '[Customer] header status to main';
  public headerStatus = {
    company: true,
    backButton: null,
    closeButton: null,
    title: null,
    notificationIcon: true,
    hamburgerMenu: true,
    closeModal: false,
    closeModalMessage: null,
    img: {
      src: null,
      alt: null,
    },
  };

  constructor() {}
}

export class CustomerDetailAction {
  public static readonly type = '[Customer] Detail';

  constructor(public customerNumber: string) {}
}

export class CustomerDetailResetAction {
  public static readonly type = '[Customer] Detail Reset';

  constructor() {}
}

export class CustomerContactAction {
  public static readonly type = '[Customer] Contact';

  constructor(public companyId: string) {}
}

export class StaticFeedbackAction {
  public static readonly type = '[Customer] Static Feedback';

  constructor() {}
}

export class CustomerLoadingAction {
  public static readonly type = '[Customer] Customer Loading';

  constructor(public loading: boolean) {}
}

export class CustomerLeasingAction {
  public static readonly type = '[Customer] Customer Calculate Leasing';

  constructor(public leasingFormBody: any, public leasingData: any) {}
}

export class ResetLeasingAction {
  public static readonly type = '[Customer] Customer Reset Leasing';

  constructor() {}
}

export class SidebarStatusAction {
  public static readonly type = '[Customer] Sidebar Toggle Status';

  constructor(public toggle: boolean) {}
}

export class SidebarStatusClearAction {
  public static readonly type = '[Customer] Sidebar Toggle Status Clear';

  constructor(public toggle: boolean) {}
}

export class CatQRAction {
  public static readonly type = '[Customer] Cat Qr';

  constructor(public value: string) {}
}

export class GetOTTAction {
  public static readonly type = '[Customer] Get OTT';

  constructor() {}
}

export class GetMyUsersAction {
  public static readonly type = '[Customer] Get Account My Users';

  constructor() {}
}

export class GetUserRoleDetailsAction {
  public static readonly type = '[Customer] Get User Role Details';

  constructor(public referenceId: string, public portalUserId: string) {}
}

export class GetManageableRolesDetailsAction {
  public static readonly type = '[Customer] Get Manageable Role Details';

  constructor() {}
}

export class GetActiveApplicationsAction {
  public static readonly type = '[Customer] Get Active Applications';

  constructor() {}
}

export class GetUserAwaitingAgreements {
  public static readonly type = '[Customer] Get User Awaiting Agreements';

  constructor(public position?: string, public onlyNotApproved?: boolean) {}
}

export class GetUserApprovedAgreements {
  public static readonly type = '[Customer] Get User Approved Agreements';

  constructor() {}
}

export class GetSurveysAction {
  public static readonly type = '[Customer] Get Survey List';

  constructor() {}
}
export class GetPulseSurveysAction {
  public static readonly type = '[Customer] Get Pulse Survey List';

  constructor() {}
}
export class GetPccOrVlinkSurvey {
  public static readonly type = '[Customer] Get Pcc Or Vlink Survey';

  constructor(public payload: any) {}
}

export class GetPCCParamsAction {
  public static readonly type = '[Customer] Get PCC Params';

  constructor() {}
}
