import { MobileVerifySendModel, MobileVerifyStartModel } from '../../model/user.model';

import { RfmsIgnoreReasonsFormSendModel } from '../../model/form.model';

export class RfmsIgnoreReasonsFormAction {
  public static readonly type = '[Form] RFMS Ignore Reason Form Get';
  constructor() { }
}

export class SendRfmsIgnoreReasonsFormAction {
  public static readonly type = '[Form] RFMS Ignore Reason Form Send';
  constructor(public rfmsFormDataSend: RfmsIgnoreReasonsFormSendModel) { }
}

export class GetRfmsFormStatusAction {
  public static readonly type = '[Form] RFMS Form Status';
  constructor(public customerNumber: string) { }
}

export class MobileVerifyStartAction {
  public static readonly type = '[User] mobile verify start';

  constructor(public body: MobileVerifyStartModel) {
  }
}

export class MobileVerifySendAction {
  public static readonly type = '[User] mobile verify send';

  constructor(public code: MobileVerifySendModel) {
  }
}

export class MobileVerifyClearAction {
  public static readonly type = '[User] mobile verify clear';

  constructor() {
  }
}
