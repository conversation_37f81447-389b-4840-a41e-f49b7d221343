import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { RfmsFormStatusModel, RfmsIgnoreReasonsFormModel } from '../../model/form.model';
import { FormService } from '../../service/form.service';
import { UserService } from '../../service/user.service';
import {
  GetRfmsFormStatusAction,
  MobileVerifyClearAction,
  MobileVerifySendAction,
  MobileVerifyStartAction,
  RfmsIgnoreReasonsFormAction,
  SendRfmsIgnoreReasonsFormAction
} from './form.actions';


export interface FormStateModel {
  rfmsIgnoreReasonsForm: RfmsIgnoreReasonsFormModel[];
  sendRfmsIgnoreReasonsForm: any;
  rfmsFormStatus: RfmsFormStatusModel;
  formLoading: boolean;
  mobileVerifyStart: any;
  mobileVerifySend: any;
}

@State<FormStateModel>({
  name: 'form',
  defaults: {
    rfmsIgnoreReasonsForm: null,
    sendRfmsIgnoreReasonsForm: null,
    rfmsFormStatus: null,
    formLoading: false,
    mobileVerifyStart: null,
    mobileVerifySend: null,
  },
})
@Injectable()
export class FormState {
  constructor(
    private readonly formService: FormService,
    private readonly userService: UserService,
  ) { }

  @Selector()
  public static formLoading({ formLoading }: FormStateModel): boolean {
    return formLoading;
  }

  @Selector()
  public static getRfmsIgnoreReasonsForm({ rfmsIgnoreReasonsForm }: FormStateModel): RfmsIgnoreReasonsFormModel[] {
    return rfmsIgnoreReasonsForm;
  }

  @Selector()
  public static sendRfmsIgnoreReasonsForm({sendRfmsIgnoreReasonsForm}: FormStateModel): any {
    return sendRfmsIgnoreReasonsForm;
  }

  @Selector()
  public static getRfmsFormStatus({rfmsFormStatus}: FormStateModel): RfmsFormStatusModel {
    return rfmsFormStatus;
  }

  @Selector()
  public static mobileVerifyStart({ mobileVerifyStart }: FormStateModel) {
    return mobileVerifyStart;
  }

  @Selector()
  public static mobileVerifySend({ mobileVerifySend }: FormStateModel) {
    return mobileVerifySend;
  }


  @Action(RfmsIgnoreReasonsFormAction)
  rfmsIgnoreReasonsFormState(
    { patchState }: StateContext<FormStateModel>
  ) {
    patchState({
      formLoading: true,
    });
    return this.formService.rfmsIgnoRereasonsFormGet().pipe(
      tap((value) => {
        patchState({
          formLoading: false,
          rfmsIgnoreReasonsForm: value,
        });
      }),
      catchError((err) => {
        patchState({
          formLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(SendRfmsIgnoreReasonsFormAction)
  sendRfmsIgnoreReasonsFormState(
    { patchState, getState }: StateContext<FormStateModel>,
    { rfmsFormDataSend }: SendRfmsIgnoreReasonsFormAction
  ) {
    patchState({
      formLoading: true,
    });
    return this.formService.rfmsIgnoRereasonsFormSend(rfmsFormDataSend).pipe(
      tap((value) => {
        patchState({
          formLoading: false,
          sendRfmsIgnoreReasonsForm: value,
        });
      }),
      catchError((err) => {
        patchState({
          formLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetRfmsFormStatusAction)
  getRfmsFormStatusState(
    { patchState, getState }: StateContext<FormStateModel>,
    { customerNumber }: GetRfmsFormStatusAction
  ) {
    patchState({
      formLoading: true,
    });
    return this.formService.getRfmsFormStatus(customerNumber).pipe(
      tap((value) => {
        patchState({
          formLoading: false,
          rfmsFormStatus: value,
        });
      }),
      catchError((err) => {
        patchState({
          formLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(MobileVerifyStartAction)
  mobileVerifyStartState(
    { getState, patchState }: StateContext<FormStateModel>,
    { body }: MobileVerifyStartAction
  ) {
    patchState({
      formLoading: true,
    });
    return this.userService.mobileVerifyStart(body)
      .pipe(
        tap((value) => {
          patchState({
            mobileVerifyStart: value,
            formLoading: false,
          });
        }),
        catchError((err) => {
          patchState({
            formLoading: false,
          });
          return throwError(err);
        })
      );
  }

  @Action(MobileVerifySendAction)
  sendMobileVerifyState(
    { getState, patchState }: StateContext<FormStateModel>,
    { code }: MobileVerifySendAction
  ) {
    patchState({
      formLoading: true,
    });
    return this.userService.mobileVerifySend(code)
      .pipe(
        tap((value) => {
          patchState({
            mobileVerifySend: value,
            formLoading: false,
          });
        }),
        catchError((err) => {
          patchState({
            formLoading: false,
          });
          return throwError(err);
        })
      );
  }

  @Action(MobileVerifyClearAction)
  clearMobileVerifyState(
    { patchState }: StateContext<FormStateModel>,
  ) {
    patchState({
      mobileVerifyStart: null,
      mobileVerifySend: null,
    });
  }
}
