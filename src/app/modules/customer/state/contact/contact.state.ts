import { Action, Selector, State, StateContext } from '@ngxs/store';
import { Injectable } from '@angular/core';
import { catchError, tap } from 'rxjs/operators';
import { of, throwError } from 'rxjs';
import { ContactLocationModel, FAQModel, LeaderCallDetail, LeaderUser, MobileSupportSubjectsModel, } from '../../model/contact';
import { ContactService } from '../../service/contact.service';
import {
  ContactAllLocationAction,
  ContactLocationAction,
  FAQAction,
  GetVideoCallLeaderDetail,
  GetVideoCallLeaders,
  GetVideoCallLeaderStatus,
  MobileSupportSubjectsAction,
} from './contact.actions';
import { CompanyDto } from '../../model/company';
import { LeaderCallStatusEnum } from '../../../definition/enum/leader-call-status.enum';

export interface ContactStateModel {
  loading: boolean;
  companyLocations: CompanyDto[];
  allLocations: ContactLocationModel[];
  FAQs: FAQModel[];
  mobileSupportSubjects: MobileSupportSubjectsModel[];
  videoCallLeaders: LeaderUser[];
  videoCallLeaderDetail: LeaderCallDetail;
  videoCallLeaderDetailLoading: boolean;
  videoCallLeaderStatus: number;
}

@State<ContactStateModel>({
  name: 'contact',
  defaults: {
    loading: false,
    companyLocations: [],
    allLocations: [],
    FAQs: [],
    mobileSupportSubjects: [],
    videoCallLeaders: [],
    videoCallLeaderDetail: null,
    videoCallLeaderDetailLoading: false,
    videoCallLeaderStatus: LeaderCallStatusEnum.offline,
  },
})
@Injectable()
export class ContactState {
  constructor(private readonly service: ContactService) {}

  @Selector()
  public static loading({ loading }: ContactStateModel) {
    return loading;
  }

  @Selector()
  public static getState(state: ContactStateModel) {
    return state;
  }

  @Selector()
  public static companyLocations({ companyLocations }: ContactStateModel) {
    return companyLocations;
  }

  @Selector()
  public static allLocations({
    allLocations,
  }: ContactStateModel): ContactLocationModel[] {
    return allLocations;
  }

  @Selector()
  public static mobileSupportSubjects({
    mobileSupportSubjects,
  }: ContactStateModel): MobileSupportSubjectsModel[] {
    return mobileSupportSubjects;
  }

  @Selector()
  public static faqList({ FAQs }: ContactStateModel): FAQModel[] {
    return FAQs;
  }

  @Selector()
  public static videoCallLeaders({
    videoCallLeaders,
  }: ContactStateModel): LeaderUser[] {
    return videoCallLeaders;
  }

  @Selector()
  public static videoCallLeaderDetail({
    videoCallLeaderDetail,
  }: ContactStateModel): LeaderCallDetail {
    return videoCallLeaderDetail;
  }

  @Selector()
  public static videoCallLeaderDetailLoading({
    videoCallLeaderDetailLoading,
  }: ContactStateModel) {
    return videoCallLeaderDetailLoading;
  }

  @Selector()
  public static videoCallLeaderStatus({
    videoCallLeaderStatus,
  }: ContactStateModel) {
    return videoCallLeaderStatus;
  }

  @Action(ContactLocationAction)
  customerContactAction(
    { patchState, getState }: StateContext<ContactStateModel>,
    { company }: ContactLocationAction
  ) {
    patchState({
      loading: true,
    });
    const currentCompanyLocations = getState().companyLocations;
    if (!currentCompanyLocations.some((c) => c.id === company.id)) {
      return this.service.locations(company.id).pipe(
        tap((value) => {
          const current = getState().companyLocations;
          patchState({
            companyLocations: current.concat([
              { ...company, locations: value },
            ]),
            loading: false,
          });
        }),
        catchError((err) => {
          patchState({
            loading: false,
          });
          return throwError(err);
        })
      );
    }
    return of(currentCompanyLocations);
  }

  @Action(ContactAllLocationAction)
  allContactAction({ patchState }: StateContext<ContactStateModel>) {
    patchState({
      loading: true,
    });
    return this.service.allLocations().pipe(
      tap((value) => {
        patchState({
          allLocations: value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(FAQAction)
  FAQAction({ patchState }: StateContext<ContactStateModel>) {
    patchState({
      loading: true,
    });

    return this.service.FAQ().pipe(
      tap((value) => {
        patchState({
          FAQs: value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(MobileSupportSubjectsAction)
  mobileSupportSubjectsAction({ patchState }: StateContext<ContactStateModel>) {
    patchState({
      loading: true,
    });
    return this.service.mobileSupportSubjects().pipe(
      tap((value) => {
        patchState({
          mobileSupportSubjects: value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetVideoCallLeaders)
  getVideoCallLeaders({ patchState }: StateContext<ContactStateModel>, { loading }: GetVideoCallLeaders) {
    patchState({
      loading,
    });
    return this.service.videoCallLeaders().pipe(
      tap((value) => {
        patchState({
          videoCallLeaders: value,
          loading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetVideoCallLeaderDetail)
  getVideoCallLeaderDetail(
    { patchState }: StateContext<ContactStateModel>,
    { payload }: GetVideoCallLeaderDetail
  ) {
    patchState({
      videoCallLeaderDetailLoading: true,
    });
    return this.service.videoCallLeaderDetail(payload).pipe(
      tap((value) => {
        patchState({
          videoCallLeaderDetail: value,
          videoCallLeaderDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          videoCallLeaderDetailLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetVideoCallLeaderStatus)
  getVideoCallLeaderStatus(
    { patchState }: StateContext<ContactStateModel>,
    { queueCode, loading }: GetVideoCallLeaderStatus
  ) {
    patchState({
      videoCallLeaderDetailLoading: loading,
    });
    const payload = {
      queueCode
    };
    return this.service.videoCallLeaderStatus(payload).pipe(
      tap((value) => {
        patchState({
          videoCallLeaderStatus: value,
          videoCallLeaderDetailLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          videoCallLeaderDetailLoading: false,
        });
        return throwError(err);
      })
    );
  }
}
