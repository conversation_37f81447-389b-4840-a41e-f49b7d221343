import { CompanyDto } from '../../model/company';

export class ContactLocationAction {
  public static readonly type = '[Contact] Location';

  constructor(public company: CompanyDto) {}
}

export class ContactAllLocationAction {
  public static readonly type = '[Contact] All Location';

  constructor() {}
}

export class FAQAction {
  public static readonly type = '[Contact] FAQ';

  constructor() {}
}


export class MobileSupportSubjectsAction {
  public static readonly type = '[Contact] MobileSupportSubjects';

  constructor() {}
}

export class GetVideoCallLeaders {
  public static readonly type = '[Contact] GetVideoCallLeaders';

  constructor(public loading = true) {}
}

export class GetVideoCallLeaderDetail {
  public static readonly type = '[Contact] GetVideoCallLeaderDetail';

  constructor(public payload: any) {}
}

export class GetVideoCallLeaderStatus {
  public static readonly type = '[Contact] GetVideoCallLeaderStatus';

  constructor(public queueCode: string, public loading: boolean = true) {}
}
