import { SanitizedCustomerModel } from '../../model/sanitized-customer.model';

export class UserAction {
  public static readonly type = '[User] get me';

  constructor(public showLoading = true, public forceReload = false) {
  }
}

export class ChangeCustomerAction {
  public static readonly type = '[Customer] change';

  constructor(public customer: SanitizedCustomerModel) { }
}

export class UpdateUserLoadingAction {
  public static readonly type = '[User] loading';

  constructor(public loading: boolean) {
  }
}

export class FindAndChangeCustomerAction {
  public static readonly type = '[User] Find and change customer';

  constructor(public customerNumber: string) { }
}
