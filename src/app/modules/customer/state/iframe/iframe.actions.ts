import { IframeStateModel } from './iframe.state';
import { IncomingMessageEnum } from 'src/app/core/enum/incoming-message.enum';

export class IframeAction {
  public static readonly type = '[Iframe] set';

  constructor(public payload: Partial<IframeStateModel>, public withOtt = false) {}
}

export class IframeUrlAction {
  public static readonly type = '[Iframe] set url';

  constructor(public url: string) {}
}

export class CloseIframeAction {
  public static readonly type = '[Iframe] close';

  constructor() {}
}

export class IframeCloseHeaderUrlAction {
  public static readonly type = '[Iframe] close header';

  constructor() {}
}

export class PostMessageAction {
  public static readonly type = '[Iframe] post message';

  constructor(public action: IncomingMessageEnum, public data: any = null) {}
}

export class GetModuleAction {
  public static readonly type = '[Iframe] get module action';

  constructor(public module: any) {}
}