import { Location } from '@angular/common';
import { Injectable } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { Navigate } from '@ngxs/router-plugin';
import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { IncomingMessageEnum } from 'src/app/core/enum/incoming-message.enum';
import { querySerialize } from 'src/app/util/query-encode.util';

import { environment } from '../../../../../environments/environment';
import { UpdateLoadingAction } from '../../../authentication/state/login/login.actions';
import { CustomerService } from '../../service/customer.service';
import { HeaderStatusAction } from '../customer/customer.actions';
import { HeaderStateModel } from '../customer/customer.state';
import { CloseIframeAction, IframeAction, IframeCloseHeaderUrlAction, IframeUrlAction, PostMessageAction, GetModuleAction } from './iframe.actions';

export interface IframeStateModel {
  active: boolean;
  loading: boolean;
  pageTitle: string;
  closeButton: boolean;
  backButton: boolean;
  url: string;
  params: any;
  previous: {
    headerStatus: HeaderStateModel;
    url?: string;
    navigate?: string;
  };
  postMessage: {
    type: IncomingMessageEnum;
    data: any;
  };
  currentModule: any;
}

@State<IframeStateModel>({
  name: 'iframe',
  defaults: {
    active: false,
    loading: false,
    pageTitle: null,
    closeButton: false,
    backButton: false,
    url: null,
    params: null,
    previous: {
      headerStatus: null,
      url: null,
      navigate: null
    },
    postMessage: {
      type: null,
      data: null
    },
    currentModule: null
  }
})
@Injectable()
export class IframeState {
  constructor(
    private readonly sanitizer: DomSanitizer,
    private readonly customerService: CustomerService,
    private readonly store: Store,
    private readonly location: Location) { }

  @Selector()
  public static getState(state: IframeStateModel) {
    return state;
  }

  @Selector()
  public static active({ active }: IframeStateModel): boolean {
    return active;
  }

  @Selector()
  public static postMessage({
    postMessage
  }: IframeStateModel): {
    type: IncomingMessageEnum;
    data: any;
  } {
    return postMessage;
  }

  @Selector()
  public static currentModule({
    currentModule
  }: IframeStateModel){
    return currentModule
  }

  @Action(IframeAction)
  public iframeAction({ patchState }: StateContext<IframeStateModel>, { payload, withOtt }: IframeAction) {
    if (payload.url) {
      this.store.dispatch(new UpdateLoadingAction(true));

      let url = payload.url;
      if (!environment.production) {
        url = url.replace(/https:\/\/prod\.borusancat.com\/lgn[dqp]\/mobileview/gm, environment.frameUrl);
      }
      if (withOtt) {
        payload.url = null;

        this.customerService.getOTT().subscribe((ott) => {
          const params = {
            ott: ott.oneTimeToken,
            ...payload.params || {}
          };
          const serializeParams = querySerialize(params); // Serialize param return k = v & ....
          const urlParts = url.split('#');
          const glue = url.includes('?') ? '&' : '?';
          if (urlParts[1]) {
            url = urlParts[0] + glue + serializeParams + '#' + urlParts[1];
          } else {
            url = url + glue + serializeParams;
          }

          url = this.sanitizer.bypassSecurityTrustResourceUrl(url) as any;

          patchState({ url });
        });
      } else {
        payload.url = this.sanitizer.bypassSecurityTrustResourceUrl(url) as any;
      }
    }
    patchState(payload);
  }

  @Action(IframeUrlAction)
  public iframeUrlAction({ patchState }: StateContext<IframeStateModel>, urlAction: IframeUrlAction) {
    patchState({
      url: this.sanitizer.bypassSecurityTrustResourceUrl(urlAction.url) as any
    });
  }

  @Action(IframeCloseHeaderUrlAction)
  public IframeCloseHeaderUrlAction({ patchState }: StateContext<IframeStateModel>) {
    patchState({
      active: false,
      pageTitle: null,
      closeButton: false,
      backButton: false,
      url: null,
      previous: {
        headerStatus: null,
        url: null
      }
    });
  }

  @Action(CloseIframeAction)
  public CloseIframeAction({ getState, patchState }: StateContext<IframeStateModel>, closeAction: CloseIframeAction) {
    const previous = Object.assign({}, getState().previous);

    if (previous.url) {
      // this.store.dispatch(new Navigate([previous.url]));
      if (previous.url === 'history.back') {
        this.location.back();
      } else {
        this.location.go(previous.url);
      }
    } else {
      this.store.dispatch(new Navigate([previous.navigate || '']));
    }

    if (previous.headerStatus) {
      this.store.dispatch(new HeaderStatusAction(previous.headerStatus));
      previous.headerStatus = null;
    } else {
      this.store.dispatch(
        new HeaderStatusAction({
          company: true,
          notificationIcon: true
        })
      );
    }
    patchState({
      active: false,
      pageTitle: null,
      closeButton: false,
      backButton: false,
      params: null,
      previous
    });
  }

  @Action(PostMessageAction)
  PostMessageAction({ patchState }: StateContext<IframeStateModel>, { action, data }: PostMessageAction) {
    patchState({ postMessage: { type: action, data } });
  }

  @Action(GetModuleAction)
  public GetModuleRoute({ getState, patchState }: StateContext<IframeStateModel>, { module }: GetModuleAction ) {
    patchState({
      currentModule: module
    });
  }
}
