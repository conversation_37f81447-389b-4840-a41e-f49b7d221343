import {Injectable} from '@angular/core';
import {
  ClearVideocallAvailableAgentAction,
  GetVideocallAvailableAgentAction,
  VideocallClearAction,
  VideocallGetFaultyComponentsAction,
  VideocallLogAction,
  VideocallSetupAction, VideocallStartAction, VideocallUpdateAction
} from './videocall.action';
import {catchError, tap} from 'rxjs/operators';
import {throwError} from 'rxjs';
import {
  VideocallAvailableAgentModel,
  VideocallFormFaultyComponentsModel,
  VideocallFormValuesModel,
  VideocallSetupModel
} from '../../model/videocal.model';
import {VideoCallService} from '../../service/video-call.service';
import {Action, Selector, State, StateContext} from '@ngxs/store';

export interface VideocallStateModel {
  videocallValues: VideocallFormValuesModel;
  videocallLoading: boolean;
  videocallFormFaultyComponents: VideocallFormFaultyComponentsModel[];
  videocallUpdate: boolean;
  videocallSetup: VideocallSetupModel;
  videocallLogSended: boolean;
  videocallAvailableAgent: VideocallAvailableAgentModel;
  videocallAgentLoading: boolean;
}

@State<VideocallStateModel>({
  name: 'videocall',
  defaults: {
    videocallValues: null,
    videocallLoading: false,
    videocallFormFaultyComponents: null,
    videocallUpdate: null,
    videocallSetup: null,
    videocallLogSended: false,
    videocallAvailableAgent: null,
    videocallAgentLoading: false,
  }
})
@Injectable()
export class VideocallState {

  constructor(
    private readonly videocallService: VideoCallService,
  ) { }

  @Selector()
  public static getVideocallLoading({ videocallLoading }: VideocallStateModel): boolean {
    return videocallLoading;
  }

  @Selector()
  public static getVideocallAgentLoading({ videocallAgentLoading }: VideocallStateModel): boolean {
    return videocallAgentLoading;
  }

  @Selector()
  public static getVideocallValues({ videocallValues }: VideocallStateModel): VideocallFormValuesModel {
    return videocallValues;
  }

  @Selector()
  public static getVideocallFormFaultyComponents({ videocallFormFaultyComponents }: VideocallStateModel)
    : VideocallFormFaultyComponentsModel[] {
    return videocallFormFaultyComponents;
  }

  @Selector()
  public static getVideocallSetup({ videocallSetup }: VideocallStateModel): VideocallSetupModel {
    return videocallSetup;
  }

  @Selector()
  public static sendedVideocallLog({ videocallLogSended }: VideocallStateModel): boolean {
    return videocallLogSended;
  }

  @Selector()
  public static getVideocallAvailableAgent({ videocallAvailableAgent }: VideocallStateModel): VideocallAvailableAgentModel {
    return videocallAvailableAgent;
  }

  @Action(VideocallGetFaultyComponentsAction)
  getVideocallFormFaultyComponentsState(
    { patchState }: StateContext<VideocallStateModel>,
  ) {
    patchState({
      videocallLoading: true
    });
    return this.videocallService.getFaultyComponents().pipe(
      tap((value) => {
        patchState({
          videocallLoading: false,
          videocallFormFaultyComponents: value
        });
      }),
      catchError((err) => {
        patchState({
          videocallLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(VideocallStartAction)
  startVideocallState(
    { patchState }: StateContext<VideocallStateModel>,
    { form }: VideocallStartAction
  ) {
    patchState({
      videocallLoading: true
    });
    return this.videocallService.startVideocallForm(form).pipe(
      tap((value) => {
        patchState({
          videocallLoading: false,
          videocallValues: value
        });
      }),
      catchError((err) => {
        patchState({
          videocallLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(VideocallUpdateAction)
  updateVideocallState(
    { patchState }: StateContext<VideocallStateModel>,
    { form }: VideocallUpdateAction
  ) {
    patchState({
      videocallLoading: true
    });
    return this.videocallService.updateVideocallForm(form).pipe(
      tap((value) => {
        patchState({
          videocallLoading: false,
          videocallUpdate: value === 'OK',
        });
      }),
      catchError((err) => {
        patchState({
          videocallLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(VideocallSetupAction)
  getVideocallSetupState(
    { patchState }: StateContext<VideocallStateModel>,
    { provider, queueName }: VideocallSetupAction
  ) {
    patchState({
      videocallLoading: true
    });
    return this.videocallService.setupVideocall(provider, queueName).pipe(
      tap((value) => {
        patchState({
          videocallLoading: false,
          videocallSetup: value,
        });
      }),
      catchError((err) => {
        patchState({
          videocallLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetVideocallAvailableAgentAction)
  getVideocallAvailableAgentState(
    { patchState }: StateContext<VideocallStateModel>,
    { queue }: GetVideocallAvailableAgentAction
  ) {
    patchState({
      videocallLoading: true,
      videocallAgentLoading: true,
    });
    return this.videocallService.getVideocallAvailableAgent(queue).pipe(
      tap((n) => {
        patchState({
          videocallAvailableAgent: n,
          videocallLoading: false,
          videocallAgentLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          videocallLoading: false,
          videocallAgentLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(VideocallLogAction)
  sendVideocallLogState(
    { patchState, getState }: StateContext<VideocallStateModel>,
    { form }: VideocallLogAction
  ) {
    patchState({
      videocallLoading: true
    });
    return this.videocallService.logVideocall(form).pipe(
      tap((value) => {
        patchState({
          videocallLoading: false,
          videocallLogSended: value
        });
      }),
      catchError((err) => {
        patchState({
          videocallLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(VideocallClearAction)
  clearVideocallState(
    { patchState }: StateContext<VideocallStateModel>,
  ) {
    patchState({
      videocallValues: null,
      videocallSetup: null,
      videocallAvailableAgent: null,
    });
  }

  @Action(ClearVideocallAvailableAgentAction)
  clearVideocallAvailableAgentState(
    { patchState }: StateContext<VideocallStateModel>,
  ) {
    patchState({
      videocallAvailableAgent: null,
    });
  }
}
