export class CatalogAction {
  public static readonly type = '[Catalog] get menu';

  constructor(public publicMenuHeaderCompany: string, public showLoading = true) {}
}

export class CatalogFindAction {
  public static readonly type = '[Catalog] find catalog menu';

  constructor(public findMenuId: string, public findProductId?: string) {}
}

export class UpdateCatalogAction {
  public static readonly type = '[Catalog] update';

  constructor() {}
}

export class FetchCategoryAction {
  public static readonly type = '[Catalog] fetch category';

  constructor(
    public url: string,
    public hierarchy: any,
    public backUrl: string = null
  ) {}
}

export class GetCampaignsAction {
  public static readonly type = '[Catalog] Get Campaigns';

  constructor(public url: string) {}
}

export class OpenMobileCatalogAction {
  public static readonly type = '[Common] Open Catalog';

  constructor(public payload: any) { }
}

export class AddToShoppingCart {
  public static readonly type = '[Catalog] add to shopping cart'

  constructor(public product: any) { }
}

export class UpdateShoppingCartAction {
  public static readonly type = '[Catalog] update shopping cart'

  constructor(public id: string, public increase?: boolean) {}
}

export class RemoveFromShoppingCart {
  public static readonly type = '[Catalog] remove from shopping cart'

  constructor(public id: string) {}
}

export class ResetShoppingCartAction {
  public static readonly type = '[Catalog] reset shopping cart'

  constructor(){}
}

export class ShoppingCartTotalPriceAction {
  public static readonly type = '[Catalog] total shopping cart price'

  constructor(){}
}

export class GetCatalogEquipmentDetailAction {
  public static readonly type = '[Catalog] Get Catalog Equipment Detail';

  constructor(public id: any, public lastFetchedId?: string) {}
}

export class ResetCatalogEquipmentDetailAction {
  public static readonly type = '[Catalog] Reset Catalog Equipment Detail';

  constructor(){};
}

export class AddToCartPopupAction {
  public static readonly type = '[Catalog] ShoppingCart Popup'

  constructor(public show: boolean, public text?: string) {}
}

export class ResetCampaignAction {
  public static readonly type = '[Catalog] reset campaign';

  constructor(){}
}
