import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { catchError, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { CustomerService } from '../../service/customer.service';
import {
  CatalogAction,
  CatalogFindAction,
  FetchCategoryAction,
  GetCampaignsAction,
  OpenMobileCatalogAction,
  UpdateCatalogAction,
  AddToShoppingCart,
  RemoveFromShoppingCart,
  UpdateShoppingCartAction,
  ResetShoppingCartAction,
  ShoppingCartTotalPriceAction,
  GetCatalogEquipmentDetailAction,
  AddToCartPopupAction,
  ResetCatalogEquipmentDetailAction,
  ResetCampaignAction
} from './catalog.actions';
import { CampaignModel, MenuModel, MenuTypes } from '../../model/menu.model';
import { Injectable } from '@angular/core';
import { UserState } from '../user/user.state';
import { FrameMessageEnum } from '../../../../core/enum/frame-message.enum';
import { CommonStateModel } from '../../../../shared/state/common/common.state';
import { FrameMessageService } from '../../../../core/service/frame-message.service';
import {FoundedCatalogModel} from '../../model/category.model';

export interface CatalogStateModel {
  loading: boolean;
  catalog: MenuModel[];
  campaigns: CampaignModel[];
  foundedCatalog: FoundedCatalogModel;
  shoppingCart: any[];
  shoppingCartTotalPrice: string | number;
  catalogEquipmentDetail: any;
  catalogEquipmentDetailLoading: boolean;
  addToCartPopup: any;
}

@State<CatalogStateModel>({
  name: 'catalog',
  defaults: {
    loading: false,
    catalog: [],
    campaigns: [],
    foundedCatalog: null,
    shoppingCart: [],
    shoppingCartTotalPrice: 0,
    catalogEquipmentDetail: null,
    catalogEquipmentDetailLoading: false,
    addToCartPopup: null
  },
})
@Injectable()
export class CatalogState {
  constructor(
    private readonly customerService: CustomerService,
    private readonly store: Store,
    private readonly frameMessageService: FrameMessageService,
  ) {}

  @Selector()
  public static getState(state: CatalogStateModel) {
    return state;
  }

  @Selector()
  public static loading({ loading }: CatalogStateModel): boolean {
    return loading;
  }

  @Selector()
  public static campaigns({ campaigns }: CatalogStateModel): CampaignModel[] {
    return campaigns;
  }

  @Selector()
  public static catalog({ catalog }: CatalogStateModel): MenuModel[] {
    return catalog;
  }

  @Selector()
  public static foundedCatalog({ foundedCatalog }: CatalogStateModel): FoundedCatalogModel {
    return foundedCatalog;
  }

  @Selector()
  public static shoppingCart({ shoppingCart }: CatalogStateModel): any[] {
    return shoppingCart;
  }

  @Selector()
  public static shoppingCartTotalPrice({ shoppingCartTotalPrice }: CatalogStateModel): number | string {
    return shoppingCartTotalPrice;
  }

  @Selector()
  public static catalogEquipmentDetail({ catalogEquipmentDetail }: CatalogStateModel): any {
    return catalogEquipmentDetail;
  }

  @Selector()
  public static catalogEquipmentDetailLoading({ catalogEquipmentDetailLoading }: CatalogStateModel): boolean {
    return catalogEquipmentDetailLoading;
  }


  @Selector()
  public static addToShoppingCartPopup({ addToCartPopup }: CatalogStateModel) {
    return addToCartPopup;
  }

  @Action(UpdateCatalogAction)
  public updateCatalogAction(
    { getState, patchState }: StateContext<CatalogStateModel>
  ) {
    // const catalog = Object.assign({}, getState().catalog);
    const catalog = JSON.parse(JSON.stringify(getState().catalog));

    patchState({
      catalog,
    });
  }

  @Action(CatalogAction)
  public customerMenuAction(
    { patchState }: StateContext<CatalogStateModel>,
    { publicMenuHeaderCompany, showLoading }: CatalogAction
  ) {
    patchState({
      loading: showLoading,
    });
    return this.customerService.getMenu(publicMenuHeaderCompany).pipe(
      tap((value) => {
        patchState({
          loading: false,
          catalog: value,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(CatalogFindAction)
  public customerFindMenuAction(
    { patchState }: StateContext<CatalogStateModel>,
    { findMenuId, findProductId }: CatalogFindAction
  ) {
    patchState({
      loading: true,
    });
    return this.customerService.findMenuCatalog(findMenuId, findProductId).pipe(
      tap((value) => {
        patchState({
          loading: false,
          foundedCatalog: value,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(FetchCategoryAction)
  public fetchCategory(
    { getState, patchState }: StateContext<CatalogStateModel>,
    { url, hierarchy, backUrl }: FetchCategoryAction
  ) {
    patchState({
      loading: true,
    });

    const currentCustomer = this.store.selectSnapshot(UserState.currentCustomer);

    return this.customerService.fetchCatalog(url, currentCustomer?.publicMenuHeaderCompany).pipe(
      tap((value) => {
        console.log('fetching catalog', { hierarchy, url });
        const allData = JSON.parse(JSON.stringify(getState().catalog));
        let that: any = allData;
        let before: any;

        hierarchy.map((item) => {
          before = that;
          // const index = isNaN(item) ? item : parseInt(item);
          // that = that[index];
          const itemId = item;
          that = item === 'categories' && that?.categories?.length
            ? that[item]
            : that && !that?.newFetchUrl
            ? that?.find(x => x.id === itemId)
            : null;
          if (!that) {
            that = before;
          }
        });

        that.categories = value;
        that.fetchUrl = null;
        that.newFetchUrl = null;
        that.fetched = true;
        that.viewType = MenuTypes.card;
        that.backUrl = backUrl;

        console.log(allData);

        patchState({
          loading: false,
          catalog: allData,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(GetCampaignsAction)
  public getCampaigns(
    { patchState }: StateContext<CatalogStateModel>,
    { url }: GetCampaignsAction
  ) {
    patchState({
      loading: true,
    });
    return this.customerService.fetchCampaigns(url).pipe(
      tap((value) => {
        patchState({
          loading: false,
          campaigns: value,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(OpenMobileCatalogAction)
  public openMobileCatalog(
    { getState }: StateContext<CommonStateModel>,
    { payload }: OpenMobileCatalogAction
  ) {
    const user = this.store.selectSnapshot(UserState.basics);
    this.frameMessageService.sendMessage(FrameMessageEnum.openCatalog, payload, {
      user
    });
  }

  @Action(GetCatalogEquipmentDetailAction)
  public GetCatalogEquipmentDetailAction(
    { patchState }: StateContext<CatalogStateModel>,
    { id, lastFetchedId }: GetCatalogEquipmentDetailAction
  ) {
    patchState({
      catalogEquipmentDetailLoading: true
    });

    return this.customerService.getCatalogEquipmentDetail(id, lastFetchedId).pipe(
      tap((data) => {
        patchState({
          catalogEquipmentDetailLoading: false,
          catalogEquipmentDetail: data,
        });
      }),
      catchError((err) => {
        patchState({
          catalogEquipmentDetailLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(ResetCatalogEquipmentDetailAction)
  public ResetCatalogEquipmentDetailAction(
    { patchState }: StateContext<CatalogStateModel>,
  ) {
    return patchState({
      catalogEquipmentDetail: null,
    });
  }

  @Action(AddToShoppingCart)
  public addToShoppingCart(
    { patchState, getState }: StateContext<CatalogStateModel>,
    { product }: AddToShoppingCart
  ) {
    const shoppingCart = getState().shoppingCart;
    const productInShoppingCart = shoppingCart.find(p => p.productId === product.productId);
    if (productInShoppingCart){
      const filteredSameProduct = shoppingCart.filter(p => p.productId !== productInShoppingCart.productId);
      const increasedAmountProduct = {...productInShoppingCart, amount: product.amount + productInShoppingCart.amount};
      patchState({
        shoppingCart: [...filteredSameProduct, increasedAmountProduct],
      });
    } else{
      patchState({
        shoppingCart: [...shoppingCart, product],
      });
    }
  }

  @Action(RemoveFromShoppingCart)
  public removeFromShoppingCart(
    { patchState, getState }: StateContext<CatalogStateModel>,
    { id }: RemoveFromShoppingCart
  ) {
    const shoppingCart = getState().shoppingCart;
    const filteredShoppingCart = shoppingCart.filter(p => p.productId !== id);

    patchState({
      shoppingCart: filteredShoppingCart
    });
  }

  @Action(UpdateShoppingCartAction)
  public updateShoppingCartAction(
    { patchState, getState }: StateContext<CatalogStateModel>,
    { id, increase = true }: UpdateShoppingCartAction
  ) {
    const shoppingCart = getState().shoppingCart;
    const productIndex = shoppingCart.findIndex(p => p.productId === id);
    const updatesShoppingCart = [...shoppingCart];

    updatesShoppingCart[productIndex] =
    {...updatesShoppingCart[productIndex], amount: increase ? updatesShoppingCart[productIndex].amount + 1 : updatesShoppingCart[productIndex].amount - 1 };

    patchState({
      shoppingCart: updatesShoppingCart
    });
  }

  @Action(ResetShoppingCartAction)
  public resetShoppingCartAction(
    { patchState }: StateContext<CatalogStateModel>
  ) {
    patchState({
      shoppingCart: []
    });
  }

  @Action(ShoppingCartTotalPriceAction)
  public shoppingCartTotalPriceAction(
    { patchState, getState }: StateContext<CatalogStateModel>
  ){
    const shoppingCart = getState().shoppingCart;

    patchState({
      shoppingCartTotalPrice: shoppingCart.reduce((total, item) => total + (item.price * (item?.amount || 1)), 0)
    });
  }

  @Action(AddToCartPopupAction)
  public addToCartPopupAction(
    { patchState }: StateContext<CatalogStateModel>,
    { show = true, text }: AddToCartPopupAction
  ){
    patchState({
      addToCartPopup: {show, text}
    });
  }

  @Action(ResetCampaignAction)
  public resetCampaignAction(
    { patchState }: StateContext<CatalogStateModel>
  ){
    patchState({
      campaigns: [],
      loading: true
    });
  }
}
