export interface EquipmentModel {
  id: string;
  productId: string;
  productCode: string;
  make: string;
  applicationCode: string;
  yearOfManufacture: string;
  division: string;
  displayModel: string;
  empc: string;
  equipmentType: string;
  pwc: string;
  pwcText: string;
  emc: string;
  engineModel: string;
  territory: string;
  dueDate: string;
  arrangementNumber: string;
  transactionStoreCode: string;
  addressId: string;
  workingHours: number;
  serialNumber: string;
  revisionIndicator: string;
  material: string;
  mainParty: string;
  counter: string;
  name: string;
  base64EncodedContent: string;
  customerId: string;
  engineSerialNumber: string;
  readingUnit: string;
  readingDate: string;
  openZMDV: number;
  completedZMDV: number;
  guaranteeEndDate: string;
  mda: string;
  city?: string;
  region?: string;
  isProductLink?: boolean;
  productLinkColor?: string;

  brand: string;
  equipmentNumber: string;
  location: {
    locationName: string;
    latitude: string;
    longtitude: string;
  };
  model: string;
  serviceInfo: {
    startDate: Date;
    serviceStatus: string;
    serviceType: string;
    workingType: string;
    description: string;
  };
  serviceHistory: {
    startDate: string;
    serviceStatus: string;
    serviceType: string;
    workingType: string;
    description: string;
    equipmentGuid: string;
    serviceNumber: string;
    serviceStatusColor: string;
    serviceStatusDescription: string;
    equipmentNumber: string;
  }[];
  pssrList: PssrListModel[];
  workingHourDate: Date;
  workingHoursUnit: null;
  productHierarchy: string;
  equipmentRevisionCampaign?: EquipmentRevisionCampaignModel;
}

export interface PssrListModel {
  telephoneList: TelephoneList[];
  pssrName: string;
}

export interface TelephoneList {
  telephoneNumber: string;
  telephoneNumberExtension: string;
}

export interface EquipmentRevisionCampaignModel {
  id: string;
  description: string;
  amount?: number;
  currency?: string;
  title: string;
  type: string;
}
export interface SosAnalyzePdfModel {
  base64Pdf: string;
  compartmentSampleList: SosCompartmentSampleList[];
}
export interface SosCompartmentSampleList {
  level: string;
  overallInterpretation: string;
  serialNumber: string;
  compartmentName: string;
  compartmentDescription: string;
  controlNumber: string;
  sampleDate: Date;
  analysisDate: Date;
}
