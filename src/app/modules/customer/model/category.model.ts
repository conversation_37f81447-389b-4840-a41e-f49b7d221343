export interface CategoryModel {
  title: string;
  imageUrl: string;
  // viewType: string;
  categories?: CategoryModel[];
  detail?: boolean;
  brand?: string;
  model?: string;
  description?: string;
  webUrl?: string;
  fetchUrl?: string;
}

export interface FoundedCatalogModel {
  menu: CatalogMenu[];
  products: CatalogProduct[];
}

export interface CatalogMenu {
  title: string;
  imageUrl: string;
  isWeb: boolean;
  webUrl: string;
  fetchUrl: string;
  newFetchUrl: string;
  order: number;
  parentMenuContentId: string;
  categories: CatalogMenu[];
  isApp: boolean;
  androidUrl: string;
  iosUrl: string;
  package: string;
  countryCode: string;
  isOuterMenuContent: boolean;
  id: string;
  tags: string;
  tagList: string[];
  minIOSVersion: string;
  maxIOSVersion: string;
  minAndroidVersion: string;
  maxAndroidVersion: string;
  isSelected: boolean;
}

export interface CatalogProduct {
  findProductId: string;
  findParentProductId: string;
  productName: string;
  productLongName: string;
  productId: string;
  productGroupId: string;
  productGroupName: string;
  productSubGroupId: string;
  productSubGroupName: string;
  brand: string;
  imageUrl: string;
  imageTitle: string;
  imageCaption: string;
  specFrequency: string;
  specMinimumKwa: string;
  specMaximumKwa: string;
  detailPageUrl: string;
  detailUrl: string;
  formUrl: string;
  sort: string;
  description: string;
  showSpecifications: boolean;
  filters: CatalogFilter[];
  specifications: Specification[];
  products: CatalogProduct[];
  isSelected: boolean;
}

export interface CatalogFilter {
  filterGroupName: string;
  filterGroupValue: string;
  items: CatalogFilterItem[];
}

export interface CatalogFilterItem {
  filterName: string;
  filterValue: string;
  filterValueId: string;
  filterValueProperty: string;
}

export interface Specification {
  sort: number;
  name: string;
  value: string;
}
