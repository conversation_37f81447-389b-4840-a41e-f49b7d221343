export interface ContactModel {
  Name: string;
  CountryCode?: string;
  PhoneNumber: string;
  Description: string;
  Email: string;
  CompanyName?: string;
  CompanyId?: string;
  CompanyPhoneNumber?: string;
  AttachmentIdList?: string[];
}

export interface ContactLocationModel {
  companyId: string;
  locationType: number;
  locationTypeText: string;
  lat: number;
  lng: number;
  city: string;
  phone: string;
  phoneNumbers: string[];
  email: string;
  fax: string;
  website: string;
  countryCode: string;
  adress: string;
  name: string;
}

export interface FAQModel {
  id: string;
  question: string;
  answer: string;
  category: string;
  isPublished: string;
}

export interface MobileSupportModel {
  Name: string;
  CountryCode?: string;
  CompanyId?: string;
  CompanyName?: string;
  PhoneNumber: string;
  Description: string;
  Email: string;
  FormSubjectCode: string;
}

export interface MobileSupportSubjectsModel {
  code: string;
  title: string;
  description: string;
}

export interface LeaderUser {
  queueId: string;
  queueName: string;
  title: string;
  queueCode?: string;
  description: string;
  imageUrl: string;
  status: number;
  nextAvailableDate: string;
  order: number;
  tags: string[];
}

export interface Availability {
  startTime: string;
  endTime: string;
}

export interface GroupedAvailability {
  date: string;
  availableHours: Availability[];
}

export interface LeaderCallDetail {
  queueId: string;
  name: string;
  imageUrl: string;
  queueCode: string;
  queueName: string;
  description: string;
  groupedAvailabilities: GroupedAvailability[];
}
