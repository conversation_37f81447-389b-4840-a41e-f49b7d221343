export interface MyUsersModel{
    userId: string;
    firstName: string;
    lastName: string;
    isUserDeleteRequested: boolean;
    relations: MyUsersRelations[];
}
export interface MyUsersRelations{
    companyId: string;
    companyName: string;
    manageAccount: boolean;
    manageEquipments: boolean;
    manageService: boolean;
    manageFinance: boolean;
    manageOffers: boolean;
    manageSpareParts: boolean;
}

export interface AccountUsersModel  {
    email: string;
    name: string;
    lastname: string;
    relatedPersonNumber: string;
    portalUserId: string;
    roleReferenceId: string;
}

export interface UserRoleModel {
    companyId: string
    roleDetails: UserRoleDetailModel[]
}

export interface UserRoleDetailModel {
    roleId: string
    roleOptions: UserRoleOptionsModel[]
}

export interface UserRoleOptionsModel {
    roleOptionId: string
    roleOption: number
    values: string[]
}

export interface ManageableRolesModel {
    manageableRoles: ManageableRoleModel[]
    roleOptions: RoleOptionModel[]
    companies: CompaniesModel[]
}

export interface ManageableRoleModel {
    roleId: string
    name: string
    description: string
}

export interface CompaniesModel {
  id: string
  companyCode: string
  countryCode: string
  name: string
  catDealerCode?: string
}
export interface RoleOptionModel {
    roleOptionId: string
    roleOptionDescription: string
    roleOption: number
    values: string[]
    isMultipleChoice: boolean
}

export enum RoleOptionsEnum {
    AuthEquipments = 1,
    UnauthEquipments = 2,
    Country = 3
}
