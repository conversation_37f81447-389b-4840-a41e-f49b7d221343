import { ContactLocationModel } from './contact';

export interface CompanyDto {
  companyCode: string;
  countryCode: string;
  countryId: string;
  regionName: string;
  companyName: string;
  catDealerCode: string;
  territorySelectionEnabled: boolean;
  id: string;
  locations: ContactLocationModel[];
}

export interface CompanyCreateModel {
  customerCompanyName: string;
  countryId: string;
  cityId: string;
  Mobile: string;
  Description: string;
}
