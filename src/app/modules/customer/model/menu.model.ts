export interface MenuModel {
  brand?: string;
  productName?: string;
  isPrice?: boolean;
  androidUrl: string;
  categories: MenuModel[];
  countryCode: string;
  fetchUrl: string;
  newFetchUrl?: string;
  fetched?: true;
  id: string;
  productId: string;
  productGroupId: string;
  productGroupName: string;
  imageUrl: string;
  iosUrl: string;
  isApp: boolean;
  isWeb: boolean;
  isOuterMenuContent: boolean;
  order: number;
  package: string;
  parentMenuContent: string;
  parentMenuContentId: string;
  title: string;
  webUrl: string;
  tagList: string[];
  isSelected?: boolean;
}

export enum MenuTypes {
  list,
  card,
  campaign,
  app,
  filterable,
}

export interface CampaignModel {
  id: string;
  title: string;
  description: string;
  actionUrl: string;
  order: number;
}
