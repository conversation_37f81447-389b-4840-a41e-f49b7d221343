.question-label {
  font-weight: 600;
}

.select-values {
  .ng-placeholder {
    background-color: red;
  }
}
:host::ng-deep
  .ng-select.ng-select-multiple
  .ng-select-container
  .ng-value-container
  .ng-placeholder {
  top: 11px !important;
  left: 17px !important;
  padding-bottom: 5px;
  padding-left: 3px;
}

.emoji-options {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-inline: 1rem;

  .emoji-btn {
    opacity: 0.3;
    border: 1px solid #ddd;

    &:focus {
      box-shadow: unset;
    }
  }

  .selected {
    opacity: 1;
  }
}

.custom-checkbox {
  accent-color: #ffa300; // modern tarayıcılarda checkbox rengi için
}

[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 4px;
  left: 4px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.rate-icon {
  min-height: 10px;
}

.rate-box {
  //width: 23px;
  //height: 23px;
  //min-height: 23px;
  border-radius: 50%;
  opacity: 0.5;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rate-container.unselected {
  opacity: 0.5;
}

.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.icon-message-success {
  font-size: 60px;
  background: -webkit-linear-gradient(#00efd1, #00acea);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.icon-coins{
  &::before{
    color: #ffa300;
  }
}

.earn-coin{
  padding: .25rem;
  border-radius: 8px;
  background-color: #f4f4f4;
}
