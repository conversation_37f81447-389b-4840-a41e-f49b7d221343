import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import { LiteConvertAction, LiteGetMeAction, LiteLoginWithOttAction, LiteLogoutAction, SetLiteLoginResponse, LiteUserDeleteAction, } from './lite-login.actions';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { AuthenticationService } from '../../authentication/service/authentication.service';
import { UpdateLanguageCodeAction, UpdateLoadingAction } from '../../authentication/state/login/login.actions';
import { NewDeviceToken } from '../../notification/state/notification/notification.actions';
import { LiteUserModel } from '../model/lite-login.model';

export interface LiteLoginStateModel {
  loginResponse: LiteUserModel;
  user: LiteUserModel;
  convertResponse: any;
  loginLoading: boolean;
  language: string;
}

export interface LiteUserDeleteModel {
  description: any;
  deleteUserLoading: boolean;
}


@State<LiteLoginStateModel>({
  name: 'lite_login_main',
  defaults: {
    loginResponse: null,
    user: null,
    convertResponse: null,
    loginLoading: false,
    language: null
  },
})
@Injectable()
export class LiteLoginState {
  constructor(
    private readonly authenticationService: AuthenticationService,
    private readonly store: Store
  ) {}

  @Selector()
  public static getState(state: LiteLoginStateModel): LiteLoginStateModel {
    return state;
  }

  @Selector()
  public static language({ language }: LiteLoginStateModel): string {
    return language;
  }

  @Selector()
  public static loginResponse({
    loginResponse,
  }: LiteLoginStateModel): LiteUserModel {
    return loginResponse;
  }

  @Selector()
  public static loginLoading({
    loginLoading,
  }: LiteLoginStateModel): boolean {
    return loginLoading;
  }

  @Selector()
  public static convertResponse({
    convertResponse,
  }: LiteLoginStateModel): LiteUserModel {
    return convertResponse;
  }

  @Selector()
  public static deleteUser({
    description,
  }: LiteUserDeleteModel): LiteUserModel {
    return description;
  }

  @Selector()
  public static user({ user }: LiteLoginStateModel): LiteUserModel {
    return user;
  }

  @Action(LiteGetMeAction)
  liteGetMe(
    { patchState }: StateContext<LiteLoginStateModel>,
  ) {
    patchState({
      loginLoading: true,
    });

    return this.authenticationService.liteMe().pipe(
      tap((value) => {
        patchState({
          user: value,
          language: value?.language,
          loginLoading: false
        });
        this.store.dispatch(new UpdateLanguageCodeAction(value?.language));
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        return throwError(err);
      }));

  }

  @Action(SetLiteLoginResponse)
  liteLoginAction(
    { patchState }: StateContext<LiteLoginStateModel>,
    { loginResponse }: SetLiteLoginResponse
  ) {
    patchState({
      loginResponse,
      language: loginResponse?.language
    });
    this.store.dispatch(new UpdateLanguageCodeAction(loginResponse?.language));
    this.store.dispatch(new NewDeviceToken(loginResponse.appSessionId));

    return this.store.dispatch(new UpdateLoadingAction(false));
  }

  @Action(LiteConvertAction)
  liteConvertAction(
    { patchState }: StateContext<LiteLoginStateModel>,
    { body, headers }: LiteConvertAction
  ): Observable<any> {
    patchState({
      loginLoading: true,
    });

    return this.authenticationService.liteUserConvert(body, headers).pipe(
      tap((value) => {
        patchState({
          convertResponse: value,
          loginLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(LiteLoginWithOttAction)
  public loginWithOttAction(
    { patchState, getState }: StateContext<LiteLoginStateModel>,
    { ott }: LiteLoginWithOttAction
  ): Observable<any> {
    // if (getState().user) {
    //   console.log('already logged in');
    //   return of('');
    // }
    console.log('XOTT REQUEST ' + new Date());
    patchState({
      loginLoading: true,
    });

    return this.authenticationService.liteXOTT(ott).pipe(
      tap((value) => {
        patchState({
          loginResponse: value,
          language: value?.language,
        });
        this.store.dispatch(new NewDeviceToken(value.appSessionId));

        return this.store.dispatch(new UpdateLoadingAction(false));
      }),
      catchError((err) => {
        patchState({
          loginLoading: false,
        });
        console.log('INVALID OTT: ' + err.message);
        return throwError(err);
      })
    );
  }

  @Action(LiteLogoutAction)
  logoutAction(
    { getState, patchState }: StateContext<LiteLoginStateModel>,
    logoutAction: LiteLogoutAction
  ): void {
    patchState({
      loginResponse: null,
      user: null,
      convertResponse: null,
      loginLoading: false,
      language: null
    });
  }

  @Action(LiteUserDeleteAction)
  liteUserDeleteAction(
    { patchState }: StateContext<LiteUserDeleteModel>,
    { description }: LiteUserDeleteAction
  ): Observable<any> {
    patchState({
      deleteUserLoading: true,
    });

    return this.authenticationService.liteUserRemove(description).pipe(
      tap((value) => {
        patchState({
          description: value,
          deleteUserLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          deleteUserLoading: false,
        });
        return throwError(err);
      })
    );
  }
}
