
export class LiteGetMeAction {
  public static readonly type = '[LITE] get me';

  constructor() {
  }
}

export class LiteLoginWithOttAction {
  public static readonly type = '[LITE] login with OTT';

  constructor(public ott: string) { }
}

export class LiteLogoutAction {
  public static readonly type = '[LITE] log out';

  constructor() {
  }
}

export class LiteConvertAction {
  public static readonly type = '[LITE] convert';

  constructor(public body: any, public headers: any) { //TODO MODEL
  }
}

export class SetLiteLoginResponse {
  public static readonly type = '[LITE] set lite login response';

  constructor(public loginResponse: any) {
  }
}

export class LiteUserDeleteAction {
  public static readonly type = '[LITE] user delete';

  constructor(public description: any) {
  }
}
