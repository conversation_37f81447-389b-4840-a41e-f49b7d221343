import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LiteConvertCorporateComponent } from './lite-convert-corporate.component';

describe('LightConvertCorporateComponent', () => {
  let component: LiteConvertCorporateComponent;
  let fixture: ComponentFixture<LiteConvertCorporateComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ LiteConvertCorporateComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(LiteConvertCorporateComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
