import { Component, OnInit } from '@angular/core';
import { AgreementTypeEnum } from 'src/app/modules/definition/enum/agreement-type.enum';
import { getFormErrorMessage, isShowFormError, validateAllFormFields } from '../../../../util/form-error.util';
import { Select, Store } from '@ngxs/store';
import { DefinitionState } from '../../../definition/state/definition/definition.state';
import { Observable, Subject } from 'rxjs';
import { Country } from '../../../definition/model/country.model';
import { GetCityListAction, GetCountryListAction } from '../../../definition/state/definition/definition.actions';
import { takeUntil } from 'rxjs/operators';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CustomValidator } from '../../../../util/custom-validator';
import { City } from '../../../definition/model/city.model';
import { LiteConvertAction } from '../../state/lite-login.actions';
import { LiteLoginState } from '../../state/lite-login.state';
import { CommonState } from 'src/app/shared/state/common/common.state';
import { LiteUserModel } from '../../model/lite-login.model';
import { SettingsService } from 'src/app/shared/service/settings.service';
import { PasswordPolicies } from 'src/app/modules/customer/response/settings.response';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';

@Component({
  selector: 'app-light-convert-corporate',
  templateUrl: './lite-convert-corporate.component.html',
  styleUrls: ['./lite-convert-corporate.component.scss']
})
export class LiteConvertCorporateComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  AgreementTypeEnum = AgreementTypeEnum;

  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;
  countryList: Country[];

  @Select(DefinitionState.countryListLoading)
  countryListLoading$: Observable<boolean>;

  @Select(DefinitionState.cityList)
  cityList$: Observable<City[]>;

  @Select(DefinitionState.cityListLoading)
  cityListLoading$: Observable<boolean>;
  selectedCountry: Country;


  @Select(LiteLoginState.user)
  user$: Observable<LiteUserModel>;

  @Select(LiteLoginState.convertResponse)
  convertResponse$: Observable<any>;
  convertResponse: any;

  @Select(LiteLoginState.loginLoading)
  loginLoading$: Observable<boolean>;

  user: LiteUserModel;
  passwordPolicies: PasswordPolicies[];
  passwordType: boolean;
  loading: boolean;
  userAggs: any;

  form: FormGroup = new FormGroup({
    Name: new FormControl({ value: null, disabled: true }, [Validators.required]),
    Surname: new FormControl({ value: null, disabled: true }, [Validators.required]),
    Password: new FormControl(null, [Validators.required, Validators.minLength(8), Validators.maxLength(16)]),
    CompanyName: new FormControl(null, [Validators.required]),
    Email: new FormControl({ value: null, disabled: true }, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    Phone: new FormControl(null),
    CountryId: new FormControl(null, [Validators.required]),
    CityId: new FormControl(null, [Validators.required]),
    MobileCountryId: new FormControl(null, [Validators.required]),
  });
  selectedPhoneCode: string;
  selectedPhoneId: string;
  isEmailVerified: boolean = true;


  showBack = true;
  isUser: boolean;
  SuccesModal: boolean;


  protected subscriptions$: Subject<boolean> = new Subject();
  cityList: City[];
  showCompanyNumber = false;
  buttonValidator: boolean = false;

  constructor(
    private readonly store: Store,
    private readonly settingsService: SettingsService,
    private readonly frameMessageService: FrameMessageService,
  ) {
  }

  ngOnInit(): void {
    this.getCountryList();
    this.cityList$.pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (data) => {
          this.cityList = data;
        }
      );
    this.loginLoading$.subscribe(i => (this.loading = i));

    this.user$.subscribe(user => {
      this.user = user;
      this.isEmailVerified = user?.isEmailVerified;
      if (!user?.isMobileVerified) {
        this.form.get('Phone').setValidators([Validators.required]);
      }
      if (user?.mobileOnly) {
        this.form.get('Phone').disable();
      }
      if (user?.mobileCountryPhoneCode) {
        this.form.get('MobileCountryId').disable();
      }
    });

      this.form.patchValue({
        Name: this.user?.firstName,
        Surname: this.user?.lastName,
        Email: this.user?.email?.toLowerCase(),
        Phone: this.user?.mobileOnly,
      })
      if(this.user?.agreements) {
       this.userAggs = this.user?.agreements?.split(';');
      }
      if(this.user?.mobileCountryId && this.countryList?.length) {
        const telId: any = this.countryList.find(x => x.id === this.user?.mobileCountryId);
        this.getSetCountryPhoneCode(telId.phoneCode);
      }

    this.settingsService.getBasic().subscribe((res) => {
      this.passwordPolicies = res.passwordPolicies;
    });

  }

  getSetCountryPhoneCode(e: any) {
    this.selectedPhoneCode = e;
    this.selectedCountry = this.countryList.find(x => x.phoneCode === this.selectedPhoneCode);
    this.selectedPhoneId = this.selectedCountry.id;
    this.form.patchValue({
      MobileCountryId: this.selectedCountry.phoneCode,
    });
  }

  handleVerifyEmail() {
    this.isEmailVerified = false;
    this.frameMessageService.sendMessage(FrameMessageEnum.liteUserVerify, {
      user: this.user,
      verifyType: 'verifyEmail'
    });
  }

  isPassValid(pol: PasswordPolicies) {
    return new RegExp(pol.regex).test(this.form.controls.Password.value);
  }

  togglePasswordType() {
    this.passwordType = !this.passwordType;
  }

  canSave() {
    let val = true;
    val = this.passwordPolicies?.reduce(
      (p, c) => p && this.isPassValid(c),
      val
    );
    const { valid } = this.form;
    val = val && valid;
    return val;
  }


  getCountryList() {
    this.store.dispatch(new GetCountryListAction());
    this.countryList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.countryList = data?.filter(x => x.phoneCode !== null);
        if (data?.length && !this.user?.mobileCountryId) {
          this.selectedCountry = data[0];
          this.selectedPhoneCode = data[0].phoneCode;
          this.getSetCountryPhoneCode(this.selectedPhoneCode);
        } else {
          if (this.countryList.length) {
            this.getSetCountryPhoneCode(this.countryList?.filter(x => x.id === this.user?.mobileCountryId)?.find(x => x?.phoneCode)?.phoneCode);
          }
        }
      });
  }

  onSelectCountry(id: string) {
    const selectedCountry = this.getCountryByCode(id);

    this.form.patchValue({ CityId: null });
    if (this.isActiveCountry(id)) {
      this.form.controls.CityId.setValidators([Validators.required]);
      this.store.dispatch(new GetCityListAction(selectedCountry.id));
    } else {
      this.form.controls.CityId.clearValidators();
      this.form.controls.CityId.updateValueAndValidity();
    }
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  getCountryByCode(id: string) {
    const countryList = this.store.selectSnapshot(DefinitionState.countryList);
    return countryList.find((country) => country.id === id);
  }

  isActiveCountry(countryId: string) {
    const countries = this.store.selectSnapshot(DefinitionState.countryList);
    const country = countries.find((item) => item.id === countryId);

    return country?.isActive;
  }

  searchCity(data, searchVal: string) {
    if (searchVal) {
      const search = searchVal?.toLowerCase();
      return data
        .filter(data => data.name?.toLowerCase().indexOf(search) !== -1)
        .sort((a, b) => {
          if (a.name?.toLowerCase().indexOf(search) > b.name?.toLowerCase().indexOf(search)) {
            return 1;
          }
          if (a.name?.toLowerCase().indexOf(search) < b.name?.toLowerCase().indexOf(search)) {
            return -1;
          }
          return 0;
        });
    }
    return data;
  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  navigateToBack() {
    history.back();
  }

  onSubmitForm() {
    if (this.form.valid && this.canSave()) {
      this.buttonValidator = true;
      console.log('valid');
      this.sendForm();
    } else {
      console.log('invalid');
      validateAllFormFields(this.form);
    }
  }

  private sendForm() {
    let headers: any;
    const agreements = this.form.value?.agreements;
    if(this.userAggs.length === 7){
      //eğer tüm agg.leri seçiliyse
      headers = { ApprovedAgreementNames: this.user?.agreements };
    } else {
      const agreementKeys = Object.entries(agreements || {})
        .filter(([_, v]) => v)
        .map(([k]) => `${k}`);
      if(!agreementKeys.length){
        //eğer ekrandakileri de seçmediyse
        headers = { ApprovedAgreementNames: this.user?.agreements };
      } else {
        //eğer ekrandakilerden seçtiyse öncekilerle birleştir
        headers = agreementKeys.length
        ? { ApprovedAgreementNames: (agreementKeys.join(';') + ';' + this.user?.agreements) }
        : {};
      }
    }
        const { value } = this.form;
        const data: any = {
         id: this.user.id,
         password: value.Password,
         customerCompanyName: value?.CompanyName,
         customerNumber: value?.CompanyNumber,
         countryId: value?.CountryId,
         cityId: value?.CityId,
         timezoneId: this.store.selectSnapshot(CommonState.timezoneId),
      }
      if(!this.user?.isMobileVerified){
        data.mobile = value.Phone
        data.mobileCountryId = this.selectedPhoneId;
      }
      headers.PublicMenuHeaderCompany = this.store.selectSnapshot(CommonState.publicMenuHeaderCompany);
      this.store.dispatch(new LiteConvertAction(data, headers))
      this.convertResponse$.subscribe((data) => {
        if(data){
          this.buttonValidator = true;
          this.convertResponse = data;
          this.SuccesModal = true;
          this.loginStarted(data?.token);
        }
      }, (err) => {
        this.buttonValidator = false;
      })

  }

  loginStarted(data) {
    this.frameMessageService.sendMessage(FrameMessageEnum.loginWithToken, data);
  }

  openMain() {
    this.SuccesModal = false;
    this.frameMessageService.sendMessage(FrameMessageEnum.openMain, this.convertResponse.token);
  }

  companyNumberSet() {
    this.showCompanyNumber = !this.showCompanyNumber;
    if (this.showCompanyNumber) {
      this.form.addControl('CompanyNumber', new FormControl(null, [Validators.required, Validators.maxLength(12), Validators.minLength(2)]));
    } else {
      this.form.removeControl('CompanyNumber');
    }
  }
}
