<div class="light-register-content">
  <div style="overflow-x: hidden; overflow-y: auto">
    <div class="px-4 h4 py-4 mb-0 text-center nav-back">
      <i class="icon icon-back mr-2 float-left" *ngIf="showBack" (click)="navigateToBack()"></i>
      <b>{{ "_light_register_header" | translate }}</b>
    </div>
    <div class="px-4 pt-2 pb-5 mb-3 form-area">
      <form (submit)="onSubmitForm()" [formGroup]="form">
        <ng-container *ngIf="!isUser">
          <div class="form-group">
            <input
              [placeholder]="'_name' | translate"
              class="form-control form-control"
              formControlName="Name"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Name) | translate }}
            </div>
          </div>
          <div class="form-group">
            <input
              [placeholder]="'_surname' | translate"
              class="form-control form-control"
              formControlName="Surname"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Surname) | translate }}
            </div>
          </div>
          <div class="form-group">
            <input
              [placeholder]="'_email' | translate"
              class="form-control form-control"
              formControlName="Email"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Email) | translate }}
            </div>
          </div>
        </ng-container>
        <div class="input-group mb-3">
          <input
            [name]="'Password'"
            [type]="passwordType ? 'text' : 'password'"
            [placeholder]="'_password' | translate"
            class="form-control form-control"
            formControlName="Password"
            type="password"
            autocomplete="off"
          />

          <div class="input-group-append">
            <span class="input-group-text" style="
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;">
              <i
                class="icon font-size-18px"
                [ngClass]="{
                  'icon-eye': passwordType,
                  'icon-eye-slash': !passwordType
                }"
                (click)="togglePasswordType()"
              ></i>
            </span>
          </div>
          <div
          [ngClass]="{ 'd-block': isShowError(form.controls.Password) }"
          class="invalid-feedback pl-3"
        >
          {{ getFormErrorMessage(form.controls.Password) | translate }}
        </div>
        </div>
        <div class="password-condition-area my-2" *ngIf="form.controls.Password.dirty">
          <ul class="password-conditions">
            <li
              class="password-condition"
              *ngFor="let pol of passwordPolicies"
              [class.text-danger]="
                !isPassValid(pol) && form.controls.Password.dirty
              "
              [class.text-success]="
                isPassValid(pol) && form.controls.Password.dirty
              "
            >
              {{ pol.description }}
            </li>
          </ul>
        </div>
        <div class="form-group d-flex flex-row mt-2 phone-dropdown-disable">
          <div class="col-4 p-0 lite-phone-area">
            <ng-select [searchable]="false"
            [placeholder]="'+' + selectedPhoneCode"
            (change)="getSetCountryPhoneCode($event)"
            formControlName="MobileCountryId"
            >
            <ng-option *ngFor="let country of countryList" [value]="country.phoneCode">
              +{{ country.phoneCode }}
            </ng-option>
          </ng-select>
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.MobileCountryId) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.MobileCountryId) | translate }}
          </div>
          </div>
          <div class="w-100">

            <input
              [placeholder]="'_phone' | translate"
              class="form-control form-control"
              formControlName="Phone"
              (input)="onInputPhone($event)"
              type="tel"
              maxlength="15"
              style="border-top-left-radius: 0px;border-bottom-left-radius: 0px;"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Phone) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Phone) | translate }}
            </div>
          </div>
        </div>
        <div class="form-group">
          <input
            [placeholder]="'_company_name' | translate"
            class="form-control form-control"
            type="text"
            formControlName="CompanyName"
          />
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
          </div>
        </div>

        <div class="country-label">
          <label style="font-size: 16px;">{{ "_select_country_and_city" | translate }}</label>
        </div>
        <div class="row">
          <div
            [ngClass]="{
          'col-6 pr-2':
            !!form.controls['CountryId'].value &&
            isActiveCountry(form.controls['CountryId'].value),
          col:
            !form.controls['CountryId'].value ||
            !isActiveCountry(form.controls['CountryId'].value)
        }"
          >
            <div class="form-group">
              <!--          <cat-form-label [label]="'_equipment_location' | translate"></cat-form-label>-->
              <ng-select
              class="service-drp"
              [searchable]="false"
              [placeholder]="'_country' | translate"
              formControlName="CountryId"
              (ngModelChange)="onSelectCountry($event)"
              [dropdownPosition]="'bottom'"
            >
              <ng-option
                *ngFor="let country of countryList$ | async"
                [value]="country.id"
              >
                {{ country.name }}
              </ng-option>
            </ng-select>
            <div
            [ngClass]="{ 'd-block': isShowError(form.controls.CountryId) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.CountryId) | translate }}
          </div>
            </div>
          </div>
          <div
            *ngIf="
          (!!form.controls['CountryId'].value ||
            (cityList$ | async)?.length > 0) &&
          isActiveCountry(form.controls['CountryId'].value)
        "
            class="col-6 pl-2"
          >
            <div class="form-group">
              <ng-select
              class="service-drp"
              [searchable]="true"
              [placeholder]="'_city' | translate"
              formControlName="CityId"
              dropdownPosition="bottom"
              (keypress)="scrollTop()"
              #city
            >
              <ng-option
                *ngFor="let city of searchCity(cityList, city.searchTerm)"
                [value]="city.id"
              >
                {{ city.name }}
              </ng-option>
            </ng-select>
            <div
            [ngClass]="{ 'd-block': isShowError(form.controls.CityId) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.CityId) | translate }}
          </div>
            </div>
          </div>
        </div>
        <app-info-box [title]="'_info' | translate">
          <div (click)="companyNumberSet()"><span [innerHTML]="('_light_convert_corporate_customer_number_text' | translate) | safeHtml"></span></div>
        </app-info-box>
        <div class="form-group" *ngIf="showCompanyNumber">
          <input
            [placeholder]="'_company_number' | translate"
            class="form-control form-control"
            formControlName="CompanyNumber"
            type="text"
          />
          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.CompanyNumber) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.CompanyNumber) | translate }}
          </div>
        </div>
        <div style="word-break: break-word;">
          <app-agreement-list [form]="form" [filter]="{ approved: user?.agreements}" [forceAgreement]="true"
          [formType]="AgreementTypeEnum.RegisterSecondStep"></app-agreement-list>
        </div>
        <div class="form-group my-4">
          <input
            appClickLog
            [section]="'LIGHT_LOGIN'"
            [subsection]="'LIGHT_CONVERT_CORPORATE'"
            [value]="'_light_convert_corporate_form_send' | translate"
            class="btn btn-info btn-gradient btn-block text-white"
            type="submit"
            [disabled]="buttonValidator"
          />
        </div>
      </form>
    </div>
  </div>
</div>
<app-loader [show]="(cityListLoading$ | async) || (countryListLoading$ | async) || loading"></app-loader>
<div *ngIf="SuccesModal" class="after-form-send">
  <div class="after-form-send-content text-center px-4">
    <i class="icon icon-message-success d-inline-block mb-4"></i>
    <div class="success-message mb-5">{{ '_lite_user_convert_success' | translate }}</div>

    <div
    class="btn btn-info btn-gradient btn-block text-white shadow btn-sm"
    (click)="openMain()"
    >
      {{ '_back_to_home' | translate }}
    </div>
</div>
</div>
<app-basic-modal
  [closeable]="false"
  [(status)]="!isEmailVerified"
  [headerText]="'_warning' | translate"
  [backdropClose]="false"
>
  <div class="text-center">
    {{'_please_verify_email' | translate}}
  </div>
  <div class="btn btn-info btn-sm font-weight-semi-bold btn-block py-2 px-4 rounded-lg my-2" (click)="handleVerifyEmail()">
    {{ '_email_verify' | translate }}
  </div>
</app-basic-modal>
