.phone-area ::ng-deep .ng-select.ng-select-single .ng-select-container{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.phone-area .phone{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.bg-soft-info{
  background-color: #E9F6FC;
  color: #4A8EB0;
}
.form-area{
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 98px);
}

.password-condition-area {
  .password-conditions {
    list-style: none;
    line-height: 1em;
    font-size: 13px;
    padding-left: 0.75rem;
    .password-condition {
      padding: 5px 0;
    }
  }
}

.service-error {
  white-space: break-spaces;
  font-size: 14px;
  text-align: start;
}

.empty-content {
  padding: 10rem 5rem;
  .icon {
    font-size: 40px;
  }
  &-message {
    font-weight: bold;
    font-size: 26px;
    margin-bottom: 3rem;
  }
  &-extra {
    font-weight: normal;
    font-size: 18px;
  }
}
.agreement {
  padding: 15px;

  //text-align: center;
  label {
    font-size: 13px;
  }

  a {
    color: #007bff;
    text-decoration: none;
    background-color: transparent;
  }
}
@media (min-width: 390px) {
  .agreement {
    margin-top: 5%;
  }

}
@media (min-width: 500px) {
  .agreement {
    margin-top: 15%;
  }

}

.form-preview, .after-form-send {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background-color: #FAFAFA;
}
.after-form-send-content {
  position: absolute;
  left: 0;
  top: 50%;
  width: 100vw;
  transform: translateY(-50%);
}

.icon-message-success {
  font-size: 60px;
  background: -webkit-linear-gradient(#00EFD1, #00ACEA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.success-message {
  font-size: 26px;
  font-weight: 700;
}

::ng-deep .lite-phone-area .ng-select-container {
  border-top-right-radius: 0px !important;
  border-bottom-right-radius: 0px !important;
}

::ng-deep .phone-dropdown-disable .ng-select.ng-select-disabled > .ng-select-container {
  background-color: #e9ecef;
}
