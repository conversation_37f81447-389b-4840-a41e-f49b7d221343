<div class="app-sidebar"  *ngIf="!successModal && user">
  <div class="app-sidebar-area">
    <div class="top">
<!--      <div class="d-flex flex-row flex-nowrap justify-content-between">-->
<!--        <i class="icon icon-x close" (click)="close()" appClickLog [section]="'MENU'" [subsection]="'CLOSE'"></i>-->

<!--        <small *ngIf="(borusanUserData$ | async)?.ldapUsername || (borusanUserData$ | async)?.isLdapLogin" class="text-white">-->
<!--          {{ (borusanUserData$ | async)?.ldapUsername }}-->
<!--          <img class="borusanIcon" [src]="borusanIcon" alt="Borusan">-->
<!--        </small>-->
<!--      </div>-->
      <div class="info">
        <div class="top-left">
          <div class="name">
            <span>{{'_dear' | translate}}</span>
            <span *ngIf="user?.firstName">{{ ' ' + user.firstName + ' ' + user.lastName }}</span>
          </div>
          <div class="coin-area">
            <div
            *ngIf="systemFeatureShowCoin"
              class="top-right" (click)="goToPromotionPage()">
              <span class="coin-number">{{ totalCoin }}</span>
              <img src="assets/boom-coin-single.png" class="coin-icon" />
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="sidebar-content">
      <div class="nav-links">
        <ul>
          <li (click)="liteBack()" appClickLog [section]="'MENU'" [subsection]="'BACK'">
            <div class="float-left" style="width: 20px">
              <i class="icon icon-home nav-links-icon" style="font-size: 22px; font-weight: 500;"></i>
            </div>
            <span>{{ "_lite_return_back" | translate }}</span>
          </li>
          <li *ngIf="!user?.isMobileVerified" (click)="handleVerifyPhone()" appClickLog [section]="'MENU'" [subsection]="'VERIFY_PHONE'">
            <div class="float-left">
              <i class="icon icon-phone-call nav-links-icon"></i>
            </div>
            <span>{{ "_mobile_no_verify" | translate }}</span>
          </li>
          <li *ngIf="!user?.isEmailVerified" (click)="handleVerifyEmail()" appClickLog [section]="'MENU'" [subsection]="'VERIFY_EMAIL'">
            <div class="float-left">
              <i class="icon icon-contact nav-links-icon"></i>
            </div>
            <span>{{ "_email_verify" | translate }}</span>
          </li>
          <li *ngIf="systemFeatureConvert" (click)="convertCompany()" appClickLog [section]="'MENU'" [subsection]="'CONVERT'">
            <div class="float-left">
              <i class="icon icon-company nav-links-icon"></i>
            </div>
            <span>{{ "_convert_corporate" | translate }}</span>
          </li>
          <li (click)="myInformation()" appClickLog [section]="'MENU'" [subsection]="'MY_INFORMATION'">
            <div class="float-left">
              <i class="icon icon-edit nav-links-icon"></i>
            </div>
            <span>{{ "_my_information" | translate }}</span>
          </li>
          <li *ngIf="systemFeatureDeleteLiteUser" (click)="deleteAccountModal()" appClickLog [section]="'MENU'" [subsection]="'DELETE_USER'">
            <div class="float-left">
              <img class="delete-user-icon" [src]="deleteUserIcon"/>
            </div>
            <span> {{ "_delete_user_me" | translate }} </span>
          </li>
          <li (click)="handleLogOut()" appClickLog [section]="'MENU'" [subsection]="'LOGOUT'">
            <div class="float-left" style="width: 20px">
              <i class="icon icon-exit nav-links-icon" style="font-size: 19px"></i>
            </div>
            <span>{{ "_log_out" | translate }}</span>
          </li>
        </ul>
      </div>
    </div>
    <div class="bottom-area">
      <div class="borusan-cat-logo">
        <img [src]="borusanCatLogo"  alt="BorusanCat"/>
      </div>
      <div class="version">{{ version$ | async }}</div>
    </div>
  </div>
</div>
<app-big-modal *ngIf="!successModal && deleteLiteAccountModal"
  [(status)]="deleteLiteAccountModal"
  (statusChange)="requestFormReset()"
  (click)="deleteAccountModal()">
  <app-error-box [title]="'_warning' | translate">
    <div class="text-danger">
      {{ "_delete_user_infobox_text" | translate }}
    </div>
  </app-error-box>
  <form (submit)="isDeleteAccount = true" [formGroup]="form">
    <div class="form-group">
      {{ "_delete_user_warning_text" | translate }}
    </div>
    <div *ngIf="email" class="form-group">
      <input
        [ngModel]="email"
        [placeholder]="'_email' | translate"
        class="form-control form-control"
        formControlName="Email"
        type="email"
        minlength="3"
        [attr.disabled]="true"
      />
    </div>
    <div *ngIf="!email" class="form-group">
      <input
        [ngModel]="phone"
        [placeholder]="'_phone' | translate"
        class="form-control form-control"
        formControlName="Phone"
        type="phone"
        minlength="3"
        [attr.disabled]="true"
      />
    </div>
    <div class="form-group">
      <textarea
        [placeholder]="'_description' | translate"
        [rows]="5"
        class="form-control"
        formControlName="Description"
        [maxLength]="descMaxLength"
        style="resize: none;"
      ></textarea>
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Description) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Description, {
          required : '_required',
          minlength: '_min_length'
        }) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        type="checkbox"
        name="Acceptance"
        id="acceptance"
        formControlName="Acceptance"
        [(ngModel)]="approve"
      />
      <label for="acceptance"></label>
      {{ "_delete_user_acceptance_text" | translate }}
    </div>
    <div class="form-group">
      <input
        [value]="'_send' | translate"
        class="btn btn-gradient btn-block text-white shadow"
        [ngClass]="{ 'btn-secondary': (!form.valid || !approve), 'btn-info': (form.valid && approve) }"
        [disabled]="!form.valid || !approve"
        type="submit"
      />
    </div>
  </form>
</app-big-modal>

<app-basic-modal
  *ngIf="!successModal && isDeleteAccount"
  [(status)]="isDeleteAccount"
  [headerText]="'_delete_user_me' | translate"
>
  <div class="mb-3">
    <div>{{ "_delete_account_acceptance_modal_text" | translate }}</div>
    <div class="mx-auto text-center mt-4">
      <button
        class="modal-btn btn-sm btn btn-secondary btn-gradient text-white shadow col-4 mr-3"
        (click)="isDeleteAccount = false"
      >
        {{ "_cancel" | translate }}
      </button>
      <button
        class="modal-btn btn-sm btn btn-danger btn-gradient text-white shadow col-4"
        (click)="sendDeleteRequestForm()"
      >
        {{ "_send" | translate }}
      </button>
    </div>
  </div>
</app-basic-modal>
<app-success-modal *ngIf="!loading && successModal" message="_account_deletion_request"></app-success-modal>
<app-loader [show]="loading"></app-loader>
