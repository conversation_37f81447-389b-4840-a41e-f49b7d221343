import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { CommonState } from '../../../../shared/state/common/common.state';
import { Observable, Subject } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { LiteLoginState } from '../../state/lite-login.state';
import { LiteUserModel } from '../../model/lite-login.model';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { map, takeUntil } from 'rxjs/operators';
import { systemFeature } from 'src/app/util/system-feature.util';
import { PromotionState } from 'src/app/modules/promotion/state/promotion.state';
import { LitePromotionAction } from 'src/app/modules/promotion/state/promotion.action';
import { LiteLogoutAction, LiteUserDeleteAction } from '../../state/lite-login.actions';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CustomValidator } from 'src/app/util/custom-validator';
import { isShowFormError, getFormErrorMessage, validateAllFormFields } from 'src/app/util/form-error.util';
import { LogService } from 'src/app/shared/service/log.service';
import { HttpClient, HttpResponse } from '@angular/common/http';
import { LogoutProcessAction } from 'src/app/modules/authentication/state/login/login.actions';
import { AuthenticationService } from 'src/app/modules/authentication/service/authentication.service';
import { SystemFeatureAction } from 'src/app/shared/state/settings/settings.actions';

@Component({
  selector: 'app-light-menu',
  templateUrl: './lite-menu.component.html',
  styleUrls: ['./lite-menu.component.scss']
})
export class LiteMenuComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Select(CommonState.version)
  version$: Observable<string>;

  @Select(LiteLoginState.user)
  user$: Observable<LiteUserModel>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(LiteLoginState.deleteUser)
  deleteUser$: Observable<any>;
  deleteUser: any;

  systemFeatureConvert: boolean;

  user: LiteUserModel;

  totalCoin: number;
  deleteUserIcon= `${environment.assets}/delete_user.svg`;
  isDeleteAccount = false;
  descMinLength = 2;
  descMaxLength = 500;
  email: string;
  phone: string;
  approve = false;
  deleteLiteAccountModal = false;
  loading = false;
  formSendStatus = false;
  successModal= false;
  systemFeatureDeleteLiteUser: boolean;
  systemFeatureShowCoin: boolean;

  borusanCatLogo = `${environment.assets}/borusan-cat-logo.svg`;

  form: FormGroup = new FormGroup({
    Email: new FormControl(null, [
      CustomValidator.mailFormat,
    ]),
    Phone: new FormControl(null, []),
    Description: new FormControl(null, [
      Validators.required,
      Validators.minLength(this.descMinLength)
    ]),
    Acceptance: new FormControl(null, [Validators.required]),
  });

  constructor(
    private readonly trans: TranslateService,
    private readonly router: Router,
    private readonly store: Store,
    private readonly frameMessageService: FrameMessageService,
    private readonly log: LogService,
    private readonly http: HttpClient,
    private readonly authenticationService: AuthenticationService,
  ) { }

  private subscriptions$: Subject<boolean> = new Subject();

  ngOnInit(): void {
    this.store.dispatch(new SystemFeatureAction());
    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        this.systemFeatureConvert = systemFeature('convert_to_corporate', features, true);
        this.systemFeatureDeleteLiteUser = systemFeature('delete_lite_user', features, false);
        this.systemFeatureShowCoin = systemFeature('lite_user_boom_coin', features, false);
      });

    this.store.dispatch(new LitePromotionAction()).pipe(map(() => this.store.selectSnapshot(PromotionState.getLitePromotion)))
      .subscribe(state => {
        this.totalCoin = state?.totalPoint;
      }
      )

    this.user$.subscribe(user => {
      this.user = user;
    });


  }

  deleteAccountModal(){
    this.email = this.user.email;
    this.phone = this.user.mobile;
    this.deleteLiteAccountModal = true;
  }

  sendDeleteRequestForm(){
    if (this.form.valid) {
      this.formSendStatus = false;
      this.loading = true;
      const body = {
        description: this.form.value.Description,
      };
      this.store.dispatch(new LiteUserDeleteAction(body.description)).subscribe(
        (val) => {
          if (val.code === 0) {
            this.formSendStatus = true;
          }
          this.successModal = true;
          this.loading = false;
          setTimeout(() => {
            this.handleLogOut();
          }, 4000);

        },
        () => {
          this.formSendStatus = false;
          this.loading = false;
          this.isDeleteAccount = false;
          this.deleteLiteAccountModal = false;
          setTimeout(() => {
            this.handleLogOut();
          }, 4000);
        }
      );
      this.log.action('CUSTOMER', 'DELETE_ACCOUNT_SEND');
      this.isDeleteAccount = true;
      if(!this.loading){
        this.formSendStatus = true;
      }

    } else {
      validateAllFormFields(this.form);
    }
  }

  requestFormReset() {
    this.form.reset();
  }

  handleLogOut() {
    this.frameMessageService.sendMessage(FrameMessageEnum.liteUserLogout, {
      data: this.user
    });
    this.store.dispatch(new LiteLogoutAction())
  }

  liteBack() {
    this.frameMessageService.sendMessage(FrameMessageEnum.liteUserBack);
  }

  handleVerifyPhone() {
    this.frameMessageService.sendMessage(FrameMessageEnum.liteUserVerify, {
      user: this.user,
      verifyType: 'verifyMobilePhone'
    });
  }

  handleVerifyEmail() {
    this.frameMessageService.sendMessage(FrameMessageEnum.liteUserVerify, {
      user: this.user,
      verifyType: 'verifyEmail'
    });
  }

  convertCompany() {
    this.router.navigate(['lite', 'convert']).then();
  }

  myInformation() {
    this.router.navigate(['lite', 'my-information']).then();
  }

  goToPromotionPage() {
    this.router.navigate(['lite', 'promotion']);
  }
}
