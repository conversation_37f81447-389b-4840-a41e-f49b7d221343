@import "variable/bootstrap-variable";

.app-sidebar {
  width: 100vw;
  height: 100vh;
  overflow: auto;
  background: #fff;

  .app-sidebar-area {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;

    .top {
      position: sticky;
      top: 0;
      z-index: 1;
      //background: #ffa300;
      padding: 22px;
      line-height: 1em;

      .info {
        width: 100%;
        display: inline-block;
        margin-top: 24px;

        .top-left {
          display: flex;
          justify-content: space-between;
          width: 100%;
          align-items: center;
          .name {
            //color: #fff;
            font-size: 18px;
            font-weight: 700;
            line-height: 21px;
            width: 100%;
            white-space: nowrap;
            overflow: hidden !important;
            text-overflow: ellipsis;
          }
        }
        .top-right {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .borusanIcon {
        height: 20px;
        border-radius: 4px;
        border: 1px solid #e4e8ebad;
      }
    }

    .sidebar-content {
      .nav-links {
        border-top: 1px solid #dbdbdb;
        //margin: 22px 0;
        padding: 16px 35px;

        &-icon {
          font-size: 20px;
        }

        ul {
          list-style: none;
          padding: 0;

          li {
            display: block;
            margin: 8px 0;
            color: #505050;
            font-size: 16px;
            font-weight: 400;
            line-height: 24px;
            padding: 8px 0;

            svg {
              float: left;
              margin-top: 4px;
            }

            span {
              display: inline-block;
              margin-left: 18px;
            }
          }
        }
      }
    }

    .bottom-area {
      margin-top: auto;
      padding-bottom: 18px;

      .borusan-cat-logo {
        display: flex;
        justify-content: center;
      }

      .version {
        font-size: 13px;
        text-align: center;
      }
    }
  }
}

.coin-area {
  .coin-icon {
    width: 40px;
    height: 40px;
    margin-left: 5px;
  }
.coin-number {
  color: #ffa300;
  font-size: 20px;
  font-weight: 600;
}
}

.delete-user-icon{
  width: 20px;
  height: 20px;
}

[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
