<div class="light-register-content">
  <div style="overflow-x: hidden; overflow-y: auto">
    <div class="h4 py-4 mb-0 text-center nav-back">
      <i class="icon icon-back mr-2 float-left" *ngIf="showBack" (click)="navigateToBack()"></i>
      <b>{{ "_light_register_header" | translate }}</b>
    </div>
    <ng-container *ngIf="lightRegisterInfo else showRegisterForm">
      <div class="form-container d-flex flex-column px-4">
        <div class="d-flex flex-column" style="overflow-x: hidden; overflow-y: auto">
          <div class="py-3 border-bottom">
            {{'_light_register_info_1'|translate}}
          </div>
          <div class="py-3 border-bottom">
            {{'_light_register_info_2'|translate}}
          </div>
          <div class="py-3 border-bottom mb-3">
            {{'_light_register_info_3'|translate}}
          </div>
        </div>
        <div class="btn btn-info btn-block mb-5 mt-auto"
             (click)="openRegisterForm(true)">{{'_light_register_register_button'|translate}}</div>
      </div>
      <div class="footer-container d-flex flex-column px-4 mb-3">
        <div class="d-flex flex-column" style="overflow-x: hidden; overflow-y: auto">
          <div class="py-3 border-bottom">
            {{'_light_register_info_company_1'|translate}}
          </div>
          <div class="py-3 border-bottom">
            {{'_light_register_info_company_2'|translate}}
          </div>
          <div class="py-3 border-bottom mb-3">
            {{'_light_register_info_company_3'|translate}}
          </div>
        </div>
        <div class="btn btn-info btn-block mb-5 mt-auto" (click)="openRegisterForm(false)">{{'_light_register_corporate_register_button'|translate}}</div>
      </div>
    </ng-container>
    <ng-template #showRegisterForm>
      <div class="px-4 pb-5 mb-3 register-form">
        <form (submit)="onSubmitForm()" [formGroup]="form">
          <div class="form-group">
            <input
              [placeholder]="'_name' | translate"
              class="form-control form-control"
              formControlName="Name"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Name) | translate }}
            </div>
          </div>
          <div class="form-group">
            <input
              [placeholder]="'_surname' | translate"
              class="form-control form-control"
              formControlName="Surname"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Surname) | translate }}
            </div>
          </div>
          <div class="form-group">
            <input
              [placeholder]="'_email' | translate"
              class="form-control form-control"
              formControlName="Email"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.Email) | translate }}
            </div>
          </div>
          <div class="form-group d-flex flex-row phone-area" style="margin-bottom: 0px;">
            <div class="p-0">
              <ng-select [searchable]="false"
                         [placeholder]="'+' + selectedPhoneCode"
                         (change)="getSetCountryPhoneCode($event)"
                         formControlName="phoneCode">
                <ng-option *ngFor="let country of countryList" [value]="country.phoneCode">
                  {{ country.code }} +{{ country.phoneCode }}&nbsp;
                </ng-option>
              </ng-select>
            </div>
            <div class="flex-fill">
              <input appInputMaxLength [name]="'Phone'" [placeholder]="'_phone' | translate"
                     class="form-control form-control phone"
                     formControlName="phoneNumber" (input)="onInputPhone($event)" minlength="7" type="tel"/>
            </div>
          </div>
          <div
          [ngClass]="{ 'd-block': isShowError(form.controls.phoneNumber) }"
          class="invalid-feedback pl-3 mb-4"
        >
          {{ getFormErrorMessage(form.controls.phoneNumber) | translate }}
        </div>
          <div *ngIf="showCompanyInput" class="form-group my-3">
            <input
              [placeholder]="'_company' | translate"
              class="form-control form-control"
              formControlName="CompanyName"
              type="text"
            />
            <div
              [ngClass]="{ 'd-block': isShowError(form.controls.CompanyName) }"
              class="invalid-feedback pl-3"
            >
              {{ getFormErrorMessage(form.controls.CompanyName) | translate }}
            </div>
          </div>
          <div class="card bg-soft-info my-4">
            <div class="card-body py-3">
              <small>{{'_light_register_form_description'|translate}}</small>
            </div>
          </div>
          <app-agreement-list [form]="form" [forceAgreement]="true"
                              [formType]="AgreementTypeEnum.RegisterSecondStep"></app-agreement-list>
          <div class="form-group my-4">
            <input
              appClickLog
              [section]="'LIGHT_REGISTER'"
              [subsection]="'LIGHT_REGISTER_SEND_FORM'"
              [value]="'_light_register_send_code_email' | translate"
              class="btn btn-gradient btn-block text-white"
              type="submit"
              [ngClass]="{
                'btn-warning': form.valid,
                'btn-secondary': !form.valid
              }"
              [disabled]="!form.valid"
            />
          </div>
        </form>
        <ng-container *ngIf="showCompanyButton">
          <hr>
          <ng-container *ngIf="!showCompanyInput else showPersonal">
            <div class="btn btn-info btn-block shadow-sm mt-4" (click)="convertCorporate()">
              {{'_light_register_form_corporation_change_button'|translate}}
            </div>
            <div class="text-center mt-4">
              {{'_light_register_company_change_description'|translate}}
            </div>
          </ng-container>
          <ng-template #showPersonal>
            <div class="btn btn-info btn-block shadow-sm mt-4" (click)="convertCorporate()">
              {{'_light_register_form_personal_change_button'|translate}}
            </div>
            <div class="text-center mt-4">
              {{'_light_register_personal_change_description'|translate}}
            </div>
          </ng-template>
        </ng-container>
      </div>
    </ng-template>
  </div>
</div>
