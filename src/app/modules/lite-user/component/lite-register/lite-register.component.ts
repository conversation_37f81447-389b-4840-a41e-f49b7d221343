import {Component, OnInit} from '@angular/core';
import {Select, Store} from '@ngxs/store';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {CustomValidator} from '../../../../util/custom-validator';
import {getFormErrorMessage, isShowFormError, validateAllFormFields} from '../../../../util/form-error.util';
import {ActivatedRoute, Router} from '@angular/router';
import {FormState} from '../../../customer/state/form/form.state';
import {Observable, Subject} from 'rxjs';
import {DefinitionState} from '../../../definition/state/definition/definition.state';
import {Country} from '../../../definition/model/country.model';
import {GetAllCountryListAction} from '../../../definition/state/definition/definition.actions';
import { takeUntil, map } from 'rxjs/operators';
import {AgreementTypeEnum} from '../../../definition/enum/agreement-type.enum';
import { AuthenticationService } from 'src/app/modules/authentication/service/authentication.service';
@Component({
  selector: 'app-light-register',
  templateUrl: './lite-register.component.html',
  styleUrls: ['./lite-register.component.scss']
})
export class LiteRegisterComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  AgreementTypeEnum = AgreementTypeEnum;

  @Select(FormState.formLoading)
  formLoading$: Observable<boolean>;
  @Select(DefinitionState.countryList)
  countryList$: Observable<Country[]>;
  countryList: Country[];
  selectedCountry: Country;
  user: any;

  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    phoneNumber: new FormControl(null, [Validators.required, Validators.minLength(7)]),
    phoneCode: new FormControl(null, [Validators.required])
  });
  selectedPhoneCode: string;
  showBack: number;


  lightRegisterInfo = true;
  showCompanyInput = false;
  showCompanyButton = 1;

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly authenticationService: AuthenticationService
  ) {
  }

  ngOnInit(): void {
    this.getCountryList();
    this.route.queryParams.subscribe(q => {
      this.showBack = 'showBack' in q ? parseInt(q.showBack, 10) : 0;
      this.showCompanyInput = 'company' in q ? (parseInt(q.company, 10) === 0) : false;
      this.lightRegisterInfo = 'form' in q ? (parseInt(q.form, 10) === 0) : true;
      this.showCompanyButton = 'company' in q ? parseInt(q.company, 10) : 1;
    });
  }

  getCountryList() {
    this.store.dispatch(new GetAllCountryListAction());
    this.countryList$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data?.length) {
          this.countryList = data?.filter(x => x.phoneCode !== null);
          this.selectedCountry = data[0];
          this.selectedPhoneCode = data[0].phoneCode;
          this.getSetCountryPhoneCode(this.selectedPhoneCode);
        }
      });
  }

  onSubmitForm() {
    console.log('submit', this.form.value);
    if (this.form.valid) {
      console.log('valid');
      this.sendForm();
    } else {
      validateAllFormFields(this.form);
    }
  }

  private sendForm() {
    // const { value } = this.form;
    // const data = {
    //   firstName: value.Name,
    //   lastName: value.Surname,
    //   email: value.Email,
    //   mobile: value.phoneNumber,
    //   agreements: value.agreements,
    // }
    // this.store.dispatch(new liteRegisterAction(data)).pipe(map(() => this.store.selectSnapshot(LoginState.liteUser))).
    // subscribe(user => {
    //   this.user = user;
    // })


  }

  onInputPhone(e) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  getSetCountryPhoneCode(e: any) {
    this.selectedPhoneCode = e;
    this.selectedCountry = this.countryList.find(x => x.phoneCode === this.selectedPhoneCode);
    this.form.patchValue({
      phoneCode: this.selectedCountry.phoneCode,
      countryId: this.selectedCountry.id,
    });
  }

  navigateToBack() {
    history.back();
  }

  openLoginPage() {

  }

  openRegisterForm(personalStatus: boolean) {
    this.lightRegisterInfo = false;
    this.showCompanyInput = personalStatus;
    this.convertCorporate();
  }

  convertCorporate() {
    this.showCompanyInput = !this.showCompanyInput;
    if (this.showCompanyInput) {
      this.form.addControl('CompanyName', new FormControl('CompanyName', [Validators.required]));
    } else {
      this.form.removeControl('CompanyName');
    }
  }
}
