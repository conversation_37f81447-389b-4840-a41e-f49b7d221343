@import "environment.scss";

.light-register-content {
  padding: 0;
  height: 100vh;
}
.register-form{
  overflow-y: auto;
  overflow-x: hidden;
  height: calc(100vh - 74px);
}
.form-container {
  height: 50vh;
  border-bottom-left-radius: 8px;
}
.footer-container {
  display: flex;
  height: 40vh;
  &:before {
    content: "";
    width: 100vw;
    height: 50vh;
    position: absolute;
    top: 50vh;
    left: 0;
    background: url("#{$assets}/bg.svg") no-repeat;
    background-size: cover;
    z-index: -1;
  }
}

.phone-area ::ng-deep .ng-select.ng-select-single .ng-select-container{
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.phone-area .phone{
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.bg-soft-info{
  background-color: #E9F6FC;
  color: #4A8EB0;
}
