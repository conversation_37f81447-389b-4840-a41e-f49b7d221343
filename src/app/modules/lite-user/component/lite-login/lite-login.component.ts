import {Component, OnInit} from '@angular/core';
import {getFormErrorMessage, isShowFormError, validateAllFormFields} from '../../../../util/form-error.util';
import {FormControl, FormGroup, Validators} from '@angular/forms';
import {CustomValidator} from '../../../../util/custom-validator';
import {ActivatedRoute, Router} from '@angular/router';

@Component({
  selector: 'app-light-login',
  templateUrl: './lite-login.component.html',
  styleUrls: ['./lite-login.component.scss']
})
export class LiteLoginComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  form: FormGroup = new FormGroup({
    Email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
  });
  formCode: FormGroup = new FormGroup({
    Code: new FormControl(null, [
      Validators.required,
      Validators.minLength(6),
      Validators.maxLength(6),
      Validators.min(100000),
      Validators.max(999999)
    ]),
  });
  formSendStatus: boolean;
  formCodeStatus: boolean;
  codeParam: number;
  emailParam: string;
  time = 30;
  displayTime = '00:00' ;
  interval: any;
  timeEnd = false;
  showBack: number;

  constructor(
    private readonly route: ActivatedRoute,
    private readonly router: Router,
    ) {
  }

  ngOnInit(): void {
    this.route.queryParams.subscribe(q => {
      this.codeParam = 'code' in q ? parseInt(q.code, 10) : 0;
      this.showBack = 'showBack' in q ? parseInt(q.showBack, 10) : 0;
      this.emailParam = 'email' in q ? q.email : null;
      if (this.codeParam && this.emailParam) {
        this.lightLoginWithCode();
        this.formCodeStatus = true;
      }
      if (this.emailParam && !this.codeParam) {
        this.lightLoginWithMail();
      }
    });
  }

  onSubmitForm() {
    console.log('submit', this.form.value);
    if (this.form.valid) {
      console.log('valid');
      this.formCodeStatus = true;
      this.startClock();
    } else {
      validateAllFormFields(this.form);
    }
  }

  codeKeyDown(e: any) {
    e.target.value = e.target.value.replace(/\D/g, '');
  }

  onSubmitCodeForm() {
    console.log('submit code', this.formCode.value);
    if (this.formCode.valid) {
      console.log('valid code');
      this.formSendStatus = true;
    } else {
      validateAllFormFields(this.formCode);
    }
  }

  private lightLoginWithCode() {
    if (this.codeParam) {
      console.log('codeParam: ' + this.codeParam);
      const patchValues = {
        Code: this.codeParam,
      };
      this.formCode.patchValue(patchValues);
      this.onSubmitCodeForm();
    }
  }

  private lightLoginWithMail() {
    if (this.emailParam) {
      console.log('mailParam: ' + this.emailParam);
      const patchValues = {
        Email: this.emailParam,
      };
      this.form.patchValue(patchValues);
      this.onSubmitForm();
    }
  }

  private startClock() {
    this.interval = setInterval(() => {
      if (this.time > 0) {
        this.time--;
      } else {
        this.stopClock();
        this.timeEnd = true;
      }
      this.displayTime = this.transform(this.time);
    }, 1000);
  }

  private stopClock() {
    clearInterval(this.interval);
    this.time = 30;
  }

  transform(value: number): string {
    const m = Math.floor(value / 60);
    const minutes = Math.floor(value / 60) > 9 ? Math.floor(value / 60) : '0' + Math.floor(value / 60);
    const seconds = (value - m * 60) > 9 ? (value - m * 60) : '0' + Math.floor(value - m);
    return minutes + ':' + seconds;
  }

  reSendCode() {
    console.log('resendCode');
    if (this.timeEnd) {
      console.log('resendCode');
      this.startClock();
      this.timeEnd = false;
    }
    return;
  }

  replaceEmail() {
    this.formCodeStatus = false;
    this.formCode.reset();
    this.stopClock();
  }

  openRegisterPage() {
    this.router.navigate(['lite', 'register']).then();
  }

  navigateToBack() {
    history.back();
  }
}
