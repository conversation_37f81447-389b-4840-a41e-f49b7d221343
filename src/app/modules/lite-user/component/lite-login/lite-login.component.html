<div class="light-login-content">
  <div class="form-container app-dashboard app-background px-4">
    <div class="h4 py-4 mb-0 text-center nav-back">
      <i class="icon icon-back mr-2 float-left" *ngIf="showBack" (click)="navigateToBack()"></i>
      <b>{{ "_light_login_header" | translate }}</b>
    </div>
    <ng-container *ngIf="!formCodeStatus">
      <form (submit)="onSubmitForm()" [formGroup]="form">
        <div class="form-group">
          <input
            [placeholder]="'_email' | translate"
            class="form-control form-control"
            formControlName="Email"
            type="text"
          />

          <div
            [ngClass]="{ 'd-block': isShowError(form.controls.Email) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(form.controls.Email) | translate }}
          </div>
        </div>

        <div class="form-group">
          <input
            appClickLog
            [section]="'LIGHT_LOGIN'"
            [subsection]="'LIGHT_LOGIN_SEND_CODE'"
            [value]="'_light_login_send_code_email' | translate"
            class="btn btn-warning btn-gradient btn-block text-white"
            type="submit"
          />
        </div>
      </form>
      <small class="text-black-50">
        <i class="icon icon-info"></i>
        {{'_light_login_send_code_mail_description'|translate}}
      </small>
    </ng-container>
    <ng-container *ngIf="formCodeStatus">
      <form (submit)="onSubmitCodeForm()" [formGroup]="formCode">
        <div class="form-group">
          <input
            [placeholder]="'_light_login_code' | translate"
            class="form-control form-control"
            formControlName="Code"
            type="number"
            autofocus
            (keydown)="codeKeyDown($event)"
          />

          <div
            [ngClass]="{ 'd-block': isShowError(formCode.controls.Code) }"
            class="invalid-feedback pl-3"
          >
            {{ getFormErrorMessage(formCode.controls.Code) | translate }}
          </div>
        </div>

        <div class="form-group">
          <input
            appClickLog
            [section]="'LIGHT_LOGIN'"
            [subsection]="'LIGHT_LOGIN_SEND_CODE'"
            [value]="'_light_login_confirm_code' | translate"
            class="btn btn-warning btn-gradient btn-block text-white"
            type="submit"
          />
        </div>
      </form>
      <small class="text-black-50">
        <i class="icon icon-info"></i>
        {{'_light_login_send_code_description'|translate}}
      </small>
      <div class="mb-4" (click)="replaceEmail()">
        <small>
          <b>{{form.value.Email}}</b> {{'_replace_email'|translate}}
          <i class="icon icon-edit"></i>
        </small>
      </div>
      <div class="d-flex flex-column justify-content-center mt-5">
        <div class="align-self-center">
          <div class="btn btn-light rounded-pill font-size-13px">
              {{displayTime}}
              <i class="icon icon-clock"></i>
          </div>
        </div>
        <small class="mt-4 text-center" (click)="reSendCode()">
          {{'_confirm_code'|translate}} <span class="text-primary">{{'_resend_code'}}</span>
        </small>
      </div>
    </ng-container>
  </div>
  <div class="footer-container px-4 mb-3">
    <div class="w-100 d-flex flex-column justify-content-center align-content-center">
      <div class="text-center mb-2">
        {{'_not_have_account_create'|translate}}
      </div>
      <div class="btn btn-info btn-block shadow-sm" (click)="openRegisterPage()">
        {{'_register'|translate}}
      </div>
    </div>
  </div>
</div>
