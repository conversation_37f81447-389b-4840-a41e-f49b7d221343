@import "environment.scss";

.light-login-content {
  padding: 0;
  height: 100vh;
}
.form-container {
  max-height: calc(100vh - 64px);
  min-height: calc(100vh - 300px);
  border-bottom-left-radius: 8px;
}
.footer-container {
  display: flex;
  height: calc(70vh - 140px);
  &:before {
    content: "";
    width: 100vw;
    height: calc(100vh - 120px);
    position: absolute;
    top: calc(100vh - 35vh);
    left: 0;
    background: url("#{$assets}/bg.svg") no-repeat;
    background-size: cover;
    z-index: -1;
  }
}
