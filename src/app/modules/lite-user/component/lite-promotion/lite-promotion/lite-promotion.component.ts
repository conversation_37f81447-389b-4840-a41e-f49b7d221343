import { Component, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { map } from 'rxjs/operators';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { LitePromotionAction } from 'src/app/modules/promotion/state/promotion.action';
import { PromotionState } from 'src/app/modules/promotion/state/promotion.state';

@Component({
  selector: 'app-lite-promotion',
  templateUrl: './lite-promotion.component.html',
  styleUrls: ['./lite-promotion.component.scss']
})
export class LitePromotionComponent implements OnInit {


  @Select(PromotionState.getLitePromotion)
  litePromotion$: any;

  liteData: any;

  constructor(
    private readonly store: Store
  ) { }

  ngOnInit() {
    this.store.dispatch(new LitePromotionAction()).pipe(map(() => this.store.selectSnapshot(PromotionState.getLitePromotion)))
    .subscribe(state => {
      this.liteData = state;
    }
    )
  }
  back() {
    history.back();
  }


}
