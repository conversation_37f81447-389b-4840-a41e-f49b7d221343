import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { CommonState } from 'src/app/shared/state/common/common.state';
import { CustomValidator } from 'src/app/util/custom-validator';
import { environment } from 'src/environments/environment';
import { LiteLoginState } from '../../state/lite-login.state';
import { LiteUserModel } from '../../model/lite-login.model';

@Component({
  selector: 'app-my-information',
  templateUrl: './my-information.component.html',
  styleUrls: ['./my-information.component.scss']
})
export class MyInformationComponent implements OnInit {
  @Select(LiteLoginState.user)
  user$: Observable<LiteUserModel>;

  @Select(CommonState.version)
  version$: Observable<string>;
  user: LiteUserModel;

  borusanCatLogo = `${environment.assets}/borusan-cat-logo.svg`;
  form: FormGroup = new FormGroup({
    Name: new FormControl({ value: null, disabled: true }, [Validators.required]),
    Surname: new FormControl({ value: null, disabled: true }, [Validators.required]),
    Email: new FormControl({ value: null, disabled: true }, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),
    phoneNumber: new FormControl({ value: null, disabled: true }, [Validators.required, Validators.minLength(7)]),
  });

  constructor(
    private readonly store: Store
  ) { }

  ngOnInit() {
    this.user$.subscribe(user => {
      this.user = user;
      if (this.user) {
        this.form.patchValue({
          Name: this.user.firstName,
          Surname: this.user.lastName,
          Email: this.user.email,
          phoneNumber: this.user.mobileOnly
        });
      }
    });
  }

  navigateToBack() {
    history.back();
  }
}
