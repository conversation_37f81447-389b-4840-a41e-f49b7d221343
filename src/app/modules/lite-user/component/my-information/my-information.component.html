<div class="app-sidebar">
  <div class="app-sidebar-area">
    <div class="h4 py-4 mb-0 text-center nav-back">
      <i class="icon icon-back ml-4 float-left" (click)="navigateToBack()"></i>
    </div>
    <div class="h4 pb-4 mb-0 text-center nav-back">
      <b>{{ "_my_information" | translate }}</b>
    </div>
    <div class="top">
      <div class="px-4 pb-5 mb-3 register-form">
        <form  [formGroup]="form">
          <div class="form-group" *ngIf="user?.firstName">
            <label class="labels" for="name"> {{'_name' | translate}} </label>
            <input
              id="name"
              class="form-control form-control"
              formControlName="Name"
              type="text"
            />

          </div>
          <div class="form-group" *ngIf="user?.lastName">
            <label class="labels" for="surname"> {{'_surname' | translate}} </label>
            <input
              id="surname"
              class="form-control form-control"
              formControlName="Surname"
              type="text"
            />

          </div>
          <div class="d-flex form-group" *ngIf="user?.email">
            <div class="input-area">
              <label class="labels" for="email"> {{'_email' | translate}} </label>
              <input
                id="email"
                class="form-control form-control"
                formControlName="Email"
                type="text"
              />
              <i class="icon icon-area icon-message-success mb-4" *ngIf="user?.isEmailVerified"></i>
            </div>
          </div>
          <div class="d-flex form-group" *ngIf="user?.mobileOnly">
            <div class="input-area">
              <label class="labels" for="phone"> {{'_phone' | translate}} </label>
              <input
              id="phone"
              class="form-control form-control"
              formControlName="phoneNumber"
              type="text"
            />
            <i class="icon icon-area icon-message-success mb-4" *ngIf="user?.isMobileVerified"></i>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div class="bottom-area">
      <div class="borusan-cat-logo">
        <img [src]="borusanCatLogo"  alt="BorusanCat"/>
      </div>
      <div class="version">{{ version$ | async }}</div>
    </div>
  </div>
</div>
