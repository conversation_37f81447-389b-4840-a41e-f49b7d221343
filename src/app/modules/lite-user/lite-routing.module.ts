import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { LiteComponent } from './container/light/lite.component';
import { LiteRegisterComponent } from './component/lite-register/lite-register.component';
import { LiteMenuComponent } from './component/lite-menu/lite-menu.component';
import { LiteConvertCorporateComponent } from './component/lite-convert-corporate/lite-convert-corporate.component';
import { MyInformationComponent } from './component/my-information/my-information.component';
import { LiteOttResolver } from './resolver/lite-ott.resolver';
import { LitePromotionComponent } from './component/lite-promotion/lite-promotion/lite-promotion.component';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'src/app/shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
const routes: Routes = [
  {
    path: '',
    component: LiteComponent,
    resolve: [LiteOttResolver],
    children: [
      // { path: 'login', component: LightLoginComponent },
      { path: 'register', component: LiteRegisterComponent },
      { path: 'menu', component: LiteMenuComponent },
      { path: 'convert', component: LiteConvertCorporateComponent },
      { path: 'my-information', component: MyInformationComponent },
      { path: 'promotion', component: LitePromotionComponent },
    ],
  },
];

@NgModule({
  imports: [CommonModule, RouterModule.forChild(routes),    SharedModule,
    TranslateModule],
  exports: [RouterModule],
  providers: [LiteOttResolver]
})
export class LiteRoutingModule {}
