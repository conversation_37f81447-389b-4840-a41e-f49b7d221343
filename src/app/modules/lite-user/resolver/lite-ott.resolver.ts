import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { LoginResponseModel } from '../../authentication/model/login-response.model';
import { Store } from '@ngxs/store';
import { Observable, of } from 'rxjs';
import { Injectable } from '@angular/core';
import { LoginState } from '../../authentication/state/login/login.state';
import { takeWhile, tap, timeoutWith } from 'rxjs/operators';

@Injectable()
export class LiteOttResolver implements Resolve<LoginResponseModel | boolean> {
  constructor(
    private readonly store: Store,
  ) {
  }

  resolve({ queryParams: { ott } }: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<LoginResponseModel | boolean> {
    if (ott) {
      // return this.store.dispatch(
      //   new LiteLoginWithOttAction(ott),
      // );
    } else {
      console.log('LITE OTT resolver');
      const loginLoading = this.store.selectSnapshot(LoginState.loginLoading);
      if (loginLoading) {
        return this.store.select(LoginState.loginLoading)
          .pipe(tap((val) => console.log('::::::::::LOGIN STATE CHAGE', val)))
          .pipe(takeWhile(i => i === true))
          .pipe(timeoutWith(30000, of(false)));
      }
    }

    return of(true);
  }

}
