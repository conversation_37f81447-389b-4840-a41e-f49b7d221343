import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { TranslateService } from '@ngx-translate/core';
import { LoginState } from '../../../authentication/state/login/login.state';
import { combineLatest, Observable } from 'rxjs';
import { LiteLoginState } from '../../state/lite-login.state';
import { LiteGetMeAction } from '../../state/lite-login.actions';

@Component({
  selector: 'app-light',
  templateUrl: './lite.component.html',
  styleUrls: ['./lite.component.scss']
})
export class LiteComponent implements OnInit {
  @Select(LoginState.language)
  language$: Observable<string>;

  @Select(LiteLoginState.loginLoading)
  loadingLoading$: Observable<boolean>;
  loading = false;

  constructor(
    private readonly store: Store,
    private readonly translateService: TranslateService,
  ) {
  }

  ngOnInit(): void {
    this.language$.subscribe((languageCode) => {
      if (languageCode) {
        console.log('language changed: ' + languageCode);
        this.translateService.use(languageCode);
      }
    });

    combineLatest([this.loadingLoading$])
      .subscribe((value) => {
        this.loading = value.reduce((acc, item) => acc || item, false);
      });

    this.store.dispatch(new LiteGetMeAction());
  }

}
