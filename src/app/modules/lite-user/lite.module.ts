import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LiteRegisterComponent } from './component/lite-register/lite-register.component';
import { LiteLoginComponent } from './component/lite-login/lite-login.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgSelectModule } from '@ng-select/ng-select';
import { LiteMenuComponent } from './component/lite-menu/lite-menu.component';
import { LiteConvertCorporateComponent } from './component/lite-convert-corporate/lite-convert-corporate.component';
import { MyInformationComponent } from './component/my-information/my-information.component';
import { LiteComponent } from './container/light/lite.component';
import { LiteRoutingModule } from './lite-routing.module';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { LitePromotionComponent } from './component/lite-promotion/lite-promotion/lite-promotion.component';
import { PromotionModule } from '../promotion/promotion.module';


@NgModule({
  declarations: [
    LiteRegisterComponent,
    LiteLoginComponent,
    LiteComponent,
    LiteMenuComponent,
    LiteConvertCorporateComponent,
    MyInformationComponent,
    LitePromotionComponent
  ],
  imports: [
    CommonModule,
    RouterModule,
    SharedModule,
    TranslateModule,
    FormsModule,
    LiteRoutingModule,
    ReactiveFormsModule,
    NgSelectModule,
    PromotionModule
  ]
})
export class LiteModule {
  constructor() {
    stopLoadingAnimation();
  }
}
