import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { TranslatePipe } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { IframeAction } from 'src/app/modules/customer/state/iframe/iframe.actions';
import { NotificationState } from 'src/app/modules/notification/state/notification/notification.state';

@Component({
  selector: 'app-borusan-user',
  templateUrl: './borusan-user.component.html',
  styleUrls: ['./borusan-user.component.scss'],
  providers: [TranslatePipe],
})
export class BorusanUserComponent implements OnInit, OnDestroy {

  @Select(NotificationState.loading)
  loading$: Observable<boolean>;
  constructor(
    private readonly store: Store,
    private readonly translatePipe: TranslatePipe
  ) { }

  ngOnInit() {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: this.translatePipe.transform('_company_search'),
      })
    );

    this.store.dispatch(
      new IframeAction({
        active: true,
        closeButton: false,
        pageTitle: this.translatePipe.transform('_company_search') ,
      })
    );
  }

  ngOnDestroy() {
    this.store.dispatch(
      new IframeAction({
        active: false,
        closeButton: false,
        pageTitle: null,
      })
    );
  }
}
