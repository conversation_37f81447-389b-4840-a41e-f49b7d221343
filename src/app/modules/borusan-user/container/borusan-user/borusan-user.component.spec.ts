/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { BorusanUserComponent } from './borusan-user.component';

describe('BorusanUserComponent', () => {
  let component: BorusanUserComponent;
  let fixture: ComponentFixture<BorusanUserComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BorusanUserComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BorusanUserComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
