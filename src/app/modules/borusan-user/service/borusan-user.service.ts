import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { BorusanCustomersModel, BorusanGetRelatedPersonsModel, BorusanRelatedPersonsModel, BorusanSearchModel } from '../model/borusan.model';
import { MeResponse } from '../../customer/response/me.response';

@Injectable({
  providedIn: 'root'
})
export class BorusanUserService {

  constructor(private readonly http: HttpClient) { }

  setBorusanCustomerSelect(
    companyId: string,
    customerId: string,
    customerRelatedPersonId: string
  ): Observable<MeResponse> {
    return this.http.post<HttpResponse<{ me: MeResponse }>>(`${environment.api}/borusan/customer/select`, {
      companyId, customerId, customerRelatedPersonId
    })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data?.me;
          }
          return null;
        })
      );
  }

  getBorusanCustomersSearch(search: string, dataSource: string, pageSize?: number, pageNumber?: number)
    : Observable<any> {
    return this.http.post<HttpResponse<any>>(
      `${environment.api}/borusan/customer/search`,
      { search, dataSource, pageSize, pageNumber }
    )
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val;
          }
          return null;
        })
      );
  }

  getBorusanRelatedPersons(customerId): Observable<BorusanRelatedPersonsModel> {
    return this.http.post<HttpResponse<BorusanRelatedPersonsModel>>(`${environment.api}/borusan/customer/relatedpersons`,
      { customerId })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getBorusanLastVisited(pageSize: number, pageNumber: number): Observable<BorusanCustomersModel> {
    return this.http
      .post<HttpResponse<BorusanCustomersModel>>(`${environment.api}/borusan/lastvisited`,
        { pageSize, pageNumber })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

}
