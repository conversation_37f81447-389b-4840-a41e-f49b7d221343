import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PermissionGuard } from 'src/app/core/guards/permission.guard';
import { BorusanCustomerSearchComponent } from './component/borusan-customer-search/borusan-customer-search.component';
import { BorusanRelatedpersonComponent } from './component/borusan-relatedperson/borusan-relatedperson.component';
import { BorusanUserComponent } from './container/borusan-user/borusan-user.component';

const routes: Routes = [
  {
    path: '',
    component: BorusanUserComponent,
    canActivate: [PermissionGuard],
    children: [
      { path: '', component: BorusanCustomerSearchComponent },
      // { path: '/search-relatedPerson', component: BorusanRelatedpersonComponent },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class BorusanUserRoutingModule {}
