export interface BorusanGetCustomerSelectModel {
  customerNumber: string;
  dataSource: string;
  relatedPersonCrmId: string;
}

export interface BorusanGetRelatedPersonsModel{
  customerNumber: string;
  dataSource: string;
}

export interface BorusanRelatedPersonsModel {
  relatedPersons: BorusanRelatedPersonModel[];
}
export interface BorusanRelatedPersonModel {
  id: string;
  customerId: string;
  dataSourceLabel: string;
  email: string;
  isArchived: boolean;
  name: string;
  relatedPersonCrmId: string;
}

export interface BorusanSearchModel {
  search: string;
  // dataSource: string;
  pageSize?: number;
  pageNumber?: number;
}

export interface BorusanCustomersModel {
  customers: BorusanCustomerModel[];
  totalCount: number;
}

export interface BorusanCustomerModel {
  id: string;
  name: string;
  customerNumber: string;
  dataSourceLabel: string;
  isArchived: boolean;
  company: {
    id: string;
    companyCode: string;
    countryCode: string;
    name: string;
    catDealerCode: string;
  };
  relatedPersons: BorusanRelatedPersonModel[] | any;
}

export interface BorusanUserPaging{
  totalCount: number;
  pageSize: number;
  pageNumber: number;
  pageIndex: number;
}

export interface BorusanUserPagingModel{
  pageIndex: number;
  totalCount: number;
  pageSize: number;
  pageNumber: number;
}
