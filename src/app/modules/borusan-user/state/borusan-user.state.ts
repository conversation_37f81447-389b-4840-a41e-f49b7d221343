import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { BorusanCustomerModel, BorusanCustomersModel, BorusanRelatedPersonsModel, BorusanUserPagingModel } from '../model/borusan.model';
import { BorusanUserService } from '../service/borusan-user.service';
import {
  BorusanUserGetLastVisitedAction,
  BorusanUserCustomerSearchAction,
  BorusanUserGetRelatedPersonsAction,
  BorusanUserResetLastVisitedAction,
} from './borusan-user.action';

export interface BorusanUserStateModel {
  lastVisited: BorusanCustomersModel;
  customers: BorusanCustomerModel[];
  relatedPersons: BorusanRelatedPersonsModel;
  borusanUserLoading: boolean;
  paging: BorusanUserPagingModel;
}

@State<BorusanUserStateModel>({
  name: 'borusan_user',
  defaults: {
    lastVisited: null,
    customers: [],
    relatedPersons: null,
    borusanUserLoading: false,
    paging: null,
  },
})
@Injectable()
export class BorusanUserState {

  constructor(
    private readonly borusanUserService: BorusanUserService,
  ) { }

  @Selector()
  public static getLastvisited({ lastVisited }: BorusanUserStateModel): BorusanCustomersModel {
    return lastVisited;
  }

  @Selector()
  public static getBorusanCustomer({ customers }: BorusanUserStateModel): BorusanCustomerModel[] {
    return customers;
  }

  @Selector()
  public static getBorusanRelatedPersons({ relatedPersons }: BorusanUserStateModel): BorusanRelatedPersonsModel {
    return relatedPersons;
  }

  @Selector()
  public static isLoading({ borusanUserLoading }: BorusanUserStateModel): boolean {
    return borusanUserLoading;
  }

  @Selector()
  public static paging({paging}: BorusanUserStateModel): BorusanUserPagingModel{
    return paging;
  }

  @Action(BorusanUserGetLastVisitedAction)
  getBorusanUserLastvisited(
    { getState, patchState }: StateContext<BorusanUserStateModel>,
    paging
  ) {
    patchState({
      borusanUserLoading: true,
    });
    return this.borusanUserService.getBorusanLastVisited(paging.pageSize, paging.pageNumber)
      .pipe(
        tap((value) => {
          patchState({
            borusanUserLoading: false,
            lastVisited: value,
          });
        }),
        catchError((err) => {
          patchState({
            borusanUserLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(BorusanUserCustomerSearchAction)
  borusanUserCustomerSearch(
    { getState, patchState }: StateContext<BorusanUserStateModel>,
    { search, dataSource, pageSize, pageNumber }) {
    patchState({
      borusanUserLoading: true,
    });
    const page = pageNumber;
    return this.borusanUserService.getBorusanCustomersSearch(search, dataSource, pageSize, pageNumber)
      .pipe(
        tap((value) => {
          let list = page === 1 ? [] : getState().customers;
          list = list.concat(value?.data?.customers);
          patchState({
            borusanUserLoading: false,
            paging: value.paging,
            customers: list,
          });
        }),
        catchError((err) => {
          patchState({
            borusanUserLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(BorusanUserGetRelatedPersonsAction)
  getBorusanUserRelatedPersons(
    { getState, patchState }: StateContext<BorusanUserStateModel>,
    { customerId }) {
    patchState({
      borusanUserLoading: true,
    });
    return this.borusanUserService.getBorusanRelatedPersons(customerId)
      .pipe(
        tap((value) => {
          patchState({
            borusanUserLoading: false,
            relatedPersons: value,
          });
        }),
        catchError((err) => {
          patchState({
            borusanUserLoading: false,
          });
          return throwError(err);
        }),
      );
  }

  @Action(BorusanUserResetLastVisitedAction)
  resetBorusanUserRelatedPersons(
    { patchState }: StateContext<BorusanUserStateModel>
  ) {
    return patchState({
      lastVisited: null,
    });
  }
}
