export class BorusanUserGetLastVisitedAction {
  public static readonly type = '[<PERSON>rusan<PERSON><PERSON>] get Last Visited ';

  constructor(public pageSize: number, public pageNumber: number) { }
}

export class BorusanUserCustomerSearchAction {
  public static readonly type = '[BorusanUser] Customer Search ';

  constructor(public search: string, public dataSource: string, public pageSize?: number, public pageNumber?: number) { }
}

export class BorusanUserGetRelatedPersonsAction {
  public static readonly type = '[BorusanUser] Releated Persons ';

  constructor(public customerId: string) { }
}

export class BorusanUserSetSelectedCustomerAction {
  public static readonly type = '[BorusanUser] Customer Change ';

  constructor(public companyId: string, public customerId: string, public customerRelatedPersonId: string) { }
}

export class BorusanUserResetLastVisitedAction {
  public static readonly type = '[BorusanUser] reset Last Visited ';

  constructor(){}
}
