import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BorusanCustomerSearchComponent } from './component/borusan-customer-search/borusan-customer-search.component';
import { BorusanLastvisitedComponent } from './component/borusan-lastvisited/borusan-lastvisited.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgSelectModule } from '@ng-select/ng-select';
import { BorusanUserComponent } from './container/borusan-user/borusan-user.component';
import { BorusanUserRoutingModule } from './borusan-user-routing.module';
import { NgbDropdownModule, NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BorusanRelatedpersonComponent } from './component/borusan-relatedperson/borusan-relatedperson.component';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule,
    SharedModule,
    NgSelectModule,
    BorusanUserRoutingModule,
    NgbTooltipModule,
    NgbDropdownModule,
    FormsModule,
    ReactiveFormsModule
  ],
  declarations: [
    BorusanUserComponent,
    BorusanCustomerSearchComponent,
    BorusanLastvisitedComponent,
    BorusanRelatedpersonComponent,
  ],
  exports: [
    BorusanLastvisitedComponent,
  ]
})
export class BorusanUserModule { }
