/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { BorusanLastvisitedComponent } from './borusan-lastvisited.component';

describe('BorusanLastvisitedComponent', () => {
  let component: BorusanLastvisitedComponent;
  let fixture: ComponentFixture<BorusanLastvisitedComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BorusanLastvisitedComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BorusanLastvisitedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
