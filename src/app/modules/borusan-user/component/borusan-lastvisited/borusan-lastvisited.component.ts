import { Component, OnDestroy, OnInit, Output, EventEmitter, Input } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BorusanCustomerModel, BorusanCustomersModel } from '../../model/borusan.model';
import { BorusanUserGetLastVisitedAction, BorusanUserResetLastVisitedAction } from '../../state/borusan-user.action';
import { BorusanUserState } from '../../state/borusan-user.state';

@Component({
  selector: 'app-borusan-lastvisited',
  templateUrl: './borusan-lastvisited.component.html',
  styleUrls: ['./borusan-lastvisited.component.scss']
})
export class BorusanLastvisitedComponent implements OnInit, OnDestroy {
  lastVisited: BorusanCustomersModel;

  @Select(BorusanUserState.getLastvisited)
  lastVisited$: Observable<BorusanCustomersModel>;

  @Output()
  selectedLastVisitedCustomer: EventEmitter<BorusanCustomerModel> = new EventEmitter<BorusanCustomerModel>();

  @Input() sliceNumber = 26;
  @Input() isBorusanPage = false;
  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly store: Store,
  ) { }

  ngOnInit() {
    this.store.dispatch(new BorusanUserResetLastVisitedAction());
    this.store.dispatch(new BorusanUserGetLastVisitedAction(5, 1));
    this.lastVisited$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe((data) => {
      if (data){
        this.lastVisited = data;
        if (this.isBorusanPage && this.lastVisited.customers?.length){
          this.selectCustomer(this.lastVisited.customers[0]);
        }
      }
    });
  }

  selectCustomer(customer: BorusanCustomerModel) {
    this.selectedLastVisitedCustomer.emit(customer);
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
