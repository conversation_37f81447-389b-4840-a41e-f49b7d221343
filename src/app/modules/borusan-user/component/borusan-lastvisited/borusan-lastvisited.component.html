<div class="borusan-lastvisited">
  <ul class="mr-0">
    <li *ngFor="let customer of lastVisited?.customers; index as currentIndex">
      <div>
        <input type="radio" [id]="'lastVisitedCustomer' + currentIndex" [checked]="currentIndex === 0" name="lastVisited" (click)="selectCustomer(customer)" />
        <label [for]="'lastVisitedCustomer' + currentIndex" class="d-flex flex-row">
          <div class="d-flex flex-column">
            <span>
              ({{ customer?.company?.countryCode }})
              {{ customer?.name | slice :0: (customer?.isArchived ? 18 : 26) }}
              {{ customer?.name?.length > 18 ? '...' : ''}}
            </span>
            <small>
              {{ '_customer_number' | translate}}: {{ customer?.customerNumber }}
            </small>
          </div>
          <small *ngIf="customer?.isArchived" class="badge badge-danger ml-auto align-self-center font-size-12px opacity-75">
            {{ '_archived' | translate }}
          </small>
        </label>
      </div>
    </li>
  </ul>
</div>
