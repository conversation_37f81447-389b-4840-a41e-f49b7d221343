.related-persons-class {
  .list-area {
    max-height: calc(100vh - 230px);
    overflow: auto;
  }

  .search-area {
    input {
      height: 25px;
      padding-left: 47px;
    }

    .form-control {
      width: 100%;
      max-width: calc(90vw - 4rem);
    }

    i {
      position: absolute;
      left: 16px;
      //top: 50%;
      top: 26px;
      font-size: 18px;
      line-height: 18px;
      height: 18px;
      margin-top: -9px;
    }
  }

  ul {
    list-style: none;
    margin: 0 9px;
    padding: 0;

    // max-height: 58vh;
    li {
      margin: 10px 0;

      &:first-child {
        margin-top: 0;
      }

      label {
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis;
        max-width: 90%;
      }
    }
  }

  [type="radio"]:checked,
  [type="radio"]:not(:checked) {
    position: absolute;
    left: -9999px;
  }

  [type="radio"]:checked+label,
  [type="radio"]:not(:checked)+label {
    position: relative;
    padding-left: 36px;
    cursor: pointer;
    display: inline-block;
    color: #666;
    font-size: 16px;
    line-height: 24px;
  }

  [type="radio"]:checked+label:before,
  [type="radio"]:not(:checked)+label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 25%;
    width: 18px;
    height: 18px;
    border: 1px solid #ddd;
    border-radius: 100%;
    background: #fff;
  }

  [type="radio"]:checked+label:before {
    border-color: #ffa300;
  }

  [type="radio"]:checked+label:after,
  [type="radio"]:not(:checked)+label:after {
    content: "";
    width: 12px;
    height: 12px;
    background: #ffa300;
    position: absolute;
    top: calc(25% + 4px);
    left: 4px;
    border-radius: 100%;
    -webkit-transition: all 0.2s ease;
    transition: all 0.2s ease;
  }

  [type="radio"]:not(:checked)+label:after {
    opacity: 0;
    -webkit-transform: scale(0);
    transform: scale(0);
  }

  [type="radio"]:checked+label:after {
    opacity: 1;
    -webkit-transform: scale(1);
    transform: scale(1);
  }

}

.font-size-24px {
  font-size: 24px;
}

.font-size-30px {
  font-size: 30px;
}

.opacity-75{
  opacity: 0.75;
}
