<div class="mb-3 related-persons-class">

  <!-- ? RP Search area -->
  <div class="mb-3">
    <div class="d-flex flex-row">
      <div class="w-100 search-area position-relative">
        <input #rpsearch [placeholder]="'_search' | translate" maxlength="200"class="form-control search-input" type="text" [(ngModel)]="rpSearch" />
        <i class="icon icon-search"></i>
      </div>
    </div>
  </div>
  <ng-container *ngIf="customer else firstCustomerSelect">
    <!-- ? Related Person -->
    <div class="list-area">
      <div *ngIf="relatedPersons?.length else notMultiblePerson">
        <ng-container *ngIf="(relatedPersons | search:'name,email':rpSearch)?.length else personSearchNull">
          <ul>
            <li *ngFor="let person of relatedPersons | search:'name,email':rpSearch; index as currentIndex;">
              <div>
                <input type="radio" [id]="'person' + currentIndex" [checked]="relatedPersonSelected?.id === person?.id" name="relatedPersons"
                  (click)="selectRelatedPersonClick(person)" />
                <label [for]="'person' + currentIndex" class="d-flex flex-column">
                  <div class="d-flex flex-row">
                    <span *ngIf="person?.name else nullPersonName">
                      {{ person?.name | slice :0: (person?.isArchived ? 18 : 26) }}
                      {{ person?.name?.length > 18 ? '...' : ''}}
                    </span>
                    <small *ngIf="person?.isArchived" class="badge badge-danger ml-auto align-self-center font-size-12px opacity-75">
                      {{ '_archived' | translate }}
                    </small>
                  </div>
                  <ng-template #nullPersonName>
                    <span>
                      -
                    </span>
                  </ng-template>
                  <small>
                    {{ '_email' | translate}}: {{ person?.email || "-" }}
                  </small>
                </label>
              </div>
            </li>
          </ul>
        </ng-container>
        <ng-template #personSearchNull>
          <span *ngIf="rpSearch" class="text-danger">
            {{ '_related_person_not_found' | translate }}
          </span>
        </ng-template>
      </div>
    </div>
    <ng-template #notMultiblePerson>
      <span class="text-center">
        {{ '_not_multible_related_person' | translate }}
      </span>
    </ng-template>
  </ng-container>
  <ng-template #firstCustomerSelect>
    <span class="text-center">
      {{ '_first_select_customer' | translate }}
    </span>
  </ng-template>
</div>
<app-error-modal [(status)]="showErrorModal" [message]="'_general_error_message'" [isTranslate]="true"></app-error-modal>
