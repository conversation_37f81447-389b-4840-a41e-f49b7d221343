/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { BorusanRelatedpersonComponent } from './borusan-relatedperson.component';

describe('BorusanRelatedpersonComponent', () => {
  let component: BorusanRelatedpersonComponent;
  let fixture: ComponentFixture<BorusanRelatedpersonComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BorusanRelatedpersonComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BorusanRelatedpersonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
