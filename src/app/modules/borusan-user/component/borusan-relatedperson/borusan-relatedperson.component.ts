import { Component, Input, OnDestroy, OnInit, Output, EventEmitter, OnChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { fromEvent, Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { BorusanCustomerModel, BorusanRelatedPersonsModel, BorusanRelatedPersonModel } from '../../model/borusan.model';
import { BorusanUserGetRelatedPersonsAction } from '../../state/borusan-user.action';
import { BorusanUserState } from '../../state/borusan-user.state';

@Component({
  selector: 'app-borusan-relatedperson',
  templateUrl: './borusan-relatedperson.component.html',
  styleUrls: ['./borusan-relatedperson.component.scss']
})
export class BorusanRelatedpersonComponent implements OnInit, OnChanges, OnDestroy, AfterViewInit {

  @Input()
  customer: BorusanCustomerModel;

  @Output()
  selectedRelatedPerson: EventEmitter<BorusanRelatedPersonModel> = new EventEmitter<BorusanRelatedPersonModel>();

  @Output()
  relatedPersonLenght: EventEmitter<number> = new EventEmitter<number>();

  @Select(BorusanUserState.getBorusanRelatedPersons)
  relatedPersons$: Observable<BorusanRelatedPersonsModel>;
  relatedPersons: BorusanRelatedPersonModel[];
  relatedPersonSelected: BorusanRelatedPersonModel;
  rpSearch = '';

  @Select(BorusanUserState.isLoading)
  isLoading$: Observable<boolean>;
  showErrorModal = false;
  @ViewChild('rpsearch') input: ElementRef;
  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly store: Store,
  ) { }

  ngOnInit() {
    this.relatedPersons$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(persons => {
        if (persons) {
          this.relatedPersonSelected = persons?.relatedPersons[0];
          this.selectRelatedPersonClick(this.relatedPersonSelected);
          this.relatedPersons = persons?.relatedPersons;
          this.relatedPersonLenght.emit(persons?.relatedPersons?.length);
        }
      },
        (error) => {
          this.showErrorModal = true;
        });
  }

  selectRelatedPersonClick(person) {
    this.selectedRelatedPerson.emit(person);
  }

  ngOnChanges(): void {
    if (this.customer) {
      this.rpSearch = '';
      this.store.dispatch(new BorusanUserGetRelatedPersonsAction(this.customer?.id));

    }
  }

  ngAfterViewInit(): void {
    fromEvent(this.input.nativeElement, 'keyup')
      .subscribe(($event) => {
        if (this.rpSearch?.length) {
          this.relatedPersonSelected = null;
          this.selectRelatedPersonClick(this.relatedPersonSelected);
        }
      });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
