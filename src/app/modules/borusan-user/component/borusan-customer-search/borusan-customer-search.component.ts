import {
  AfterContentChecked, AfterViewInit, ChangeDetectorRef, Component,
  ElementRef, Input, OnDestroy, OnInit, ViewChild
} from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute } from '@angular/router';
import { NgbTooltip } from '@ng-bootstrap/ng-bootstrap';
import { Navigate } from '@ngxs/router-plugin';
import { Select, Store } from '@ngxs/store';
import { fromEvent, Observable, Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, takeUntil, tap } from 'rxjs/operators';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { LogService } from 'src/app/shared/service/log.service';
import { BorusanCustomerModel, BorusanRelatedPersonModel, BorusanUserPagingModel } from '../../model/borusan.model';
import { <PERSON>rusanUserCustomerSearchAction, BorusanUserSetSelectedCustomerAction } from '../../state/borusan-user.action';
import { BorusanUserState } from '../../state/borusan-user.state';
import { ResetCampaignAction } from 'src/app/modules/customer/state/catalog/catalog.actions';

@Component({
  selector: 'app-borusan-customer-search',
  templateUrl: './borusan-customer-search.component.html',
  styleUrls: ['./borusan-customer-search.component.scss']
})
export class BorusanCustomerSearchComponent implements OnInit, AfterViewInit, OnDestroy, AfterContentChecked {
  @Input()
  showRelatedPersonIcon = true;
  @Input()
  showRegionIcon = true;

  @Select(BorusanUserState.getBorusanCustomer)
  borusanCustomers$: Observable<BorusanCustomerModel[]>;
  borusanCustomers: BorusanCustomerModel[];

  @Select(BorusanUserState.isLoading)
  isLoading$: Observable<boolean>;
  isLoading = true;

  @Select(UserState.userLoading)
  userLoading$: Observable<boolean>;
  userLoading = false;

  @Select(BorusanUserState.paging)
  paging$: Observable<BorusanUserPagingModel>;
  paging: BorusanUserPagingModel;

  selectedCustomer: BorusanCustomerModel;
  relatedPerson: BorusanRelatedPersonModel;
  relatedPersonLenght: number;
  isChangeButton = false;
  showRPlist = false;
  dataSource = 'SAP';
  page = 1;

  @ViewChild('search') input: ElementRef;
  form: any;

  @ViewChild('t') tooltip: NgbTooltip;
  searchText = '';

  hasSearch = false;
  showPageSize = 12;
  isForceLdpa = false;
  showErrorModal = false;
  protected subscriptions$: Subject<boolean> = new Subject();
  protected subscriptionsSearch$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly log: LogService,
    private readonly fb: FormBuilder,
    private readonly cdRef: ChangeDetectorRef,
    private readonly route: ActivatedRoute,
  ) { }

  ngOnInit() {
    this.route.queryParams
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(params => {
        if (params?.forceLdpa) {
          this.isForceLdpa = params.forceLdpa;
        }
      });

    this.form = this.fb.group({
      search: [null, Validators.minLength(3)],
    });

    this.borusanCustomers$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.borusanCustomers = data;
      },
        (error) => {
          this.showErrorModal = true;
        });

    this.paging$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (data) {
          this.paging = data;
          this.page = this.paging.pageNumber;
        }
      });

    this.isLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(load => this.isLoading = load);

    this.userLoading$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(load => this.userLoading = load);

    this.log.action('BORUSAN_USER', 'CUSTOMER_SEARCH_PAGE_OPEN', {
      isForceLdpa: this.isForceLdpa
    });
  }

  selectCompanyClick(customer: BorusanCustomerModel) {
    this.selectedCustomer = customer;
    this.log.action('BORUSAN_USER', 'CUSTOMER_SELECTED', {
      selectedCompanyId: this.selectedCustomer?.company?.id,
      selectedCustomerId: this.selectedCustomer?.id,
    });
    this.isChangeButton = true;
  }

  selectedRelatedPerson(person: BorusanRelatedPersonModel) {
    this.relatedPerson = person;
    this.log.action('BORUSAN_USER', 'RELATED_PERSON_SELECTED', {
      selectedCompanyId: this.selectedCustomer?.company?.id,
      selectedCustomerId: this.selectedCustomer?.id,
      relatedPersonId: this.relatedPerson?.id
    });
  }

  getRelatedPersonLenght(e: number) {
    if (typeof (e) === 'number') {
      this.relatedPersonLenght = e;
    }
  }

  goRelatePersonList() {
    if (this.relatedPersonLenght === 1) {
      this.changeCompanyClick();
    } else {
      this.showRPlist = true;
    }
  }

  changeCompanyClick() {
    const action = this.store.dispatch(new BorusanUserSetSelectedCustomerAction(
      this.selectedCustomer?.company.id,
      this.selectedCustomer?.id,
      this.relatedPerson?.id
    ));
    action.pipe(takeUntil(this.subscriptions$))
      .subscribe(
        (c) => {
          if (c) {
            this.log.action('BORUSAN_USER', 'CHANGE_COMPANY', {
              selectedCompanyId: this.selectedCustomer?.company?.id,
              selectedCustomerId: this.selectedCustomer?.id,
              relatedPersonId: this.relatedPerson?.id
            });
            this.store.dispatch(new Navigate(['dashboard']));
          }
        },
        (error) => {
          this.showErrorModal = true;
        }
      );
    this.store.dispatch(new ResetCampaignAction());
  }

  searchCompanyList() {
    this.page = 1;
    this.borusanCustomers = null;
    if (this.searchText) {
      this.store.dispatch(new BorusanUserCustomerSearchAction(
        this.searchText, this.dataSource, this.showPageSize, this.page
      ));
      this.hasSearch = true;

      this.log.action('BORUSAN_USER', 'CUSTOMER_SEARCH', {
        searchText: this.searchText
      });
      this.selectedCustomer = null;
    }
  }

  loadCompanyList() {
    if (this.searchText) {
      this.store.dispatch(new BorusanUserCustomerSearchAction(
        this.searchText, this.dataSource, this.showPageSize, this.page
      ));
      this.hasSearch = true;

      this.log.action('BORUSAN_USER', 'CUSTOMER_LOADING', {
        searchText: this.searchText
      });
      this.selectedCustomer = null;
    }
  }

  openDashboard() {
    this.store.dispatch(new Navigate(['dashboard']));
  }

  onScroll(e) {
    if ((e?.target?.offsetHeight + e?.target?.scrollTop >= e?.target?.scrollHeight) && !this.isLoading) {
      this.nextPage();
    }
  }

  nextPage() {
    if (this.paging.pageNumber * this.paging.pageSize < this.paging.totalCount) {
      this.page++;

      this.log.action('BORUSAN_USER', 'CUSTOMER_SEARCH_PAGE_SCROLL', {
        page: this.page,
        totalCount: this.paging.totalCount,
        pageSize: this.paging.pageSize,
        searchText: this.searchText
      });

      this.loadCompanyList();
    }
  }

  ngAfterViewInit(): void {
    fromEvent(this.input.nativeElement, 'keyup')
      .pipe(
        filter(Boolean),
        tap((event) => {
          if (this.form.controls.search.hasError('minlength')) {
            this.tooltip.open();
          }
        }),
        debounceTime(400),
        distinctUntilChanged(),
        filter(
          (i) =>
            this.input.nativeElement.value.length >= 3 ||
            this.input.nativeElement.value.length === 0,
        ),
        tap((event) => {
          this.tooltip.close();
          if (this.form.controls.search?.length === 0) {
            this.hasSearch = false;
          }
        }),
      )
      .subscribe(($event) => {
        this.searchText = this.form.get('search').value || '';
      });
  }

  ngAfterContentChecked() {
    this.cdRef.detectChanges();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
