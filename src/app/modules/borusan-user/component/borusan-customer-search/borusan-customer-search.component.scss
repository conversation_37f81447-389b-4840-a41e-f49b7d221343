.borusan-header {
  display: inline-block;
  background: #f5f4f4;
  top: 0;
  left: 0;
  width: 100%;
  padding: 11px 0px;
  z-index: 50;
  max-height: 57px;
  &-title {
    float: left;
    color: #2c2c22;
    line-height: 35px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    max-width: 70%;
    font-weight: normal;
    font-size: 16px;
  }

  button {
    border: none;
    background: none;
    width: 40px;
    height: 35px;
    &.back {
      float: left;
      font-size: 19px;
      line-height : 2em;
    }
    &.close {
      float: left;
      position: relative;
      line-height: 1em;
      // top: 3px;
    }
  }
}
.borusan-body {
  height: calc(100vh - 90px);
}

.borusan-customer-search {
  box-sizing: initial;
  // position: relative;

  .list-area {
    // max-height: calc(100vh - 220px);
    overflow: auto;
  }

  .search-area {
    input {
      height: 25px;
      padding-left: 47px;
    }

    .form-control {
      width: 100%;
      max-width: calc(90vw - 10rem);
    }

    i {
      position: absolute;
      left: 16px;
      //top: 50%;
      top: 26px;
      font-size: 18px;
      line-height: 18px;
      height: 18px;
      margin-top: -9px;
    }
  }

  ul {
    list-style: none;
    margin: 0 9px;
    padding: 0;

    // max-height: 58vh;
    li {
      margin: 10px 0;

      &:first-child {
        margin-top: 0;
      }

      label {
        white-space: nowrap;
        overflow: hidden !important;
        text-overflow: ellipsis;
        max-width: 90%;
      }
    }
  }

  :host ::ng-deep .tooltip .tooltip-inner {
    background-color: #444444;
  }

  :host ::ng-deep .tooltip .arrow::before {
    border-left-color: #444444;
  }

}

.borusan-related-person-list {
  box-sizing: initial;
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 100;
  transform: translateX(100%);
  transition: all 0.3s ease;
  background-color: #fff;

  &.show {
    transition: all 0.3s ease;
    transform: translateX(0);
  }
}

// Checkbox
[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 20px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 4px;
  left: 4px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 4px;
  left: 4px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

// Radio
[type="radio"]:checked,
[type="radio"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="radio"]:checked+label,
[type="radio"]:not(:checked)+label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 24px;
}

[type="radio"]:checked+label:before,
[type="radio"]:not(:checked)+label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 25%;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  border-radius: 100%;
  background: #fff;
}

[type="radio"]:checked+label:before {
  border-color: #ffa300;
}

[type="radio"]:checked+label:after,
[type="radio"]:not(:checked)+label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: calc(25% + 4px);
  left: 4px;
  border-radius: 100%;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="radio"]:not(:checked)+label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="radio"]:checked+label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.opacity-75{
  opacity: 0.75;
}
