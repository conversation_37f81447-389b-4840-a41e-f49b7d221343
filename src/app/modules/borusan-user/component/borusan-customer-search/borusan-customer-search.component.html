<div class="borusan-customer-search">
  <div class="borusan-header">
    <button type="button" class="button close">
      <i class="icon icon-x" *ngIf="!isForceLdpa" (click)="openDashboard()"></i>
    </button>
    <div class="borusan-header-title">
      {{ '_select_company' | translate }}
    </div>
  </div>
  <div #list class="px-3 py-2 d-flex flex-column borusan-body">
    <!-- ? Search area -->
    <div class="mb-2">
      <div class="d-flex flex-row flex-nowrap">
        <div class="search-area position-relative" [formGroup]="form">
          <input #search [placeholder]="'_search' | translate" maxlength="200" class="form-control search-input d-inline-block" type="text"
            formControlName="search" placement="bottom" [ngbTooltip]="'_search_min_length_3' | translate" triggers="none" #t="ngbTooltip"
            />
          <i class="icon icon-search"></i>
        </div>
        <button class="flex-grow-1 btn btn-warning text-white ml-3" (click)="searchCompanyList()" [disabled]="!(searchText?.length >= 3)">
          {{ '_search' | translate }}
        </button>
      </div>
    </div>
    <!-- ? Search Response List -->
    <div class="list-area" (scroll)="onScroll($event)" [class.d-none]="!hasSearch">
      <ul *ngIf="borusanCustomers?.length else searchNotFound">
        <li *ngFor="let customer of borusanCustomers; index as currentIndex">
          <div>
            <input type="radio" [id]="'customer' + currentIndex" name="customer" (click)="selectCompanyClick(customer)" />
            <label [for]="'customer' + currentIndex" class="d-flex flex-row">
              <div class="d-flex flex-column">
                <span>
                  ({{ customer?.company?.countryCode }})
                  {{ customer?.name | slice :0: (customer?.isArchived ? 18 : 26) }}
                  {{ customer?.name?.length > 18 ? '...' : ''}}
                </span>
                <small>
                  {{ '_customer_number' | translate}}: {{ customer?.customerNumber }}
                </small>
              </div>
              <small *ngIf="customer?.isArchived" class="badge badge-danger ml-auto align-self-center font-size-12px opacity-75">
                {{ '_archived' | translate }}
              </small>
            </label>
          </div>
        </li>
      </ul>
      <ng-template #searchNotFound>
        <span *ngIf="showRelatedPersonIcon && !isLoading" class="text-danger">
          {{ '_customer_not_found' | translate }}
        </span>
      </ng-template>
    </div>
    <!-- ? Last Visited Company -->
    <div class="list-area" [class.d-none]="hasSearch">
      <app-borusan-lastvisited (selectedLastVisitedCustomer)="selectCompanyClick($event)" [isBorusanPage]="true"></app-borusan-lastvisited>
    </div>
    <!-- ? Next Button -->
    <div class="mt-auto text-center pt-3">
      <button class="btn btn-warning text-white px-5 font-weight-bolder w-50" [disabled]="!selectedCustomer" (click)="goRelatePersonList()">
        {{ (relatedPersonLenght === 1 ? '_change_company' : '_next') | translate }}
      </button>
    </div>
  </div>
</div>
<div class="borusan-related-person-list" [class.show]="showRPlist">
  <div class="borusan-header">
    <button type="button" class="button back">
      <i class="icon icon-back" (click)="showRPlist = false"></i>
    </button>
    <div class="borusan-header-title">
      {{ '_select_related_person' | translate }}
    </div>
  </div>
  <div class="px-3 py-2 d-flex flex-column borusan-body">
    <div class="list-area">
      <app-borusan-relatedperson [customer]="selectedCustomer"
      (selectedRelatedPerson)="selectedRelatedPerson($event)" (relatedPersonLenght)="getRelatedPersonLenght($event)">
      </app-borusan-relatedperson>
    </div>

    <!-- ? Change Company Button -->
    <div class="mt-auto text-center">
      <button class="btn btn-warning text-white px-5 font-weight-bolder w-50" [disabled]="!relatedPerson" (click)="changeCompanyClick()">
        {{ '_change_company' | translate }}
      </button>
    </div>
  </div>
</div>
<app-error-modal [(status)]="showErrorModal" [message]="'_general_error_message'" [isTranslate]="true"></app-error-modal>
<app-loader [show]="isLoading || userLoading"></app-loader>
