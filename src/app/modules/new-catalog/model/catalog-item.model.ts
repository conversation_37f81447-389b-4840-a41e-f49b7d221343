export interface CatalogItemModel {
  brand: string;
  detailPageUrl: string;
  detailUrl: string;
  filters: CatalogFilter[];
  imageCaption: string;
  imageTitle: string;
  imageUrl: string;
  productGroupId: string;
  productGroupName: string;
  productId: string;
  productLongName: string;
  productName: string;
  productSubGroupId: string;
  productSubGroupName: string;
  products: CatalogItemModel[];
  sort: any;
  specFrequency: any;
  specMaximumKwa: any;
  specMinimumKwa: any;
}

export interface CatalogFilter {
  filterGroupName: string;
  filterGroupValue: string;
  items: {
    filterName: string;
    filterValue: string;
    filterValueId: string;
    filterValueProperty: string;
  }[];
}


export interface CatalogEquipmentModel {
  findProductId: string
  findParentProductId: string
  productName: string
  productLongName: string
  productId: string
  productGroupId: string
  productGroupName: string
  productSubGroupId: string
  productSubGroupName: string
  brand: string
  imageUrl: string
  imageTitle: string
  imageCaption: string
  specFrequency: any
  specMinimumKwa: any
  specMaximumKwa: any
  specOperatingWeight: string
  detailPageUrl: string
  detailUrl: any
  formUrl: string
  sort: any
  description: string
  showSpecifications: boolean
  filters: any[]
  specifications: SpecificationModel[]
  products: any[]
  isSelected: boolean
  detail: boolean
  model: string
  title: string
  webUrl: string
}

export interface SpecificationModel {
  sort: number
  name: string
  value: string
}
