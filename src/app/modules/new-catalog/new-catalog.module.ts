import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CatalogFilterViewComponent } from './container/catalog-card-view/catalog-filter-view.component';
import { SharedModule } from '../../shared/shared.module';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule } from '@angular/forms';
import { CatalogSpecsComponent } from './container/catalog-specs/catalog-specs.component';


@NgModule({
  declarations: [
    CatalogFilterViewComponent,
    CatalogSpecsComponent
  ],
  exports: [
    CatalogFilterViewComponent,
    CatalogSpecsComponent
  ],
  imports: [
    CommonModule,
    SharedModule,
    TranslateModule,
    ReactiveFormsModule,
  ]
})
export class NewCatalogModule {}
