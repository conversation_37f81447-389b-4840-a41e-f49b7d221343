import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { MenuModel } from '../../../customer/model/menu.model';
import { FormControl, FormGroup } from '@angular/forms';
import { CatalogFilter, CatalogItemModel } from '../../model/catalog-item.model';
import { Select, Store } from '@ngxs/store';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { Observable } from 'rxjs';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { systemFeature } from 'src/app/util/system-feature.util';
import { ActivatedRoute } from '@angular/router';
import { LogService } from 'src/app/shared/service/log.service';

@Component({
  selector: 'app-catalog-filter-view',
  templateUrl: './catalog-filter-view.component.html',
  styleUrls: ['./catalog-filter-view.component.scss'],
})
export class CatalogFilterViewComponent implements OnInit {

  @Input() catalog: CatalogItemModel | any;
  @Input() categories: CatalogItemModel[] | any;
  // tslint:disable-next-line:no-output-on-prefix
  @Output() onClick: EventEmitter<{
    item: MenuModel;
    index: number;
  }> = new EventEmitter();

  @Output() filterFormData: EventEmitter<any> = new EventEmitter();
  filterModal = false;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  originals: any;
  filtering = false;
  opportunityProducts = false;

  form = new FormGroup({});
  systemFeatureEquipmentOrder = false;
  private reservedForm: any;

  constructor(
    private readonly store: Store,
    private readonly activatedRoute: ActivatedRoute,
    private readonly log: LogService
  ) { }

  ngOnInit() {
    this.opportunityProducts = this.activatedRoute.snapshot.queryParams['opportunityProducts'];

    // setTimeout(() =>
    //   console.log('item', this.catalog), 1000);

    this.catalog?.filters?.map((filter: CatalogFilter) => {
      const controls = {};

      filter.items.map((item) => {
        controls[item.filterValue] = new FormControl(null, []);
      });

      this.form.addControl(filter.filterGroupName, new FormGroup(controls));

      filter.items.map((item) => {
        const controls = {};

        controls[item.filterValueId] = new FormControl(null, []);
        this.form.addControl(item.filterValueProperty, new FormGroup(controls));
      });
    });

    if (window.history.state?.filterFormData) {
      try {
        this.form.setValue(window.history.state?.filterFormData);
        this.filter();
      } catch (err) {}
    }

    this.systemFeatures$
      .subscribe(features => {
        const isBorusanUser = this.store.selectSnapshot(LoginState.user);
        if (!isBorusanUser?.isLdapLogin) {
          this.systemFeatureEquipmentOrder = systemFeature('equipment_order', features, false);
        }

        if (isBorusanUser?.isLdapLogin) {
          this.systemFeatureEquipmentOrder = systemFeature('equipment_order_borusan_user', features, false);
        }
      });
  }

  onClickItem(item: MenuModel, index: number) {
    if(item?.isPrice) {
      this.log.action('OpportunityProduct', 'EquipmentDetail', {
        categoryName: item?.productGroupName,
        brand: item?.brand,
        title: item?.title,
        productId: item?.productId,
        productGroupId: item?.productGroupId,
        productName: item?.productName,
        isPrice: item?.isPrice
      }).subscribe();
    }
    this.onClick.emit({ item, index });
  }

  filter() {
    if (!this.originals) {
      this.originals = this.categories;
    }

    const rawFilters = this.form.value;
    this.filterFormData.emit(rawFilters);
    console.log('RAW_FILTERS', rawFilters);
    const filters = Object.entries(rawFilters).filter(([, values]: [string, any]) => {
      return Object.values(values).reduce((acc, item) => acc || item, false);
    });
    this.filtering = filters?.length > 0;

    console.log('FILTERS', filters);


    this.categories = this.originals.filter(item => {
      return filters.reduce((stt, [key, values]: [string, any]) => {
        const property = item[key[0].toLowerCase() + key.substr(1)];
        // console.log('key', key);
        // console.log('PROP', property);
        // console.log('VALUEs', values);
        //
        // console.log('STATE', stt);
        return stt && values[property];
      }, true);
    });

    this.filterModal = false;
  }

  openFilter() {
    this.reservedForm = this.form.value;
    this.filterModal = true;
  }

  modalClosed($event: boolean) {
    console.log('modalClosed', $event);
    this.form.patchValue(this.reservedForm);
  }

  hasItemsWithCount(items: any[]): boolean {
    if (this.opportunityProducts) {
      return items.some(item => item.count - 1 > 0);
    }

    return items.some(item => item.count > 0);
  }
}
