.link-categories {
  display: flex;
  flex-wrap: wrap;
  margin: -10px;

  .category {
    flex-basis: calc(50% - 20px);
    flex-grow: 0;
    margin: 10px;
    margin-bottom: 15px;
    border-radius: 4px;
    padding: 10px;
    border: 1px solid #f1f1f1;
    box-shadow: 0px 0px 6px 0px rgba(210, 210, 210, 0.64);

    .category-item-badge{
      position: absolute;
      top: 0;
      right: 0;
      background-color: #ffa300;
      color: #fff;
      font-weight: bold;
      border-radius: 0 4px 0 4px;
      z-index: 12;
      padding: .125rem .55rem;

      .icon-container{
        transform: rotate(45deg);
      }  
    }
    
    .isRental-category-item-badge{
      position: absolute;
      top: 0;
      left: 0;
      background-color: #4A8EB0;
      z-index: 12;
      padding: .125rem .55rem;
      border-radius: 4px 0 4px 0;
      background-color: #4A8EB0;
    }

    .title {
      font-size: 14px;
      color: #505050;
      margin-top: 10px;
      text-align: center;
      line-height: 18px;
    }
  }
}

#image-container {
  display: inline-block;
  position: relative;
  width: 100%;

  &:before {
    content: "";
    display: block;
    margin-top: 75%;
  }
}

#element {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  //background-color: #f2f2f2;

  img {
    position: absolute;
    top: 50%;
    left: 50%;
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    max-width: 100%;
    max-height: 100%;
  }
}


[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}

.filter-btn {
  width: 80%;
}

.header-filter {
  position: absolute;
  top: 11px;
  right: 11px;
  line-height: 35px;
  color: #000;

  &-red{
    color: #ff7474;
  }


  span {
    font-size: 9px;
  }
}
