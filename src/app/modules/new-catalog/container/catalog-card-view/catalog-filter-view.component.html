<div class="header-filter mr-2"
     [class.header-filter-red]="filtering">
  <i (click)="openFilter()" class="icon icon-filter"></i>
  <span>{{this.categories.length}}</span>
</div>
<div class="link-categories card-view">
  <div *ngFor="let item of categories; let i = index" class="category position-relative">

    <ng-container *ngIf="systemFeatureEquipmentOrder">
      <div class="category-item-badge" *ngIf="item?.isPrice">
        <div class="icon-container">
          <i class="icon icon-coupon"></i>
        </div>
      </div>
      <div class="isRental-category-item-badge" *ngIf="item?.isUsed">
        <span class="font-weight-semi-bold text-white font-size-12px">{{ "_second_hand" | translate }}</span>
      </div>
    </ng-container>

    <a (click)="onClickItem(item, i)"
       appClickLog [section]="'CATALOG'" [subsection]="'SUB_CATALOG_CLICK'"
       [data]="{
         id:item?.id,
         title: item?.title,
         productId: item?.productId,
         productGroupId: item?.productGroupId
        }"
    >

      <div id="image-container">
        <div id="element">
          <img height="" [src]="item.imageUrl" [alt]="item.title"/>
        </div>
      </div>
      <!--      <div class="image-container">-->
      <!--        <img height="" [src]="item.imageUrl" [alt]="item.title" />-->
      <!--      </div>-->
      <div class="title">{{ item.title }}</div>
    </a>
  </div>
</div>
<app-big-modal [(status)]="filterModal" [headerText]="'_filters'| translate" [width]="'80vw'"
(statusChange)="modalClosed($event)"
>
  <div [formGroup]="form">
    <div *ngFor="let filter of catalog?.filters" [formGroupName]="filter.filterGroupName">
      <div *ngIf="filter.filterGroupName !== 'ProductStatus'" >
        <div class="font-weight-semi-bold mb-2">
          {{filter?.filterGroupValue}}
        </div>
        <div class="form-check" *ngFor="let item of filter.items;let i = index">
          <input [id]="filter?.filterGroupValue+i" class="form-check-input" type="checkbox"
                [formControlName]="item.filterValue">
          <label [for]="filter?.filterGroupValue+i">{{item.filterValue}}</label>
        </div>
      </div>
    </div>
    <div *ngFor="let filter of catalog?.filters">
      <div *ngIf="filter.filterGroupName === 'ProductStatus'">
        <div class="font-weight-semi-bold mb-2" *ngIf="hasItemsWithCount(filter.items)">
          {{filter?.filterGroupValue}}
        </div>
        <div class="form-check" *ngFor="let item of filter.items;let i = index"  [ngClass]="{'d-none': opportunityProducts && item.filterValueProperty === 'IsPrice'}" [formGroupName]="item.filterValueProperty">
          <div *ngIf="item.count > 0">
            <input [id]="item?.filterValueProperty+i" class="form-check-input" type="checkbox"
                    [formControlName]="item.filterValueId">
            <label [for]="item?.filterValueProperty+i" >{{item.filterValue}}</label>
          </div>
        </div>
      </div>
    </div>
    <div class="m-3 mt-4 text-center">
      <button class="btn btn-sm btn-warning text-white filter-btn"
              (click)="filter()"
      >
        {{'_filter' | translate}}
      </button>
    </div>
  </div>

</app-big-modal>
