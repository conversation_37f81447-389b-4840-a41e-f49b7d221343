import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';
import { BoomClubAdvantagesModel, BoomClubCategoriesModel, BoomClubInterestModel, BoomClubJoinModel, BoomClubUserModel, SalesOfficeModel, BoomCoinPromotionProductsModel, PromotionModel } from '../model/promotion.model';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class PromotionService {
  constructor(
    private readonly httpClient: HttpClient
  ) {}

  getPoints(param?: { key: string, value: string }) {
    const p = param ? `?${param.key}=${param.value}` : '';
    return this.httpClient.get<HttpResponse<PromotionModel>>(`${environment.api}/loyality/get/points${p}`);
  }

  getLiteUserPoints() {
    return this.httpClient.get<HttpResponse<PromotionModel>>(`${environment.api}/loyality/get/points/liteuser`);
  }

  boomClubRegister(form: any, headers = {}): Observable<HttpResponse<any>> {
    return this.httpClient.post<HttpResponse<any>>(
      `${environment.api}/form/boomclub/create`,
      form,
      {
        headers,
      }
    );
  }

  sendCoinPromotionRequest(form: any): Observable<HttpResponse<any>> {
    return this.httpClient.post<HttpResponse<any>>(`${environment.api}/form/promotion/request`, form);
  }

  getPSSRAccount(): Observable<HttpResponse<any>> {
    return this.httpClient.get<HttpResponse<any>>(`${environment.api}/user/getBoomCoins`);
  }

  boomClubJoin(form: any, headers = {}): Observable<HttpResponse<any>>{
    return this,this.httpClient.post<HttpResponse<any>>(`${environment.api}/boomclub/add`, form, {headers})
  }

  boomClubCategories(){
    return this.httpClient.get<HttpResponse<BoomClubCategoriesModel[]>>(`${environment.api}/boomclub/categories`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  boomClubAdvantages(isUsed: boolean){
    return this.httpClient.get<HttpResponse<BoomClubAdvantagesModel[]>>(`${environment.api}/boomclub/advantages${isUsed ? '?isUsed=true' : ''}`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  boomClubAdvantageDetail(id: string){
    return this.httpClient.get<HttpResponse<BoomClubAdvantagesModel>>(`${environment.api}/boomclub/advantagedetails?AdvantageId=${id}`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  boomClubUserMe(){
    return this.httpClient.get<HttpResponse<BoomClubUserModel>>(`${environment.api}/boomclub/me`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  boomClubInterests(){
    return this.httpClient.get<HttpResponse<BoomClubInterestModel[]>>(`${environment.api}/boomclub/interests`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  boomClubJoinAdvantage(advantageId: string){
    return this.httpClient.post<HttpResponse<BoomClubJoinModel>>(`${environment.api}/boomclub/join`, { AdvantageId: advantageId }).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  salesOffices(countryCode: string){
    return this.httpClient.get<HttpResponse<SalesOfficeModel[]>>(`${environment.api}/loyality/get/addresses?countryCode=${countryCode}`).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data;
        }
        return null;
      })
    );
  }

  boomCoinPromotionProducts(countryCode: string, limit: number , offset: number){
    return this.httpClient.post<HttpResponse<BoomCoinPromotionProductsModel>>(`${environment.api}/loyality/get/products`, {countryCode, limit, offset} ).pipe(
      map((val) => {
        if (val.code === 0) {
          return val.data?.rows || [];
        }
        return null;
      })
    );
  }

}
