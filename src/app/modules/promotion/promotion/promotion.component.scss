@import "variable/icon-variable";
.point {
  height: calc(100vh - 74px);
  background: linear-gradient(180deg, white, #80808082),
    url("../../../../assets/promotion_background_image.png");
  background-repeat: no-repeat;
  background-position: bottom;
  background-size: 101vw 62vh;

}

.point-show {
  border-radius: 100%;
  height: 150px;
  width: 150px;
  padding: 25px 25px;
  background: #fff;
  border: 30px solid #ffa300;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
}

.point-text {
  font-size: rem;
  font-weight: bold;
}

.coin-button {
  background-color: #ffa300;
  border-radius: 6px;
  width: calc(100vw - 70px);
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-weight: 600;
  font-size: 17px;
  color: white;
  padding: 5px 20px;
  margin-right: 15px;
  margin-bottom: 10px;
}

.diamond-button{
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-weight: 600;
  font-size: 20px;
  color: rgba(77, 142, 160, 1);
  padding-right: 55px;
  padding-top: 10px;
  margin-bottom: 10px;
  width: 318px;
  height: 109px;
  border-radius: 5px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.16);
}

.gold-button{
  position: relative;
  display: flex;
  align-items: center;
  text-decoration: none;
  font-weight: 600;
  font-size: 20px;
  color: rgba(238, 188, 72, 1);
  padding-left: 26px;
  padding-top: 10px;
  margin-bottom: 10px;
  width: 318px;
  height: 109px;
  border-radius: 5px;
  box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.16);
}

ngb-accordion .card {
  border-radius: 0px !important;
  margin: 0px 10px 0px 10px;
  &-header {
    padding: 0.25rem !important;
    background-color: #fff;
    border: none;
    margin-bottom: -5px !important;
    .btn {
      width: 100%;
      float: left;
      text-align: left !important;
      box-shadow: none;
      border-radius: 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.125);
      font-weight: bold;
    }

    .btn-link:hover {
      box-shadow: none;
      text-decoration: none;
    }

    button:not(.collapsed) {
      .icon-chevron-down {
        transform: rotate(-180deg);
      }
    }

    .icon-chevron-down {
      line-height: 1em;
      height: 1em;
      transition: all 0.4s ease;
    }
  }
  &-body {
    max-height: calc(100vh - 420px) !important;
    overflow-y: scroll !important;
    overflow-x: scroll !important;
    padding: 0.25rem !important;
  }
}

.point-history {
  margin: 0px 0px;
}

.table-header {
  font-size: 12px;
  text-align: center;
  &-break {
    font-size: 12px;
    text-align: center;
    word-break: break-all;
  }
}

.spend-point {
  color: red;
}

.gain-point {
  color: green;
}

.expire-point {
  color: #ffa300;
}

.page-content {
  height: calc(100vh - 120px) !important;
}

.font-size-12 {
  font-size: 12px;
}

.font-size-14 {
  font-size: 14px;
}

.font-size-10 {
  font-size: 10px;
}

.font-size-16 {
  font-size: 16px;
}

.text-area {
  margin: 10px 0 10px 25px;
  width: 80%;
}

.text-area-diamond {
  text-align: center;
  align-self: center;
}

.text-area-gold {
  text-align: center;
  align-self: center;
}

.text-area-body{
  // margin: 0px 0 10px 25px;
  width: 100%;
  text-align: -webkit-center;
}

.redirect-text {
  font-weight: bold;
}

.use-conditions {
  bottom: 0;
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  text-align: center;
  opacity: 0.7;
  height: 30px;
  color: white;
  &-text {
    margin-bottom: 0.05rem !important;
  }
}

ngb-accordion .btn:focus .btn:hover {
  box-shadow: none;
}

h6 {
  font-size: 0.8rem !important;
}

.boom-coin {
  position: absolute;
  right: 0;
}

.boom-diamond {
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
}

.coin-point-area {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 20px;
}

.diamond-point-area{
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.gold-point-area{
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}

.row {
  margin-right: 0 !important;
  margin-left: 0 !important;
}

.col {
  padding-left: 0.25rem !important;
  padding-right: 0.25rem !important;
}

.custom-card-body {
  background-color: #f3f3f3;
  border-radius: 8px !important;
}

.custom-card {
  border: 0 !important;
  padding: 1px !important;
  height: 80px;
  width: 75px;
}

.point-box {
  // padding-left: 15px;
  padding-right: 20px;
  margin-top: 15px;
}

.custom-card-text {
  bottom: 0;
  position: absolute;
  margin-left: auto;
  margin-right: auto;
  left: 0;
  right: 0;
  text-align: center;
  margin-bottom: 6px !important;
}

.operation-history-table {
  // margin-left: 15px !important;
    // margin-right: 15px !important;
  width: calc(100vw - 45px) !important;
  margin: 0 auto;
}

.point-header {
  margin: 10px 0 10px 25px;
  width: 70%;
}

.description-text-diamond{
  color: rgba(80, 80, 80, 1);
  font-family: Poppins;
  font-size: 15px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0px;
  text-align: left;
  width: 328px;
  height: 46px;
}

.description-text-gold{
  color: rgba(80, 80, 80, 1);
  font-family: Poppins;
  font-size: 15px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0px;
  text-align: left;
  width: 328px;
  height: 46px;
}

.body-text-diamond{
  text-align: center;
  color: rgba(77, 142, 160, 1);
  font-size: 52px;
  font-weight: 900;
  line-height: 84px;
  letter-spacing: 0px;
  margin-top: 50px;
  width: 83%;
}

.body-text-gold{
  text-align: center;
  color: rgba(238, 188, 72, 1);
  font-size: 52px;
  font-weight: 900;
  line-height: 84px;
  letter-spacing: 0px;
  margin-top: 50px;
  width: 83%;
}

.go-home-button{
  background: linear-gradient(91.31deg, #FFA300 0%, #FFB532 100%);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
  color: white;
  height: 50px;
  border-radius: 6px;
  border: none;
  width: 90%;
}

.go-home-text{
  font-family: Poppins;
  font-size: 16px;
  font-weight: 700;
  line-height: 28px;
  letter-spacing: 0px;
  text-align: center;
  color: rgba(255, 255, 255, 1);
}

.page-content-diamond{
  text-align: center;
}

.page-content-gold{
  text-align: center;
}

.boom-gold-icon{
  position: absolute;
  right: 25px;
  top: 50%;
  transform: translateY(-50%);
}

.point-diamond{
  height: calc(100vh - 90px);
}