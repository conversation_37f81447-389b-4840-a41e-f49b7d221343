<div *ngIf="(!pssrAccount?.isPssrUser && !pssrAccount?.isPssrManagerUser) || !(boomDiamondSystemFeature && boomGoldSystemFeature)" class="point">
  <div class="page-content">
    <!-- <div class="point-header">
      <h4>
        <strong>{{ "_point" | translate }}</strong>
      </h4>
    </div> -->
    <div class="point-area d-flex justify-content-center align-items-center" [hasPermission]="PermissionEnum.MenuPromotionPortal">
      <div class="coin-point-area">
        <p class="coin-button" (click)="redirect()"
           appClickLog
           [section]="'PROMOTION_PORTAL'"
           [subsection]="'PROMOTION_PORTAL_CLICK'">
          {{ pointHistory?.totalPoint }} {{ '_boom_coin' | translate }}
        </p>
        <img
          src="assets/boom-coin.png"
          class="boom-coin"
          alt=""
          width="130"
          height="130"
        />
      </div>
    </div>
    <div class="text-area" [hasPermission]="PermissionEnum.MenuPromotionPortal">
      <p class="description-text font-size-16">
        {{ "_can_use_boom_coins_gifts_promotions" + (currentCustomer?.countryCode === 'KZ' ? '_KZ':'') | translate }}.
        <span
          *ngIf="boomCoinShopShow || lite"
          (click)="redirect()"
          appClickLog
          [section]="'PROMOTION_PORTAL'"
          [subsection]="'PROMOTION_PORTAL_CLICK'"
          class="redirect-text text-primary"
        >
          {{ "_click_use_now" | translate }}
          <i class="icon icon-external-link font-size-22px"></i>
        </span>
      </p>
    </div>
    <div class="point-history" *ngIf="pointHistory">
      <ngb-accordion
        #acc="ngbAccordion"
        [closeOthers]="true"
        activeIds="earned_points"
      >
        <ngb-panel *ngIf="earnedPointsSystemFeature" id="earned_points">
          <ng-template ngbPanelTitle>
            <div
              class="d-flex justify-content-between"
              style="padding-left: 5px"
            >
              <div class="d-flex">{{ "_your_earned_points" | translate }}</div>
              <div class="d-flex">
                <i class="text-decoration-none icon icon-chevron-down"></i>
              </div>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <div class="row row-cols-sm-4 point-box">
              <ng-container
                *ngFor="let earnedPoint of earnedPoints; index as i"
              >
                <div class="col col-3 mb-4">
                  <div class="card custom-card">
                    <div class="card-body text-center custom-card-body">
                      <h6
                        class="card-title">{{earnedPoint?.name?.length > 21 ? (earnedPoint?.name | slice: 0:21) + "..." : earnedPoint?.name}}</h6>
                      <p class="card-text custom-card-text">
                        {{ earnedPoint.point }}
                      </p>
                    </div>
                  </div>
                </div>
                <div *ngIf="(i + 1) % 4 == 0 && i != 0" class="w-100"></div>
              </ng-container>
            </div>
          </ng-template>
        </ngb-panel>
        <ngb-panel *ngIf="activitySystemFeature" id="operation_history"
        >
          <ng-template ngbPanelTitle>
            <div
              class="d-flex justify-content-between"
              style="padding-left: 5px"
            >
              <div class="d-flex">{{ "_coin_activity" | translate }}</div>
              <div class="d-flex">
                <i class="text-decoration-none icon icon-chevron-down"></i>
              </div>
            </div>
          </ng-template>
          <ng-template ngbPanelContent>
            <table class="table table-striped table-sm operation-history-table">
              <thead>
              <tr>
                <th class="table-header" scope="col">
                  {{ "_point_type" | translate }}
                </th>
                <th class="table-header" scope="col">
                  {{ "_point" | translate }}
                </th>
                <th class="table-header" scope="col">
                  {{ "_description" | translate }}
                </th>
                <th class="table-header" scope="col">
                  {{ "_operation_date" | translate }}
                </th>
                <th class="table-header" scope="col">
                  {{ "_expire_date" | translate }}
                </th>
              </tr>
              </thead>
              <tbody>
              <tr *ngFor="let operation of pointHistory?.details; index as i">
                <td
                  class="table-header"
                  [ngClass]="{
                      'spend-point': operation.pointType == 'Spend',
                      'gain-point': operation.pointType == 'Gain',
                      'expire-point': operation.pointType == 'Expire'
                    }"
                >
                  {{ operation.pointTypeDescription }}
                </td>
                <td class="table-header">{{ operation.point }}</td>
                <td class="table-header" [class]="operation.description.length > 50 ? 'table-header-break': ''">{{ operation.description }}</td>
                <td class="table-header">
                  {{ operation.createdDate | date: "dd.MM.yyyy" }}
                </td>
                <td class="table-header">
                  {{ operation.expireDate | date: "dd.MM.yyyy" }}
                </td>
              </tr>
              </tbody>
            </table>
          </ng-template>
        </ngb-panel>
      </ngb-accordion>
    </div>
    <!-- <div class="use-conditions">
      <p
        class="font-size-14 use-conditions-text"
        (click)="openUseCondition()"
        [innerHTML]="'_terms_of_use' | translate"
      ></p>
    </div> -->
  </div>
</div>
<div *ngIf=" boomDiamondSystemFeature && pssrAccount?.isPssrManagerUser" class="point-diamond px-3 py-2 d-flex flex-column">
    <div class="point-area d-flex justify-content-center align-items-center">
      <div class="diamond-point-area">
        <p class="diamond-button" (click)="redirectLoyalty()"
           appClickLog
           [section]="'PROMOTION_PORTAL'"
           [subsection]="'PROMOTION_PORTAL_CLICK'">
          {{ pssrAccount?.boomCoin }} {{ pssrAccount?.boomCoinTypeName }}
        </p>
        <img
          src="assets/diamond-icon-mid.svg"
          class="boom-diamond"
          alt=""/>
      </div>
    </div>
    <div class="text-area-diamond text-center">
      <p class="description-text-diamond">
        {{ "_diamond_description_text" | translate }}
      </p>
    </div>
    <div class="text-area-body">
      <p class="body-text-diamond"> {{"_very_soon_text" | translate}} </p>
      <img src="assets/diamond1.svg">
    </div>
    <div class="mt-auto text-center pt-3" >
      <button type="button" class="go-home-button btn btn-warning text-white px-5 font-weight-bolder">
        <p class="go-home-text" (click)="openDashboard()"> {{ "_return_to_homepage" | translate}} </p>
      </button>
    </div>
</div>

<div *ngIf="boomGoldSystemFeature && pssrAccount?.isPssrUser" class="point-diamond px-3 py-2 d-flex flex-column">
    <div class="point-area d-flex justify-content-center align-items-center">
      <div class="gold-point-area">
        <p class="gold-button" (click)="redirectLoyalty()"
           appClickLog
           [section]="'PROMOTION_PORTAL'"
           [subsection]="'PROMOTION_PORTAL_CLICK'">
          {{ pssrAccount?.boomCoin }} {{ pssrAccount?.boomCoinTypeName }}
        </p>
        <img
          src="assets/kulcealtin_2.png"
          class="boom-gold-icon"
          alt=""/>
      </div>
    </div>
    <div class="text-area-gold text-center">
      <p class="description-text-gold">
        {{ "_gold_description_text" | translate }}
      </p>
    </div>
    <div class="text-area-body">
      <p class="body-text-gold"> {{"_very_soon_text" | translate}} </p>
      <img src="assets/kulcealtin_3.png">
    </div>
    <div class="mt-auto text-center pt-3" >
      <button type="button" class="go-home-button btn btn-warning text-white px-5 font-weight-bolder">
        <p class="go-home-text" (click)="openDashboard()"> {{ "_return_to_homepage" | translate}} </p>
      </button>
    </div>
</div>
<app-loader [show]="(loading$ | async)"></app-loader>
<app-basic-modal [(status)]="liteUserPopupShow" [headerText]="'_info' | translate" >
  <div class="d-flex flex-column justify-content-center align-items-center">
    <div class="m-1">
      {{'_use_coin_lite_user' | translate}}
    </div>
    <div class="my-4">
      <button class="btn btn-info btn-gradient btn-block text-white rounded-lg btn-sm" (click)="redirectCorporate()">{{'_convert_corporate' | translate}}</button>
    </div>
  </div>
</app-basic-modal>
