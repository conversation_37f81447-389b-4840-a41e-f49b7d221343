import { Component, Input, On<PERSON><PERSON>roy, OnInit, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';
import { PromotionModel, PssrAccountModel } from '../model/promotion.model';
import { PromotionState } from '../state/promotion.state';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { systemFeature } from 'src/app/util/system-feature.util';
import { SystemFeature } from '../../customer/response/settings.response';
import { PromotionAction } from '../state/promotion.action';
import { UserState } from '../../customer/state/user/user.state';
import { CustomerModuleService } from '../../customer/service/customer-module.service';
import { Router } from '@angular/router';
import { SanitizedCustomerModel } from '../../customer/model/sanitized-customer.model';
import { takeUntil } from 'rxjs/operators';
import { IncomingMessageEnum } from '../../../core/enum/incoming-message.enum';
import { IncomingMessageService } from '../../../core/service/incoming-message.service';
import { BorusanBlockedActionsEnum } from '../../definition/enum/borusan-blocked-actions.enum';
import { LogService } from '../../../shared/service/log.service';
import { environment } from '../../../../environments/environment';
import { BorusanUserLdapData } from '../../authentication/model/user.model';
import { Navigate } from '@ngxs/router-plugin';
import { PermissionEnum } from '../../definition/enum/permission.enum';

@Component({
  selector: 'app-promotion',
  templateUrl: './promotion.component.html',
  styleUrls: ['./promotion.component.scss'],
  encapsulation: ViewEncapsulation.None
})
export class PromotionComponent implements OnInit, OnDestroy {

  @Input()
  lite: boolean;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(PromotionState.getLoading)
  loading$: Observable<boolean>;

  @Select(PromotionState.get)
  promotion$: Observable<PromotionModel>;

  @Select(PromotionState.getLitePromotion)
  litePromotion$: Observable<PromotionModel>;

  boomCoinShopShow = false;
  isBorusanUser: boolean;

  pointHistory: PromotionModel;
  gainPoints: any[] = [];

  liteUserPopupShow: boolean;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  earnedPoints: any[] = [];

  @Select(SettingsState.borusanBlockedActions)
  borusanBlockedActions$: Observable<string[]>;
  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;
  PermissionEnum = PermissionEnum;

  @Select(UserState.borusanUserData)
  borusanUserData$: Observable<BorusanUserLdapData>;

  @Select(PromotionState.getPssrAccount)
  pssrAccount$: Observable<PssrAccountModel>;

  protected subscriptions$: Subject<boolean> = new Subject();
  currentCustomer: SanitizedCustomerModel;
  private boomCoinStuffEnable: boolean;
  pssrAccount: PssrAccountModel;
  boomDiamondSystemFeature: boolean;
  boomGoldSystemFeature: boolean;
  earnedPointsSystemFeature: boolean;
  activitySystemFeature: boolean;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly translateService: TranslateService,
    private readonly customerModuleService: CustomerModuleService,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly log: LogService,
  ) { }

  ngOnInit(): void {


    this.log.action('PROMOTION_PORTAL', 'PROMOTION_PORTAL_OPENED').subscribe();

    this.pssrAccountControl()
    this.pssrAccount$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(data => {
      this.pssrAccount = data;
    })
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.boomDiamondSystemFeature = systemFeature('boom_diamond',features, false);
        this.boomGoldSystemFeature = systemFeature('boom_gold',features, false);
        this.earnedPointsSystemFeature = systemFeature('boom_coin_earned_points', features, false);
        this.activitySystemFeature = systemFeature('boom_coin_activity_history', features, false);
      }
    });

    if(this.lite) {
      this.loadLiteData()
    } else {
      this.setHeader();
      this.loadData();
      this.loadSystemFeatures();
      this.listenPromotionPortalClosed();
    }


    // this.store.dispatch([
    //   new GetUserAgreementsAction(AgreementTypeEnum.PromotionPortal),
    // ]).pipe(map(() => this.store.selectSnapshot(DefinitionState.getForceAprovalAgreement)))
    //   .subscribe((forceAgreements) => {
    //     if (forceAgreements?.length && !!forceAgreements.filter(x => x.approvedVersion !== x.latestVersion)?.length)
    //       this.router.navigate(['promotion']);
    //   });
  }

  pssrAccountControl() {
    this.store.selectSnapshot(PromotionState.getPssrAccount)
  }
  loadLiteData() {
    if (this.store.selectSnapshot(PromotionState.getLitePromotion)) {
      this.pointHistory = this.store.selectSnapshot(PromotionState.getLitePromotion);
      this.gainPoints = this.store.selectSnapshot(PromotionState.getLitePromotion)?.details?.filter(detail => detail.pointType === 'Gain') || [];
      this.setGroup();
    } else {
      this.litePromotion$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((response) => {
          if (response) {
            this.pointHistory = response;
            this.gainPoints = response?.details?.filter(detail => detail.pointType === 'Gain') || [];
            this.setGroup();
          }
        });
    }
  }
  loadData() {
    this.currentCustomer$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(current => {
        if (current) {
          this.currentCustomer = current;
          if (!this.store.selectSnapshot(PromotionState.get)) {
            this.store.dispatch(new PromotionAction());
          }
        }
      });

    if (this.store.selectSnapshot(PromotionState.get)) {
      this.pointHistory = this.store.selectSnapshot(PromotionState.get);
      this.gainPoints = this.store.selectSnapshot(PromotionState.get)?.details?.filter(detail => detail.pointType === 'Gain') || [];
      this.setGroup();
    } else {
      this.promotion$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((response) => {
          if (response) {
            this.pointHistory = response;
            this.gainPoints = response?.details?.filter(detail => detail.pointType === 'Gain') || [];
            this.setGroup();
          }
        });
    }
  }

  loadSystemFeatures() {
    this.borusanBlockedActions$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(x => this.borusanBlockedActions = x);
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.boomCoinShopShow = systemFeature('loyality_point_shop', features, false);
        this.boomCoinStuffEnable = systemFeature('loyality_stuff_list', features,
          ['local', 'development'].indexOf(environment.envName) !== -1);

        const isBorusanUser = this.store.selectSnapshot(UserState.isBorusanUser);
        if (isBorusanUser && this.boomCoinShopShow) {
          this.boomCoinShopShow = systemFeature('loyality_point_shop_borusan_user', features, false);
        }
      }
    });
  }

  setGroup() {
    this.earnedPoints = [];
    this.gainPoints.reduce((res, value) => {
      if (!res[value.title]) {
        res[value.title] = { name: value.title, point: 0 };
        this.earnedPoints.push(res[value.title]);
      }
      res[value.title].point += parseInt(value.point, 10);
      return res;
    }, {});
  }

  redirectLoyalty() {
    // console.log(this.currentCustomer);
    if (this.currentCustomer && (this.currentCustomer?.countryCode === 'AZ' || this.currentCustomer?.countryCode === 'KZ') && this.boomCoinStuffEnable) {
      this.router.navigate(['promotion', 'promotion-stuff-list']).then();
    } else if(this.currentCustomer && (this.pssrAccount?.isPssrUser || this.pssrAccount?.isPssrManagerUser)) {
      this.router.navigate(['promotion', 'promotion-stuff-list']).then();
    }
     else if (!(this.borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.UsePromotionPortal) >= 0)) {
      if (!this.boomCoinShopShow) {
        return;
      }
      this.customerModuleService.openPromotionPortal();
    }
  }

  setHeader() {
    const headerInfo = this.getHeaderInfo();
    this.translateService.get(headerInfo.title).subscribe((trns) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: false,
          closeButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trns,
          img: { src: headerInfo.imageSrc, alt: headerInfo.altText, altColor: headerInfo.altColor},
        })
      );
    });
  }

  getHeaderInfo() {
    let imageSrc = "";
    let altText = "";
    let title = " ";
    let altColor = null;
    if (this.pssrAccount?.isPssrUser && this.boomGoldSystemFeature) {
      imageSrc = "assets/kulcealtin.svg";
      altText = "Boom Gold";
      altColor = "gold";
    } else if (this.pssrAccount?.isPssrManagerUser && this.boomDiamondSystemFeature) {
      imageSrc = "assets/header-diamond-icon.svg";
      altText = "Boom Diamond";
      altColor = "blue";
    } else {
      title = "_boom_coin";
    }

    return { imageSrc, altText, title, altColor };
  }

  protected listenPromotionPortalClosed() {
    this.incomingMessageService
      .observable(IncomingMessageEnum.webviewReopened)
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(() => {
        this.store.dispatch(new PromotionAction());
      });
  }

  openDashboard() {
    this.store.dispatch(new Navigate(['dashboard']));
  }

  redirectCorporate() {
    this.router.navigate(['/lite/convert']);
  }

  litePopup() {
    this.liteUserPopupShow = true
  }

  redirect() {
    if(this.lite)
      this.litePopup();
    else
      this.redirectLoyalty();
  }
  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

}
