import { ActivatedRouteSnapshot, Resolve, Router, RouterStateSnapshot } from '@angular/router';
import { Store } from '@ngxs/store';
import { Observable, of } from 'rxjs';
import { Injectable } from '@angular/core';
import { PromotionState } from '../state/promotion.state';
import { UserState } from '../../customer/state/user/user.state';
import { filter, switchMap } from 'rxjs/operators';
import { PromotionAction } from '../state/promotion.action';
import { UserAction } from '../../customer/state/user/user.actions';

@Injectable()
export class PromotionResolver implements Resolve<boolean> {
  constructor(
    private readonly store: Store,
    private readonly router: Router,
  ) {
  }

  resolve({ queryParams }: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    if (this.store.selectSnapshot(PromotionState.getTotalPoints) >= 0) {
      return of(true);
    }

    // this.store.dispatch(new UserAction(false));
    return this.store.select(UserState.currentCustomer)
      .pipe(filter(Boolean))
      .pipe(switchMap(customer => {
        const isGdprApproved = this.store.selectSnapshot(UserState.isGdprApproved);
        if (!isGdprApproved) {
          return this.router.navigate(['promotion']);
        }
        return this.store.dispatch(new PromotionAction());
      }));
  }

}
