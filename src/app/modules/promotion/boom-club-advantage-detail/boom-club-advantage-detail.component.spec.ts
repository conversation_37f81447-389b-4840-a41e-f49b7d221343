/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { BoomClubAdvantageDetailComponent } from './boom-club-advantage-detail.component';

describe('BoomClubAdvantageDetailComponent', () => {
  let component: BoomClubAdvantageDetailComponent;
  let fixture: ComponentFixture<BoomClubAdvantageDetailComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BoomClubAdvantageDetailComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BoomClubAdvantageDetailComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
