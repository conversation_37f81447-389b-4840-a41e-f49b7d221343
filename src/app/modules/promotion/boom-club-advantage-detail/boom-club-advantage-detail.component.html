<div class="advantage-detail-container">
  <div class="advantage-detail py-4 d-flex flex-column" *ngIf="!scanQr">
    <div class="h5 font-weight-bold">{{ advantageDetail?.title }}</div>
    <img
      [src]="environment.api + '/image/boomclubadvantage?advantageId=' + advantageDetail?.id + '&lang=' + boomClubUserMe?.countryCode"
      alt=""
      width="100%"
      height="200"
      class="mb-2"
      style="border-radius: .5rem;"
    />
    <p class="pb-4 pt-3 mb-0 flex-grow-1">
      {{ advantageDetail?.description }}
    </p>

    <div class="advantage-dates">
      <div>
        <span>{{ "_start_date" | translate }}:</span>
        <span class="font-weight-semi-bold ml-3">{{
          advantageDetail?.startDate | date : "dd.MM.YYYY"
        }}</span>
      </div>
      <div>
        <span>{{ "_end_date" | translate }}:</span>
        <span class="font-weight-semi-bold ml-3">
          {{advantageDetail?.validDate | date : "dd.MM.YYYY"}}
        </span>
      </div>
    </div>

    <button (click)="joinAdvantage()" [disabled]="advantageDetail?.isUsed" class="btn btn-warning text-white join-advantage-btn">
      {{ (advantageDetail?.isUsed ? "_used_advantage" : "_take_advantage") | translate }}
    </button>
  </div>
  <div class="scan-qr h-100 d-flex flex-column  justify-content-center" *ngIf="scanQr">
    <div class="advantage-qr-number border-bottom d-flex align-items-center justify-content-center">
      <span class="font-size-22px font-weight-bold">BOOMCLUB</span>
    </div>
    <div class="qr-code-container d-flex align-items-center justify-content-center border-top">
      <!-- <img src="assets/qr.png" alt="" width="200" height="200" /> qr için sonra değişebilir -->
      <span class="font-size-22px font-weight-bold">{{ joinAdvantageDetail?.code }}</span>
    </div>
    <div class="qr-code-info mt-4 d-flex flex-column align-items-center">
      <!-- <span class="font-weight-bold text-center w-100 mb-4">{{ "_please_scan_qr" | translate}}</span> -->
      <p class="text-center">{{ '_coupon_code_info' | translate }}</p>
      <div *ngIf="joinAdvantageDetail?.validDate">
        {{ "_end_date" | translate }}:
        <span class="font-weight-semi-bold ml-2">
          {{ joinAdvantageDetail?.validDate | date: 'dd.MM.YYYY' }}
        </span>
      </div>
    </div>
  </div>
</div>
<app-loader [show]="(advantageDetailLoading$ | async) || joinAdvantageLoading"></app-loader>
<!-- //TODO success ekranı mobilden gelen evente göre yönlendirilecek yani navigate query params => isSucces=1  -->
