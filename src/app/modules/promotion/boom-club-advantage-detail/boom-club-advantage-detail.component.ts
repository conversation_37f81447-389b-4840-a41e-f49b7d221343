import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { PromotionState } from '../state/promotion.state';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import {
  BoomClubAdvantagesModel,
  BoomClubJoinModel,
  BoomClubUserModel,
} from '../model/promotion.model';
import {
  BoomClubAdvantageDetailAction,
  BoomClubAdvantagesAction,
} from '../state/promotion.action';
import { takeUntil } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { PromotionService } from '../service/promotion.service';
import { ModalService } from 'src/app/shared/service/modal.service';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';

@Component({
  selector: 'app-boom-club-advantage-detail',
  templateUrl: './boom-club-advantage-detail.component.html',
  styleUrls: ['./boom-club-advantage-detail.component.scss'],
})
export class BoomClubAdvantageDetailComponent implements OnInit, OnDestroy {
  @Select(PromotionState.boomClubAdvantageDetail)
  advantageDetail$: Observable<BoomClubAdvantagesModel>;
  @Select(PromotionState.boomClubAdvantageDetailLoading)
  advantageDetailLoading$: Observable<boolean>;
  @Select(PromotionState.boomClubUserMe)
  boomClubUserMe$: Observable<BoomClubUserModel>;

  environment = environment;
  advantageDetail: BoomClubAdvantagesModel;
  scanQr = 0;
  joinAdvantageDetail: BoomClubJoinModel;
  joinAdvantageLoading = false;
  boomClubUserMe: BoomClubUserModel;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly store: Store,
    private readonly promotionService: PromotionService,
    private readonly modalService: ModalService
  ) {}

  ngOnInit() {
    const { id } = this.route.snapshot.params;

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: null,
        bgcolor: 'transparent',
        img: {
          src: 'assets/boom_club.svg',
          alt: 'Boom Club',
        }
      })
    );

    this.store.dispatch(new BoomClubAdvantageDetailAction(id));
    this.advantageDetail$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.advantageDetail = data;
      });

    this.boomClubUserMe$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.boomClubUserMe = data;
      });

    this.route.queryParams.subscribe((q) => {
      this.scanQr = 'scanQr' in q ? parseInt(q.scanQr, 2) : 0;
    });
  }

  navigateToQr() {
    this.router
      .navigate([], {
        queryParams: { scanQr: 1 },
        queryParamsHandling: 'merge',
        replaceUrl: true
      })
      .then();
  }

  joinAdvantage() {
    this.joinAdvantageLoading = true;

    this.promotionService
      .boomClubJoinAdvantage(this.advantageDetail?.id)
      .subscribe(
        (data) => {
          if(!data){
            this.joinAdvantageLoading = false;
            return this.modalService.errorModal({ message: '_used_advantage' })
          }
          this.joinAdvantageDetail = data; 
          this.joinAdvantageLoading = false;
          return this.navigateToQr();
        },
        () => {
          this.joinAdvantageLoading = false;
        }
      );
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
