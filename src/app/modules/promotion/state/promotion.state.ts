import { Injectable } from '@angular/core';
import { Action, Selector, State, StateContext } from '@ngxs/store';
import { throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { BoomClubAdvantagesModel, BoomClubCategoriesModel, BoomClubInterestModel, BoomClubJoinModel, BoomClubUserModel, SalesOfficeModel, BoomCoinPromotionProductsModel, PromotionModel } from '../model/promotion.model';
import { PromotionService } from '../service/promotion.service';
import { LitePromotionAction, PromotionAction, PssrAccountAction ,BoomClubCategoriesAction, BoomClubAdvantagesAction, BoomClubUserMeAction, BoomClubInterestAction, BoomClubAdvantageDetailAction, BoomClubJoinAdvantageAction, SalesOfficeAction, BoomCoinPromotionProductsAction} from './promotion.action';

export interface PromotionStateModel {
  // totalPoint: number;
  // details: Detail[];
  promotion: any;
  litePromotion: any;
  loading: boolean;
  boomClubLoading: boolean;
  boomClubCategories: BoomClubCategoriesModel[];
  boomClubAdvantages: BoomClubAdvantagesModel[];
  boomClubUserMe: BoomClubUserModel;
  boomClubInterests: BoomClubInterestModel[];
  boomClubAdvantageDetail: BoomClubAdvantagesModel;
  boomClubAdvantageDetailLoading: boolean;
  boomClubJoinLoading: boolean;
  boomClubJoinDetail: BoomClubJoinModel;
  body: any;
  SalesOffice: SalesOfficeModel[];
  boomCoinPromotionListLoading: boolean;
  BoomCoinPromotionProducts: any;
}

@State<PromotionStateModel>({
  name: 'promotion_main',
  defaults: {
    loading: false,
    promotion: null,
    litePromotion: null,
    boomClubLoading: false,
    boomClubCategories: [],
    boomClubAdvantages: [],
    boomClubUserMe: null,
    boomClubInterests: [],
    boomClubAdvantageDetail: null,
    boomClubAdvantageDetailLoading: false,
    boomClubJoinDetail: null,
    boomClubJoinLoading: false,
    body: null,
    SalesOffice: [],
    boomCoinPromotionListLoading: false,
    BoomCoinPromotionProducts: null,
  },
})
@Injectable()
export class PromotionState {
  constructor(private readonly promotionService: PromotionService) {}

  @Selector()
  public static get({ promotion }: PromotionStateModel): PromotionModel {
    return promotion;
  }

  @Selector()
  public static getLitePromotion({ litePromotion }: PromotionStateModel): PromotionModel {
    return litePromotion;
  }

  @Selector()
  public static getLoading({ loading }: PromotionStateModel): boolean {
    return loading;
  }

  @Selector()
  public static getTotalPoints({ promotion }: PromotionStateModel): number {
    return promotion?.totalPoint;
  }

  @Selector()
  public static boomClubLoading({ boomClubLoading }: PromotionStateModel) {
    return boomClubLoading;
  }

  @Selector()
  public static boomClubCategories({ boomClubCategories }: PromotionStateModel) {
    return boomClubCategories;
  }

  @Selector()
  public static boomClubAdvantages({ boomClubAdvantages }: PromotionStateModel) {
    return boomClubAdvantages;
  }

  @Selector()
  public static boomClubUserMe({ boomClubUserMe }: PromotionStateModel) {
    return boomClubUserMe;
  }

  @Selector()
  public static boomClubInterests({ boomClubInterests }: PromotionStateModel) {
    return boomClubInterests;
  }

  @Selector()
  public static boomClubAdvantageDetail({ boomClubAdvantageDetail }: PromotionStateModel) {
    return boomClubAdvantageDetail;
  }

  @Selector()
  public static boomClubAdvantageDetailLoading({ boomClubAdvantageDetailLoading }: PromotionStateModel) {
    return boomClubAdvantageDetailLoading;
  }

  @Selector()
  public static boomClubJoinLoading({ boomClubJoinLoading }: PromotionStateModel) {
    return boomClubJoinLoading;
  }

  @Selector()
  public static boomClubJoinDetail({ boomClubJoinDetail }: PromotionStateModel) {
    return boomClubJoinDetail;
  }

  @Selector()
  public static getPssrAccount({ body }: PromotionStateModel): any {
    return body;
  }

  @Selector()
  public static getSalesOffice({ SalesOffice }: PromotionStateModel){
    return SalesOffice;
  }

  @Selector()
  public static getPromotionProducts({ BoomCoinPromotionProducts }: PromotionStateModel) {
    return BoomCoinPromotionProducts;
  }

  @Action(PromotionAction)
  getPromotion(
    { patchState }: StateContext<PromotionStateModel>,
    payload: PromotionAction
  ) {
    patchState({
      loading: true,
    });
    return this.promotionService.getPoints().pipe(
      tap((response) => {
        patchState({
          loading: false,
          promotion: response?.data,
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(LitePromotionAction)
  getLiteUserPromotion(
    { patchState }: StateContext<PromotionStateModel>,
    payload: PromotionAction
  ) {
    patchState({
      loading: true,
    });
    return this.promotionService.getLiteUserPoints().pipe(
      tap((response) => {
        patchState({
          loading: false,
          litePromotion: response?.data ? response?.data : {totalPoint: 0, details: []},
        });
      }),
      catchError((err) => {
        patchState({
          loading: false,
        });
        return throwError(err);
      })
    );
  }

  @Action(BoomClubCategoriesAction)
  getBoomClubCategories(
    { patchState }: StateContext<PromotionStateModel>
  ) {
    patchState({
      boomClubLoading: true
    });
    return this.promotionService.boomClubCategories().pipe(
      tap((data) => {
        patchState({
          boomClubCategories: data,
          boomClubLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomClubLoading: false
        });
        return throwError(err);
      })
    );
  }

  @Action(BoomClubAdvantagesAction)
  getBoomClubAdvantages(
    { patchState }: StateContext<PromotionStateModel>,
    { isUsed }: BoomClubAdvantagesAction,
  ) {
    patchState({
      boomClubLoading: true
    });
    return this.promotionService.boomClubAdvantages(isUsed).pipe(
      tap((data) => {
        patchState({
          boomClubAdvantages: data,
          boomClubLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomClubLoading: false
        });
        return throwError(err);
      })
    );
  }

  @Action(BoomClubUserMeAction)
  getBoomClubUserMe(
    { patchState }: StateContext<PromotionStateModel>
  ) {
    patchState({
      boomClubLoading: true
    });
    return this.promotionService.boomClubUserMe().pipe(
      tap((data) => {
        patchState({
          boomClubUserMe: data,
          boomClubLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomClubLoading: false
        });
        return throwError(err);
      })
    );
  }

  @Action(BoomClubInterestAction)
  getBoomClubInterests(
    { patchState }: StateContext<PromotionStateModel>
  ) {
    patchState({
      boomClubLoading: true
    });
    return this.promotionService.boomClubInterests().pipe(
      tap((data) => {
        patchState({
          boomClubInterests: data,
          boomClubLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomClubLoading: false
        });
        return throwError(err);
      })
    );
  }

  @Action(BoomClubAdvantageDetailAction)
  getBoomClubAdvantageDetail(
    { patchState }: StateContext<PromotionStateModel>,
    { id }: BoomClubAdvantageDetailAction
  ) {
    patchState({
      boomClubAdvantageDetailLoading: true
    });
    return this.promotionService.boomClubAdvantageDetail(id).pipe(
      tap((data) => {
        patchState({
          boomClubAdvantageDetail: data,
          boomClubAdvantageDetailLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomClubAdvantageDetailLoading: false
        });
        return throwError(err);
      })
    );
  }

  @Action(BoomClubJoinAdvantageAction)
  getBoomClubJoinDetail(
    { patchState }: StateContext<PromotionStateModel>,
    { advantageId }: BoomClubJoinAdvantageAction
  ) {
    patchState({
      boomClubJoinLoading: true
    });
    return this.promotionService.boomClubJoinAdvantage(advantageId).pipe(
      tap((data) => {
        patchState({
          boomClubJoinDetail: data,
          boomClubJoinLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomClubJoinLoading: false
        });
        return throwError(err);
      })
    );
  }

  @Action(PssrAccountAction)
  getPSSRAccount(
    { patchState }: StateContext<PromotionStateModel>,

  ) {
    return this.promotionService.getPSSRAccount().pipe(
      tap((response) => {
        patchState({
          body: response?.data,
        });
      }),
      catchError((err) => {
        return throwError(err);
      })
    );
  }

  @Action(SalesOfficeAction)
  getSalesOffice(
    { patchState }: StateContext<PromotionStateModel>,
    { countryCode }: SalesOfficeAction
  ) {
    patchState({
      boomClubLoading: true
    });
    return this.promotionService.salesOffices(countryCode).pipe(
      tap((data) => {
        patchState({
          SalesOffice: data,
        });
      }),
      catchError((err) => {
        return throwError(err);
      })
    );
  }

  @Action(BoomCoinPromotionProductsAction)
  getBoomCoinPromotionProducts(
    { patchState }: StateContext<PromotionStateModel>,
    { countryCode, limit, offset }: BoomCoinPromotionProductsAction
  ) {
    patchState({
      boomCoinPromotionListLoading: true
    });
    return this.promotionService.boomCoinPromotionProducts(countryCode, limit, offset).pipe(
      tap((data) => {
        patchState({
          BoomCoinPromotionProducts: data,
          boomCoinPromotionListLoading: false
        });
      }),
      catchError((err) => {
        patchState({
          boomCoinPromotionListLoading: false
        });
        return throwError(err);
      })
    );
  }

}
