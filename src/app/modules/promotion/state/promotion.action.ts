export class PromotionAction {
  public static readonly type = '[Promotion] get';

  constructor(public param?: { key: string, value: string }) {}
}

export class PssrAccountAction {
  public static readonly type = '[Promotion] getPssrAccounts';

  constructor() {}
}

export class LitePromotionAction {
  public static readonly type = 'Lite [Promotion] get';

  constructor() {}
}

export class BoomClubCategoriesAction{
  public static readonly type = '[BoomClub] get categories';

  constructor() {};
}

export class BoomClubAdvantagesAction{
  public static readonly type = '[BoomClub] get Advantages';

  constructor(public isUsed: boolean) {};
}

export class BoomClubUserMeAction{
  public static readonly type = '[BoomClub] user me';

  constructor() {};
}

export class BoomClubInterestAction{
  public static readonly type = '[BoomClub] interest';

  constructor() {};
}

export class BoomClubAdvantageDetailAction{
  public static readonly type = '[BoomClub] advantage detail';

  constructor(public id: string) {};
}

export class BoomClubJoinAdvantageAction{
  public static readonly type = '[BoomClub] advantage join';

  constructor(public advantageId: string) {};
}

export class SalesOfficeAction {
  public static readonly type = '[Promotion] getSalesOffice';

  constructor(public countryCode: string) {}
}

export class BoomCoinPromotionProductsAction{
  public static readonly type = '[Promotion] products';

  constructor(public countryCode: string, public limit: number, public offset: number) {};
}
