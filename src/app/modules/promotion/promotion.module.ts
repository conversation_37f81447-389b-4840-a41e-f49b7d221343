import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PromotionComponent } from './promotion/promotion.component';
import { PromotionRoutingModule } from './promotion-routing.module';
import { SharedModule } from 'src/app/shared/shared.module';
import { NgbAccordionModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { ForceAgreementComponent } from './force-agreement/force-agreement.component';
import { PromotionResolver } from './resolvers/promotion.resolver';
import { BoomClubComponent } from './boom-club/boom-club.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BoomClubRegisterComponent } from './boom-club-register/boom-club-register.component';
import { PromotionStuffListComponent } from './promotion-stuff-list/promotion-stuff-list.component';
import { BoomClubAdvantageDetailComponent } from './boom-club-advantage-detail/boom-club-advantage-detail.component';
import { BoomClubAdvantagesComponent } from './boom-club-advantages/boom-club-advantages.component';
import { BoomClubUsedAdvantagesComponent } from './boom-club-used-advantages/boom-club-used-advantages.component';
import { HasPermissionsModule } from 'src/app/export/file-upload/permissions/has-permissions.module';

@NgModule({
  exports: [PromotionComponent],
  declarations: [
    PromotionComponent,
    ForceAgreementComponent,
    BoomClubComponent,
    BoomClubRegisterComponent,
    PromotionStuffListComponent,
    BoomClubAdvantagesComponent,
    BoomClubAdvantageDetailComponent,
    BoomClubUsedAdvantagesComponent
  ],
  imports: [
    CommonModule,
    PromotionRoutingModule,
    NgbAccordionModule,
    SharedModule,
    TranslateModule,
    NgSelectModule,
    FormsModule,
    ReactiveFormsModule,
    HasPermissionsModule
  ],
  providers: [PromotionResolver]
})
export class PromotionModule {}
