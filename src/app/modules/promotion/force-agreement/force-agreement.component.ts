import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { LoggerService } from 'src/app/shared/service/logger.service';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';
import { AgreementTypeEnum } from '../../definition/enum/agreement-type.enum';
import { DefinitionService } from '../../definition/service/definition.service';
import { DefinitionState } from '../../definition/state/definition/definition.state';
import { ApproveUserAgreementAction } from '../../definition/state/definition/definition.actions';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-force-agreement',
  templateUrl: './force-agreement.component.html',
  styleUrls: ['./force-agreement.component.scss']
})
export class ForceAgreementComponent implements OnInit, OnDestroy {
  @Select(DefinitionState.getAgreementLoading)
  agreementsLoading$: Observable<boolean>;

  agreementTypeEnum = AgreementTypeEnum;
  agreementLength: number;

  @Select(DefinitionState.approveUserAgreementSend)
  approvedUserAgreementSend$: Observable<boolean>;

  approvedSuccessModal = false;

  isGdprApprovedError = false;
  isGdprApproved = false;

  form: FormGroup = new FormGroup({
    // agreements: new FormControl()
  });


  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly definitionService: DefinitionService,
    private readonly router: Router,
    private readonly logger: LoggerService,
    private readonly translateService: TranslateService,
  ) { }

  ngOnInit() {
    this.setHeader();
    // TODO daha sonra etkin hale getirilecek
    // this.isGdprApproved = this.store.selectSnapshot(UserState.isGdprApproved);
    // if (this.isGdprApproved) {
    //   this.store.dispatch(new GetUserAgreementsAction(AgreementTypeEnum.PromotionPortal));
    //   this.router.navigate(['promotion', 'coin'])
    // }
  }

  sendAgreement() {
    const entries = this.findHeaders();
    if (entries?.length === this.agreementLength) {
      const headers = { ApprovedAgreementNames: entries.join(';') };
      this.store.dispatch(new ApproveUserAgreementAction(headers));
      this.approvedUserAgreementSend$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe(x => {
          if (x) {
            this.approvedSuccessModal = true;
          }
        });
    } else {
      this.isGdprApprovedError = true;
    }
  }

  findHeaders() {
    return Object.entries(this.form.get('agreements')?.value || {})
      .filter(([key, value]) => value)
      .map((a) => a[0]);
  }

  setHeader() {
    this.translateService.get('_boom_coin').subscribe((trns) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: false,
          closeButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trns,
        })
      );
    });
  }

  closeSuccessModal() {
    this.router.navigate(['promotion', 'coin'], { replaceUrl: true });
  }

  setAgreementLength(e) {
    this.agreementLength = e;
  }

  returnBack() {
    history.back();
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
