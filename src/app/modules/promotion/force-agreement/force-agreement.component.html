<div [class.d-none]="approvedSuccessModal" class="p-4 mb-3">
  <app-agreement-list [form]="form" [formType]="agreementTypeEnum.PromotionPortal" [forceAgreement]="true"
    (agreementLength)="setAgreementLength($event)">
  </app-agreement-list>
  <div *ngIf="!(agreementsLoading$ | async)" class="d-flex justify-content-center">
    <div class="flex-fill btn btn-success mr-1" (click)="sendAgreement()">
      {{ '_accept' | translate }}
    </div>
    <div class="flex-fill btn btn-warning text-white ml-1" (click)="returnBack()">
      {{ '_close' | translate }}
    </div>
  </div>
</div>

<app-success-modal *ngIf="approvedSuccessModal" [message]="'_aggreement_approved_success_message'">
  <div class="btn btn-warning btn-gradient btn-block text-white shadow" (click)="closeSuccessModal()">
    {{ "_close" | translate }}
  </div>
</app-success-modal>

<app-basic-modal [(status)]="isGdprApprovedError">
  <div class="agreement-force-content d-flex align-items-center flex-column justify-content-center">
    <div class="agreement-force-content-logo mb-2">
      <i class="icon icon-x-bold text-warning"></i>
    </div>
    <div class="agreement-force-content-message text-center ">
      {{ '_kvkk_unauthorized' | translate }}
    </div>
    <button class="btn btn-warning btn-gradient btn-block text-white rounded-lg btn-sm" (click)="isGdprApprovedError = false">
      {{ "_close" | translate }}
    </button>
  </div>
</app-basic-modal>

<app-loader [show]="(agreementsLoading$ | async) && !approvedSuccessModal"></app-loader>
