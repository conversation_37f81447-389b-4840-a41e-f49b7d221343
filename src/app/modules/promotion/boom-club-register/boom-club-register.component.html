<div class="club">
  <div class="boom-club-form mt-4" *ngIf="!formSendStatus">
    <div class="preview-info mb-3">
      <div class="title">
        {{ '_join_to_boom_club' | translate }}
        <div class="sub-title">
          {{ '_start_enjoying_privileges' | translate }}
        </div>
      </div>
      <div class="preview-subclause">
        <ul>
          <li>
            <img src="assets/boom_club.svg">
            <div class="subclause-text">
              {{ '_boom_club_privilege_of_participating' | translate }}
            </div>
          </li>
          <li>
            <img src="assets/boom_club.svg">
            <div class="subclause-text">
              {{ '_benefit_from_a_variety_of_surprises' | translate }}
            </div>
          </li>
          <li>
            <img src="assets/boom_club.svg">
            <div class="subclause-text">
              <span [innerHTML]="('_boom_club_soon' | translate) | safeHtml"></span>
            </div>
          </li>
        </ul>
      </div>
    </div>
    <div class="profile-card">
        <div class="profile-card-header mt-2">
          <div class="profile-card-picture">
            <img src="assets/boom_club_pp.svg" alt="">
          </div>
          <div class="profile-card-info">
            <div class="profile-card-name"  *ngIf="currentUser$ | async">
                <!-- {{ currentUser?.firstName?.length + currentUser?.lastName?.length > 18 ? (currentUser?.firstName +" "+ currentUser?.lastName | slice: 0:18) + "..." : (currentUser?.firstName + " " + currentUser?.lastName ) }} -->
                {{ currentUser?.firstName + " " + currentUser?.lastName }}
            </div>
            <div class="profile-card-location" *ngIf="currentUser?.email"><i class="icon icon-contact px-1"></i> {{currentUser.email}} </div>
            <div class="profile-card-location" *ngIf="currentUser?.email"><i class="icon icon-phone px-1"></i> {{currentUser.mobile}} </div>
            <!-- <div class="profile-card-location"><i class="icon icon-location px-1"></i>Sivas Turkey</div> -->
        </div>
    </div>
    <!-- <div class="profile-card-interests">
      <div class="profile-card-interests-title my-2">
       <div>
        {{ '_interests' | translate }}
       </div>
        <div class="profile-card-interests-add-item-icon">
          <i class="icon" style="font-size: 15px; margin-right: 15px;" [ngClass]="{'icon-x': interestInput, 'icon-plus': !interestInput}" (click)="showInterestsArea()"></i>
        </div>
      </div>
      <div class="profile-card-interests-add mb-1">
        <div class="profile-card-interests-add-item">
          <div class="profile-card-interests-add-item-text" >
            <div *ngFor="let item of selectedInterest; let i = index">
              <div class="d-flex">
                <div class="pr-1 px-2" style="width: max-content;">{{ item.name | translate }}</div>
                <span class="pr-1" *ngIf="selectedInterest[i+1]" style="color: #F7A226;">|</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="interest-input-area mt-3" *ngIf="interestInput">
        <div class="select-interest">
          <div class="interest" *ngFor="let item of interests" >
            <span class="d-flex align-items-center" (click)="selectInterest(item)" [ngClass]="{
              'activeCategory': selectedInterest.includes(item)
            }" >
            <div style="width: 10px; height: 10px; margin-right: 2px;" *ngIf="!selectedInterest.includes(item)"></div>
            <img style="width: 10px; height: 10px; margin-right: 2px;" *ngIf="selectedInterest.includes(item)" src="assets/boom_club.svg">
              {{ item.name | translate }}
            </span>
          </div>
        </div>
      </div>
    </div> -->

  </div>
  <div class="footer">
    <div class="campaignTerms mt-5">
      <div class="campaignTerms-input">
        <form [formGroup]="form">
          <app-agreement-list *ngIf="form.value.CampaignNews && (isGdprApproved$ | async)" [form]="form" [formType]="agreementTypeEnum.BoomClub"></app-agreement-list>
            <input [name]="'CampaignNews'" id="CampaignNews" formControlName="CampaignNews" class="form-check-input" type="checkbox"
              [checked]="form.value.CampaignNews">
              <label [for]="'CampaignNews'"></label>
        </form>
      </div>
      <div class="campaignTerms-text">
      {{ '_campaign_terms' | translate }}
      </div>
    </div>
    <div class="mb-4" style="width: 100%; display: flex; justify-content: center;">
      <button style="width: 90%;"
              [disabled]="borusanBlockedActions && borusanBlockedActions?.indexOf(BorusanBlockedActionsEnum.SubmitBoomClubForm) >= 0"
              type="button" (click)="onSubmitForm()"
              class="action-btn btn btn-warning btn-sm px-4 mb-5 mb-2 joinButton text-white">
        {{ '_join_to_boom_club' | translate }}
      </button>
    </div>
  </div>
</div>

<app-success-modal *ngIf="formSendStatus" message="_general_successfully_send_form">
  <div
    class="btn btn-info btn-gradient btn-block text-white shadow"
    (click)="back()"
  >
    {{ "_return_back" | translate }}
  </div>
</app-success-modal>
</div>
<app-loader [show]="loading"></app-loader>
