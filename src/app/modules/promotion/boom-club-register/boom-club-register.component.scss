@import "variable/icon-variable";
.club {
  height: calc(100vh - 30px);
  width: 90%;
  overflow-y: scroll;
  margin: 0 auto;
}

.preview-info {
  box-sizing: border-box;
  width: 100%;
  height: 222px;
  border: 3px solid #F7A226;
  border-radius: 17px;
  padding-left: 25px;
  padding-top: 12px;
  .title {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    color: #3A3A3A;
  }
  .preview-subclause {
    width: 95%;
    ul {
      padding: 0;
      li {
        display: flex;
        list-style: none;
        margin-top: 3px;
        padding-bottom: 10px;
        color: #000000;
        img {
          margin-top: 3px;
          width: 14px;
          height: 11.88px;
          margin-right: 5px;
        }
      }
    }
  }
  .sub-title {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 300;
    font-size: 11px;

  }
  .subclause-text {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 172%;
  }
}

.profile-card {
  width: 99%;
  min-height: 200px;
  height: auto;
  box-shadow: 0px 18px 25px rgba(0, 0, 0, 0.06), 0px 2px 6px rgba(0, 0, 0, 0.03), 0px 10px 20px rgba(0, 0, 0, 0.08);
  border-left: 7px solid #F7A226;
  border-radius: 5px;
  margin-bottom: 5px;
  padding: 3px;
  transition: height 1s;
  &-header{
    display: flex;
    padding: 0px;
    margin-top: 18px;
    margin-left: 28px;
    &-picture img {
      width: 89px;
      height: 89px;
      background: #F5F5F5;
      box-shadow: 30px 30px 30px rgba(0, 0, 0, 0.25);
    }

  }
  &-info {
    height: 100%;
  }
  &-name {
    display: flex;
    margin-top: 26px;
    margin-left: 18px;
    font-style: normal;
    font-weight: 700;
    font-size: 16px;
    color: #3A3A3A;

    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  &-location {
    display: flex;
    align-items: center;
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    color: #CECECE;
    padding-left: 13px;
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
 &-interests {

  &-title {
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 600;
    font-size: 13px;
    padding-left: 15px;
    display: flex;
    justify-content: space-between;
  }
  &-add {
    display: flex;
    justify-content: center;
    &-item {
      display: flex;
      width: 95%;
      &-text {
        display: flex;
        font-weight: 500;
        font-size: 12px;
        color: #CECECE;
        width: 100%;
        // for scroll
        overflow-x: scroll;
        overscroll-behavior-x: contain;
      }

    }
  }
 }
}
::-webkit-scrollbar {
  width: 0%;
  height: 0%;
  background: transparent;
  border: none;
}
.campaignTerms {
  display: flex;
  margin-top: 65px;
  justify-content: center;
  margin-left: 10px;
  &-text {
    width: 90%;
    height: 58px;
    font-family: 'Poppins';
    font-style: normal;
    font-weight: 400;
    font-size: 13px;
    line-height: 140%;
  }
}

.joinButton {
  margin-top: 30px;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-size: 16px;
  color: #FFFFFF;
}

.form-check-input {
  position: relative;
}

.interest-input-area {
  max-width: 348px;

}
::ng-deep app-success-modal .content-area .content-area-body .icon-message-success {
  display: none !important;
}


[type="checkbox"]:checked,
[type="checkbox"]:not(:checked) {
  position: absolute;
  left: -9999px;
}

[type="checkbox"]:checked + label,
[type="checkbox"]:not(:checked) + label {
  position: relative;
  padding-left: 36px;
  cursor: pointer;
  display: inline-block;
  color: #666;
  font-size: 16px;
  line-height: 18px;
  margin-bottom: 0.8rem;
}

[type="checkbox"]:checked + label:before,
[type="checkbox"]:not(:checked) + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked + label:before {
  border-color: #ffa300;
}

[type="checkbox"]:checked + label:after,
[type="checkbox"]:not(:checked) + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #ffa300;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
}

[type="checkbox"]:checked.special + label:before,
[type="checkbox"]:not(:checked).special + label:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
  border: 1px solid #ddd;
  background: #fff;
}

[type="checkbox"]:checked.special + label:after,
[type="checkbox"]:not(:checked).special + label:after {
  content: "";
  width: 12px;
  height: 12px;
  background: #6c6c6c;
  position: absolute;
  top: 3px;
  left: 3px;
  -webkit-transition: all 0.2s ease;
  transition: all 0.2s ease;
  background-image: none;
  opacity: 1;
  -webkit-transform: none;
  transform: none;
}

[type="checkbox"]:not(:checked) + label:after {
  opacity: 0;
  -webkit-transform: scale(0);
  transform: scale(0);
}

[type="checkbox"]:checked + label:after {
  opacity: 1;
  -webkit-transform: scale(1);
  transform: scale(1);
}
.select-interest {
  display: grid;
  grid-template-columns: auto auto auto;
  width: 100%;
  justify-content: center;
}

.interest {
  font-weight: 500;
  font-size: 12px;
  color: #CECECE;
  padding: 3px;

}

.activeCategory{
  color: #ffa300 !important;
}
