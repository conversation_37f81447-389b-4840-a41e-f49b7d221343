import { Component, OnInit, OnDestroy } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';
import { Observable, Subject } from 'rxjs';
import { CustomerState, HeaderStateModel } from '../../customer/state/customer/customer.state';
import { takeUntil } from 'rxjs/operators';
import { UserState } from '../../customer/state/user/user.state';
import { SanitizedCustomerModel } from '../../customer/model/sanitized-customer.model';
import {
  getFormErrorMessage,
  isShowFormError,
  validateAllFormFields,
} from '../../../util/form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { AgreementTypeEnum } from '../../definition/enum/agreement-type.enum';
import { PromotionService } from '../service/promotion.service';
import { Navigate } from '@ngxs/router-plugin';
import { InterestsEnum } from '../enum/interests.enum';
import {SettingsState} from '../../../shared/state/settings/settings.state';
import { BorusanBlockedActionsEnum } from '../../definition/enum/borusan-blocked-actions.enum';
import { PromotionState } from '../state/promotion.state';
import { BoomClubInterestModel } from '../model/promotion.model';
import { BoomClubInterestAction } from '../state/promotion.action';
import { Router } from '@angular/router';

@Component({
  selector: 'app-boom-club-register',
  templateUrl: './boom-club-register.component.html',
  styleUrls: ['./boom-club-register.component.scss']
})
export class BoomClubRegisterComponent implements OnInit, OnDestroy {

  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly store: Store,
    private readonly promotionService: PromotionService,
    private readonly router: Router
  ) { }
  currentUser: any;
  currentCustomer: any;
  @Select(CustomerState.header)
  status$: Observable<HeaderStateModel>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(UserState.basics)
  currentUser$: Observable<SanitizedCustomerModel>;

  @Select(UserState.isGdprApproved)
  isGdprApproved$: Observable<boolean>;

  @Select(PromotionState.boomClubInterests)
  boomClubInterests$: Observable<BoomClubInterestModel[]>;

  agreementTypeEnum = AgreementTypeEnum;
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  showRegisterForm: boolean;
  interestInput = false;
  loading = false;
  formSendStatus = false;
  interestsEnum = InterestsEnum;
  interests: BoomClubInterestModel[] = [];
  selectedInterest = [];
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [Validators.required, Validators.email]),
    Phone: new FormControl(null, [Validators.required]),
    //Interests: new FormControl(null, [Validators.required]),
    CampaignNews: new FormControl(false),
  });

  borusanBlockedActions: string[];
  BorusanBlockedActionsEnum = BorusanBlockedActionsEnum;

  ngOnInit() {
    this.store.dispatch(new BoomClubInterestAction())
    this.boomClubInterests$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.interests = data;
      })
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: null,
        bgcolor: 'transparent',
        img: {
          src: 'assets/boom_club.svg',
          alt: '_join_to_boom_club',
        }
      })
    );

    this.currentCustomer$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(current => {
      this.currentCustomer = current;
    });
    this.currentUser$.pipe(takeUntil(this.subscriptions$)).subscribe((user) => {
      this.currentUser = user;
      if (this.currentUser) {
        this.form.patchValue({
          Name: this.currentUser.firstName,
          Surname: this.currentUser.lastName,
          Email: this.currentUser.email,
          Phone: this.currentUser.mobile,
        });
      }
    });

    this.borusanBlockedActions = this.store.selectSnapshot(SettingsState.borusanBlockedActions);
  }

  selectInterest(item: any) {
    if (this.selectedInterest.includes(item)) {
      this.selectedInterest = this.selectedInterest.filter((data) => data.id !== item.id);
    } else {
      this.selectedInterest.push(item);
    }
    this.form.patchValue({
      Interests: this.selectedInterest.map(item => item.id),
    });
  }
  showInterestsArea() {
    this.interestInput = !this.interestInput;
  }
  onSubmitForm() {
    if (this.form.valid) {
      console.log('valid');
      this.sendForm();
    } else {
      validateAllFormFields(this.form);
      this.interestInput = true;
    }
  }

  back() {
    this.store.dispatch(new Navigate(['/promotion/boom-club']));
  }

  sendForm() {
    const entries = this.form.value?.agreements
    ? Object.entries(this.form.value?.agreements?.value)
    : [].filter(([key, v]) => v).map((a) => a[0]);
    const headers = entries.length
      ? { ApprovedAgreementNames: entries.join(';') }
      : {};
    const countryCode = this.currentCustomer.countryCode;
    this.loading = true;
    const { value } = this.form;
    this.promotionService
    .boomClubJoin({interestIds: []}, headers) // value.Interests
    .subscribe(
      (res) => {
        this.loading = false;

        if (res.code === 0) {
          this.router.navigate(['promotion', 'boom-club', 'advantages'], { replaceUrl: true });
        }
      },
      ({ error }) => {
        this.loading = false;
        this.formSendStatus = false;
        console.log('error', error);
      }
    );
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }


}
