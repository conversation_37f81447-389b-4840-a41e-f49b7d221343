import { Component, OnInit, OnDestroy } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';
import { Observable, Subject } from 'rxjs';
import { CustomerState, HeaderStateModel } from '../../customer/state/customer/customer.state';
import { takeUntil } from 'rxjs/operators';
import { UserState } from '../../customer/state/user/user.state';
import { SanitizedCustomerModel } from '../../customer/model/sanitized-customer.model';
import {
  getFormErrorMessage,
  isShowFormError,
  isShowFormErrorTouched,
  validateAllFormFields,
} from '../../../util/form-error.util';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { UserService } from '../../customer/service/user.service';
import { BoomClubCategoriesAction, BoomClubUserMeAction } from '../state/promotion.action';
import { PromotionState } from '../state/promotion.state';
import { BoomClubUserModel } from '../model/promotion.model';
import { environment } from 'src/environments/environment';
import { PromotionService } from '../service/promotion.service';

@Component({
  selector: 'app-boom-club',
  templateUrl: './boom-club.component.html',
  styleUrls: ['./boom-club.component.scss'],
})
export class BoomClubComponent implements OnInit, OnDestroy {

  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly userService: UserService,
    private readonly promotionService: PromotionService
  ) { }

  @Select(CustomerState.header)
  status$: Observable<HeaderStateModel>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;

  @Select(UserState.basics)
  currentUser$: Observable<SanitizedCustomerModel>;

  @Select(PromotionState.boomClubUserMe)
  boomClubUserMe$: Observable<BoomClubUserModel>;

  @Select(PromotionState.boomClubLoading)
  boomClubLoading$: Observable<boolean>;

  loading = false;
  currentUser: any;
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  showRegisterForm = false;
  meData: any;
  form: FormGroup = new FormGroup({
    Name: new FormControl(null, [Validators.required]),
    Surname: new FormControl(null, [Validators.required]),
    Email: new FormControl(null, [Validators.required, Validators.email]),
    Phone: new FormControl(null, [Validators.required]),
    Interests: new FormControl(null, [Validators.required]),
    CampaignNews: new FormControl(false),
  });
  usedAdvantageIcon = `${environment.assets}/orange-succes.png`;

  ngOnInit() {
    this.loading = true;

    this.setHeader();
    this.promotionService.boomClubUserMe().subscribe(
      data => {
        if (data){
          this.router.navigate(['promotion', 'boom-club', 'advantages'], { replaceUrl: true });
          this.loading = false;
        } else {
          this.router.navigate(['promotion', 'boom-club', 'register'], { replaceUrl: true });
          this.loading = false;
        }
      }
    );

    this.currentUser$.pipe(takeUntil(this.subscriptions$)).subscribe((user) => {
      this.currentUser = user;
      if (this.currentUser) {
        this.form.patchValue({
          Name: this.currentUser.firstName,
          Surname: this.currentUser.lastName,
          Email: this.currentUser.email,
          Phone: this.currentUser.mobile,
        });
      }
    });
  }

  scrollTop() {
    if (document.getElementsByClassName('ng-dropdown-panel-items')[0]) {
      document.getElementsByClassName('ng-dropdown-panel-items')[0].scrollTop = 0;
    }
  }

  setHeader() {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: false,
          closeButton: true,
          hamburgerMenu: false,
          notificationIcon: false,
          title: null,
          bgcolor: 'transparent',
          img: {
            src: 'assets/boom_club.svg',
            alt: 'Boom Club',
          },
          titleContentStyle: 'flex-direction: row !important; gap: .5rem; align-items: baseline !important;'
        })
      );
  }


  showForm(){
    this.showRegisterForm = true;
  }

  onSubmitForm() {
    if (this.form.valid) {
    console.log({
        name: this.form.value.Name,
        surname: this.form.value.Surname,
        email: this.form.value.Email,
        phone: this.form.value.Phone,
        interests: this.form.value.Interests,
        campaignNews: this.form.value.CampaignNews,
      });
    } else {
      validateAllFormFields(this.form);
    }
  }
  toRegister() {
    this.router.navigate(['promotion', 'boom-club', 'register']);
  }

  navigateUsedAdvantage(){
    this.router.navigate(['promotion', 'boom-club', 'advantages', 'used-advantages']);
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
