@import "variable/icon-variable";

.club {
  height: calc(100dvh - 48px);
  overflow-x: hidden !important;
}

.radial-top {
  position: absolute;
  width: 437px;
  height: 437px;
  left: -237px;
  top: -125px;
  background: radial-gradient(50% 50% at 50% 50%, #FFE7D0 0%, rgba(255, 255, 255, 0) 100%);
  z-index: -5;
}

.radial-top1 {
  position: absolute;
  width: 437px;
  height: 437px;
  left: -160px;
  top: -200px;
  background: radial-gradient(50% 50% at 50% 50%, #FFE7D0 0%, rgba(255, 255, 255, 0) 100%);
  z-index: -5;
}

.radial-bottom {
  position: absolute;
  width: 532px;
  height: 532px;
  left: -67.72px;
  top: 500.21px;
  background: radial-gradient(50% 50% at 50% 50%, #FFE7D0 0%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(-37.55deg);
  z-index: -5;
}
.radial-bottom1 {
  position: absolute;
  width: 531px;
  height: 532px;
  left: 43px;
  top: 641.03px;

  background: radial-gradient(50% 50% at 50% 50%, #D8D9FF 0%, rgba(255, 255, 255, 0) 100%);
  transform: rotate(-37.55deg);
  z-index: -5;
}

.soon{
  //height: calc(100vh - 64px);
}


.boom-club-join {
  position: relative;
  z-index: 999;
  height: 100%;
  font-family: "Poppins";
  font-style: normal;
  font-weight: 400;
  font-size: 15px;
  line-height: 21px;
  color: #232121;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  margin-bottom: 10%;
  margin-top: 15%;
  border-radius: 5%;
  padding: 10px;


}



.boom-club-register {
  width: 90%;
  margin: 0 auto;
  height: 100%;
}

app-warning-box ::ng-deep .box  {
  font-size: 11px;
  border: 2px solid #F7A226;
  border-radius: 17px;
  margin-top: 1.4em;
}

app-warning-box ::ng-deep .box .title .text .icon  {
  display: none;
}

app-warning-box ::ng-deep .box .title .text:last-child {
  border-radius: 25px;
  border: 1px solid white !important;
  background: #fff;
  white-space: nowrap;
  padding-right: 3px !important;
  padding-left: 3px !important;
  color: #000000 !important;
  font-family: 'Poppins' !important;
  font-style: normal !important;
  font-weight: 500 !important;
}

.footer {
  display: flex;
  flex-direction: column;
  justify-content: baseline;

}

.form-check-input {
  position: relative;
}

::ng-deep .ng-select.ng-select-multiple .ng-select-container .ng-value-container .ng-placeholder {
  top: 11px !important;
  left: 17px !important;
  padding-bottom: 5px;
  padding-left: 3px;
}