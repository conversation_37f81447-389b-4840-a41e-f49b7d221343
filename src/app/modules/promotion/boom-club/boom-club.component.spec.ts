/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { BoomClubComponent } from './boom-club.component';

describe('BoomClubComponent', () => {
  let component: BoomClubComponent;
  let fixture: ComponentFixture<BoomClubComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ BoomClubComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(BoomClubComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
