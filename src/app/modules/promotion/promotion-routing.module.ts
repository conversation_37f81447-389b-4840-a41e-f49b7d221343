import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { PromotionComponent } from './promotion/promotion.component';
import { ForceAgreementComponent } from './force-agreement/force-agreement.component';
import { BoomClubComponent } from './boom-club/boom-club.component';
import { BoomClubRegisterComponent } from './boom-club-register/boom-club-register.component';
import {PromotionStuffListComponent} from './promotion-stuff-list/promotion-stuff-list.component';
import { BoomClubAdvantageDetailComponent } from './boom-club-advantage-detail/boom-club-advantage-detail.component';
import { BoomClubAdvantagesComponent } from './boom-club-advantages/boom-club-advantages.component';
import { BoomClubUsedAdvantagesComponent } from './boom-club-used-advantages/boom-club-used-advantages.component';

const routes: Routes = [
  {
    path: '',
    component: ForceAgreementComponent
  },
  {
    path: 'coin',
    component: PromotionComponent,
    // resolve: [PromotionResolver]
  },
  {
    path: 'boom-club',
    component: BoomClubComponent,
    children: [
      {
        path: 'advantages',
        component: BoomClubAdvantagesComponent
      },
      {
        path: 'advantages/used-advantages',
        component: BoomClubUsedAdvantagesComponent
      },
      {
        path: 'advantage/:id',
        component: BoomClubAdvantageDetailComponent
      }
    ]
  },
  {
    path: 'boom-club/register',
    component: BoomClubRegisterComponent
  },
  {
    path: 'promotion-stuff-list',
    component: PromotionStuffListComponent
  },
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    RouterModule.forChild(routes)
  ],
  exports: [
    RouterModule
  ]
})
export class PromotionRoutingModule {}
