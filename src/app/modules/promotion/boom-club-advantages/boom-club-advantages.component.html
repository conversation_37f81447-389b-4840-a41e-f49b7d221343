<app-boom-club-user-badge
  *ngIf="showUserBadge"
  [variant]="'standart'"
  [user]="boomClubUserMe"
></app-boom-club-user-badge>
<div class="categories-bar mt-4 mb-3">
  <div
    class="scrollable-content overflow-auto d-flex align-items-center justify-content-between"
  >
    <button
      (click)="selectCategory(category)"
      class="btn py-0 category px-3 font-weight-bold font-size-14px w-100"
      [ngClass]="{
        'selected-category': category?.name === selectedCategory.name
      }"
      *ngFor="let category of boomClubCategories"
    >
      {{ category?.name }}
    </button>
  </div>
</div>
<div class="advantages pb-3" *ngIf="filteredAdvantages?.length">
  <div class="h5">{{ title | translate }}</div>
  <ng-container *ngFor="let advantage of filteredAdvantages">
    <app-boom-club-activities-card
      class="pt-2"
      [backgroundImage]="
        environment.api +
        '/image/boomclubadvantage?advantageId=' +
        advantage.id +
        '&lang=' +
        boomClubUserMe?.countryCode
      "
      [isNew]="advantage?.isNew"
      [activitiesSoonContent]="advantage?.title"
      activitiesSoonSeeAll="_more_information"
      [enableToRegister]="false"
      [advantageId]="advantage?.id"
      [categoryId]="advantage?.categories?.length ? advantage?.categories[0]['id'] : null"
      [isUsed]="true"
    ></app-boom-club-activities-card>
  </ng-container>
</div>
<div
  *ngIf="!filteredAdvantages?.length && !(boomClubLoading$ | async)"
  class="not-found-message h-25 d-flex flex-column align-items-center justify-content-center"
>
  <i class="icon icon-coupon mb-2"></i>
  <span class="font-weight-semi-bold">
    {{ "_not_found_advantages" | translate }}
  </span>
</div>
<button (click)="navigateUsedAdvantage()" class="btn p-0 navigate-used-advantage-btn">
  <img [src]="usedAdvantageIcon" alt="" width="50" height="50">
</button>

<app-loader [show]="boomClubLoading$ | async"></app-loader>
