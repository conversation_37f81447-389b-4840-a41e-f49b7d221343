import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { PromotionState } from '../state/promotion.state';
import { Observable, Subject } from 'rxjs';
import {
  BoomClubAdvantagesModel,
  BoomClubCategoriesModel,
  BoomClubUserModel,
} from '../model/promotion.model';
import { takeUntil } from 'rxjs/operators';
import {
  BoomClubAdvantagesAction,
  BoomClubCategoriesAction,
  BoomClubUserMeAction,
} from '../state/promotion.action';
import { environment } from 'src/environments/environment';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';
import { TranslateService } from '@ngx-translate/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-boom-club-advantages',
  templateUrl: './boom-club-advantages.component.html',
  styleUrls: ['./boom-club-advantages.component.scss'],
})
export class BoomClubAdvantagesComponent implements OnInit, OnDestroy {
  @Input()
  showUserBadge = true;

  @Input()
  isUsed = false;

  @Input()
  title = '_advantages';

  @Select(PromotionState.boomClubCategories)
  boomClubCategories$: Observable<BoomClubCategoriesModel[]>;
  @Select(PromotionState.boomClubUserMe)
  boomClubUserMe$: Observable<BoomClubUserModel>;
  @Select(PromotionState.boomClubLoading)
  boomClubLoading$: Observable<boolean>;
  @Select(PromotionState.boomClubAdvantages)
  boomClubAdvantages$: Observable<BoomClubAdvantagesModel[]>;

  environment = environment;
  selectedCategory: BoomClubCategoriesModel;
  boomClubCategories: BoomClubCategoriesModel[] = [];
  boomClubUserMe: BoomClubUserModel;
  boomClubAdvantages: BoomClubAdvantagesModel[] = [];
  filteredAdvantages: BoomClubAdvantagesModel[] = [];
  usedAdvantageIcon = `${environment.assets}/orange-succes.png`;

  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly translateService: TranslateService
  ) {}

  ngOnInit() {
    this.store.dispatch(new BoomClubCategoriesAction());
    this.boomClubCategories$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        const allCategories: BoomClubCategoriesModel = {
          icon: null,
          id: '0',
          name: this.translateService.instant('_all'),
        };
        this.selectedCategory = allCategories;
        this.boomClubCategories = [allCategories, ...data];
      });

    this.store.dispatch(new BoomClubUserMeAction());
    this.boomClubUserMe$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.boomClubUserMe = data;
      });

    this.store.dispatch(new BoomClubAdvantagesAction(this.isUsed));
    this.boomClubAdvantages$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data) => {
        this.boomClubAdvantages = data;
        this.filteredAdvantages = data;
      });

    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: true,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
        title: null,
        bgcolor: 'transparent',
        img: {
          src: 'assets/boom_club.svg',
          alt: 'Boom Club',
        },
      })
    );
  }

  selectCategory(category: BoomClubCategoriesModel) {
    this.selectedCategory = category;
    this.filteredAdvantages =
      category.id === '0'
        ? this.boomClubAdvantages
        : this.filterAdvantagesByCategory(category);
  }

  filterAdvantagesByCategory(selectedCategory: BoomClubCategoriesModel) {
    return this.boomClubAdvantages.filter((advantage) => {
      return advantage.categories.some(
        (category) => category.name === selectedCategory.name
      );
    });
  }

  navigateUsedAdvantage(){
    this.router.navigate(['promotion', 'boom-club', 'advantages', 'used-advantages']);
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
