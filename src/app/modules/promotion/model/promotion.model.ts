export interface PromotionModel {
  totalPoint: number;
  details: PromotionDetail[];
}

export interface PromotionDetail {
  recordId: string;
  referenceKey: string;
  pointTypeDescription: string;
  pointType: string;
  point: number;
  totalPoint: number;
  expireDate: Date;
  description: string;
  createdDate: string;
  createdTime: string;
}

export interface PromotionStuffListModel {
  id: string;
  name: string;
  coinPrice: number;
  imageUrl: string;
}

export interface PssrAccountModel {
  isPssrUser: boolean;
  isPssrManagerUser: boolean;
  boomCoinType: number;
  boomCoinTypeName: string;
  boomCoin: number;
}
export interface BoomClubCategoriesModel {
  id: string;
  name: string;
  icon: any;
}

export interface BoomClubAdvantagesModel {
  id: string;
  startDate: string;
  validDate: string;
  title: string;
  displayOrder: number;
  link: string;
  isNew: boolean;
  description: string;
  categories: Category[];
  isUsed: boolean;
}

export interface Category {
  id: string;
  name: string;
  icon: any;
}

export interface BoomClubUserModel {
  formId: string;
  countryCode: string;
  email: string;
  name: string;
  surname: string;
  phone: string;
  formData: string;
  borusanUserLoginId: any;
  borusanUsername: any;
  borusanFullname: any;
  portalUserId: string;
  customerId: string;
  relatedPersonId: string;
  companyId: string;
  membershipTypeId: string;
  advantages: AdvantageModel[];
  interests: BoomClubInterestModel[];
}

export interface AdvantageModel {
  id: string;
  imageUrl: string;
  startDate: string;
  validDate: string;
  title: string;
  displayOrder: number;
  link: any;
  isNew: boolean;
  description: string;
  categories: any;
}
export interface BoomClubInterestModel {
  id: string;
  name: string;
}

export interface BoomClubJoinModel {
  advantageId: string;
  code: string;
  validDate: string;
}

export interface BoomCoinPromotionProductsModel {
  countryCode: string;
  limit: number;
  offset: number;
  total: number;
  rows: BoomCoinPromotionProduct[];
}
export interface BoomCoinPromotionProduct {
  id: string;
  title: string;
  code: string;
  point: number;
  url: string;
  thumbnailUrl: string;
  createdBy: string;
  dateCreated: string;
  order: number;
  isActive: boolean;
}

export interface SalesOfficeModel{
  name: string;
  address: string;
  countryCode: string;
  lat: number;
  lng: number;
  city: string;
}