<div *ngIf="(!pssrAccount?.isPssrUser && !pssrAccount?.isPssrManagerUser) || !(boomDiamondSystemFeature && boomGoldSystemFeature)" class="stuffListPage px-4 pb-5">
    <div class="header d-flex justify-content-between flex-row align-items-center">
      <div></div>
      <div class="header-text">{{'_promotion_stuff_list'|translate}}</div>
      <div class="d-flex flex-row align-items-center text-black-50 p-2" style="background-color: #F5F5F5; border-radius: 8px;">
        <b class="h4 mb-0 text-warning pr-2">{{boomPoint}}</b>
        <i class="icon icon-coins text-warning" style="font-size: 20px;"></i>
      </div>
    </div>

    <div class="content">
      <ng-container>
        <div class="products-container">
          <div class="products" *ngFor="let product of promotionProducts">
            <div class="products-body my-2">
              <div class="d-flex products-body-container">
                <div class="products-image">
                  <img class="images" [src]="product?.thumbnailUrl " [alt]="" (click)="openImagePopup(product)">
              </div>
                <div class="flex-fill d-flex flex-column justify-content-center ml-2 pl-2">
                  <div class="products-info h6">
                    {{product?.title | translate}}
                  </div>
                  <div class="font-weight-semi-bold h5">
                    {{product?.point}}
                    <i class="icon icon-coins"></i>
                  </div>
                </div>
              </div>
              <button
                appClickLog
                [section]="'PROMOTION'"
                [subsection]="'PROMOTION_STUFF_REQUEST_CLICK'"
                [data]="{
                  name: product?.title,
                  coinPrice: product?.point,
                  customerCoin: boomPoint
                }"
                class="request-button btn btn-warning p-0 text-white"
                [ngClass] = "boomPoint < product?.point ? 'disabled' : '' "
                (click)="requestStuff(product)">
                  {{'_promotion_stuff_request_send'|translate}}
              </button>
            </div>
          </div>
        </div>
      </ng-container>
    </div>
</div>

<div *ngIf="boomDiamondSystemFeature && pssrAccount?.isPssrManagerUser" class="stuffListPage-diamond px-4 pb-5">
  <div class="header-diamond">
    <div class="diamond-point-area p-2">
      <img class="diamond-icon-point" src="assets/stuff-list-diamond-icon.svg"/>
      <b class="dioamond-point">{{pssrAccount?.boomCoin}} {{"_point" | translate}}</b>
    </div>
  </div>
  <div class="px-3 py-2 d-flex flex-column">
    <div class="text-area-body">
      <p class="stuff-list-body-text-diamond"> {{"_very_soon_text" | translate}} </p>
      <img src="assets/diamond1.svg">
    </div>
    <button type="button" class="go-home-button btn btn-warning text-white px-5 font-weight-bolder">
      <p class="go-home-text" (click)="openDashboard()"> {{ "_return_to_homepage" | translate}}</p>
    </button>
  </div>
</div>
<div *ngIf="boomGoldSystemFeature && pssrAccount?.isPssrUser" class="stuffListPage px-4 pb-5">
  <div class="header-gold">
    <div class="gold-point-area p-2">
      <img class="gold-icon-point" src="assets/stuff-list-gold-icon.svg"/>
      <b class="gold-point">{{pssrAccount?.boomCoin}} {{"_point" | translate}} </b>
    </div>
  </div>
  <div class="px-3 py-2 d-flex flex-column" >
    <div class="text-area-body">
      <p class="body-text-gold"> {{"_very_soon_text" | translate}} </p>
      <img src="assets/kulcealtin_3.png">
    </div>
    <button type="button" class="go-home-button btn btn-warning text-white px-5 font-weight-bolder">
      <p class="go-home-text" (click)="openDashboard()"> {{ "_return_to_homepage" | translate}}</p>
    </button>
  </div>
</div>

<app-big-modal [(status)]="showStuffRequestForm" (statusChange)="requestFormReset()" [headerText]="'_promotion_staff_request' | translate">
  <div class="d-flex products-body-container mb-3" style="height: 83px">
    <div class="products-image">
      <img class="images" [src]="selectedProducts?.thumbnailUrl || 'assets/boom-coin.png'" [alt]="selectedProducts?.title">
    </div>
    <div class="d-flex flex-column justify-content-center ml-2">
      <div class="font-weight-semi-bold h5">
        <i class="icon icon-coins"></i>
        {{selectedProducts?.point}}
      </div>
      <div class="products-info">
        {{selectedProducts?.title | translate}}
      </div>
    </div>
  </div>
  <form (submit)="sendStuffRequestForm()" [formGroup]="form">
    <div class="form-group">
      <input
        [name]="'Name'"
        [placeholder]="'_name' | translate"
        [formControlName]="'Name'"
        class="form-control form-control"
        type="text"
        max="155"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Name) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Name) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        [name]="'Surname'"
        [placeholder]="'_surname' | translate"
        [formControlName]="'Surname'"
        class="form-control form-control"
        type="text"
        max="155"
      />
      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Surname) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Surname) | translate }}
      </div>
    </div>
    <div class="form-group d-flex flex-row flex-nowrap">
      <button [disabled]="true" type="button" class="btn text-light bg-info increment-button">
        -
      </button>
      <input
        [name]="'StuffQuantity'"
        [placeholder]="'_address' | translate"
        [formControlName]="'StuffQuantity'"
        class="form-control form-control rounded-0 text-center"
        type="number"
        maxlength="155"
        min="1"
        [attr.disabled]="true"
      />
      <button [disabled]="true" type="button" class="btn text-light bg-info decrement-button">
        +
      </button>
      <div
        [ngClass]="{ 'd-block': !form.get('StuffQuantity').valid && form.get('StuffQuantity').touched }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.StuffQuantity) | translate }}
      </div>
    </div>
    <div *ngIf="currentCustomer?.countryCode == 'KZ'" class="form-group">
      <ng-select
        class="service-drp"
        [searchable]="false"
        [placeholder]="'_choose_sales_office_' | translate"
        [clearable]="false"
        [formControlName]="'SalesOffice'">
        <ng-option
          *ngFor="let offices of salesOffice"
          [value]="offices.city + offices.address"
          >{{offices.city}} {{offices.address}}
        </ng-option>
      </ng-select>
      <!-- <div
        [ngClass]="{ 'd-block': isShowError(form.controls.SalesOffice) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.SalesOffice) | translate }}
      </div> -->
    </div>
    <div *ngIf="currentCustomer?.countryCode == 'AZ'" class="form-group">
      <textarea
        [name]="'Address'"
        [placeholder]="'adress_portal_kz' | translate"
        [formControlName]="'Address'"
        class="form-control form-control"
        type="text"
        style="resize: none;"
      ></textarea>

      <div
        [ngClass]="{ 'd-block': isShowError(form.controls.Address) }"
        class="invalid-feedback pl-3"
      >
        {{ getFormErrorMessage(form.controls.Address) | translate }}
      </div>
    </div>
    <div class="form-group">
      <input
        [value]="'_send' | translate"
        class="btn btn-warning btn-gradient btn-block text-white"
        type="submit"
      />
    </div>
  </form>
</app-big-modal>
<app-loader [show]="loading"></app-loader>
<app-success-modal [(status)]="stuffRequestFormSended" [inModal]="true" [message]="'_general_successfully_send_form'"></app-success-modal>
<app-basic-modal [(status)]="coinWarningModal">
  <div class="promotion-not-enough"> {{'_promotion_not_enough_' | translate}} </div>
</app-basic-modal>
<app-basic-modal [(status)]="imageModal">
  <div class="image-popup">
    <img class="promotion-product-image" [src]="selectedImg?.url || 'assets/boom-coin-single.png'">
  </div>
</app-basic-modal>
