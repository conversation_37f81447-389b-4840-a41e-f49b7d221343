@import "variable/bootstrap-variable";

.stuffListPage {
  font-style: normal;
  line-height: normal;
  overflow-y: auto;
  height: calc(100vh - 70px);
  width: 100%;
  display: flex;
  flex-direction: column;
  // justify-content: space-between;

  .header {
    i {
      margin: 0 auto;
    }

    &-text {
      margin: 10px 0 10px 0;
      font-size: 19px;
      font-weight: 700;
      line-height: 18px;
      letter-spacing: 0;
      text-align: center;
    }
  }

  .content {
    margin: 0 auto;
    width: 100%;

    .products-container {
      padding-bottom: 25px;
    }
  }

}

.stuffListPage-diamond {
  font-style: normal;
  line-height: normal;
  overflow-y: auto;
  height: calc(100vh - 55px);
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .header {
    i {
      margin: 0 auto;
    }

    &-text {
      margin: 10px 0 10px 0;
      font-size: 19px;
      font-weight: 700;
      line-height: 18px;
      letter-spacing: 0;
      text-align: center;
    }
  }
  .content {
    margin: 0 auto;
    width: 100%;

    .products-container {
      padding-bottom: 25px;
    }
  }
}

.products {
  width: 98%;
  margin-top: 15px;
  flex-direction: column;

  &-body {
    &-container {
      width: 89%;
    }

    border-radius: 8px;
    background: #FBFBFB;
    width: 100%;
    height: 83px;
    display: flex;
    justify-content: space-between;
  }

  &-image {
    display: flex;
    justify-content: center;
    border-radius: 8px;
    background: #F5F5F5;
    max-width: 85px;
    max-height: 83px;
    min-width: 85px;
  }

  &-info {
    padding-top: 6px;
    color: #505050;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 0.7rem;
  }
}
.request-button {
  border-radius: 8px;
  color: #fff;
  background-color: var(--warning);
  height: 100%;
}
.increment-button {
  margin-right: -1px;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}
.decrement-button {
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}
.pl-2{
  padding-left: 0.3rem !important;
}
.flex-fill{
  flex: 1 10 auto !important;
}
.promotion-not-enough{
  text-align: center;
  margin-top: 20px;
  margin-bottom: 40px;
}
.images {
  max-height: 100%;
  align-self: center;
  object-fit: contain;
  max-width: 100%;
}

.promotion-product-image{
  margin-top: 20px;
  margin-bottom: 40px;
}

.header-diamond{
  display: flex;
  align-self: flex-end;
}

.header-gold{
  display: flex;
  align-self: flex-end;
}

.dioamond-point{
  color: rgba(81, 129, 175, 1);
  font-family: Poppins;
  font-size: 13px;
  font-weight: 700;
  line-height: 22px;
  letter-spacing: 0px;
  text-align: center;
  padding-right: 5px;
}

.gold-point{
  color: rgba(228, 151, 59, 1);
  font-family: Poppins;
  font-size: 13px;
  font-weight: 700;
  line-height: 22px;
  letter-spacing: 0px;
  text-align: center;
  padding-right: 5px;
}

.diamond-icon-point{
  padding-right: 10px;
}

.gold-icon-point{
  padding-right: 10px;
}

.text-area-body{
  width: 100%;
  text-align: -webkit-center;
}

.stuff-list-body-text-diamond{
  text-align: center;
  color: rgba(77, 142, 160, 1);
  font-size: 52px;
  font-weight: 900;
  line-height: 84px;
  letter-spacing: 0px;
  margin-top: 65px;
  width: 80%;
}

.body-text-gold{
  text-align: center;
  color: rgba(238, 188, 72, 1);
  font-size: 52px;
  font-weight: 900;
  line-height: 84px;
  letter-spacing: 0px;
  margin-top: 65px;
  width: 80%;
}

.go-home-button{
  background: linear-gradient(91.31deg, #FFA300 0%, #FFB532 100%);
  box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.05);
  color: white;
  height: 50px;
  border-radius: 6px;
  border: none;
  margin-top: 110px;
  width: 100%;
}

.go-home-text{
  font-family: Poppins;
  font-size: 16px;
  font-weight: 700;
  line-height: 28px;
  letter-spacing: 0px;
  text-align: center;
  color: rgba(255, 255, 255, 1);
  height: 22px;
}

.diamond-point-area{
  display: flex;
  align-items: center;
  border-radius: 8px;
  background-color: rgba(240, 245, 247, 1);
}

.gold-point-area{
  display: flex;
  align-items: center;
  border-radius: 8px;
  background-color: rgba(248, 244, 238, 1);
}

.image-popup{
  display: flex;
  justify-content: center;
}