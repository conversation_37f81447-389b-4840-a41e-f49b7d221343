import { Component, HostListener, OnInit } from '@angular/core';
import { HeaderStatusAction } from '../../customer/state/customer/customer.actions';
import { Select, Store } from '@ngxs/store';
import { TranslateService } from '@ngx-translate/core';
import { PromotionState } from '../state/promotion.state';
import { BoomCoinPromotionProductsAction, PromotionAction, SalesOfficeAction } from '../state/promotion.action';
import { Observable, Subject } from 'rxjs';
import { SalesOfficeModel, BoomCoinPromotionProduct, BoomCoinPromotionProductsModel, PromotionModel, PromotionStuffListModel, PssrAccountModel } from '../model/promotion.model';
import { takeUntil } from 'rxjs/operators';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { LoginState } from '../../authentication/state/login/login.state';
import { UserState } from '../../customer/state/user/user.state';
import { getFormErrorMessage, isShowFormError, validateAllFormFields } from 'src/app/util/form-error.util';
import { PromotionService } from '../service/promotion.service';
import { LogService } from '../../../shared/service/log.service';
import { SanitizedCustomerModel } from '../../customer/model/sanitized-customer.model';
import { Navigate } from '@ngxs/router-plugin';
import { SettingsState } from 'src/app/shared/state/settings/settings.state';
import { SystemFeature } from '../../customer/response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';


@Component({
  selector: 'app-promotion-stuff-list',
  templateUrl: './promotion-stuff-list.component.html',
  styleUrls: ['./promotion-stuff-list.component.scss']
})
export class PromotionStuffListComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;
  @Select(PromotionState.get)
  promotion$: Observable<PromotionModel>;

  @Select(PromotionState.getLoading)
  promotionLoading$: Observable<boolean>;

  @Select(UserState.currentCustomer)
  currentCustomer$: Observable<SanitizedCustomerModel>;
  currentCustomer: SanitizedCustomerModel;

  @Select(PromotionState.getPssrAccount)
  pssrAccount$: Observable<PssrAccountModel>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  boomPoint: number;

  @Select(PromotionState.getSalesOffice)
  salesOffice$: Observable<SalesOfficeModel[]>;
  salesOffice: SalesOfficeModel[];

  @Select(PromotionState.getPromotionProducts)
  promotionProducts$: Observable<BoomCoinPromotionProduct[]>;
  promotionProducts: BoomCoinPromotionProduct[];

  selectedProducts: BoomCoinPromotionProduct;

  stuffList: PromotionStuffListModel[];
  pssrAccount: PssrAccountModel;
  office: any ;
  form: FormGroup;
  addressType: any;
  loading = false;
  selectedStuff: PromotionStuffListModel;
  showStuffRequestForm: boolean;
  stuffRequestFormSended: boolean;
  coinWarningModal: boolean = false;
  imageModal: boolean = false;
  selectedImg: any;
  boomDiamondSystemFeature: boolean;
  boomGoldSystemFeature: boolean;
  promotionProductsApiControlFeature: boolean;
  protected subscriptions$: Subject<boolean> = new Subject();

  constructor(
    private readonly store: Store,
    private readonly translateService: TranslateService,
    private readonly promotionService: PromotionService,
    private readonly log: LogService,
  ) {
  }

  @HostListener('window:resize', ['$event'])
  onResize() {
    this.scrollActiveElementIntoView();
  }

  scrollActiveElementIntoView() {
    const activeElement = document.activeElement as HTMLElement;

    if (activeElement && ['INPUT', 'TEXTAREA'].includes(activeElement.tagName)) {
      activeElement.scrollIntoView();
    }
  }

  ngOnInit(): void {
    this.pssrAccountControl()

    this.pssrAccount$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(data => {
      this.pssrAccount = data;
    })
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.boomDiamondSystemFeature = systemFeature('boom_diamond',features, false)
        this.boomGoldSystemFeature = systemFeature('boom_gold',features, false)
        this.promotionProductsApiControlFeature = systemFeature('promotion_products_api_control',features, false)
      }
    });
    this.setHeader();
    this.currentCustomer$.subscribe(currentCustomer => {
      this.currentCustomer = currentCustomer;
      const userData = this.store.selectSnapshot(UserState.basics);

    });
    if(this.currentCustomer?.countryCode === "KZ"){
      this.store.dispatch( new SalesOfficeAction(this.currentCustomer?.countryCode));
      this.salesOffice$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        this.salesOffice = data;
      });
    }
    if(this.promotionProductsApiControlFeature){
      this.boomCoinPromotionProducts();
    }

    if (this.store.selectSnapshot(PromotionState.get)) {
      this.boomPoint = this.store.selectSnapshot(PromotionState.get)?.totalPoint;
    } else {
      this.store.dispatch(new PromotionAction());
      this.promotion$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((promotion) => {
          if (promotion) {
            this.boomPoint = promotion.totalPoint;
          }
        });
      this.promotionLoading$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((load) => {
          this.loading = load;
        });
    }


    this.form = new FormGroup({
      StuffName: new FormControl(null, [Validators.required]),
      StuffId: new FormControl(null, [Validators.required]),
      StuffCoinPrice: new FormControl(0, [Validators.required]),
      Name: new FormControl(null, [Validators.required, Validators.maxLength(50)]),
      Surname: new FormControl(null, [Validators.required, Validators.maxLength(25)]),
      // Address: new FormControl(null, [Validators.required, Validators.minLength(15),Validators.maxLength(500)]),
      StuffQuantity: new FormControl({value: 1, disabled: false}, [Validators.required, Validators.min(1)]),
      // SalesOffice: new FormControl(null, [Validators.required]),
    });

    if(this.currentCustomer?.countryCode === 'KZ'){
    this.form.addControl('SalesOffice', new FormControl(null, [Validators.required]))
    }

    if(this.currentCustomer?.countryCode === 'AZ'){
      this.form.addControl('Address', new FormControl(null, [Validators.required, Validators.minLength(15),Validators.maxLength(500)]))
      }

    if(this.currentCustomer?.countryCode == 'AZ'){
      this.stuffList = [
        // {
        //   name: '_promotion_product_24-05010001_',
        //   imageUrl: 'assets/promotionimages/Resim1.jpg',
        //   coinPrice: 70,
        //   id: '24-05010001'
        // },
        // {
        //   name: '_promotion_product_24-05021301_',
        //   imageUrl: 'assets/promotionimages/Resim2.png',
        //   coinPrice: 40,
        //   id: '24-05021301'
        // },
        {
          name: '_promotion_product_24-05030001_',
          imageUrl: 'assets/promotionimages/Cat_kupa-1.png',
          coinPrice: 50,
          id: '24-05030001'
        },
        {
          name: '_promotion_product_24-05030003_',
          imageUrl: 'assets/promotionimages/Resim4.png',
          coinPrice: 100,
          id: '24-05030003'
        },
        // {
        //   name: '_promotion_product_24-05050001_',
        //   imageUrl: 'assets/promotionimages/Resim5.png',
        //   coinPrice: 60,
        //   id: '24-05050001'
        // },
        {
          name: '_promotion_product_24-05050006_',
          imageUrl: 'assets/promotionimages/Resim6.jpg',
          coinPrice: 160,
          id: '24-05050006'
        },
        {
          name: '_promotion_product_24-05050009_',
          imageUrl: 'assets/promotionimages/Resim7.png',
          coinPrice: 120,
          id: '24-05050009'
        },
        {
          name: '_promotion_product_24-05060002_',
          imageUrl: 'assets/promotionimages/Resim8.png',
          coinPrice: 25,
          id: '24-05060002'
        },
        // {
        //   name: '_promotion_product_24-05060003_',
        //   imageUrl: 'assets/promotionimages/Resim9.png',
        //   coinPrice: 15,
        //   id: '24-05060003'
        // },
        // {
        //   name: '_promotion_product_24-05060005_',
        //   imageUrl: 'assets/promotionimages/Resim10.png',
        //   coinPrice: 10,
        //   id: '24-05060005'
        // },
        {
          name: '_promotion_product_24-05070001_',
          imageUrl: 'assets/promotionimages/Resim11.png',
          coinPrice: 15,
          id: '24-05070001'
        },
        {
          name: '_promotion_product_980_WL_',
          imageUrl: 'assets/promotionimages/980_WL-real machine.jpg',
          coinPrice: 900,
          id: '980_WL'
        },
        {
          name: '_promotion_product_D9T_',
          imageUrl: 'assets/promotionimages/D9T.png',
          coinPrice: 1000,
          id: 'D9T'
        },
        {
          name: '_promotion_product_950_GC_',
          imageUrl: 'assets/promotionimages/950GC.png',
          coinPrice: 700,
          id: '950_GC'
        },
        {
          name: '_promotion_product_730_',
          imageUrl: 'assets/promotionimages/730.png',
          coinPrice: 500,
          id: '730'
        },
        {
          name: '_promotion_product_777D_',
          imageUrl: 'assets/promotionimages/777D.png',
          coinPrice: 800,
          id: '777D'
        },
        {
          name: '_promotion_product_349F_L_XE_',
          imageUrl: 'assets/promotionimages/349F_L_XE.png',
          coinPrice: 950,
          id: '349F_L_XE'
        },
      ];
    } else if(this.currentCustomer?.countryCode == 'KZ') {

      this.stuffList = [
        {
          name: '_promotion_product_kz_1_',
          imageUrl: 'assets/promotionimages/kz_image1.jpg',
          coinPrice: 60000,
          id: '1'
        },
        // {
        //   name: '_promotion_product_kz_2_',
        //   imageUrl: 'assets/promotionimages/kz_image1.jpg',
        //   coinPrice: 60000,
        //   id: '2'
        // },
        {
          name: '_promotion_product_kz_3_',
          imageUrl: 'assets/promotionimages/kz_image2.jpg',
          coinPrice: 32000,
          id: '3'
        },
        {
          name: '_promotion_product_kz_4_',
          imageUrl: 'assets/promotionimages/kz_image3.jpg',
          coinPrice: 25000,
          id: '4'
        },
        {
          name: '_promotion_product_kz_5_',
          imageUrl: 'assets/promotionimages/kz_image4.jpg',
          coinPrice: 60000,
          id: '5'
        },
        {
          name: '_promotion_product_kz_6_',
          imageUrl: 'assets/promotionimages/kz_image5.jpg',
          coinPrice: 60000,
          id: '6'
        },
        {
          name: '_promotion_product_kz_7_',
          imageUrl: 'assets/promotionimages/kz_image6.jpg',
          coinPrice: 80000,
          id: '7'
        },
        {
          name: '_promotion_product_kz_8_',
          imageUrl: 'assets/promotionimages/kz_image7.jpg',
          coinPrice: 2000,
          id: '8'
        },
        {
          name: '_promotion_product_kz_9_',
          imageUrl: 'assets/promotionimages/kz_image8.jpg',
          coinPrice: 3000,
          id: '9'
        },
        {
          name: '_promotion_product_kz_10_',
          imageUrl: 'assets/promotionimages/kz_image9.jpg',
          coinPrice: 7000,
          id: '10'
        },
        {
          name: '_promotion_product_kz_11_',
          imageUrl: 'assets/promotionimages/kz_image10.jpg',
          coinPrice: 4500,
          id: '11'
        },
        {
          name: '_promotion_product_kz_12_',
          imageUrl: 'assets/promotionimages/kz_image11.jpg',
          coinPrice: 20000,
          id: '12'
        },
        {
          name: '_promotion_product_kz_13_',
          imageUrl: 'assets/promotionimages/kz_image12.jpg',
          coinPrice: 15000,
          id: '13'
        },
        {
          name: '_promotion_product_kz_14_',
          imageUrl: 'assets/promotionimages/kz_image13.jpg',
          coinPrice: 15000,
          id: '14'
        },
        {
          name: '_promotion_product_kz_15_',
          imageUrl: 'assets/promotionimages/kz_image14.jpg',
          coinPrice: 21000,
          id: '15'
        },
        {
          name: '_promotion_product_kz_16_',
          imageUrl: 'assets/promotionimages/kz_image15.jpg',
          coinPrice: 10000,
          id: '16'
        },
        {
          name: '_promotion_product_kz_17_',
          imageUrl: 'assets/promotionimages/kz_image16.jpg',
          coinPrice: 9000,
          id: '17'
        },
      ];
    };

    // this.office = [
    //   {
    //     officeCenter: "_sales_office_almaty_"
    //   },
    //   {
    //     officeCenter: "_sales_office_astana_"
    //   },
    //   {
    //     officeCenter: "_sales_office_aktobe_"
    //   },
    //   {
    //     officeCenter: "_sales_office_aktau_"
    //   },
    //   {
    //     officeCenter: "_sales_office_atyrau_"
    //   },
    //   {
    //     officeCenter: "_sales_office_kyzylorda_"
    //   },
    //   {
    //     officeCenter: "_sales_office_zhezkazgan_"
    //   },
    //   {
    //     officeCenter: "_sales_office_karaganda_"
    //   },
    //   {
    //     officeCenter: "_sales_office_kostanay_"
    //   },
    //   {
    //     officeCenter: "_sales_office_uralsk_"
    //   },
    //   {
    //     officeCenter: "_sales_office_shymkent_"
    //   },
    //   {
    //     officeCenter: "_sales_office_ore_"
    //   },
    //   {
    //     officeCenter: "_sales_office_ust-kamenogorsk_"
    //   },
    //   {
    //     officeCenter: "_sales_office_ekibastuz_"
    //   }
    // ];

    if(this.currentCustomer?.countryCode === 'KZ' || this.currentCustomer?.countryCode === 'AZ'){
    const sufficientCoinProduct = this.stuffList.filter((product) => this.boomPoint >= product.coinPrice);
    const inSufficientCoinProduct = this.stuffList.filter((product) => this.boomPoint < product.coinPrice);
    this.stuffList = [...sufficientCoinProduct, ...inSufficientCoinProduct];
    }
  }

  requestStuff(product: any) {
    if (this.boomPoint < product?.point) {
      this.coinWarningModal = true;
      return;
    }
    this.selectedProducts = product;

    this.setStuffForm();
    this.showStuffRequestForm = true;
  }

  setHeader() {
    const headerInfo = this.getHeaderInfo();
    this.translateService.get(headerInfo.title).subscribe((trns) => {
      this.store.dispatch(
        new HeaderStatusAction({
          company: false,
          backButton: true,
          closeButton: false,
          hamburgerMenu: false,
          notificationIcon: false,
          title: trns,
          img: { src: headerInfo.imageSrc, alt: headerInfo.altText, altColor: headerInfo.altColor},
        })
      );
    });
  }

  getHeaderInfo() {
    let imageSrc = "";
    let altText = "";
    let title = " ";
    let altColor = null;

    if (this.pssrAccount?.isPssrUser && this.boomGoldSystemFeature) {
      imageSrc = "assets/kulcealtin.svg";
      altText = "Boom Gold";
      altColor = "gold";
    } else if (this.pssrAccount?.isPssrManagerUser && this.boomDiamondSystemFeature) {
      imageSrc = "assets/header-diamond-icon.svg";
      altText = "Boom Diamond";
      altColor = "blue";
    } else {
      title = "_boom_coin";
    }

    return { imageSrc, altText, title, altColor };
  }

  private setStuffForm() {
    const user = this.store.selectSnapshot(LoginState.user);
    const userData = this.store.selectSnapshot(UserState.basics);

    const firstName = userData?.firstName || user?.firstName;
    const lastName = userData?.lastName || user?.lastName;
    const patchValues = {
      StuffName: this.selectedProducts.title,
      StuffId: this.selectedProducts.code,
      StuffCoinPrice: this.selectedProducts.point,
      Name: this.form.value.Name? this.form.value.Name: firstName,
      Surname: this.form.value.Surname? this.form.value.Surname: lastName,
      StuffQuantity: 1
    };
    this.form.patchValue(patchValues);
    this.form.controls.StuffQuantity.setValidators([Validators.max(this.boomPoint / this.selectedProducts.point)]);
  }

  sendStuffRequestForm() {
    if (this.form.valid) {
      console.log('submit', this.form.value);
      console.log('valid');
      this.sendStaffRequestForm();
    } else {
      validateAllFormFields(this.form);
    }
  }

  private sendStaffRequestForm() {
    const userData = this.store.selectSnapshot(UserState.basics);

    const { value } = this.form;

    if(this.currentCustomer?.countryCode === 'KZ'){
       this.addressType = this.translateService.instant(value.SalesOffice)
    }
    else{
      this.addressType = value.Address
    }

    const form = {
      CountryCode: this.currentCustomer?.countryCode,
      RecipientName: this.form.value.Name,
      RecipientSurname: this.form.value.Surname,
      RecipientPhoneNumber: userData.mobile,
      Description: 'code: ' + this.selectedProducts.code,
      // RequestDate: new Date().toISOString(),
      RecipientAddress: this.addressType,
      CoinAmount: this.selectedProducts.point,
      RequesterEmail: userData.email,
      RequesterCustomerName: this.currentCustomer?.name,
      RecipientEmail: userData.email,
      ProductName: this.selectedProducts.title,
      CompanyId: this.currentCustomer?.publicMenuHeaderCompany

    };
    this.loading = true;
    this.log.action('PROMOTION', 'SEND_REQUEST_FORM', form).subscribe();

    this.promotionService.sendCoinPromotionRequest(form)
      .subscribe((val) => {
          if (val.code === 0) {
            this.stuffRequestFormSended = true;
            this.showStuffRequestForm = false;
          }
          this.loading = false;
          this.form.reset();
        },
        () => {
          this.loading = false;
        });
  }

  setQuantity(process) {
    const quantity: number = this.form.get('StuffQuantity')?.value;
    if (process === '-' && quantity === 1) {
      return;
    }
    if (process === '+' &&
      (this.boomPoint - (quantity * this.selectedStuff.coinPrice)) < this.selectedStuff.coinPrice) {
      return;
    }
    this.form.get('StuffQuantity').setValue(
      process === '-' ? quantity - 1 : quantity + 1
    );
  }
  requestFormReset() {
    this.form.reset();
  }

  openImagePopup(product: any){
    if(this.selectedImg = product) {
      this.imageModal = true;
    }
  }


  openDashboard() {
    this.store.dispatch(new Navigate(['dashboard']));
  }

  pssrAccountControl() {
    this.store.selectSnapshot(PromotionState.getPssrAccount)
  }

  boomCoinPromotionProducts(){
    this.store.dispatch(new BoomCoinPromotionProductsAction(this.currentCustomer?.countryCode, 100, 0));

    this.promotionProducts$
        .pipe(takeUntil(this.subscriptions$))
        .subscribe((data) => {
          if (data) {
            this.promotionProducts = data;
          }
        });
  }


}
