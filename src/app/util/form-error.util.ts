import { AbstractControl, FormGroup } from '@angular/forms';

export const defaultFormErrorMessages = {
  required: '_required',
  email: '_email_pattern',
  notMatched: '_not_matched',
  minlength: '_min_length',
  maxlength: '_max_length',
};

export const getFormErrorMessage = (control: AbstractControl, messages: any = defaultFormErrorMessages) => {
  for (const messagesKey in messages) {
    if (messages.hasOwnProperty(messagesKey) && control.hasError(messagesKey)) {
      return messages[messagesKey];
    }
  }
  return '';
};

export const isShowFormError = (control: AbstractControl) => {
  return control && control.invalid && (
    control.dirty || control.touched
  );
};
export const isShowFormErrorTouched = (control: AbstractControl) => {
  return control && control.invalid && (
    control.dirty && control.touched
  );
};

export const validateAllFormFields = (formGroup: FormGroup) => {
  Object.keys(formGroup.controls).forEach(field => {
    const control = formGroup.get(field);
    control.markAsTouched({ onlySelf: true });
  });
};
