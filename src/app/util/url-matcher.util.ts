import { UrlMatchResult, UrlSegment } from '@angular/router';

export function matchPathsStartingWith(startingWith: string[]) {
  return (url: UrlSegment[]): UrlMatchResult => {
    if (url.length >= startingWith.length) {
      for (let i = 0; i < startingWith.length; i++) {
        if (url[i].path !== startingWith[i]) {
          return null;
        }
      }
      return { consumed: url };
    }
    return null;
  };
}
