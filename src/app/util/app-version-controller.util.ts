import { environment } from 'src/environments/environment';
import { getOS } from './os.util';

export function versionToNumeric(version: string) {
  return version?.split('.').reduce((acc, value, key) => acc + (Number(value) * (10 ** (8 - (key * 4)))), 0);
}

export function appVersionController(version, event: string) {
  const envName = environment?.envName !== 'production' ? 'other' : environment?.envName;

  if (getOS() === 'Android') {
    return versionToNumeric(version) > versionToNumeric(androidVersion[envName][event]);
  } else if (getOS() === 'IOS') {
    return versionToNumeric(version) > versionToNumeric(iosVersion[envName][event]);
  }

  return false;
}

export function appForceUpdateController(version: string, minAppVersion: string) {
  return versionToNumeric(version) < versionToNumeric(minAppVersion);
}

const androidVersion = {
  production: {
    downloadFile: '5.0.9',
    // shareContent: '4.9.3',
    openCatApp: '5.0.9',
    offlineAndQr: '9.1.7'
  },
  other: {
    downloadFile: '5.0.2',
    // shareContent: '4.9.3',
    openCatApp: '5.1.3',
    offlineAndQr: '9.1.2'
  }
};

const iosVersion = {
  production: {
    downloadFile: '6.0.2',
    // shareContent: '4.9.3',
    openCatApp: '6.0.2',
    offlineAndQr: '10.5.9'
  },
  other: {
    downloadFile: '5.8.9',
    // shareContent: '4.9.3',
    openCatApp: '6.0.1',
    offlineAndQr: '10.4.9'
  }
};
