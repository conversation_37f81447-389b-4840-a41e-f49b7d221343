export const browserLanguage = () => {
  const wn :any= window.navigator;
  let lang = wn.languages ? wn.languages[0] : null;
  lang = lang || wn.language || wn.browserLanguage || wn.userLanguage;

  let shortLang = lang;
  if (shortLang.indexOf('-') !== -1) {
    shortLang = shortLang.split('-')[0];
  }

  if (shortLang.indexOf('_') !== -1) {
    shortLang = shortLang.split('_')[0];
  }

  return shortLang;

};
