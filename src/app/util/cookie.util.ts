export const getCookie = (name: string) => {
  const ca: Array<string> = document.cookie.split(';');
  const caLen: number = ca.length;
  const cookieName = `${name}=`;
  let c: string;

  for (let i = 0; i < caLen; i += 1) {
    c = ca[i].replace(/^\s+/g, '');
    if (c.indexOf(cookieName) == 0) {
      return c.substring(cookieName.length, c.length);
    }
  }
  return undefined;
};

export const deleteCookie = (name) => {
  setCookie(name, '', { expireDays: -1 });
};

export interface CookieOptions {
  path?: string;
  expireDays?: number;
  domain?: string;
  expires?: string;
}

export const setCookie = (name: string, value: string, options?: CookieOptions) => {
  let cookieText = '';
  let pathText;
  let domainText;
  let expires = options?.expires;
  if (options) {
    if (options.expireDays) {
      const date: Date = new Date();
      date.setTime(date.getTime() + options.expireDays * 24 * 60 * 60 * 1000);
      expires = `expires=${date.toUTCString()};`;
    }
    if (options.path) {
      pathText = `path=${options.path};`;
    }
    if (options.domain) {
      domainText = `domain=${options.domain};`;
    }
  }
  cookieText += `${name}=${value};`;

  if (expires) {
    cookieText += expires;
  }
  if (domainText) {
    cookieText += domainText;
  }
  if (pathText) {
    cookieText += pathText;
  }

  document.cookie = cookieText;
};

export enum CookieTypes {
  SSOTARGETPROJECT = 'SSOTargetProject',
}
