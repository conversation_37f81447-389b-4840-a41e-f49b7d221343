export const handleBackButtonUrl = (url: string) => {
  if (url.includes('catalog')) {
    const params = urlParts(url);
    params.splice(params.length - 1, 1);
    const last = params[params.length - 1];

    if (isNaN(last)) {
      params.splice(params.length - 1, 1);
    }

    if (params.length > 0) {
      return `/catalog/${params.join('/')}`;
    }

    return '/';
  }
  return null;
};

export const urlParts = (url) => {
  return url.replace('/catalog/', '').split('/');
};
