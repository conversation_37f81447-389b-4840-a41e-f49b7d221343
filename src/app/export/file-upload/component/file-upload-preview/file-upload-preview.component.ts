import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FileModel } from '../../model/file.model';
import { FileUploadService } from '../../service/file-upload.service';

@Component({
  selector: 'app-file-upload-preview',
  templateUrl: './file-upload-preview.component.html',
  styleUrls: ['./file-upload-preview.component.scss'],
})
export class FileUploadPreviewComponent implements OnInit {
  @Input()
  files: FileModel[] = [];

  @Input()
  deleteButtonStatus: boolean;

  @Output()
  deleteFile: EventEmitter<FileModel> = new EventEmitter<FileModel>();
  loading = false;

  constructor(protected api: FileUploadService) {}

  ngOnInit(): void {}

  humanFileSize(bytes, si = false, dp = 1) {
    const thresh = si ? 1000 : 1024;

    if (Math.abs(bytes) < thresh) {
      return bytes + ' B';
    }

    const units = si
      ? ['kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      : ['KiB', 'MiB', 'GiB', 'TiB', 'PiB', 'EiB', 'ZiB', 'YiB'];
    let u = -1;
    const r = 10 ** dp;

    do {
      bytes /= thresh;
      ++u;
    } while (
      Math.round(Math.abs(bytes) * r) / r >= thresh &&
      u < units.length - 1
      );

    return bytes.toFixed(dp) + ' ' + units[u];
  }

  onClickDeleteFile(file: FileModel) {
    this.loading = true;
    this.fileDeleteService(file.id).subscribe(
      (res) => {
        this.loading = false;
        if (res) {
          this.deleteFile.emit(file);
        }
      },
      () => {
        this.loading = false;
      }
    );
  }

  fileDeleteService(id) {
    return this.api.delete(id);
  }
}
