<div class="d-flex flex-wrap">
  <div
    *ngFor="let file of files"
    class="d-flex align-items-center border border-warning rounded-sm mr-2 overflow-hidden mb-2">
    <div
      *ngIf="deleteButtonStatus"
      class="bg-warning h-100 d-flex align-items-center px-2 cursor-pointer" (click)="onClickDeleteFile(file)">
      <i class="icon icon-x-bold font-size-12px"></i>
    </div>
    <div class="font-size-10px py-1 px-2">
      {{file.Name}}.{{file.FileType}} <br>
      <span *ngIf="deleteButtonStatus" class="text-muted">{{humanFileSize(file?.Size)}}</span>
    </div>
  </div>
</div>
<app-loader [show]="loading"></app-loader>
