import { ChangeDetectorRef, Component, ElementRef, forwardRef, Input, OnDestroy, OnInit, ViewChild, } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { TranslatePipe, TranslateService } from '@ngx-translate/core';
import { FileModel, FileSettingsModel, FormType } from '../../model/file.model';
import { FileUploadService } from '../../service/file-upload.service';
import { Observable, Subject } from 'rxjs';
import { Select, Store } from '@ngxs/store';
import { takeUntil } from 'rxjs/operators';
import { ModalService } from 'src/app/shared/service/modal.service';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { PermissionEnum } from 'src/app/modules/definition/enum/permission.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { IncomingMessageService } from 'src/app/core/service/incoming-message.service';
import { IncomingMessageEnum } from 'src/app/core/enum/incoming-message.enum';
import { CommonState } from 'src/app/shared/state/common/common.state';
import { ShowPermissionErrorAction } from 'src/app/shared/state/common/common.actions';

@Component({
  selector: 'app-file-upload',
  templateUrl: './file-upload.component.html',
  styleUrls: ['./file-upload.component.scss'],
  providers: [
    TranslatePipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => FileUploadComponent),
      multi: true,
    },
  ],
})
export class FileUploadComponent implements OnInit, OnDestroy {
  @Input()
  files: FileModel[];

  @Input()
  id: string;

  @Input()
  formType: FormType;

  @Input()
  name: string;

  @Input()
  disabledItem: boolean;

  @ViewChild('fileInput')
  fileInput: ElementRef;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;


  loading: boolean;
  loadingCount = 0;
  settings: FileSettingsModel;
  accept = '';
  val: FileModel[] = [];
  private permissionOkSub: any;
  errorModal: any;
  private subscriptions$: Subject<boolean> = new Subject();

  constructor(
    protected readonly fileUploadService: FileUploadService,
    protected readonly translatePipe: TranslatePipe,
    protected readonly modalService: ModalService,
    protected readonly frameMessageService: FrameMessageService,
    protected readonly incomingMessageService: IncomingMessageService,
    protected readonly translateService: TranslateService,
    protected readonly cdr: ChangeDetectorRef,
    protected readonly store: Store,
  ) {
    this.getSettings();
  }

  protected getSettings() {
    this.fileUploadService.settings().subscribe((res: any) => {
      if (res.code === 0) {
        this.settings = res.data;
        // this.accept = this.settings.availableMimeTypes.join(', ');
      }
    });
  }

  get value() {
    return this.val;
  }

  set value(val) {
    this.val = val;
    this.onChange(val);
    this.onTouch(val);
  }

  ngOnInit() {
    this.store.dispatch(new ShowPermissionErrorAction(null));
    this.listenPermissionError();

    // this.historyBack$
    //   .pipe(tap(i => console.log('history111', i)))
    //   .pipe(takeUntil(this.subscriptions$), skip(1), filter(i => i))
    //   .subscribe(h => {
    //     console.log('hsss', h);
    //     this.errorModal?.close('t');
    //   });

  }


  onChange(val): any {
    console.log('onChange', val);
  }

  onTouch(val): any {
    console.log('onTouch', val);
  }

  writeValue(value: any) {
    this.value = value;
  }

  registerOnChange(fn: any) {
    this.onChange = fn;
  }

  registerOnTouched(fn: any) {
    this.onTouch = fn;
  }

  onUpload(data: any, fileName: string, size: number) {


    if (data) {
      console.log('data:::::', data);

      const file = {
        AssignedId: this.id,
        Size: size,
        Name: fileName.substr(0, fileName.lastIndexOf('.')),
        FileType: fileName.split('.').pop() || '',
        FormType: this.formType,
        ImageContent: data.match(/,([\w\W]+)/g)[0].replace(',', ''),
      } as FileModel;
      console.log('file upload', file);
      this.loadingCount++;
      this.uploadService(file).subscribe(
        (res) => {
          // setTimeout(() => this.loading = false, 30000);
          this.loading = false;
          this.loadingCount--;
          if (!res.data.isSuccessful) {
            this.modalService.errorModal({
              message: res.data.errorMessage,
              translate: false,
            });
            return;
          }

          this.fileInput.nativeElement.value = '';

          if (res.code === 0) {
            file.id = res.data.attachmentId;
          }

          this.value = [...this.value, file];
        },
        () => {
          // this.showErrorModal = true;
          // this.errorMessage = this.translatePipe.transform(
          //   '_file_upload_error'
          // );
          this.loading = false;
          this.loadingCount--;
        }
      );
    }
  }

  uploadService(file) {
    return this.fileUploadService.upload(file);
  }


  fileChangeListener($event: Event) {
    const element = $event.target as HTMLInputElement;
    const fileList: FileList | null = element.files;
    console.log('$event', $event);



    if (this.isFileValid(fileList)) {
      for (let i = 0; i < fileList.length; i++) {
        console.log('fileList', fileList[i]);

        const file = fileList[i];
        const path: string = file.name;

        const that = this;
        const image: any = new Image();
        const myReader: FileReader = new FileReader();

        myReader.onloadend = (loadEvent: ProgressEvent<FileReader>) => {
          image.src = loadEvent.target.result;
          that.onUpload(image.src, path, file.size);
        };
        this.loading = true;
        myReader.readAsDataURL(file);
      }
    } else {
      element.value = '';
    }
  }

  isFileValid(fileList: FileList) {
    let isValid = true;

    console.log('File Size', fileList.length, this.val);
    const totalCount = fileList.length + this.val.length;

    if (totalCount > this.settings.maxAttachmentCount) {
      this.modalService.errorModal({
        message: this.translatePipe
          .transform('_max_attachment_count_error')
          .replace('{0}', this.settings.maxAttachmentCount),
      });
      return false;
    }

    for (let i = 0; i < fileList.length; i++) {
      const file = fileList[i];
      const path: string = file.name;
      const names = path.split('.');
      const extension = `${names[names.length - 1]}`;

      if (!this.settings.availableFileTypes.some(t => t.toLowerCase() === extension.toLowerCase())) {
        this.modalService.errorModal({
          message: this.translatePipe
            .transform('_file_type_not_valid')
            .replace('{0}', this.settings.availableFileTypes.join(', ')),
        });
        isValid = false;
        break;
      }

      const size = file.size;
      if (this.settings && size > this.settings.maxSizeInBytes) {
        const maxSize = (this.settings.maxSizeInBytes / 1024).toFixed(0);
        this.modalService.errorModal({
          message: this.translatePipe
            .transform('_file_is_too_big')
            .replace('{0}', maxSize),
        });
        isValid = false;
        break;
      }

      if (this.settings && size < this.settings.minSizeInBytes) {
        const minSize = (this.settings.minSizeInBytes / 1024).toFixed(0);
        this.modalService.errorModal({
          message: this.translatePipe
            .transform('_file_is_too_small')
            .replace('{0}', minSize),
        });
        isValid = false;
        break;
      }
    }
    return isValid;
  }

  permissionRequest() {
    this.frameMessageService.sendMessage(FrameMessageEnum.permissions, [
      PermissionEnum.FileUpload,
    ]);
  }

  checkPermission() {
    this.frameMessageService.sendMessage(FrameMessageEnum.checkPermissions, [
      PermissionEnum.FileUpload,
    ]);
  }

  listenPermissionOK(): void {
    if (this.permissionOkSub) {
      return;
    }
    this.permissionOkSub = this.incomingMessageService
      .subscribe(IncomingMessageEnum.permissionOk, data => {
        // console.log('received data', data);
        this.fileInput.nativeElement.click();
        this.permissionOkSub?.unsubscribe();
        this.permissionOkSub = null;
      });
  }

  goToSettings(action) {
    this.frameMessageService.sendMessage(action);
  }

  listenPermissionError() {
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        // this.listenPermissionErrorSub = this.incomingMessageService
        //   .subscribe(IncomingMessageEnum.showPermissionError,
        data => {
          if (!data) {
            return;
          }
          // console.log('received data', data);
          this.permissionRequest();

          // this.errorModal = {
          //   message: data.message,
          //   button: this.translateService.instant('_go_to_settings'),
          //   buttonClick: () => this.goToSettings(data.action)
          // };

          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.goToSettings(data.action)
          });
        });
  }

  chooseFile() {
    console.log('choose', (this as any).question);
    // this.permissionRequest();
    // this.listenPermissionOK();
    this.fileInput.nativeElement.click();
    this.checkPermission();

    if (window.location.hostname === 'localhost') {
      (window as any).handlePostMessage('{"type":"permissionOk","data":null}');
      // (window as any).handlePostMessage('{"type":"showPermissionError","data":{"message" : "no permission"}}');
    }
  }

  ngOnDestroy() {
    this.permissionOkSub?.unsubscribe();
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
    this.errorModal?.close();
  }

}
