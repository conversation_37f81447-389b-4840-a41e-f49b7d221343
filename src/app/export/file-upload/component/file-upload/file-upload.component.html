<div class="position-relative">
  <div
    (click)="chooseFile()"
    class="btn btn-info btn-sm font-weight-semi-bold btn-block py-1 px-4 rounded-lg"
  >
    {{ "_choose_file" | translate }}
  </div>
  <input
    class="file-upload-input"
    #fileInput
    (change)="fileChangeListener($event)"
    type="file"
    multiple
    [accept]="accept"
  />
</div>
<app-loader [show]="loading || loadingCount > 0"></app-loader>

<!--<app-error-modal-->
<!--  *ngIf="errorModal"-->
<!--  [status]="errorModal"-->
<!--  [message]="errorModal?.message"-->
<!--  [button]="errorModal?.button"-->
<!--  [buttonClick]="errorModal?.buttonClick"-->
<!--  [backdropClose]="false"-->
<!--&gt;-->

<!--</app-error-modal>-->

<!-- <cat-basic-modal *ngIf="showErrorModal" [(status)]="showErrorModal">
  <div class="after-form-send">
    <div class="after-form-send-content text-center px-4">
      <img [src]="warningIcon" />
      <div
        class="h3 my-4 text-center"
        [ngClass]="{ 'service-error': !!errorMessage }"
      >
        {{ errorMessage || ("_some_error_form" | translate) }}
      </div>
    </div>
  </div>
</cat-basic-modal> -->
