import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgxLoadingModule } from 'ngx-loading';
import { FileUploadPreviewComponent } from './component/file-upload-preview/file-upload-preview.component';
import { FileUploadComponent } from './component/file-upload/file-upload.component';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'src/app/shared/shared.module';

@NgModule({
  declarations: [
    FileUploadComponent,
    FileUploadPreviewComponent,
  ],
  exports: [
    FileUploadComponent,
    FileUploadPreviewComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule,
    NgxLoadingModule,
    SharedModule
  ],
})
export class FileUploadModule {
}
