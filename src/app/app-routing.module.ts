import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { OttResolver } from './modules/ott/resolver/ott.resolver';
import { OttModule } from './modules/ott/ott.module';
import { AuthGuard } from './core/guards/auth.guard';
import { environment } from '../environments/environment';
import { LogoutComponent } from './container/logout/logout.component';
import { PermissionGuard } from './core/guards/permission.guard';
import { NonBoomComponent } from './container/non-boom/non-boom.component';
import { stopLoadingAnimation } from './util/stop-loading-animation.util';
import { EquipmentOrderContainerComponent } from './modules/catalog/equipment-order-container/equipment-order-container.component';
import { LeasingCalculationComponent } from './modules/customer/component/leasing-calculation/leasing-calculation.component';

const routes: Routes = [
  {
    path: environment.rootUrl,
    canActivate: [AuthGuard, PermissionGuard],
    resolve: [OttResolver],
    children: [
      {
        path: '',
        loadChildren: () =>
          import('./modules/customer/customer.module').then(
            (m) => m.CustomerModule
          ),
      },
    ],
  },
  {
    path: environment.rootUrl,
    canActivate: [AuthGuard, PermissionGuard],
    resolve: [OttResolver],
    children: [
      {
        path: 'borusan',
        loadChildren: () =>
        import('./modules/borusan-user/borusan-user.module').then(
          (m) => m.BorusanUserModule
        ),
      },
    ],
  },
  {
    path: environment.rootUrl,
    resolve: [],
    children: [
      {
        path: 'lite',
        loadChildren: () => {
          stopLoadingAnimation(false);
          return import('./modules/lite-user/lite.module').then(
            (m) => m.LiteModule
          );
        },
      },
    ],
  },
  {
    path: environment.rootUrl,
    children: [
      { path: 'logout', component: LogoutComponent },
    ],
  },
  {
    path: environment.rootUrl,
    children: [
      { path: 'non-boom', component: NonBoomComponent },
    ],
  },
  {
    path: environment.rootUrl,
    children: [
      { path: 'equipment-order/:id', component: EquipmentOrderContainerComponent },
    ],
  },
  {
    path: environment.rootUrl,
    children: [
      { path: 'leasing', component: LeasingCalculationComponent, },
    ],
  },

  {
    path: environment.rootUrl,
    children: [
      {
        path: 'auth',
        loadChildren: () => import('./modules/authentication/authentication.module').then((m) => m.AuthenticationModule)
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes), OttModule],
  exports: [RouterModule],
})
export class AppRoutingModule {}
