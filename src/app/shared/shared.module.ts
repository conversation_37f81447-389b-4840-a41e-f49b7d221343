import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { BasicModalComponent } from './component/basic-modal/basic-modal.component';
import { LoaderComponent } from './component/loader/loader.component';
import { AgreementModalComponent } from './component/agreement-modal/agreement-modal.component';
import { ngxLoadingAnimationTypes, NgxLoadingModule } from 'ngx-loading';
import { BigModalComponent } from './component/big-modal/big-modal.component';
import { SuccessModalComponent } from './component/success-modal/success-modal.component';
import { AgreementListComponent } from './component/agreement-list/agreement-list.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ClickLogDirective } from './directive/click-log.directive';
import { ErrorModalComponent } from './component/error-modal/error-modal.component';
import { EmptyContentComponent } from './component/empty-content/empty-content.component';
import { ModalComponent } from './component/modal/modal.component';
import { UpdateModalComponent } from './component/update-modal/update-modal.component';
import { SafeHtmlPipe } from './pipe/safe-html.pipe';
import { SearchPipe } from './pipe/search.pipe';
import { WarningBoxComponent } from './component/warning-box/warning-box.component';
import { SerialFormatPipe } from './pipe/serial-format.pipe';
import { InfoBoxComponent } from './component/info-box/info-box.component';
import { ErrorBoxComponent } from './component/error-box/error-box.component';
import { TabComponent } from './component/tabs/tab.component';
import { TabsComponent } from './component/tabs/tabs.component';
import { SortPipe } from './pipe/sort.pipe';
import { InputMaxLengthDirective } from './directive/input-max-length.directive';
import { DownloadFileComponent } from './component/download/download-file.component';
import { DisableDocumentScrollDirective } from './directive/document-search.directive';
import { FormScrollDirective } from './directive/form-scroll.directive';
import { CurrencyIconPipe } from './pipe/curreny-icon.pipe';
import { ImagePreviewComponent } from './component/image-preview/image-preview.component';
import { MediaSecurePipe } from './pipe/media-secure.pipe';
import { DeepLinkDirective } from './directive/deep-link.directive';
import { DeepLinkIncludeDirective } from './directive/deep-link-include.directive';
import { BoomClubActivitiesCardComponent } from './component/boom-club-activities-card/boom-club-activities-card.component';
import { BoomClubUserBadgeComponent } from './component/boom-club-user-badge/boom-club-user-badge.component';
import { DotLoaderComponent } from './component/dot-loader/dot-loader.component';
import { SkeletonLoaderComponent } from './component/skeleton-loader/skeleton-loader.component';
import { AgreementApprovalBaseComponent } from '../modules/customer/component/agreement-approval-base/agreement-approval-base.component';
import { NotificationApprovalAgreementComponent } from '../modules/customer/component/notification-approval-agreement/notification-approval-agreement.component';

@NgModule({
  declarations: [
    BasicModalComponent,
    LoaderComponent,
    AgreementModalComponent,
    BigModalComponent,
    SuccessModalComponent,
    AgreementListComponent,
    ClickLogDirective,
    ErrorModalComponent,
    EmptyContentComponent,
    ModalComponent,
    UpdateModalComponent,
    SafeHtmlPipe,
    SearchPipe,
    WarningBoxComponent,
    InfoBoxComponent,
    ErrorBoxComponent,
    SerialFormatPipe,
    TabComponent,
    TabsComponent,
    SortPipe,
    InputMaxLengthDirective,
    DownloadFileComponent,
    DisableDocumentScrollDirective,
    FormScrollDirective,
    CurrencyIconPipe,
    ImagePreviewComponent,
    MediaSecurePipe,
    DeepLinkDirective,
    DeepLinkIncludeDirective,
    BoomClubActivitiesCardComponent,
    BoomClubUserBadgeComponent,
    DotLoaderComponent,
    SkeletonLoaderComponent,
    AgreementApprovalBaseComponent,
    NotificationApprovalAgreementComponent
  ],
  exports: [
    BasicModalComponent,
    LoaderComponent,
    AgreementModalComponent,
    BigModalComponent,
    SuccessModalComponent,
    EmptyContentComponent,
    AgreementListComponent,
    ClickLogDirective,
    ErrorModalComponent,
    UpdateModalComponent,
    SafeHtmlPipe,
    SearchPipe,
    WarningBoxComponent,
    InfoBoxComponent,
    ErrorBoxComponent,
    SerialFormatPipe,
    TabComponent,
    TabsComponent,
    InputMaxLengthDirective,
    DownloadFileComponent,
    SortPipe,
    DisableDocumentScrollDirective,
    FormScrollDirective,
    CurrencyIconPipe,
    ImagePreviewComponent,
    DeepLinkDirective,
    DeepLinkIncludeDirective,
    BoomClubActivitiesCardComponent,
    BoomClubUserBadgeComponent,
    DotLoaderComponent,
    SkeletonLoaderComponent,
    AgreementApprovalBaseComponent,
    NotificationApprovalAgreementComponent
  ],
  imports: [
    CommonModule,
    TranslateModule,
    NgxLoadingModule.forRoot({
      animationType: ngxLoadingAnimationTypes.circle,
      backdropBorderRadius: '2px',
      fullScreenBackdrop: true,
      primaryColour: '#7b7b7b',
    }),
    ReactiveFormsModule,
    FormsModule,
  ],
})
export class SharedModule {}
