import {Component, EventEmitter, Input, Output } from '@angular/core';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-success-modal',
  templateUrl: './success-modal.component.html',
  styleUrls: ['./success-modal.component.scss'],
})
export class SuccessModalComponent {
  @Input()
  message = '_success_message';

  @Input() detail: string;

  @Input() inModal = false;

  @Input() status: boolean;

  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();
  successIcon = `${environment.assets}/success.svg`;
  constructor() {}
}
