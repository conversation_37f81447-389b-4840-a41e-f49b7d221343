<ng-container *ngIf="inModal else inPage">
  <app-basic-modal [(status)]="status" (statusChange)="status = false; statusChange.emit(status)">
    <div class="d-flex flex-column align-items-center justify-content-center my-3">
      <i class="icon icon-message-success d-inline-block mb-3"></i>
      <div class="success-message">
        <p class="modal-body-message text-center">
          {{ message | translate }}
        </p>
      </div>
    </div>
  </app-basic-modal>
</ng-container>
<ng-template #inPage>
  <div class="content-area d-flex">
    <div class="content-area-body d-flex">
      <img alt="Success" [src]="successIcon"/>
      <p class="content-area-body-message text-center">
        {{ message | translate }}
      </p>
      <p *ngIf="detail" class="content-area-body-detail text-center">
        {{ detail | translate }}
      </p>
      <ng-content></ng-content>
    </div>
  </div>
</ng-template>
