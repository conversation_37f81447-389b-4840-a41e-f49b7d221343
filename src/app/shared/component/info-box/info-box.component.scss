
.box {
  font-size: 11px;
  border: 1px solid #bee5eb;
  border-radius: 6px;
  margin-top: 1.4em;

  .title {
    position: relative;
    width: 0;
    height: 0;

    .text {
      position: absolute;
      //width: 130px;
      top: -18px;
      border-radius: 14px;
      //padding: 0 8px;
      border: 1px solid #bee5eb;
      background: #fff;
      color: #0c5460;

      white-space: nowrap;
      padding-right: 27px;
      padding-left: 8px;
    }
    .title-icon {
      line-height: 1em;
    }
    .icon-cls{
      font-size: 11px;
      line-height: 16px;
    }
  }

  .content {
  }

}


