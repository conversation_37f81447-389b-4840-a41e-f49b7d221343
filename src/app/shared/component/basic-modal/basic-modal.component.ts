import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Store } from '@ngxs/store';
import { animate, state, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-basic-modal',
  templateUrl: './basic-modal.component.html',
  styleUrls: ['./basic-modal.component.scss'],
  animations: [
    trigger('modal', [
      state('void', style({ top: '25%', opacity: 0 })),
      state('*', style({ top: '20%', opacity: 1 })),
      transition('* => *', [animate('.05s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: .1 })),
      transition('* => *', [animate('.05s')]),
    ]),
  ],
})
export class BasicModalComponent implements OnInit {

  @Output()
  statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Input()
  status: boolean;

  @Input()
  headerText: string;

  @Input()
  backdropClose = false;

  @Input()
  closeable = true;

  constructor(
    private readonly store: Store,
  ) {}

  ngOnInit(): void {
    // this.customer = this.store.selectSnapshot(LoginState.customer);
    // this.store.dispatch(new GetEquipmentListAction(this.customer.customerNumber));
    //
    // console.log('#### customer ', this.customer);
    // if (this.customer) {
    //   this.store.dispatch(new GetServiceListAction(snq(() => this.customer.customerNumber)));
    // }
  }

  onCloseModal() {
    this.status = false;
    this.statusChange.emit(this.status);
  }

  onClickBackdrop() {
    if (this.backdropClose) {
      this.onCloseModal();
    }
  }

}
