.modal {
  left: 50%;
  top: 20%;
  transform: translate(-50%);
  width: 90vw;
  max-width: 316px;
  height: auto;
  overflow: visible;

  &-content {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    border: none;
    max-height: 70vh;
    overflow-y: scroll;
  }

  &-header {
    padding: 1rem 1rem 0 0;
    font-size: 20px;
    font-weight: 600;
    border: 0;
    min-height: 55px;
  }

  &-title {
    font-size: 18px;
    font-weight: 800;
    text-align: center;
    min-height: 55px;
  }

  &-body {
    padding: 0.2rem 1rem;
    overflow-x: hidden;
  }

  &-backdrop {
    opacity: 0.5;
    background-color: #727272;
  }

  &-btn {
    border-radius: 6px;
  }

  .icon-x {
    position: absolute;
    right: 20px;
    top: 20px;
    font-size: 19px;
    color: #2c2c2c;
  }
}

@media screen and (min-width: 750px) {
  .modal {
    max-width: 450px;
  }
}
