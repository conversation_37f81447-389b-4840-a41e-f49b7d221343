import { AfterContentChecked, Component, ElementRef, Input, OnInit, ViewChild, } from '@angular/core';
import { Store } from '@ngxs/store';
import { FrameMessageEnum } from '../../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../../core/service/frame-message.service';
import { AgreementLink } from '../../../modules/definition/model/agreement.model';
import { DefinitionService } from '../../../modules/definition/service/definition.service';

@Component({
  selector: 'app-agreement-modal',
  templateUrl: './agreement-modal.component.html',
  styleUrls: ['./agreement-modal.component.scss'],
})
export class AgreementModalComponent implements OnInit, AfterContentChecked {
  @ViewChild('target')
  target: ElementRef<HTMLDivElement>;

  @Input()
  content: AgreementLink;

  modalContent: any;
  loading = false;
  text: string;

  constructor(
    private readonly definitionService: DefinitionService,
    private readonly frameMessageService: FrameMessageService,
  ) { }

  ngAfterContentChecked() {
    const elements: any = this.target?.nativeElement?.querySelectorAll('a');
    // console.log('element found', elements);
    if (elements?.length > 0) {
      elements.forEach(el => {
        el.onclick = () => {
          this.openFrame(el?.href, el?.innerText);
          return false;
        };
      });
    }
  }

  ngOnInit(): void {
    this.loading = true;

    this.definitionService.getAgreementContent(this.content.url).subscribe(response => {
      this.text = response;
      this.loading = false;
    }, () => {
      this.loading = false;
    });

  }

  openFrame(url, title) {
    // this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
    //   url,
    //   title: title || 'Aydınlatma Metni',
    // });
    if (url.slice(0, 4) === 'kvk:') {

      this.definitionService.agreementDetails(url.slice(4))
        .subscribe(data => {
          if (!data.length) {
            return;
          }
          this.modalContent = {
            text: '',
            linkText: '',
            url: data[0]?.url,
          };
        });
    } else {
      this.frameMessageService.sendMessage(FrameMessageEnum.openModule, {
        url,
        title
      });
    }
  }
}
