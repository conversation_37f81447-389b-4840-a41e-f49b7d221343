import { Component, Input } from '@angular/core';
import { BasicModalComponent } from '../basic-modal/basic-modal.component';
import { animate, state, style, transition, trigger } from '@angular/animations';

@Component({
  selector: 'app-big-modal',
  templateUrl: 'big-modal.component.html',
  styleUrls: ['./big-modal.component.scss'],
  animations: [
    trigger('modal', [
      state('void', style({ top: '20%', opacity: 0 })),
      state('*', style({ opacity: 1 })),
      transition('* => *', [animate('.1s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: .4 })),
      transition('* => *', [animate('.1s')]),
    ]),
  ],
})
export class BigModalComponent extends BasicModalComponent {
  @Input()
  width = '95vw';

}
