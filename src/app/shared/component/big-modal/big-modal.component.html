<div (click)="onClickBackdrop()" *ngIf="status" [@backdrop] class="modal-backdrop"></div>
<div *ngIf="status" [@modal] class="modal big-modal d-block" role="dialog" tabindex="-1"
     [style.width]="width">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <div class="modal-title" *ngIf="headerText">
          {{headerText | translate}}
        </div>
        <i *ngIf="closeable" (click)="onCloseModal()" class="icon icon-x cursor-pointer"></i>
      </div>
      <div class="modal-body ">
        <ng-content></ng-content>
      </div>
    </div>
  </div>
</div>
