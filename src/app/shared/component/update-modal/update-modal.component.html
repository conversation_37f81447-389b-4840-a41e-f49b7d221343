<app-basic-modal
  *ngIf="true"
  [(status)]="updateModal"
>
  <div class="mb-2">
    <p class="update-text text-center"> {{'_updates_available' | translate}}</p>
  </div>
  <div class="d-flex justify-content-around mb-3">
    <button
      (click)="update()"
      class="btn btn-info py-1 text-white shadow"
    >
      {{ "_update" | translate }}
    </button>

  </div>
  <div class="mb-2">

    <a class="btn btn-link link" (click)="helpModal = true">
      {{'_trouble_while_updating' | translate}}
    </a>
  </div>
</app-basic-modal>

<app-basic-modal
  *ngIf="systemFeatureForceUpdate"
  [(status)]="forceUpdateNeeded"
  [backdropClose]="false"
  [closeable]="false"
>
  <div class="mb-2">
    <p class="update-text text-center"> {{'_force_update_needed' | translate}}</p>
  </div>
  <div class="d-flex justify-content-around mb-3">
    <button
      (click)="update()"
      class="btn btn-info py-1 text-white shadow"
    >
      {{ "_update" | translate }}
    </button>

  </div>
</app-basic-modal>


<app-basic-modal
  *ngIf="true"
  [(status)]="helpModal"

>
  <div style="min-height: 150px">
    <div *ngIf="!helpSent" class="mb-2">
      <div class="mb-4">
        <p class="update-text text-center">
          {{'_share_email_address' | translate}}
        </p>
      </div>
      <form (submit)="onSubmitForm()" [formGroup]="form">
        <div class="d-flex justify-content-around input-group-sm">
          <input formControlName="email" placeholder="" type="email" class="form-control" name="email" minlength="3" maxlength="200"/>
        </div>
        <div
          [ngClass]="{ 'd-block': isShowError(form.controls.email) }"
          class="invalid-feedback "
        >
          {{ getFormErrorMessage(form.controls.email) | translate }}
        </div>

        <div class="d-flex justify-content-center pb-3 pt-3">
          <input
            type="submit"
            class="btn btn-info py-1 text-white shadow send-button"
            [value]="'_send' | translate"
          />
        </div>
      </form>
    </div>

    <div *ngIf="helpSent" class="p-3">
      <p class="update-text text-center ">
        {{'_we_will_contact_soon' | translate}}
      </p>

    </div>
  </div>
</app-basic-modal>
