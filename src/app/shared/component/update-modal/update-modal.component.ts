import { Component, OnInit } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { CommonState } from '../../state/common/common.state';
import { CommonStoreAction } from '../../state/common/common.actions';
import { LoginState } from '../../../modules/authentication/state/login/login.state';
import { FrameMessageService } from '../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../core/enum/frame-message.enum';
import { ContactService } from '../../../modules/customer/service/contact.service';
import { UserState } from '../../../modules/customer/state/user/user.state';
import { environment } from '../../../../environments/environment';
import { Observable, Subject } from 'rxjs';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CustomValidator } from '../../../util/custom-validator';
import { getFormErrorMessage, isShowFormError, validateAllFormFields } from '../../../util/form-error.util';
import { SettingsState } from '../../state/settings/settings.state';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { appForceUpdateController } from 'src/app/util/app-version-controller.util';
import { TranslateService } from '@ngx-translate/core';
import { getOS } from 'src/app/util/os.util';
import { systemFeature } from 'src/app/util/system-feature.util';
import { ForceUpdateControllerAction } from '../../state/settings/settings.actions';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'app-update-modal',
  templateUrl: './update-modal.component.html',
  styleUrls: ['./update-modal.component.scss']
})
export class UpdateModalComponent implements OnInit {
  getFormErrorMessage = getFormErrorMessage;
  isShowError = isShowFormError;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  @Select(CommonState.version)
  version$: Observable<string>;

  @Select(SettingsState.minAppVersion)
  minAppVersion$: Observable<string>;
  minAppVersion: string;

  private version: string;

  constructor(
    private readonly store: Store,
    private readonly contactService: ContactService,
    private readonly frameMessageService: FrameMessageService,
    private readonly translateService: TranslateService,
  ) { }

  helpModal = false;
  helpSent = false;
  updateModal = false;
  forceUpdateNeeded = false;
  email: any;
  settingsIosVersion: string;
  settingsAndroidVersion: string;
  systemFeatureForceUpdate: boolean;
  iosString = 'iosMinAppVersion';
  androidString = 'androidMinAppVersion';
  private iosVersion = environment.envName === 'stage' ? '2.1.9' : '2.4.2';
  private androidVersion = environment.envName === 'stage' ? '2.0.5' : '2.3.8';
  protected subscriptions$: Subject<boolean> = new Subject();

  form: FormGroup = new FormGroup({
    email: new FormControl(null, [
      Validators.required,
      CustomValidator.mailFormat,
    ]),

  });

  ngOnInit(): void {
    let os = getOS() === 'IOS' ? this.iosString : this.androidString;
    this.minAppVersion$.pipe(takeUntil(this.subscriptions$))
    .subscribe(value => {
      if(value) {
        this.minAppVersion = value;
        this.forceUpdateNeeded = appForceUpdateController(this.store.selectSnapshot(CommonState.version), value);
      }
    })

    this.systemFeatures$
    .pipe(takeUntil(this.subscriptions$))
    .subscribe(features => {
      if (features?.length) {
        this.systemFeatureForceUpdate = systemFeature('force_update_needed', features, false);
        if(this.systemFeatureForceUpdate && !this.minAppVersion) {
          this.store.dispatch(new ForceUpdateControllerAction(os))
        }
      }
    });

    this.version$.subscribe(version => {
      this.version = version;
      const updateModalShowed = this.store.selectSnapshot(CommonState.updateModalShowed);

      console.log('version', this.version);
      this.updateModal = !updateModalShowed && [this.iosVersion, this.androidVersion].indexOf(this.version) !== -1;
      if (this.updateModal) {
        this.store.dispatch(new CommonStoreAction({
          updateModalShowed: true
        }));
      }
    });

  }

  onSubmitForm() {
    if (!this.form.valid) {
      return validateAllFormFields(this.form);
    }

    const user = this.store.selectSnapshot(LoginState.user);
    const current = this.store.selectSnapshot(UserState.currentCustomer);

    this.contactService
      .callrequest({
        PhoneNumber: user.mobile,
        Description: 'Uygulama Güncellemede sorun yaşıyorum formundan iletilmiştir. mail:' + this.email,
        Name: user?.firstName + ' ' + user?.lastName,
        Email: this.email.toLowerCase(),
        CountryCode: current?.groupKey,
        CompanyName: current?.name,
        CompanyId: current?.publicMenuHeaderCompany,
        // CompanyName: current?.customer?.name,
        // CompanyPhoneNumber: mobile,
      })
      .subscribe(() => {
        this.helpSent = true;
        this.updateModal = false;
      });

  }

  update() {
    let url;
    if(!this.forceUpdateNeeded) {
      if (environment.envName === 'stage') {
        url = this.version === this.iosVersion ?
          'https://dist.appcircle.io?email=<EMAIL>&profileId=71cb16c9-48fe-4ff3-8721-28ac6befefd7&AppVersionId=a5a6909d-3d11-45a7-a728-954237366d08&type=10'
          : 'https://dist.appcircle.io/?email=<EMAIL>&profileId=71cb16c9-48fe-4ff3-8721-28ac6befefd7&AppVersionId=97a943c0-0d1f-4758-85be-2cdcee1a4d5d&type=10';
      } else {
        url = this.version === this.iosVersion ?
          'https://dist.appcircle.io/?email=<EMAIL>&profileId=02064d0e-044c-48b2-af0b-f6ebdb070cc5&AppVersionId=0707d225-8363-4f46-9c83-b7d5d6eadaeb&type=10'
          : 'https://dist.appcircle.io/?email=<EMAIL>&profileId=7d2beb12-6f88-424e-bcda-8914a4e4c978&AppVersionId=7f96e053-8805-46b5-8d16-34acfbf87c19&type=10';
      }
    }
    if(this.forceUpdateNeeded) {
      if(getOS() === 'IOS') {
        this.translateService.get('_ios_store_link').subscribe(trns => {
          url = trns;
        });
      }
      if(getOS() === 'Android') {
        this.translateService.get('_android_store_link').subscribe(trns => {
          url = trns;
        });
      }
    }

    this.frameMessageService.sendMessage(FrameMessageEnum.openStore, {
      url
    });

  }
}
