.box {
  font-size: 11px;
  border: 1px solid #ffe5b8;
  border-radius: 6px;
  margin-top: 1.4em;

  .title {
    position: relative;
    width: 0;
    height: 0;

    .text {
      position: absolute;
      //width: 130px;
      top: -18px;
      border-radius: 14px;
      //padding: 0 8px;
      border: 1px solid #ffc660;
      background: #fff;
      color: #ffa300;

      white-space: nowrap;
      padding-right: 27px;
      padding-left: 8px;
    }
    .title-icon {
      line-height: 1em;
    }
    .icon-cls{
      font-size: 15px;
    }
  }

  .content {
  }
}
