import { HttpClient } from '@angular/common/http';
import * as moment from 'moment';
import { Component, Input, OnInit, OnD<PERSON>roy } from '@angular/core';
import { environment } from 'src/environments/environment';
import { Observable, Subject } from 'rxjs';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { map, takeUntil } from 'rxjs/operators';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { CommonState } from '../../state/common/common.state';
import { Select, Store } from '@ngxs/store';
import { appVersionController } from 'src/app/util/app-version-controller.util';
import { ModalService } from '../../service/modal.service';
import { TranslateService } from '@ngx-translate/core';
import { StartDownloadAction } from '../../state/common/common.actions';

@Component({
  selector: 'app-download-file',
  templateUrl: './download-file.component.html',
  styleUrls: ['./download-file.component.scss']
})
export class DownloadFileComponent implements OnInit, OnDestroy {

  @Input() downloads;

  @Select(CommonState.startDownload)
  startDownload$: Observable<any>;

  @Select(CommonState.finishDownload)
  finishDownload$: Observable<any>;

  @Select(CommonState.showPermissionError)
  showPermissionError$: Observable<any>;

  version: boolean;
  loadMore: any = {};
  loadingAttachment: any[] = [];
  subscribe = true;

  // Base64 API List
  base64UrlList = ['equipment/SosAnalyzePdf'];

  private subscriptions$: Subject<boolean> = new Subject();
  private errorModal: any;

  constructor(
    private readonly http: HttpClient,
    private readonly messageFrameService: FrameMessageService,
    private readonly modalService: ModalService,
    private readonly translateService: TranslateService,
    private readonly store: Store
  ) { }

  ngOnInit(): void {
    this.version = appVersionController(this.store.selectSnapshot(CommonState.version), 'downloadFile');
    this.finishDownload$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (!data) {
          return;
        }
        this.downloading({ data, downloading: false });
      });
    this.startDownload$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        if (!data) {
          return;
        }
        this.downloading({ data, downloading: true });
      });
    this.showPermissionError$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(
        data => {
          if (!data) {
            return;
          }
          this.errorModal = this.modalService.errorModal({
            message: data.message,
            button: this.translateService.instant('_go_to_settings'),
            buttonClick: () => this.goToSettings(data.action)
          });
        });
  }

  goToSettings(action) {
    this.messageFrameService.sendMessage(action);
  }

  customDate(value: string) {
    // ? Weking Gelen Tarih Formatı 09.12.2021 11:20
    // ? Cdoms Gelen Tarih Formatı 20210903111723
    // ? KZ icin gelen format 20211803111723 !!IPTAL

    let date = moment(value, 'DD.MM.YYYY HH:mm');
    if (date.isValid()) {
      return date.toISOString();
    }
    // date = moment(value, this.company?.countryCode === 'KZ' ?
    //   'YYYYDDMMHHmmss' : 'YYYYMMDDHHmmss');
    date = moment(value, 'YYYYMMDDHHmmss');
    if (date.isValid()) {
      return date.toISOString();
    }

    return '';
  }

  getAttachments(service: any) {
    return service.workOrderStatus.attachments.length < 5
    || this.loadMore[service.serviceNumber] ?
      service.workOrderStatus.attachments : service.workOrderStatus.attachments.slice(0, 5);
  }

  lessMoreFunc(serviceNumber: any) {
    this.loadMore[serviceNumber] = !this.loadMore[serviceNumber];
    if (!this.loadMore[serviceNumber]) {
      this.loadingAttachment = [];
    }
  }

  download(attachment, serviceOrganization, workOrderNumber) {
    const startDownload = {
      id: '', url: '', isBase64: false, extension: '.pdf',
    };
    let isBase64 = false;
    if (attachment.uploadTypeDescription === 'SERVICE_FORM') {
      startDownload.url = `/equipment/GetWorkOrderAttachment?workOrderNumber=` + workOrderNumber + '&attachmentId=' + attachment.id;
      startDownload.id = attachment.id;
      startDownload.extension = attachment?.extension;
      isBase64 = false;
    }
    else {
      startDownload.url = `/equipment/getattachment?attachmentId=` + attachment.id + '&serviceOrganization=' + serviceOrganization;
      startDownload.id = attachment.id;
      startDownload.extension = attachment?.extension;
      isBase64 = false;
    }
    this.messageFrameService.sendMessage(FrameMessageEnum.downloadFile, {
      id: startDownload.id,
      url: startDownload.url,
      isBase64,
      extension: startDownload?.extension.charAt(0) === '.'
        ? startDownload?.extension
        : '.' + startDownload?.extension,
    });
  }

  downloading(attachment) {
    if (attachment.downloading && !this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.push(attachment.data);
    }
    if (!attachment.downloading && this.isDownloading(attachment.data.id)) {
      this.loadingAttachment.splice(
        this.loadingAttachment
          .map((object) => object.id)
          .indexOf(attachment.data.id),
        1
      );
    }
  }

  isDownloading(id) {
    if (this.loadingAttachment?.filter(data => data.id === id).length) {
      return true;
    }
    return false;
  }

  cancelDownloadFile(id) {
    this.store.dispatch(new StartDownloadAction(null));
    this.messageFrameService.sendMessage(FrameMessageEnum.cancelDownloadFile, this.loadingAttachment.find(data => data.id === id));
    if (this.isDownloading(id)) {
      this.loadingAttachment.splice(this.loadingAttachment.findIndex(data => data.id === id), 1);
    }
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
