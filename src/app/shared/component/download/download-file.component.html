<div *ngIf="downloads.workOrderStatus && version">
  <div class="p-2" *ngIf="downloads.workOrderStatus.attachments.length > 0">
    <div *ngFor="
    let attachment of getAttachments(downloads)
  ">
      <div class="row no-gutters d-flex align-items-center py-2">
        <div [id]="attachment.id" appClickLog [section]="'SERVICE_LIST'" [subsection]="'ATTACHMENT_DOWNLOAD'"
          [data]="{ attachmentId: attachment.id }" (click)="
          download(
            attachment,
            downloads.serviceOrganization,
            downloads.workOrderStatus.workOrderNumber
          )
        " class="row m-0" (downloadLoading)="downloading($event)">
          <div
            class="download-file border text-info border-info rounded-circle d-flex justify-content-center align-items-center"
            [class.spinner]="isDownloading(attachment.id)">
            <i class="icon icon-spinner8" *ngIf="isDownloading(attachment.id)"></i>
            <i class="icon icon-download" *ngIf="!isDownloading(attachment.id)"></i>
            <a class="d-none" [download]="attachment.id"></a>
          </div>
          <div>
            <div class="attachment-name text-info ml-3 font-size-14px font-weight-bold">
              {{
              (attachment.uploadTypeDescription === "SERVICE_FORM" ? "_service_form"
              : attachment.uploadType === 23 ? "_dispatch_report"
              : attachment.uploadType === 18 ? "_reception_report"
              : attachment.uploadType === 2 ? "_technical_report"
              : attachment.uploadType === 5 ? "_test_report"
              : attachment.uploadType === 9 ? "_sos_report"
              : attachment.uploadType === 0 ? "_unknown_attachment"
              : "_document"
              ) | translate
              }}
            </div>
            <div *ngIf="attachment.dateUpload" class="text-muted font-size-12px ml-3">
              {{ customDate(attachment.dateUpload) | date: 'shortDate' }}
            </div>
          </div>
        </div>
        <div class="ml-auto" *ngIf="isDownloading(attachment.id)" (click)="cancelDownloadFile(attachment.id)">
          <i class="icon icon-x"></i>
        </div>
      </div>
    </div>
    <div (click)="lessMoreFunc(downloads.serviceNumber)"
      *ngIf="downloads.workOrderStatus.attachments.length > 5" class="mt-1 text-center">
      <div class="font-weight-medium">
        {{ (loadMore[downloads.serviceNumber] ? '_less' : '_more') | translate }}
        <!--                    <i class="icon icon-chevron-down"></i>-->
      </div>
    </div>
  </div>
</div>
