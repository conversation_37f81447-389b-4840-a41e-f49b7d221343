<div class="agreement" *ngIf="(!isGdprApproved && agreements) || showAgreements" [formGroup]="form">
  <div *ngFor="let agree of agreements" formGroupName="agreements" class="mb-3 agreement d-flex flex-row flex-nowrap justify-content-between">
    <input *ngIf="agree.selectable" [formControlName]="agree?.name" type="checkbox" class="form-check-input"
           [id]="agree.name">
    <label class="flex-grow-1 form-check-label text-secondary font-size-13px mb-0" [for]="agree.name">
      <ng-container *ngFor="let item of agree.descParts">
        <a *ngIf="item.linkText" (click)="showContent(item)">{{item.linkText }}</a> {{item.text}}
      </ng-container>

    </label>
  </div>
</div>


<app-big-modal *ngIf="modalContent" [(status)]="modalContent">
  <app-agreement-modal [content]="modalContent"></app-agreement-modal>
</app-big-modal>

<app-loader [show]="agreementsLoading$ | async"></app-loader>
<!--  <div formGroupName="agreements" class="form-check mb-3">-->
<!--    <input [formControlName]="'sd'" type="checkbox" class="form-check-input"-->
<!--           id="222">-->
<!--    <label class="form-check-label text-secondary font-size-13px" [for]="'222'">-->
<!--      asd-->
<!--    </label>-->
<!--  </div>-->

