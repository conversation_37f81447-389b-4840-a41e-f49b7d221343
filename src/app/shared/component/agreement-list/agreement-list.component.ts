import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';
import { AgreementLink, AgreementModel, ApprovalAgreementModel } from '../../../modules/definition/model/agreement.model';
import { AgreementTypeEnum } from '../../../modules/definition/enum/agreement-type.enum';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { Select, Store } from '@ngxs/store';
import { UserState } from '../../../modules/customer/state/user/user.state';
import { Observable, Subject } from 'rxjs';
import { WindowManagerService } from '../../service/window-manager.service';
import { TranslateService } from '@ngx-translate/core';
import { takeUntil } from 'rxjs/operators';
import { GetAgreementsAction } from 'src/app/modules/definition/state/definition/definition.actions';
import { DefinitionState } from 'src/app/modules/definition/state/definition/definition.state';

@Component({
  selector: 'app-agreement-list',
  templateUrl: './agreement-list.component.html',
  styleUrls: ['./agreement-list.component.scss'],
})
export class AgreementListComponent implements OnInit, OnDestroy {
  @Select(UserState.isGdprApproved)
  isGdprApproved$: Observable<boolean>;
  isGdprApproved: boolean;

  @Input()
  formType: AgreementTypeEnum;

  @Input()
  form: FormGroup;

  @Input()
  forceAgreement = false;

  @Input()
  filter: any;

  @Input()
  showAgreements = false;

  @Output()
  agreementLength: EventEmitter<number> = new EventEmitter<number>();

  modalContent: any | AgreementLink;
  agreements: AgreementModel[];

  isReApproved = true;

  @Select(DefinitionState.getAgreements)
  agreements$: Observable<AgreementModel[]>;

  @Select(DefinitionState.getAgreementLoading)
  agreementsLoading$: Observable<boolean>;

  // TODO Eklenecek özellik
  // @Select(DefinitionState.getApprovedAgreements)
  // approvedAgreement$: Observable<ApprovalAgreementModel[]>;
  // approvedAgreement: ApprovalAgreementModel[];

  protected subscriptions$: Subject<boolean> = new Subject();
  constructor(
    private readonly fb: FormBuilder,
    private readonly windowManagerService: WindowManagerService,
    private readonly translateService: TranslateService,
    private readonly store: Store,
  ) { }

  ngOnInit(): void {
    console.log('AGREEMENT ', [this.formType, this.form]);
    if (!this.forceAgreement) {
      this.isGdprApproved$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(data => {
        console.log('isGdprApproved: ', this.isGdprApproved);
        this.isGdprApproved = data;
      });
    } else {
      this.isGdprApproved = !this.forceAgreement;
    }
    console.log('forceAgreement: ', this.forceAgreement);
    console.log('isGdprApproved:(with agreement) ', this.isGdprApproved);
    this.callAgreementList();
  }

  callAgreementList() {
    this.store.dispatch( new GetAgreementsAction(this.formType, true));
    this.agreements$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe((data: any) => {
        if (!data?.length) {
          return;
        }
        this.agreements = data;
        console.log('agreements: ',this.agreements);
        const group = this.fb.group({});

        if(this.filter){
          const alreadyApprovedAgreements = this.filter?.approved?.split(';');
          this.agreements = data.filter(agg => !alreadyApprovedAgreements?.includes(agg.name));
        }

        this.agreements.map(item => {
          group.addControl(item.name, new FormControl(!item.selectable));
        });
        this.form.addControl('agreements', group);

        this.agreementLength.emit(this.agreements?.length);
        // TODO Düzenlenecek
        // this.callReapprovedAgreement();
      });
  }

  // TODO Düzenlenecek
  // callReapprovedAgreement() {
  //   this.definitionService.getApprovedAgreements()
  //     .subscribe(data => {
  //       if (!data) { return; }
  //       this.isReApproved = false;
  //       const reApprovedList: any = data.filter(x => x.reapproveNeeded === true).map(({ name }) => name);
  //       if (reApprovedList?.length) {
  //         this.agreements = this.agreements.filter(x => reApprovedList.includes(x.name));
  //       }
  //       this.agreementLength.emit(this.agreements?.length);
  //     });
  // }

  showContent(item: AgreementLink) {
    if (item.url.includes(window.location.host)) {
      this.modalContent = item;
      return;
    }
    this.windowManagerService.openInWebview(item.url, this.translateService.instant('_agreements'));
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

}
