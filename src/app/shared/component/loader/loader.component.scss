.app-loader {
  z-index: 10000;
  position: absolute;
  left: 50%;
  top: calc(50vh + 29px);
  //top: calc(50 + 29px + env(safe-area-inset-bottom, 0px)/2);
  margin-left:-50px;
  margin-top:-50px;
  border: 16px solid #7f7e7e;
  border-radius: 50%;
  border-top: 16px solid #FFA300;
  border-bottom: 16px solid #FFA300;
  width: 100px;
  height: 100px;
  animation: spin 1s linear infinite;
}

@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
