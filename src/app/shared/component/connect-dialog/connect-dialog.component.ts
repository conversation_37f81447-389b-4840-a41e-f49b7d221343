import { Component, ElementRef, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { FrameMessageEnum } from 'src/app/core/enum/frame-message.enum';
import { IncomingMessageEnum } from 'src/app/core/enum/incoming-message.enum';
import { FrameMessageService } from 'src/app/core/service/frame-message.service';
import { IncomingMessageService } from 'src/app/core/service/incoming-message.service';
import { IframeState } from 'src/app/modules/customer/state/iframe/iframe.state';
import { CommonState } from '../../state/common/common.state';
import { OfflineEquipmentData } from '../../state/common/common.actions';
import { SettingsState } from '../../state/settings/settings.state';
import { Observable } from 'rxjs';
import { SystemFeature } from 'src/app/modules/customer/response/settings.response';
import { systemFeature } from 'src/app/util/system-feature.util';
import { appVersionController } from 'src/app/util/app-version-controller.util';

@Component({
  selector: 'app-connect-dialog',
  templateUrl: './connect-dialog.component.html',
  styleUrls: ['./connect-dialog.component.scss'],
})
export class ConnectDialogComponent implements OnInit, OnChanges {
  @Input() show: boolean;
  @Output() showChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;

  systemFeatureOfflineModeButton: boolean
  offlineAndQrVersionController: boolean

  private element: any;

  constructor(el: ElementRef, private frameMessageService: FrameMessageService, private store: Store) {
    this.element = el.nativeElement;
  }

  ngOnInit(): void {
    this.onVisibilityChange();
    this.offlineAndQrVersionController = appVersionController(this.store.selectSnapshot(CommonState.version), 'offlineAndQr');
    this.systemFeatures$.subscribe(features => {
      if (features) {
        this.systemFeatureOfflineModeButton = systemFeature('offline_mode', features, true);
      }
    });
  }

  ngOnChanges(changes: SimpleChanges) {
    this.onVisibilityChange();
  }

  onCloseModal() {
    this.show = false;
    this.showChange.emit();
  }

  openOfflineMode() {
    this.frameMessageService.sendMessage(FrameMessageEnum.openOfflineMenu, {
      equipment: this.store.selectSnapshot(CommonState.offlineEquipmentData),
      selectedApp: 'Menu'
    });
  }

  onVisibilityChange() {
    this.element.style.display = this.show ? 'block' : 'none';
  }
}
