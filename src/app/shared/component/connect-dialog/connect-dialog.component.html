<div class="modal-backdrop"></div>

<div class="modal d-block" tabindex="-1" role="dialog">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-body py-4 px-4">
        <i class="icon icon-x cursor-pointer" (click)="onCloseModal()"></i>
        <div class="d-flex flex-column align-items-center justify-content-center">
          <i class="icon icon-connect-fail mb-4"></i>
          <p class="mb-4" [innerHTML]="('_internet_connection_error_message' | translate) | safeHtml"></p>
          <div appClickLog
          [section]="'OFFLINE'"
          [subsection]="'OFFLINE_MODE_POPUP_CLICK'" *ngIf="systemFeatureOfflineModeButton && offlineAndQrVersionController" (click)="openOfflineMode()" class="btn btn-info btn-gradient btn-block text-white rounded-lg text-center">
            {{ "_switch_offline_mode" | translate }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
