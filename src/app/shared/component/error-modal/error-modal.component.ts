import { animate, state, style, transition, trigger, } from '@angular/animations';
import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Modal } from '../../models/modal.model';
import { environment } from '../../../../environments/environment';
import { disableBack } from '../../../util/disable-back.util';

@Component({
  selector: 'app-error-modal',
  templateUrl: './error-modal.component.html',
  styleUrls: ['./error-modal.component.scss'],
  animations: [
    trigger('modal', [
      state('void', style({ top: '60%', opacity: 0 })),
      state('*', style({ top: '20%', opacity: 1 })),
      transition('* => *', [animate('.10s')]),
    ]),
    trigger('backdrop', [
      state('void', style({ opacity: 0 })),
      state('*', style({ opacity: 0.1 })),
      transition('* => *', [animate('.05s')]),
    ]),
  ],
})
export class ErrorModalComponent extends Modal implements OnInit {
  @Input() message = '';
  @Input() backdropClose = true;
  @Input() isTranslate: boolean;

  @Input() button = null;
  @Input() buttonClick = null;

  @Input() status: boolean;
  @Output() statusChange: EventEmitter<boolean> = new EventEmitter<boolean>();

  warningIcon = `${environment.assets}/warning.svg`;
  private disabledBack: any;

  constructor(
    private readonly cdRef: ChangeDetectorRef,
  ) {
    super();
  }

  onInjectInputs(inputs: any): void {
    this.message = inputs.message;
    this.isTranslate = inputs.translate;
    this.status = inputs.status;
    this.backdropClose = inputs.backdropClose;
    this.button = inputs.button;
    this.buttonClick = inputs.buttonClick;
  }

  ngOnInit(): void {
  }
  disableBack() {
    this.disabledBack = disableBack(() => {
      this.close();
    });
  }

  onCloseModal() {
    this.status = false;
    this.statusChange.emit();
    this.close();
  }
  close(output?: any) {
    if (this.disabledBack) {
      this.disabledBack?.removeAndPop();
      this.disabledBack = null;
    }
    super.close(output);
  }

  onClickBackdrop() {
    if (this.backdropClose) {
      this.onCloseModal();
    }
  }

  onButtonClick() {
    if (typeof this.buttonClick === 'function') {
      this.buttonClick(this);
    }
  }
}
