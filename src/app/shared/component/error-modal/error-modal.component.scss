.modal {
  left: 50%;
  top: 50%;
  transform: translate(-50%);
  width: 100%;
  height: auto;
  overflow: visible;
  align-items: center;
  justify-content: center;
  &-dialog {
    width: 75%;
  }
  &-content {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.05), 0 20px 50px rgba(0, 0, 0, 0.15);
    border-radius: 6px;
    border: none;
    max-height: 70vh;
    overflow-y: scroll;
  }
  &-header {
    padding: 1rem 1rem 0 0;
    font-size: 20px;
    font-weight: 600;
    border: 0;
    min-height: 40px;
    .icon-x {
      position: absolute;
      right: 20px;
      top: 20px;
      font-size: 19px;
      color: #2c2c2c;
    }
  }
  &-body {
    padding: 1rem 2rem 2rem;
    overflow-x: hidden;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    img {
      width: 60px;
      height: 60px;
      margin-bottom: 1rem;
    }
    &-message {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.modal-backdrop {
  opacity: 0.2;
  background-color: white;
}

.action-button {
  padding: 0.3rem 1rem;
}
