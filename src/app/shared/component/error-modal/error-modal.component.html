<div
  (click)="onClickBackdrop()"
  *ngIf="status"
  [@backdrop]
  class="modal-backdrop"
></div>

<div *ngIf="status" [@modal] class="modal d-flex" role="dialog" tabindex="-1">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <i (click)="onCloseModal()" class="icon icon-x cursor-pointer"></i>
      </div>
      <div class="modal-body d-flex">
        <img [src]="warningIcon"/>
        <p *ngIf="!isTranslate" class="modal-body-message text-center">
          {{ message }}
        </p>
        <p *ngIf="isTranslate" class="modal-body-message text-center">
          {{ message | translate }}
        </p>
        <button *ngIf="button" class="action-button btn btn-info btn-sm" (click)="onButtonClick()">
          {{button}}
        </button>

      </div>
    </div>
  </div>
</div>
