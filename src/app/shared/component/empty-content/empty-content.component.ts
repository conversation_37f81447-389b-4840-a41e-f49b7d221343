import { Component, Input, OnInit } from '@angular/core';
import {Navigate} from '@ngxs/router-plugin';
import {Store} from '@ngxs/store';

@Component({
  selector: 'app-empty-content',
  templateUrl: './empty-content.component.html',
  styleUrls: ['./empty-content.component.scss'],
})
export class EmptyContentComponent implements OnInit {
  @Input() iconName = '';
  @Input() message = '';
  @Input() hasBackButton = true;
  @Input() extraMessage = '';

  constructor(
    private readonly store: Store,
  ) {}

  ngOnInit(): void {}

  backToHome() {
    this.store.dispatch(new Navigate(['/']));
  }
}
