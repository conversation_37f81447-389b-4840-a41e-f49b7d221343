<div class="user-card w-100 p-3 mt-5 position-relative">
  <div class="h-100">
    <div class="user-card-content d-flex align-items-center justify-content-between">
      <div class="user-badge d-flex align-items-center flex-column" [class]="variant">
        <div class="badge-icon-container d-flex align-items-center justify-content-center">
          <img class="badge-icon" [src]="IconUrl">
        </div>
        <span class="font-size-12px badge-type font-weight-bold">{{ variant }}</span>
      </div>
      <div class="user-card-content-text">
        <div
          class="mb-2 pl-2 h6"
          [innerHTML]="'_welcome_to_boom_club' | translate"
        ></div>
        <!-- <div class="interests-container overflow-auto d-flex align-items-center">
            <span class="interest-text font-size-13px px-3 d-flex align-items-center mb-1" *ngFor="let interest of user?.interests">
              {{ interest?.name }}
            </span>
        </div> <PERSON><PERSON> sonra kullanılabilir -->
        <div class="pl-2 customer-name pt-3" *ngIf="user">
          {{ user?.name + " " + user?.surname }}
        </div>
      </div>
    </div>
  </div>
</div>

