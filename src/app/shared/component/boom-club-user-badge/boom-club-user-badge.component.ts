import { Component, Input, OnInit } from '@angular/core';
import { Select } from '@ngxs/store';
import { Observable } from 'rxjs';
import { SanitizedCustomerModel } from 'src/app/modules/customer/model/sanitized-customer.model';
import { UserState } from 'src/app/modules/customer/state/user/user.state';
import { BoomClubUserModel } from 'src/app/modules/promotion/model/promotion.model';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-boom-club-user-badge',
  templateUrl: './boom-club-user-badge.component.html',
  styleUrls: ['./boom-club-user-badge.component.scss']
})
export class BoomClubUserBadgeComponent implements OnInit {
  @Input()
  variant: 'premium' | 'standart' = 'premium';
  @Input()
  user: BoomClubUserModel;

  IconUrl: string;

  constructor() { }

  ngOnInit() {
    this.IconUrl = `${environment.assets}/boom_club_${this.variant}.svg`;
  }

}
