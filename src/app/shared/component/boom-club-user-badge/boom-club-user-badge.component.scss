.user-card{
    background-color: #FFFFFF;
    box-shadow: 0px 4px 4px 0px #00000029;
    border-radius: 8px;
    color: #F7A226;

    &-content{

        .user-badge{
        
            .badge-icon-container{
                width: 36px;
                height: 36px;
                border: 4px solid #F7A226;
                border-radius: 50%;
        
                .badge-icon {
                    color: #F7A226;
                    width: 16px;
                    height: 16px;
                }
            }
        
            .badge-type{
                color: #F7A226;
                text-transform: capitalize;
            }
        
            &.standart{
        
                .badge-icon-container{
                    border-color: #012195;
                }
        
                .badge-type{
                    background: linear-gradient(180deg, #001B79 0%, #315CF4 100%);
                    background-clip: text;
                    -webkit-background-clip: text;
                    color: transparent;
                }
            }
        }

        &-text{
    
            .customer-name{
                color: #000000;
                border-top: 1px solid #FFEACB;
            }

            .interests-container{
                width: 212px;
                &::-webkit-scrollbar{
                    display: none;
                }

                .interest-text{
                    color: #CECECE;
                    border-right: 1px solid #F7A226;
                    
                    &:last-child{
                        border: none;
                    }
                }
            }
        }
    }
}
