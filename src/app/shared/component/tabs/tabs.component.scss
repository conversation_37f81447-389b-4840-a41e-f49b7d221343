.ulTab {
  margin: 0;
  padding: 0;
  overflow: hidden;
  margin-bottom: -1px;
  height: 2.25em;
  display: flex;
  flex-direction: row-reverse;
}

.liTab {
  // float: right;
  list-style: none;
  margin: 0;
  // padding: .25em .25em 0;
  height: 2em;
  overflow: hidden;
  position: relative;
  z-index: 1;
  display: flex;

  .active {
    z-index: 3;
    text-decoration: none;
  }
}

.ulTab .liTab:last-child{
  margin-right: 1rem;
}

.aTab {
  height: 2em;
  line-height: 2em;
  padding: 0 10px;
  color: #000;
}

.activeTab a,
.activeTab .special-fwb {
  text-decoration: none;
  font-weight: bold;
}

::ng-deep .tabPane {
  width: 100%;
  position: relative;
  z-index: 2;
  max-height: calc(100vh - 110px);
  overflow-y: scroll;
  overflow-x: hidden;
}

.font-size-24px {
  font-size: 24px;
}
