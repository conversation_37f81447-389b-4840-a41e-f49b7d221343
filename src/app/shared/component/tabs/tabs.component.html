<ul class="tabs ulTab">
  <li class="liTab" *ngFor="let tab of tabs" (click)="activateTab(tab)" [class.activeTab]="tab.active">
    <a class="aTab">
      <i *ngIf="tab.isIcon" class="r-90" [class]="
          isDown === true
          ? 'icon icon-chevron-down font-size-11px align-self-center'
          : 'icon icon-chevron-right font-size-11px align-self-center'
        "></i>
      <i *ngIf="tab.icon" class="align-self-center align-middle icon {{tab.icon}} font-size-24px special-fwb"></i>
      {{ tab.name }}
    </a>
  </li>
</ul>
<ng-content></ng-content>
