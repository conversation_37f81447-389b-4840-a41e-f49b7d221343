import { Component, Input, Output, EventEmitter } from '@angular/core';
import {  } from 'protractor';

@Component({
  selector: 'app-tab',
  styleUrls: ['./tabs.component.scss'],
  template: `
    <div [hidden]="!active" (scroll)="onScroll($event)" class="tabPane">
      <div class="content">
        <ng-content></ng-content>
      </div>
    </div>
  `,
})
export class TabComponent {
  @Input() name: string;
  @Input() active = false;
  @Input() isIcon = false;
  @Input() icon = '';
  @Output() scrollEnd: EventEmitter<boolean> = new EventEmitter<boolean>();
  onScroll(e){
    if ((e?.target?.offsetHeight + e?.target?.scrollTop >= e?.target?.scrollHeight) && this.isIcon) {
      this.scrollEnd.emit(true);
    }
  }
}
