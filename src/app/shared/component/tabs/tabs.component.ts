
import { AfterContentInit, Component, ContentChildren, EventEmitter, Input, Output, QueryList } from '@angular/core';
import { TabComponent } from './tab.component';

@Component({
  selector: 'app-tabs',
  templateUrl: './tabs.component.html',
  styleUrls: ['./tabs.component.scss'],
})
export class TabsComponent implements AfterContentInit {
  @ContentChildren(TabComponent) tabs: QueryList<TabComponent>;

  isDown = false;
  @Output() isDownEvent: EventEmitter<boolean> = new EventEmitter();
  ngAfterContentInit() {
    const activeTabs = this.tabs.filter(tab => tab.active);
    if (!activeTabs.length) {
      this.activateTab(this.tabs.last);
    }
  }

  activateTab(tab: TabComponent) {
    if (tab.isIcon){
      this.isDown = this.isDown === false ? true : false;
      this.isDownEvent.emit(this.isDown);
    }
    this.tabs.toArray().forEach(item => item.active = false);
    tab.active = true;
  }
}
