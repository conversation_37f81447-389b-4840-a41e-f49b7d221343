import { Component, OnInit, Input } from '@angular/core';
import { Router } from '@angular/router';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-boom-club-activities-card',
  templateUrl: './boom-club-activities-card.component.html',
  styleUrls: ['./boom-club-activities-card.component.scss']
})
export class BoomClubActivitiesCardComponent implements OnInit {


  @Input() title: string;
  @Input() isNew: boolean;
  @Input() activitiesSoonContent: string;
  @Input() activitiesSoonSeeAll: string;
  @Input() backgroundImage: string;
  @Input() enableToRegister: boolean;
  @Input() advantageId: string
  @Input() isUsed: boolean;
  @Input() categoryId: string;

  environment = environment;
  usedAdvantageIcon = `${environment.assets}/orange-succes.png`;

  constructor(
    private readonly router: Router,
  ) { }

  ngOnInit() {
  }

  toRegister() {
    if (!this.enableToRegister) { return; }
    this.router.navigate(['promotion', 'boom-club', 'register']);
  }

  navigateAdvantageDetail(){
    this.router.navigate(['promotion', 'boom-club', 'advantage', this?.advantageId])
  }
}
