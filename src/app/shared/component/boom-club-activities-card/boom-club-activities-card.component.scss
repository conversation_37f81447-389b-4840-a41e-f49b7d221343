.activites {
  background-color: #FFFFFF;
  border-radius: 6px;
  box-shadow: rgba(149, 157, 165, 0.2) 0px 8px 24px;

  .activities-soon {
    background-repeat: no-repeat;
    background-position: top;
    background-color: white;
    border-radius: 12px;
    box-shadow: rgba(100, 100, 111, 0.2) 0px 7px 29px 0px;
  }

  .activities-img-container{
    img{
      border-top-left-radius: 6px;
      border-top-right-radius: 6px;
    }
  }

  .isNew-badge{
    background-color: #ffa300;
    color: #fff;
    position: absolute;
    top: .5rem;
    right: 1rem;
    border-radius: 1rem;
  }

  .text-content {
    padding: 12px;
  }

  .isCategory-badge{
    width: 30px;
    height: 30px;
    background-color: #ffa300;
    color: #fff;
    position: absolute;
    top: 0;
    left: 1rem;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .isUsed-badge{
    // width: 25px;
    // height: 25px;
    // background-color: #ffa300;
    // color: #fff;
    position: absolute;
    bottom: 1rem;
    right: 1rem;
  }
}
