import { Directive, HostListener } from '@angular/core';
import { IncomingMessageService } from '../../core/service/incoming-message.service';

@Directive({
  selector: '[appDeepLink]'
})
export class DeepLinkDirective {
  // <a appDeepLink href="borusancat360://open_module?moduleCode=equipment">
  //   linkasd
  //   </a>

  @HostListener('click', ['$event']) onClick($event) {
    this.incomingMessageService.open_module($event.target.href);
    return false;
  }

  constructor(
    private readonly incomingMessageService: IncomingMessageService,
  ) { }

}
