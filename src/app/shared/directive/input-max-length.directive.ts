import { AfterViewInit, Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { Observable, Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { SettingsResponse } from 'src/app/modules/customer/response/settings.response';
import { SettingsState } from '../state/settings/settings.state';

@Directive({
  selector: '[appInputMaxLength]'
})
export class InputMaxLengthDirective implements AfterViewInit {
  @Input() name;

  @Select(SettingsState.basic)
  settings$: Observable<SettingsResponse>;

  protected subscriptions$: Subject<boolean> = new Subject();

  defaultMaxLength = [
    { name: 'Model', maxLength: 120 },
    { name: 'SerialNumber', maxLength: 120 },
    { name: 'Description', maxLength: 500 },
    { name: 'Name', maxLength: 100 },
    { name: 'Surname', maxLength: 100 },
    { name: 'Email', maxLength: 200 },
    { name: 'Phone', maxLength: 15 },
    { name: 'CompanyName', maxLength: 500 },
    { name: 'Make', maxLength: 120 },
    { name: 'Address', maxLength: 500 },
    { name: 'Comment', maxLength: 500 },
    { name: 'MachineSerialNumber', maxLength: 50 },
    { name: 'NameSurname', maxLength: 200 },
    { name: 'Password', maxLength: 120 },
  ];

  maxLength: number;
  minLength: number;

  constructor(
    private readonly el: ElementRef,
    private readonly store: Store,
    private renderer: Renderer2
  ) {
  }
  ngAfterViewInit(): void {
    this.settings$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(settings => {
        this.maxLength = settings?.formParametersMaxCharacterLimit
          .find(data => data.formName === 'DefaultForm')?.parameters
          .find(data => data.name === this.name)?.maxLength;
        if (!this.maxLength) {
          this.maxLength = this.defaultMaxLength.find(data => data.name === this.name)?.maxLength;
        }
        if (!this.maxLength) {
          this.maxLength = 120;
        }
        this.renderer.setAttribute(this.el.nativeElement, 'maxLength', this.maxLength.toString());
      });
  }

  ngOnDestroy(): void {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }

}
