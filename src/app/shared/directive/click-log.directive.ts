import { Directive, HostListener, Input, OnInit } from '@angular/core';
import { Store } from '@ngxs/store';
import { LogService } from '../service/log.service';
import { UserState } from '../../modules/customer/state/user/user.state';
import { CommonState } from '../state/common/common.state';

@Directive({
  selector: '[appClickLog]',
})
export class ClickLogDirective implements OnInit {
  @Input()
  section: string;
  @Input()
  subsection: string;
  @Input()
  data: {};
  protected customerId: string;
  protected companyId: string;
  protected trackerUserId: string;

  @HostListener('click') onClick() {
    // console.log('click logger');
    this.buildParams();

    this.logService.log(this.section, this.subsection, {
      userClicked: true,
      ...this.data,
      companyId: this.companyId,
      customerId: this.customerId,
      ...(this.trackerUserId ? { trackerUserId: this.trackerUserId } : {})
    }).subscribe();
  }

  constructor(
    private readonly logService: LogService,
    private readonly store: Store,
  ) { }

  ngOnInit(): void {
    // this.customer = this.store.selectSnapshot(LoginState.customer);
    // this.company = this.store.selectSnapshot(LoginState.company);
  }

  protected buildParams() {
    const customer = this.store.selectSnapshot(UserState.currentCustomer);
    if (customer?.customer) {
      this.customerId = customer.customer.id;
    }
    if (customer) {
      this.companyId = customer.publicMenuHeaderCompany;
    }
    this.trackerUserId = this.store.selectSnapshot(CommonState.trackerUser)?.uuid;
  }

}
