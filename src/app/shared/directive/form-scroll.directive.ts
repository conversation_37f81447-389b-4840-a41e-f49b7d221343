import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';
import { fromEvent, interval, Subject } from 'rxjs';
import { getOS } from 'src/app/util/os.util';

@Directive({
  selector: '[appFormScroll]'
})
export class FormScrollDirective {
  @Input() formClass = 'form-container'; // scroll olan yerin ID'si
  @Input() changeClass = 'form-container-overflow-y'; // eklenip çıkarılacak class


  protected subscriptionsSearch$: Subject<boolean> = new Subject();

  formAreaClassList;

  constructor(
    private  element: ElementRef,
    private renderer: Renderer2
  ) {}

  ngAfterViewInit(): void {
    this.formAreaClassList = document.getElementById(this.formClass)?.classList;
    this.addClassToFormArea();
    this.listenToInputFocus();
    this.listenToInputBlur();
  }

  listenToInputFocus(){
    this.renderer.listen(this.element.nativeElement, 'focus', () => {
      getOS() === 'IOS'? this.removeClassToFormArea() : null;
    });
  }

  listenToInputBlur(){
    this.renderer.listen(this.element.nativeElement, 'blur', () => {
      getOS() === 'IOS'? this.addClassToFormArea() : null;
    });
  }

  removeClassToFormArea(){
    this.formAreaClassList?.remove(this.changeClass);
    setTimeout(() => {
      this.addClassToFormArea();
    }, 0);
  }

  addClassToFormArea(){
    this.formAreaClassList?.add(this.changeClass);
  }

}
