import { AfterViewInit, Directive, ElementRef } from '@angular/core';
import { IncomingMessageService } from '../../core/service/incoming-message.service';

@Directive({
  selector: '[appDeepLinkInclude]'
})
export class DeepLinkIncludeDirective implements AfterViewInit {
  constructor(
    private element: ElementRef,
    private readonly incomingMessageService: IncomingMessageService,
  ) { }

  ngAfterViewInit(): void {
    const elements = this.element.nativeElement.querySelectorAll('a');
    // console.log('element found', elements);
    if (elements?.length > 0) {
      elements.forEach(el => {
        el.onclick = () => {
          if (el?.href?.startsWith('borusancat360:')) {
            this.openDeeplink(el?.href);
            return false;
          }

          return true;
        };
      });
    }
  }

  openDeeplink(href) {
    this.incomingMessageService.open_module(href);
  }
}
