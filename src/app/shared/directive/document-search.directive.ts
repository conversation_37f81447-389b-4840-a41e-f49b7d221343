import { Directive, ElementRef, Renderer2 } from '@angular/core';
import { fromEvent, Subject } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { getOS } from 'src/app/util/os.util';

@Directive({
  selector: '[appDisableDocumentScroll]',
})
export class DisableDocumentScrollDirective {
  protected subscriptionsSearch$: Subject<boolean> = new Subject();

  constructor(
    private element: ElementRef
    ) {}

  ngAfterViewInit(): void {
    //Called after ngAfterContentInit when the component's view has been initialized. Applies to components only.
    //Add 'implements AfterViewInit' to the class.
    // this.renderer.listen(this.element.nativeElement, 'blur', () => {
    //   getOS() === 'IOS'? this.searchInputOnBlur() : null;
    // });

    // this.renderer.listen(this.element.nativeElement, 'focus', () => {
    //   getOS() === 'IOS'? this.searchInputOnFocus() : null;
    // });
    getOS() === 'IOS'? this.searchInputOnFocus() : null;
  }

  searchInputOnFocus() {
    fromEvent(document, 'scroll')
      .pipe( 
        takeUntil(this.subscriptionsSearch$),
        debounceTime(300))
      .subscribe((e) => {
        const inputs = this.element.nativeElement.querySelectorAll('input');
        const iframe = document.getElementById('moduleIframe');
        if (document.documentElement.scrollTop > 0 && (inputs.length > 0 || iframe)) {
          document.documentElement.scroll(0, 0);
          //this.searchInputOnBlur();
        }
      });
  }

  searchInputOnBlur() {
    // this.subscriptionsSearch$.next(true);
    // this.subscriptionsSearch$.complete();
  }

  ngOnDestroy(): void {
    this.searchInputOnBlur();
  }
}
