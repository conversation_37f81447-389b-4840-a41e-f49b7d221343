export interface CustomerModel {
  customerNumber: string;
  details: Details;
  id: string;
  name: string;
}

export interface Details {
  adresses: Adresses[];
  bankDetails: undefined[];
  bankoList: BankoList[];
  complaints: undefined[];
  customerNumber: string;
  depts: Depts[];
  equipments: Equipments[];
  faxes: Faxes[];
  isArchived: boolean;
  maglevs: Maglevs[];
  mails: Mails[];
  name: string;
  nationality: string;
  nationalityDescription: string;
  paymentTerms: PaymentTerms;
  pssrList: PssrList[];
  quotations: undefined[];
  relatedPersons: RelatedPersons[];
  salesAreas: SalesAreas[];
  sectors: Sectors[];
  telephones: Telephones[];
  workOrders: undefined[];
}

export interface Adresses {
  customerNumber: string;
  adressNumber: string;
  adressKind: string;
  adressInfo: string;
  latitude: string;
  longtitude: string;
}

export interface BankoList {
  workingHour: WorkingHour;
  customerNumber: string;
  bankoNumber: string;
  bankoName: string;
  telephoneList: TelephoneList[];
  mailList: MailList[];
}

export interface Telephones {
  customerNumber: string;
  telephoneNumber: string;
  adressNumber: string;
  telephoneNumberShort: string;
  telephoneNumberExtension: string;
}

export interface Mails {
  customerNumber: string;
  adressNumber: string;
  mailAdress: string;
}

export interface Faxes {
  customerNumber: string;
  adressNumber: string;
  faxNumber: string;
}

export interface WorkingHour {
  isWorkingHour: boolean;
  message: string;
  serviceOrganizationCode: string;
  serviceOrganizationDisplayName: string;
  workEndTime: string;
  workStartTime: string;
}
export interface PssrList {
  customerNumber: string;
  pssrNumber: string;
  pssrName: string;
  telephoneList: TelephoneList[];
  mailList: MailList[];
  workingHour: WorkingHour;
  titles: string[];
}

export interface TelephoneList {
  customerNumber: string;
  telephoneNumber: string;
  adressNumber: string;
  telephoneNumberShort: string;
  telephoneNumberExtension: string;
}

export interface MailList {
  customerNumber: string;
  adressNumber: string;
  mailAdress: string;
}

export interface Maglevs {
  customerNumber: string;
  maglevCode: string;
  maglevDescription: string;
}

export interface Sectors {
  customerNumber: string;
  sectorSystemCode: string;
  sectorCode: string;
  defaultIndustrySector: string;
  sectorDescription: string;
}

export interface RelatedPersons {
  customerNumber: string;
  relatedPersonNumber: string;
  relatedPersonName: string;
  position: string;
}

export interface PaymentTerms {
  customerNumber: string;
  paymentTerms: PaymentTerms2[];
  outPaymentTerms: OutPaymentTerms[];
}

export interface PaymentTerms2 {
  term: string;
  description: string;
  currency: string;
}

export interface OutPaymentTerms {
  term: string;
  description: string;
  currency?: any;
}

export interface SalesAreas {
  customerNumber: string;
  salesOrganization: string;
  channel: string;
  division: string;
  salesOrganizationDescription: string;
  channelDescription: string;
  divisionDescription: string;
  salesOrganizationR3: string;
  channelR3: string;
  divisionR3: string;
}

export interface Depts {
  customerNumber?: any;
  profitCenter: string;
  description: string;
  top: string;
  toP2: string;
  toP3: string;
  dmbtr: string;
  dmbE2: string;
  dmbE3: string;
  vade: string;
  vadE2: string;
  vadE3: string;
  valueDate: string;
  currency2: string;
  currency3: string;
}

export interface Equipments {
  customerNumber: string;
  equipmentGuid: string;
  equipmentDescription: string;
  serialNumber: string;
  batchId: string;
  materialNumber: string;
  equipmentNumber: string;
  hierarchy: string;
  brandCode: string;
  brand: string;
  applicationCode: string;
  manifactureYear: string;
  manifactureSerialNumber: string;
  manifactureCode: string;
  deliveryDate: string;
  pwcCode: string;
  model: string;
  workingHours: number;
  workingHoursUnit: string;
  workingHourDate: string;
  locationName: string;
  latitude: string;
  longtitude: string;
}

export interface CustomerContactModel {
  callCenterNumber: string;
  whatsappNumber: string;
  phoneNumber: string;
  webSite: string;
  email: string;
  fax: string;
}

export interface FeedbackModel {
  feedbackId: string;
  feedbackType: string;
  message: string;
  appUsageType: string;
  feedbackUrl: string;
  hasAnswered: boolean;
}

export interface LeasingModel {
  dataSourceLabel: any;
  customerId: string;
  customerNumber: any;
  exchangeRate: number;
  vat: number;
  leasingCalculationId: string;
  leasingData: any;
  paymentTable: PaymentTableModel[];
}

export interface PaymentTableModel {
  paymentNumber: number;
  monthlyPayment: number;
  principalPayment: number;
  interestPayment: number;
  openingBalance: number;
  closingBalance: number;
}

export interface CatQRModel {
  catQrNumber: string;
  companyId: string;
  companyCode: string;
  dataSourceLabel: string;
  customerNumber: string;
  equipmentSerialNumber: string;
  equipmentNumber: string;
}
