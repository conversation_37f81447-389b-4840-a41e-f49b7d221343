import { Injectable } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { LoggerModel } from '../models/logger.model';
import { Store } from '@ngxs/store';
import { FrameMessageEnum } from '../../core/enum/frame-message.enum';
import { HttpResponse } from '../../core/interfaces/http.response';
import { environment } from '../../../environments/environment';
import { map } from 'rxjs/operators';
import { FrameMessageService } from '../../core/service/frame-message.service';
import { HttpClient } from '@angular/common/http';
import { UserState } from '../../modules/customer/state/user/user.state';
import { CommonState } from '../state/common/common.state';
import {LoginState} from '../../modules/authentication/state/login/login.state';

@Injectable({
  providedIn: 'root',
})
export class LogService {
  constructor(
    private readonly http: HttpClient,
    private readonly store: Store,
    private readonly frameService: FrameMessageService,
  ) {
  }
  uuid: string;
  public action(section: string, subsection: string, data: any = {}): Observable<LoggerModel> {
    // const customer = this.store.selectSnapshot(LoginState.customer);
    // const company = this.store.selectSnapshot(LoginState.company);

    let trackerUser = this.store.selectSnapshot(CommonState.trackerUser);
    if (trackerUser) {
      trackerUser = this.store.selectSnapshot(LoginState.trackerUser);
    }

    if (trackerUser && trackerUser.uuid) {
      this.uuid = trackerUser.uuid;
    }
    // console.log('::: DATA', data);

    return this.log(section, subsection, data);
  }

  public log(
    section: string,
    subsection: string,
    data: object | any
  ): Observable<LoggerModel> {
    const request = {
      section,
      subsection,
      logData: data,
      uuid: this.uuid,
    };
    if (data.trackerUserId) {
      request.uuid = data.trackerUserId;
      delete request.logData.trackerUserId;
    }
    this.frameService.sendMessage(FrameMessageEnum.log, request);

    return this.http
      .post<HttpResponse<LoggerModel>>(`${environment.api}/log`, request)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  public actionInstant(section: string, subsection: string, data: any = {}): Subscription {
    return this.action(section, subsection, data).subscribe();
  }

}
