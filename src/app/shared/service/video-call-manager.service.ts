import { Injectable } from '@angular/core';
import { Store } from '@ngxs/store';
import * as moment from 'moment-timezone';
import { ModalService } from './modal.service';
import { CustomerModuleService } from '../../modules/customer/service/customer-module.service';
import { LogService } from './log.service';
import { SettingsState } from '../state/settings/settings.state';
import { TranslateService } from '@ngx-translate/core';
import { VideoCallService } from '../../modules/customer/service/video-call.service';
import { UpdateUserLoadingAction } from '../../modules/customer/state/user/user.actions';
import { UserState } from 'src/app/modules/customer/state/user/user.state';

@Injectable({
  providedIn: 'root',
})
export class VideoCallManagerService {
  constructor(
    protected readonly store: Store,
    // protected readonly router: Router,
    protected readonly translateService: TranslateService,
    protected readonly modalService: ModalService,
    protected readonly customerModuleService: CustomerModuleService,
    protected readonly log: LogService,
    protected readonly videoCallService: VideoCallService,
  ) {}

  startVideoCall(queueName, source = null, onSuccess: () => {} = null, callType = null) {
    this.loading(true);
    this.availableAgent(queueName)
      .subscribe(agent => {
        const hour = this.findWorkingHours('CONTACTUS');
        let time;
        if (hour) {
          time = moment.tz(hour.workTimezoneCode || 'UTC').format('HH:mm');
        }
        this.log.actionInstant('CONTACT', 'VIDEOCALL_AGENT_AVAILABLE', {
          agentCount: agent.availableAgentCount,
          queueName,
          isWorkingHours: time && hour ? (time > hour.workStartTime && time < hour.workEndTime) : '',
          source
        });

        if (agent?.availableAgentCount === 0) {
          if (agent?.backupQueueName) {
            if (source === 'boomShop') {
              this.customerModuleService.openSparePartModule(
                this.store.selectSnapshot(UserState.currentCustomer)?.customer?.customerNumber);
            }
            this.startVideoCall(agent.backupQueueName);
            return;
          }
          this.loading(false);

          this.modalService.errorModal({
            message: this.translateService.instant('_videocall_not_have_available_text'),
            backdropClose: true,
          });
        } else if (agent) {
          this.loading(false);
          if (source === 'boomShop') {
            this.customerModuleService.openSparePartModule(this.store.selectSnapshot(UserState.currentCustomer)?.customer?.customerNumber);
          }
          this.customerModuleService.openLiveVideoByQueue(queueName, callType);
        }
        return null;
      }, () => {
        this.loading(false);
      });
  }

  protected loading(loading: boolean) {
    this.store.dispatch(new UpdateUserLoadingAction(loading));
  }


  protected availableAgent(queueName) {
    return this.videoCallService.getVideocallAvailableAgent({ queueName });
  }


  protected findWorkingHours(code) {
    const contactWorkingHours = this.store.selectSnapshot(SettingsState.contactWorkingHours);

    if (contactWorkingHours?.length > 0) {
      return contactWorkingHours.find((c) => c.code === code);
    }
    return null;
  }


}
