import { ApplicationRef, ComponentFactory, ComponentFactoryResolver, Injectable, Type, } from '@angular/core';
import { ErrorModalComponent } from '../component/error-modal/error-modal.component';
import { ModalComponent } from '../component/modal/modal.component';
import { Modal, ModalRef } from '../models/modal.model';

@Injectable({
  providedIn: 'root',
})
export class ModalService {
  private modalContainer: HTMLElement;
  private modalContainerFactory: ComponentFactory<ModalComponent>;

  protected errorModals: any = {};

  constructor(
    private componentFactoryResolver: ComponentFactoryResolver,
    private appRef: ApplicationRef
  ) {
    this.setupModalContainerFactory();
  }

  errorModal(
    data: {
    title?: string;
    message: string;
    translate?: boolean;
    backdropClose?: boolean;
    button?: string;
    buttonClick?: any;
  },
    container = null):
  // @ts-ignore:next-line
    ModalRef {
    if (this.errorModals[data.message]) {
      return null;
    }
    this.errorModals[data.message] = true;
    // ApplicationRef.tick fix
    setTimeout(() => {
      return this.open(ErrorModalComponent, { ...data, status: true }, container);
    }, 0);
  }

  open<T extends Modal>(component: Type<T>, inputs?: any, container = null): ModalRef {
    this.setupModalContainerDiv(container);

    const modalContainerRef = this.appRef.bootstrap(
      this.modalContainerFactory,
      this.modalContainer
    );

    const modalComponentRef = modalContainerRef.instance.createModal(component);

    if (inputs) {
      modalComponentRef.instance.onInjectInputs(inputs);
    }

    const modalRef: any = new ModalRef(modalContainerRef, modalComponentRef);
    modalRef.onResult().subscribe(() => {
      this.errorModals[modalRef?.modal?.instance?.message] = false;
      // console.log('::: TEST', (window as any).t = modalRef);
    });
    this.appRef.tick();
    return modalRef;
  }

  // Error modal not reOpening fix
  clearErrorModals() {
    this.errorModals = {};
    return null;
  }

  private setupModalContainerDiv(container = null): void {
    this.modalContainer = document.createElement('div');
    (container || document.getElementsByTagName('body')[0])
      .appendChild(this.modalContainer);
  }

  private setupModalContainerFactory(): void {
    this.modalContainerFactory = this.componentFactoryResolver.resolveComponentFactory(
      ModalComponent
    );
  }
}
