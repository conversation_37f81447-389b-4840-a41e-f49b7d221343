import { Injectable } from '@angular/core';
import { IframeStateModel } from '../../modules/customer/state/iframe/iframe.state';
import { CustomerState } from '../../modules/customer/state/customer/customer.state';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { v4 as uuidv4 } from 'uuid';
import { TranslateService } from '@ngx-translate/core';
import { environment } from '../../../environments/environment';
import { FrameMessageDataType, FrameMessageEnum } from '../../core/enum/frame-message.enum';
import { FrameMessageService } from '../../core/service/frame-message.service';

@Injectable({
  providedIn: 'root',
})
export class WindowManagerService {
  constructor(
    protected readonly store: Store,
    protected readonly router: Router,
    protected readonly translateService: TranslateService,
    protected readonly frameMessageService: FrameMessageService
  ) {}

  public openServiceForm(overrides = {}) {
    return this.openFrame({
      url: environment.frameUrl + '/form/request-service',
      pageTitle: this.translateService.instant('_service_request'),
      ...overrides
    });
  }

  openSparePartForm(overrides = {}) {
    return this.openFrame({
      url: environment.frameUrl + '/form/request-equipment-part',
      pageTitle: this.translateService.instant('_spare_part_request'),
      ...overrides
    });
  }

  public openFrame(module: Partial<IframeStateModel>) {
    const iframeAction = {
      // url: environment.frameUrl + '/form/request-equipment-part',
      params: {
        showHeader: false,
        // navigatedPage: 'Catalog',
      },
      active: true,
      closeButton: true,
      backButton: false,
      // pageTitle: item?.title,
      previous: {
        headerStatus: this.store.selectSnapshot(CustomerState.header),
        url: 'history.back'
      },
      ...module
    };
    // this.store.dispatch(new IframeAction(iframeAction, true));

    this.router.navigate(['module', uuidv4()], { state: { iframeAction } });
  }


  openOfferForm(messageData: any = {}) {
    // const data = {
    //   productId: messageData.product?.productDetail?.id,
    //   model: messageData.product?.productDetail?.name?.text,
    //   title: messageData.product?.productDetail?.name?.text,
    //   brand: messageData.product?.productDetail?.brand?.text,
    //   imageUrl: messageData.product?.marketingContents?.[0]?.imageUrl,
    // };

    const data = messageData.product;

    return this.router.navigate(['get-offer'], { state: { data } });
  }

  openInWebview(url, title) {
    this.openWebview({
      url,
      title
    });
  }

  openWebview(data: FrameMessageDataType<FrameMessageEnum.openModule>) {
    return this.frameMessageService.sendMessage(FrameMessageEnum.openModule, data);
  }


}
