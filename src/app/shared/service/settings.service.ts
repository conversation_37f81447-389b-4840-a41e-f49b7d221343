import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { HttpResponse } from 'src/app/core/interfaces/http.response';
import { environment } from 'src/environments/environment';
import { SettingsResponse, SocialMediaModel, SystemFeature } from '../../modules/customer/response/settings.response';
import { ContactWorkingHoursModel } from '../models/contact-working-hours.model';

@Injectable({
  providedIn: 'root',
})
export class SettingsService {
  constructor(
    private readonly http: HttpClient,
  ) {}

  getBasic(): Observable<SettingsResponse> {
    return this.http
      .get<HttpResponse<SettingsResponse>>(`${environment.api}/settings/basic`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  contactWorkingHours(countryCode = 'TR'): Observable<ContactWorkingHoursModel[]> {
    return this.http
      .get<HttpResponse<ContactWorkingHoursModel[]>>(`${environment.api}/settings/contactWorkingHours`, {
        params: { countryCode }
      })
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  systemFeatures(): Observable<SystemFeature[]> {
    return this.http
      .get<HttpResponse<SystemFeature[]>>(`${environment.api}/settings/systemFeatures`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getSocialMedia(){
    return this.http
      .get<HttpResponse<SocialMediaModel>>(`${environment.api}/settings/socialMediaAccounts`)
      .pipe(
        map((val) => {
          if (val.code === 0) {
            return val.data;
          }
          return null;
        })
      );
  }

  getBorusanBlockedActions(): Observable<string[]> {
    return this.http
      .get<HttpResponse<string[]>>(`${environment.api}/borusan/blockedactions`)
      .pipe(
        map( (res) => {
          if (res.code === 0) {
            return res.data;
          }
          return null;
        })
      );
  }

  GetUserPermissions(): Observable<string[]> {
    return this.http
      .get<HttpResponse<string[]>>(`${environment.api}/permission/roles`)
      .pipe(
        map( (res) => {
          if (res.code === 0) {
            return res.data;
          }
          return null;
        })
      );
  }
  iosMinAppVersion(): Observable<any> {
    return this.http.get<HttpResponse<any>>(`${environment.api}/settings/get?key=iosminappversion`).pipe(
      map(val => {
        if(val.code === 0){
          return val.data
        }
        return null
      })
    )
  }

  androidMinAppVersion(): Observable<any> {
    return this.http.get<HttpResponse<any>>(`${environment.api}/settings/get?key=androidminappversion`).pipe(
      map(val => {
        if(val.code === 0){
          return val.data
        }
        return null
      })
    )
  }
}
