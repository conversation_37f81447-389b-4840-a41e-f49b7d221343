import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { LoggerModel } from '../models/logger.model';
import { HttpResponse } from '../../core/interfaces/http.response';
import { environment } from '../../../environments/environment';
import { LoggerDataModel } from '../models/logger-data.model';
import { ConsoleErrorLoggerModel } from '../models/console-error-logger.model';

@Injectable({
  providedIn: 'root',
})
export class LoggerService {
  constructor(
    private readonly http: HttpClient,
  ) {}

  public cateventlog(logData: LoggerDataModel) {
    return this.http.post<HttpResponse<LoggerModel>>(
      `${environment.logUrl}-waf`,
      logData,
      {
        headers: { TOKEN_FREE: 'true' }
      }
    );
  }

  public jsErrorLog(logData: ConsoleErrorLoggerModel) {
    return this.http.post<HttpResponse<LoggerModel>>(
      `${environment.logUrl}-js-console`,
      logData,
      {
        headers: { TOKEN_FREE: 'true' }
      }
    );
  }
}
