import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'currencyIcon'
})
export class CurrencyIconPipe implements PipeTransform {

  transform(value: string): string {
    const currencyIcons = {
      TL: 'icon-tl',
      USD: 'icon-usd',
      EUR: 'icon-euro',
      RUBLE: 'icon-ruble',
      TENGE: 'icon-tenge',
      MANAT: 'icon-manat',
      LARI: 'icon-lari',
    };
    return currencyIcons[value] ? currencyIcons[value] : currencyIcons.USD;
  }

}
