import { SettingsStateModel } from './settings.state';

export class ContactWorkingHoursAction {
  public static readonly type = '[Common] contactWorkingHours';

  constructor() { }
}

export class SocialMediaAccountsAction {
  public static readonly type = '[Common] socialMedias';

  constructor() { }
}


export class SystemFeatureAction {
  public static readonly type = '[Common] systemFeature';

  constructor() { }
}

export class BorusanBlockedActionsAction {
  public static readonly type = '[Common] Borusan Blocked Actions';
  constructor() {}
}

export class GetMyUsersAction {
  public static readonly type = '[Common] myUsers';

  constructor() { }
}

export class UpdateMyUsersStateAction {
  public static readonly type = '[Common] update myUsers';

  constructor(public state: Partial<SettingsStateModel>) { }
}

export class GetBasisSettingsAction {
  public static readonly type = '[Common] basicSettings';

  constructor() { }
}

export class UserPermissionAction {
  public static readonly type = '[Common] User Permissions Actions';
  constructor() {}
}

export class ForceUpdateControllerAction {
  public static readonly type = '[Common] Force Update Controller';
  constructor(public OS: string) {}
}


