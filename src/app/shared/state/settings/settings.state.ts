import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import {
  BorusanBlockedActionsAction,
  ContactWorkingHoursAction,
  ForceUpdateControllerAction,
  GetBasisSettingsAction,
  GetMyUsersAction,
  SocialMediaAccountsAction,
  SystemFeatureAction,
  UpdateMyUsersStateAction,
  UserPermissionAction
} from './settings.actions';
import { ContactWorkingHoursModel } from '../../models/contact-working-hours.model';
import { SettingsService } from '../../service/settings.service';
import { Injectable } from '@angular/core';
import { UserState } from '../../../modules/customer/state/user/user.state';
import { SettingsResponse, SocialMediaModel, SystemFeature } from '../../../modules/customer/response/settings.response';
import { MyUsersModel } from 'src/app/modules/customer/model/my-users.model';
import { UpdateMyUsersModel } from 'src/app/modules/customer/model/update-users.model';
import { UserService } from 'src/app/modules/customer/service/user.service';
import { EditUserRolesModel } from 'src/app/modules/customer/model/edit-user-roles.model';
import { catchError, map, tap } from 'rxjs/operators';
import { throwError } from 'rxjs';


export interface SettingsStateModel {
  contactWorkingHours: ContactWorkingHoursModel[];
  systemFeatures: SystemFeature[];
  myUsers: MyUsersModel[];
  updateMyUsers: UpdateMyUsersModel[];
  myUsersLoading: boolean;
  editUserRoles: EditUserRolesModel[];
  socialMediaAccounts: SocialMediaModel;
  basic: SettingsResponse;
  borusanBlockedActions: string[];
  userPermission: string[];
  minAppVersion: string;
}

@State<SettingsStateModel>({
  name: 'settings',
  defaults: {
    contactWorkingHours: null,
    systemFeatures: [],
    myUsers: null,
    updateMyUsers: null,
    myUsersLoading: false,
    editUserRoles: [],
    socialMediaAccounts: null,
    basic: null,
    borusanBlockedActions: null,
    userPermission: null,
    minAppVersion: null
  }
})
@Injectable()
export class SettingsState {

  constructor(
    private readonly settingsService: SettingsService,
    private readonly store: Store,
    private readonly userService: UserService,
  ) {
  }

  @Selector()
  public static getState(state: SettingsStateModel) {
    return state;
  }

  @Selector()
  public static contactWorkingHours(
    { contactWorkingHours }: SettingsStateModel): ContactWorkingHoursModel[] {
    return contactWorkingHours;
  }

  @Selector()
  public static socialMedia(
    { socialMediaAccounts }: SettingsStateModel): SocialMediaModel {
    return socialMediaAccounts;
  }

  @Selector()
  public static systemFeatures(
    { systemFeatures }: SettingsStateModel): SystemFeature[] {
    return systemFeatures;
  }

  @Selector()
  public static myUsers(
    { myUsers }: SettingsStateModel): MyUsersModel[] {
    return myUsers;
  }

  @Selector()
  public static UpdateMyUsers(
    { updateMyUsers }: SettingsStateModel): UpdateMyUsersModel[] {
    return updateMyUsers;
  }

  @Selector()
  public static myUsersLoading(
    { myUsersLoading }: SettingsStateModel): boolean {
    return myUsersLoading;
  }

  @Selector()
  public static basic(
    { basic }: SettingsStateModel): SettingsResponse {
    return basic;
  }

  @Selector()
  public static borusanBlockedActions(
    { borusanBlockedActions }: SettingsStateModel): string[] {
    return borusanBlockedActions;
  }

  @Selector()
  public static userPermission(
    { userPermission }: SettingsStateModel): string[] {
    return userPermission;
  }

  @Selector()
  public static minAppVersion(
    { minAppVersion }: SettingsStateModel): string {
    return minAppVersion;
  }

  @Action(ContactWorkingHoursAction)
  public contactWorkingHours(
    { patchState }: StateContext<SettingsStateModel>) {

    const countryCode = this.store.selectSnapshot(UserState.currentCustomer)?.groupKey;
    this.settingsService.contactWorkingHours(countryCode).subscribe(workingHours => {
      return patchState({
        contactWorkingHours: workingHours
      });
    });

  }

  @Action(SocialMediaAccountsAction)
  public socialMediaAccountAction(
    { patchState }: StateContext<SettingsStateModel>
  ) {
    this.settingsService.getSocialMedia().subscribe((socialMediaAccounts) => {
      return patchState({
        socialMediaAccounts
      });
    });
  }

  @Action(SystemFeatureAction)
  public systemFeature(
    { patchState }: StateContext<SettingsStateModel>) {

    return this.settingsService.systemFeatures()
      .pipe(map(systemFeatures =>
        patchState({ systemFeatures })));
  }

  @Action(UpdateMyUsersStateAction)
  public UpdateMyUsersStateAction(
    { patchState }: StateContext<SettingsStateModel>,
    { state }: UpdateMyUsersStateAction
  ) {
    patchState(state);
  }

  @Action(GetMyUsersAction)
  getMyUsers(
    { patchState }: StateContext<SettingsStateModel>
  ) {
    patchState({
      myUsersLoading: true
    });
    return this.userService.getMyUsers().pipe(
      tap((value) => {
        patchState({
          myUsers: value,
          myUsersLoading: false,
        });
      }),
      catchError((err) => {
        patchState({
          myUsersLoading: false,
        });
        return throwError(err);
      }),
    );
  }

  @Action(GetBasisSettingsAction)
  public GetBasisSettingsAction(
    { patchState }: StateContext<SettingsStateModel>
  ) {
    this.settingsService.getBasic().subscribe(basic => {
      return patchState({
        basic
      });
    });
  }

  @Action(BorusanBlockedActionsAction)
  public BorusanBlockedActionsAction(
    { patchState }: StateContext<SettingsStateModel>,
  ) {
    this.settingsService.getBorusanBlockedActions().subscribe(res => {
      return patchState({
        borusanBlockedActions: res,
      });
    });
  }

  @Action(UserPermissionAction)
  public userPermissionAction(
    { patchState }: StateContext<SettingsStateModel>,
  ) {
    this.settingsService.GetUserPermissions().subscribe((res: any) => {
      return patchState({
        userPermission: res.permissions
      });
    });
  }

  @Action(ForceUpdateControllerAction)
  public forceUpdateControls(
    { patchState }: StateContext<SettingsStateModel>,
    { OS }: ForceUpdateControllerAction
  ) {
    this.settingsService[OS]().subscribe((res: any) => {
      return patchState({
        minAppVersion: res
      });
    });
  }

}

