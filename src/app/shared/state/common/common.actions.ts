import { AppStorageModel, CommonStateModel } from './common.state';
import { SanitizedCustomerModel } from '../../../modules/customer/model/sanitized-customer.model';
import { ChangeCustomerTokenModel } from '../../../modules/customer/model/user.model';

export class CommonStoreAction {
  public static readonly type = '[Common] set state';

  constructor(public payload: Partial<CommonStateModel>) { }
}
export class ShowPermissionErrorAction {
  public static readonly type = '[Common] showPermissionError';

  constructor(public data: any) { }
}

export class LanguageChangedAction {
  public static readonly type = '[Common] language changed state';

  constructor(public ott: string) { }
}

export class UpdateAppStorageAction {
  public static readonly type = '[Common] update App storage';

  constructor(public appStorage: Partial<AppStorageModel>) { }
}

export class SetAppStorageAction {
  public static readonly type = '[Common] set App storage';

  constructor(public appStorage: AppStorageModel) { }
}

export class SetMustChangePasswordAction {
  public static readonly type = '[Common] set App storage';

  constructor(public mustChangePassword: boolean) { }
}

export class OpenModuleAction {
  public static readonly type = '[Common] Open Module';

  constructor(public payload: { url: string, title: string }) { }
}
export class OpenStoreAction {
  public static readonly type = '[Common] Open Store';

  constructor(public payload: { url: string }) { }
}

export class OpenLinkAction {
  public static readonly type = '[Common] Open Link';

  constructor(public payload: { url: string }) { }
}

export class EmitCustomerChanged {
  public static readonly type = '[Common] CurrentCustomerChanged';

  constructor(public currentCustomer: SanitizedCustomerModel) { }
}

export class FinishDownloadAction {
  public static readonly type = '[Common] finish download';
  constructor(public data: []) { }
}

export class StartDownloadAction {
  public static readonly type = '[Common] start download';
  constructor(public data: []) { }
}

export class PaymentFinishedAction {
  public static readonly type = '[Common] payment finished';
  constructor(public data: []) { }
}

export class ChangeCustomerTokenAction {
  public static readonly type = '[Customer] customer token change';

  constructor(public changedCustomerToken: ChangeCustomerTokenModel) {
  }
}

export class PageOpenedAction {
  public static readonly type = '[Common] page opened';
  constructor(public data: any) { }
}

export class PageOpenedClearAction {
  public static readonly type = '[Common] page opened clear';
  constructor() { }
}


export class OfflineEquipmentData {
  public static readonly type = '[Common] offline equipment data';
  constructor(public data: any) { }
}

export class LeasingPlanAction {
  public static readonly type = '[Common] leasing calculate data'

  constructor(public data: any) { }
}

export class BackGroundFrameStatusAction {
  public static readonly type = '[Common] BackGroundFrameStatusAction'

  constructor(public status: string, public data: any = null) { }
}
