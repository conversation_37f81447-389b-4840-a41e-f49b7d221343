import { Action, Selector, State, StateContext, Store } from '@ngxs/store';
import {
  BackGroundFrameStatusAction,
  ChangeCustomerTokenAction,
  CommonStoreAction,
  EmitCustomerChanged,
  FinishDownloadAction,
  LanguageChangedAction,
  LeasingPlanAction,
  OfflineEquipmentData,
  OpenLinkAction,
  OpenModuleAction,
  OpenStoreAction,
  PageOpenedAction,
  PageOpenedClearAction,
  PaymentFinishedAction,
  SetAppStorageAction,
  SetMustChangePasswordAction,
  ShowPermissionErrorAction,
  StartDownloadAction,
  UpdateAppStorageAction,
} from './common.actions';
import { Injectable } from '@angular/core';
import { FrameMessageService } from '../../../core/service/frame-message.service';
import { FrameMessageEnum } from '../../../core/enum/frame-message.enum';
import { NotificationState } from '../../../modules/notification/state/notification/notification.state';
import { UserService } from '../../../modules/customer/service/user.service';
import { LoggerService } from '../../service/logger.service';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { UserPermissionAction } from '../settings/settings.actions';
import { GetUserAwaitingAgreements } from 'src/app/modules/customer/state/customer/customer.actions';
import { LogService } from '../../service/log.service';

export interface AppStorageModel {
  serviceWarningShowed: string[];
  currentCustomerTitle: string;
}

export interface TrackerUserModel {
  uuid: string;
}

export interface CommonStateModel {
  userAgent: string;
  timezoneId: string;
  version: string;
  mustChangePassword: boolean;
  currentRegion: string;
  countryCode: string;
  appStorage: AppStorageModel;
  languageOtt: string;
  updateModalShowed: boolean;
  showPermissionError: any;
  startDownload: [];
  finishDownload: [];
  paymentFinished: any;
  openPage: string;
  trackerUser: TrackerUserModel;
  publicMenuHeaderCompany: string;
  offlineEquipmentData: any;
  leasingPlanData: any;
  adid: string;
  adjustToken: string;
  storeVersion: string;
  backGroundFrameStatus: string;
  backGroundFrameData: any;
}

@State<CommonStateModel>({
  name: 'common_main',
  defaults: {
    userAgent: null,
    timezoneId: null,
    version: null,
    appStorage: null,
    languageOtt: null,
    mustChangePassword: false,
    currentRegion: null,
    countryCode: null,
    updateModalShowed: false,
    showPermissionError: null,
    startDownload: [],
    finishDownload: [],
    paymentFinished: null,
    openPage: null,
    trackerUser: null,
    publicMenuHeaderCompany: null,
    offlineEquipmentData: null,
    leasingPlanData: null,
    adid: null,
    adjustToken: null,
    storeVersion: null,
    backGroundFrameStatus: null,
    backGroundFrameData: null
  },
})
@Injectable()
export class CommonState {
  constructor(
    private readonly frameMessageService: FrameMessageService,
    private readonly store: Store,
    private readonly userService: UserService,
    private readonly logger: LoggerService,
    private readonly logService: LogService,
  ) { }

  @Selector()
  public static getState(state: CommonStateModel) {
    return state;
  }

  @Selector()
  public static userAgent({ userAgent }: CommonStateModel): string {
    return userAgent;
  }

  @Selector()
  public static mustChangePassword({
    mustChangePassword,
  }: CommonStateModel): boolean {
    return mustChangePassword;
  }

  @Selector()
  public static languageOtt({
    languageOtt,
  }: CommonStateModel): string {
    return languageOtt;
  }


  @Selector()
  public static timezoneId({ timezoneId }: CommonStateModel): string {
    return timezoneId;
  }

  @Selector()
  public static version({ version }: CommonStateModel): string {
    return version;
  }

  @Selector()
  public static offlineEquipmentData({ offlineEquipmentData }: CommonStateModel): any {
    return offlineEquipmentData;
  }

  @Selector()
  public static leasingPlanData({ leasingPlanData }: CommonStateModel): any {
    return leasingPlanData;
  }

  @Selector()
  public static trackerUser({ trackerUser }: CommonStateModel): TrackerUserModel {
    return trackerUser;
  }

  @Selector()
  public static updateModalShowed({ updateModalShowed }: CommonStateModel): boolean {
    return updateModalShowed;
  }

  @Selector()
  public static appStorage({ appStorage }: CommonStateModel): AppStorageModel {
    return appStorage;
  }

  @Selector()
  public static startDownload({ startDownload }: CommonStateModel): [] {
    return startDownload;
  }

  @Selector()
  public static finishDownload({ finishDownload }: CommonStateModel): [] {
    return finishDownload;
  }

  @Selector()
  public static showPermissionError({ showPermissionError }: CommonStateModel): any {
    return showPermissionError;
  }

  @Selector()
  public static countryCode({ countryCode }: CommonStateModel): string {
    return countryCode;
  }

  @Selector()
  public static publicMenuHeaderCompany({ publicMenuHeaderCompany }: CommonStateModel): string {
    return publicMenuHeaderCompany;
  }

  @Selector()
  public static currentRegion({ currentRegion }: CommonStateModel): string {
    return currentRegion;
  }

  @Selector()
  public static adid({ adid }: CommonStateModel): string {
    return adid;
  }

  @Selector()
  public static adjustToken({ adjustToken }: CommonStateModel): string {
    return adjustToken;
  }

  @Selector()
  public static backGroundFrameStatus({ backGroundFrameStatus }: CommonStateModel): string {
    return backGroundFrameStatus;
  }

  @Selector()
  public static backGroundFrameLastUrl({ backGroundFrameData }: CommonStateModel): string {
    return backGroundFrameData?.loadTimes[backGroundFrameData?.loadTimes?.length - 1];
  }

  @Selector()
  public static backGroundFrameData({ backGroundFrameData }: CommonStateModel): string {
    return backGroundFrameData;
  }

  @Selector()
  public static isActiveBackGroundFrame({ backGroundFrameStatus }: CommonStateModel): boolean {
    return backGroundFrameStatus === 'started';
  }

  @Action(CommonStoreAction)
  public add(
    { patchState }: StateContext<CommonStateModel>,
    { payload }: CommonStoreAction
  ) {
    patchState(payload);
  }

  @Action(SetMustChangePasswordAction)
  public SetMustChangePassword(
    { patchState }: StateContext<CommonStateModel>,
    { mustChangePassword }: SetMustChangePasswordAction
  ) {
    patchState({
      mustChangePassword,
    });
  }

  @Action(LanguageChangedAction)
  public LanguageChanged(
    { patchState }: StateContext<CommonStateModel>,
    { ott }: LanguageChangedAction
  ) {
    patchState({
      languageOtt: ott,
    });
  }

  @Action(ChangeCustomerTokenAction)
  public changeCustomerToken(
    { patchState }: StateContext<CommonStateModel>,
    { changedCustomerToken }: ChangeCustomerTokenAction
  ) {
    return this.userService.changeCustomerToken(changedCustomerToken);
  }

  @Action(SetAppStorageAction)
  public setAppStorage(
    { getState, patchState }: StateContext<CommonStateModel>,
    { appStorage }: SetAppStorageAction
  ) {
    patchState({
      appStorage,
    });
  }

  @Action(UpdateAppStorageAction)
  public updateAppStorage(
    { getState, patchState }: StateContext<CommonStateModel>,
    { appStorage }: UpdateAppStorageAction
  ) {
    const storage = { ...getState().appStorage, ...appStorage };
    patchState({
      appStorage: storage,
    });
    this.frameMessageService.sendMessage(FrameMessageEnum.appStorage, JSON.stringify({
      ...getState().appStorage,
    }));
  }

  @Action(OpenModuleAction)
  public openModule(
    { getState }: StateContext<CommonStateModel>,
    { payload }: OpenModuleAction
  ) {
    this.frameMessageService.sendMessage(FrameMessageEnum.openModule, payload);
  }


  @Action(OpenStoreAction)
  public openStore(
    { getState }: StateContext<CommonStateModel>,
    { payload }: OpenStoreAction
  ) {
    this.frameMessageService.sendMessage(FrameMessageEnum.openStore, payload);
  }

  @Action(OpenLinkAction)
  public openLink(
    { getState }: StateContext<CommonStateModel>,
    { payload }: OpenLinkAction
  ) {
    this.frameMessageService.sendMessage(FrameMessageEnum.openStore, payload);
  }

  @Action(EmitCustomerChanged)
  public emitCustomerChanged(
    { getState }: StateContext<CommonStateModel>,
    { currentCustomer }: EmitCustomerChanged
  ) {
    this.frameMessageService.sendMessage(FrameMessageEnum.userXottResponse, this.store.selectSnapshot(LoginState.user));
    // TODO ust satir silinicek
    this.frameMessageService.sendMessage(FrameMessageEnum.loginResponse, this.store.selectSnapshot(LoginState.loginResponse));
    this.frameMessageService.sendMessage(FrameMessageEnum.customerChanged, {
      ...currentCustomer,
      user: this.store.selectSnapshot(LoginState.user)
    });
    if (currentCustomer && !currentCustomer?.publicMenuHeaderCompany) {
      this.logger.jsErrorLog({
        message: 'Customer change event. publicMenuHeaderCompany is null.',
        name: 'publicMenuHeaderCompany null'
      });
    }
    this.store.dispatch(new UserPermissionAction());
    this.store.dispatch(new GetUserAwaitingAgreements('', true));
    const version = this.store.selectSnapshot(CommonState.version);
    const deviceToken = this.store.selectSnapshot(NotificationState.deviceToken);
    const countryCode = currentCustomer.countryCode;
    if (deviceToken) {
      this.store.dispatch(new ChangeCustomerTokenAction({
        Version: version,
        Token: deviceToken,
        CountryCode: countryCode,
      }));
    }
  }

  @Action(FinishDownloadAction)
  public FinishDownloadAction(
    { getState, patchState }: StateContext<CommonStateModel>,
    { data }: FinishDownloadAction
  ) {
    patchState({
      finishDownload: data,
    });
  }

  @Action(StartDownloadAction)
  public StartDownloadAction(
    { getState, patchState }: StateContext<CommonStateModel>,
    { data }: StartDownloadAction
  ) {
    patchState({
      startDownload: data,
    });
  }

  @Action(ShowPermissionErrorAction)
  public showPermissionError(
    { patchState }: StateContext<CommonStateModel>,
    { data }: ShowPermissionErrorAction
  ) {
    patchState({
      showPermissionError: data,
    });
  }

  @Action(PaymentFinishedAction)
  public PaymentFinishedAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: PaymentFinishedAction
  ) {
    patchState({
      paymentFinished: data,
    });
  }

  @Action(PageOpenedClearAction)
  public OpenPageClear(
    { patchState }: StateContext<CommonStateModel>
  ) {
    patchState({
      openPage: null,
    });
  }

  @Action(PageOpenedAction)
  public OpenPage(
    { patchState }: StateContext<CommonStateModel>,
    { data }: PageOpenedAction
  ) {
    this.frameMessageService.sendMessage(FrameMessageEnum.pageOpened, { page: data });
    patchState({
      openPage: data,
    });
  }

  @Action(OfflineEquipmentData)
  public OfflineEquipmentData(
    { patchState }: StateContext<CommonStateModel>,
    { data }: OfflineEquipmentData
  ) {
    patchState({
      offlineEquipmentData: data,
    });
  }

  @Action(LeasingPlanAction)
  public LeasingPlanAction(
    { patchState }: StateContext<CommonStateModel>,
    { data }: LeasingPlanAction
  ) {
    patchState({
      leasingPlanData: data,
    });
  }

  @Action(BackGroundFrameStatusAction)
  public backGroundFrameStatusAction(
    { patchState }: StateContext<CommonStateModel>,
    { status, data }: BackGroundFrameStatusAction
  ) {
    this.logService.log('BACKGROUND_FRAME', 'STATE_CHANGE', {
      backGroundFrameStatus: status,
      backGroundFrameData: data,
    }).subscribe();

    patchState({
      backGroundFrameStatus: status,
    });
    if (data) {
      patchState({
        backGroundFrameData: data,
      });
    }
  }
}
