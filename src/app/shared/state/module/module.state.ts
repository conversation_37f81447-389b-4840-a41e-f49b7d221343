import { Action, Selector, State, StateContext } from '@ngxs/store';
import { OpenInPCCAction, OpenSparePartModuleAction, } from './common.actions';
import { Injectable } from '@angular/core';
import { CustomerModuleService } from '../../../modules/customer/service/customer-module.service';

export interface MdouleStateModel {

}

@State<MdouleStateModel>({
  name: 'module_main',
  defaults: {},
})
@Injectable()
export class ModuleState {
  constructor(
    private readonly customerModuleService: CustomerModuleService,
  ) { }

  @Selector()
  public static getState(state: any) {
    return state;
  }

  @Action(OpenSparePartModuleAction)
  public openSparePartModuleAction(
    { patchState }: StateContext<MdouleStateModel>,
    { customerNumber, options }: OpenSparePartModuleAction
  ) {
    return this.customerModuleService.openSparePartModule(customerNumber, options);
  }

  @Action(OpenInPCCAction)
  public openInPCCAction(
    { patchState }: StateContext<MdouleStateModel>,
    { payload }: OpenInPCCAction
  ) {
    return this.customerModuleService.openInPCC(payload);
  }
}
