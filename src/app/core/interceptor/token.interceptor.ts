import {
  <PERSON><PERSON>p<PERSON><PERSON><PERSON>,
  HttpHeaderResponse,
  HttpHeaders,
  HttpInterceptor,
  HttpProgressEvent,
  HttpRequest,
  HttpResponse,
  HttpSentEvent,
  HttpUserEvent,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { LoginState } from '../../modules/authentication/state/login/login.state';
import { UserState } from '../../modules/customer/state/user/user.state';
import { CommonState } from '../../shared/state/common/common.state';
import { NotificationState } from '../../modules/notification/state/notification/notification.state';
import { browserLanguage } from '../../util/broser-language.util';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class TokenInterceptor implements HttpInterceptor {

  constructor(
    private readonly translateService: TranslateService,
    private readonly store: Store,
  ) {
  }

  intercept(
    request: HttpRequest<any>,
    next: HttpHand<PERSON>,
  ): Observable<HttpSentEvent | HttpHeaderResponse | HttpProgressEvent | HttpResponse<any> | HttpUserEvent<any> | any> {
    return next.handle(this.addTokenToRequest(request));
  }

  private addTokenToRequest(request: HttpRequest<any>): HttpRequest<any> {
    return request.clone({
      headers: this.addExtraHeaders(request.headers),
    });
  }

  private addExtraHeaders(headers: HttpHeaders): HttpHeaders {
    const token = this.store.selectSnapshot(LoginState.token);
    const headerCompanies = this.store.selectSnapshot(LoginState.headerCompanies);
    const customer = this.store.selectSnapshot(UserState.currentCustomer);
    const timezoneId = this.store.selectSnapshot(CommonState.timezoneId);
    const deviceToken = this.store.selectSnapshot(NotificationState.deviceToken);

    headers = headers.append('ReqId', uuidv4());

    if (headerCompanies) {
      headers = headers.append('HeaderCompanies', headerCompanies);
    }
    if (customer && customer?.customer?.id) {
      headers = headers.append('CustomerId', customer?.customer.id);
    }
    const lang = this.store.selectSnapshot(LoginState.language) || this.translateService.currentLang ||
      this.translateService.defaultLang || browserLanguage();

    if (lang) {
      headers = headers.append('Language', lang);
      headers = headers.append('Accept-Language', lang);
    }

    if (timezoneId) {
      headers = headers.append('TimezoneId', timezoneId);
    }
    if (deviceToken) {
      headers = headers.append('AppSessionId', deviceToken);
    }

    if (token) {
      if (!headers.get('TOKEN_FREE')) {
        headers = headers.append('Authorization', `Bearer ${token}`);
      } else {
        headers = headers.delete('TOKEN_FREE');
      }
    }
    if (headers.get('IgnoreHeaders')) {
      headers.get('IgnoreHeaders')
        .split(',')
        .map(ignore => headers = headers.delete(ignore));
      headers = headers.delete('IgnoreHeaders');
    }

    return headers;
  }
}
