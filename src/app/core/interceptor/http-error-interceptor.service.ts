import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  HttpHeaderResponse,
  HttpInterceptor,
  HttpProgressEvent,
  HttpRequest,
  HttpResponse,
  HttpSentEvent,
  HttpUserEvent,
} from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, filter, switchMap, take } from 'rxjs/operators';
import { AuthenticationService } from 'src/app/modules/authentication/service/authentication.service';
import { LoginState } from 'src/app/modules/authentication/state/login/login.state';
import { PostMessageAction } from 'src/app/modules/customer/state/iframe/iframe.actions';
import { ChangeTokenAction, ClearTokenAction, } from '../../modules/authentication/state/login/login.actions';
import { ModalService } from '../../shared/service/modal.service';
import { FrameMessageEnum } from '../enum/frame-message.enum';
import { IncomingMessageEnum } from '../enum/incoming-message.enum';
import { FrameMessageService } from '../service/frame-message.service';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(
    null
  );

  constructor(
    private readonly router: Router,
    private readonly store: Store,
    private readonly frameMessage: FrameMessageService,
    private readonly authenticationService: AuthenticationService,
    private readonly modalService: ModalService
  ) {}

  intercept(
    request: HttpRequest<any>,
    next: HttpHandler
  ): Observable<| HttpSentEvent
    | HttpHeaderResponse
    | HttpProgressEvent
    | HttpResponse<any>
    | HttpUserEvent<any>
    | any> {
    return next.handle(this.requestCheck(request)).pipe(
      catchError((error) => {
        if (error.status === 401 && !request.url.includes('refreshtoken')) {
          return this.handle401Error(request, next);
        }

        if (request.url.includes('refreshtoken')) {
          return throwError(error);
        }

        if (error.status === 403) {
          const errorBody = JSON.parse(error.error);

          this.modalService.errorModal({ message: errorBody.message });
          return throwError(error);
        }

        try {
          const body = JSON.parse(error.error);

          if (body.code !== undefined && body.code !== 0 && !request.headers.get('SkipError')) {
            this.modalService.errorModal({ message: body.message });
          }
        } catch (e) {
        }

        return throwError(error);
      })
    );
  }

  private requestCheck(request) {
    if (request.headers.get('SkipError')) {
      return request.clone({
        headers: request.headers.delete('SkipError')
      });
    }
    return request;

  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler) {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);
      const token = this.store.selectSnapshot(LoginState.token);
      const refreshToken = this.store.selectSnapshot(LoginState.refreshToken);
      return this.authenticationService.refreshToken(token, refreshToken).pipe(
        switchMap((res) => {
          this.isRefreshing = false;
          // Token Güncelle ve mobile yolla
          this.store.dispatch(new ChangeTokenAction(res, true));

          // 724 e Yolla
          this.store.dispatch(
            new PostMessageAction(IncomingMessageEnum.refreshTokenChanged, res)
          );
          this.refreshTokenSubject.next(res);
          return next.handle(request);
        }),
        catchError((error) => {
          this.redirectGetOtt();
          // return throwError(error);
          return of();
        })
      );
    } else {
      return this.refreshTokenSubject.pipe(
        filter((token) => token != null),
        take(1),
        switchMap(() => {
          return next.handle(request);
        })
      );
    }
  }

  redirectGetOtt(): void {
    this.frameMessage.sendMessage(FrameMessageEnum.unauthorised);
    this.store.dispatch(new ClearTokenAction());
  }
}
