import { HttpClient, HttpParams } from '@angular/common/http';
import { Injectable } from '@angular/core';
import * as moment from 'moment';
import { Observable } from 'rxjs';


@Injectable({ providedIn: 'root' })
export class CacheService {

  public static CACHE_DELIMITER = '_MAIN_';
  public static TRANSLATE_KEY = 'translates';
  public static LANGUAGE_KEY = 'languages';
  public static CURRENT_LANG_KEY = 'currentLang';
  public static CURRENT_USER_KEY = 'currentUser';
  private static cacheTimeMs = {
    translates: (
      60 * 1000
    ),
    languages: (
      60 * 1000
    ),
  };
  private static _REFRESH_HOUR_KEY_SUFFIX = 'RefreshHour';

  constructor(
    private http: HttpClient,
  ) {
  }


  static getCurrentLang(): string {
    return this.getLocalCache(CacheService.CURRENT_LANG_KEY);
  }

  public static getLocalCache(key: string, defaultObj = null): any {
    const cacheTime = this._getCacheTime(key);
    const parsedObject = JSON.parse(localStorage.getItem(key) || '[]');
    const cacheStartTime = localStorage.getItem(key + CacheService._REFRESH_HOUR_KEY_SUFFIX) || '0';
    if (parsedObject.length <= 0 || (
      cacheTime && moment().valueOf() - (+cacheStartTime) > cacheTime
    )) {
      return defaultObj;
    } else {
      return parsedObject;
    }
  }

  public static set(key: string, data: any) {
    if (typeof data !== 'undefined') {
      localStorage.setItem(key, JSON.stringify(data));
      localStorage.setItem(key + CacheService._REFRESH_HOUR_KEY_SUFFIX, moment().valueOf() + '');
    }
  }

  public static delete(key: string) {
    localStorage.removeItem(key);
    localStorage.removeItem(key + this._REFRESH_HOUR_KEY_SUFFIX);
  }

  private static _getCacheTime(key: string): any {
    let cacheTime = CacheService.cacheTimeMs[key];
    if (!cacheTime) {
      const newKey = key.substring(key.indexOf(CacheService.CACHE_DELIMITER) + CacheService.CACHE_DELIMITER.length);
      cacheTime = CacheService.cacheTimeMs[newKey];
    }
    return cacheTime;
  }

  get(key: string, remoteUrl: string, params?: HttpParams, resultGeneratorCall?): Observable<any> {
    return Observable.create(observer => {
      const parsedObject = CacheService.getLocalCache(key);
      if (!parsedObject) {
        this.http.get<any>(remoteUrl, { params }).subscribe((data) => {
          let result = data;
          if (resultGeneratorCall) {
            result = resultGeneratorCall(data);
          }
          CacheService.set(key, result);
          observer.next(result);
          observer.complete();
        }, error1 => {
          throw error1;
        });
      } else {
        observer.next(parsedObject);
        observer.complete();
      }
    });

  }

}
