import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { TranslateLoader } from '@ngx-translate/core';
import { Observable } from 'rxjs';

import { environment } from '../../../environments/environment';
import { CacheService } from './cache.service';

@Injectable({ providedIn: 'root' })
export class CustomTranslateLoader implements TranslateLoader {
  constructor(private readonly http: HttpClient) { }

  getTranslation(lang: string): Observable<any> {
    let key = '';
    return new Observable((observer) => {
      let prefix = '';
      if (lang != null) {
        prefix = lang + CacheService.CACHE_DELIMITER;
      }
      key = prefix + CacheService.TRANSLATE_KEY;
      this.getApiTranslate().subscribe(
        (translates) => {
          if (translates) {
            CacheService.set(key, translates);
            const translateObjects = JSON.parse(translates.data);
            observer.next(translateObjects);
            observer.complete();
          }
        },
        () => {
          console.log('Translate API Loading Error');
          const cacheTranslate = CacheService.getLocalCache(key);
          if (cacheTranslate) {
            const translateObjects = JSON.parse(cacheTranslate.data);
            console.log('Translate Cache Load: ', translateObjects);
            observer.next(translateObjects);
            observer.complete();
          } else {
            this.getLocalTranslate(key, lang).subscribe(
              (translates) => {
                console.log('Translate Local File Load: ', translates);
                observer.next(translates.data);
                observer.complete();
              }
            );
          }
        }
      );
    });
  }

  getApiTranslate(): Observable<any> {
    return this.http.get(`${environment.api}/localization/getmainlocalizations?t=` + new Date().getTime());
  }

  getLocalTranslate(key, lang): Observable<any> {
    return new Observable((observer) => {
      this.http.get(`${environment.language}/i18n/${lang}.json?t=` + new Date().getTime()).subscribe(
        (data) => {
          console.log('Translate Loaded Local...');
          const translateData = JSON.stringify(data);
          CacheService.set(key, { data: translateData });
          observer.next({ data });
          observer.complete();
        }
      );
    });
  }
}
