import { Injectable } from '@angular/core';
import { Store } from '@ngxs/store';
import { ChangeTokenAction } from 'src/app/modules/authentication/state/login/login.actions';
import { CloseIframeAction } from 'src/app/modules/customer/state/iframe/iframe.actions';
import { FrameMessageDataType, FrameMessageEnum, } from '../enum/frame-message.enum';
import { ActivatedRoute, NavigationExtras, Router } from '@angular/router';
import { v4 as uuidv4 } from 'uuid';
import { Survey } from '../../modules/definition/model/survey.model';
import { TranslateService } from '@ngx-translate/core';
import { NotificationDeleteAction } from '../../modules/notification/state/notification/notification.actions';
import {
  GetPccOrVlinkSurvey,
  HeaderStatusAction
} from '../../modules/customer/state/customer/customer.actions';
import { UserState } from '../../modules/customer/state/user/user.state';
import { OfflineEquipmentData } from 'src/app/shared/state/common/common.actions';
import { LoginState } from '../../modules/authentication/state/login/login.state';
import { OpenInPCCAction, OpenSparePartModuleAction } from '../../shared/state/module/common.actions';
import { CustomerState } from 'src/app/modules/customer/state/customer/customer.state';
import { switchMap } from 'rxjs/operators';

@Injectable({
  providedIn: 'root',
})
export class FrameMessageService {
  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly route: ActivatedRoute,
    private readonly translateService: TranslateService,
  ) { }

  public sendMessage<T extends FrameMessageEnum>(
    action: T,
    data: FrameMessageDataType<T> = null,
    append: any = {}
  ) {
    const message = {
      function: action,
      data,
      ...append
    };
    this.emitMessage(JSON.stringify(message));
  }

  public emitMessage(message) {
    const w: any = window;
    if (typeof w.Borusan !== 'undefined' && w.Borusan?.postMessage) {
      console.log('sent message to APP', message);
      w.Borusan.postMessage(message);
    } else if (w.flutter_inappwebview) {
      console.log('sent message to APP', message);
      w.flutter_inappwebview.callHandler('Borusan', message)
        .then(result => {
          console.log('message delivered');
        });
    } else if ((window as any).flutter_inappwebview) {
      (window as any).flutter_inappwebview.callHandler('Borusan', message)
        .then(result => {
          console.log('message delivered');
        });
    } else {
      console.log('sent message FAIL', message);
    }
  }

  public handleMessageFromIframe(message) {
    const check = this.checkInternalActions(message);
    if (check) {
      this.emitMessage(check);
    }
  }

  public checkInternalActions(message) {
    if (typeof message !== 'string') {
      return null;
    }

    let data;
    try {
      data = JSON.parse(message);
    } catch (e) {
      console.log(e.message);
      return null;
    }

    if (data && data.function) {
      switch (data.function) {
        case 'back_home':
          this.store.dispatch(new CloseIframeAction());
          return message;
        case 'headerStatus': {
          this.store.dispatch(new HeaderStatusAction(data.data));
          return null;
        }
        case 'changeToken':
          this.changeToken(data.data);
          return null; // prevent emit to app
        case 'openSurvey':
          this.openSurvey(data.data);
          // return null; // prevent emit to app
          break;
        case 'surveyDone':
          this.surveyDone();
          return null;
        case 'openSparePart':
          this.openSparePart(data.data);
          return null;
        case 'offlineEquipmentData':
          this.offlineEquipmentData(data);
          return null;
        case 'redirectMainPage':
          this.redirectMainPage(data.data);
          return null;
        case 'openInPCC':
          this.openInPCC(data.data);
          return null;
        case 'openVlSurvey':
          this.redirectPccOrVLSurveyPage(data.data);
          return null;
        case FrameMessageEnum.openCatalog:
          data.user = this.store.selectSnapshot(UserState.basics);
          return JSON.stringify(data);
        default:
          break;
      }
    }

    return message;
  }

  private changeToken(data) {
    // Tokenı değiştir
    this.store.dispatch(new ChangeTokenAction(data));
    // Mobile yolla // internal action yolluyor
    // this.sendMessage(FrameMessageEnum.changeToken, data);
  }

  public openSurvey(data: Partial<Survey>, messageId = null, backNotification = false) {
    // this.sendMessage(FrameMessageEnum.openModule, {
    //   url: data.smsLinkUrl,
    //   title: this.translateService.instant('_survey')
    // });

    const iframeAction: any = {
      url: data.smsLinkUrl + (data.smsLinkUrl.includes('?') ? '&' : '?') + `view=boom`,
      active: true,
      closeButton: false,
      backButton: true,
      pageTitle: this.translateService.instant('_survey'),
      withOtt: false,
    };


    const extras: NavigationExtras = { state: { iframeAction } };
    if (messageId) {
      extras.queryParams = { messageId };
    }
    if (backNotification) {
      extras.replaceUrl = true;
    }
    this.router.navigate(['module', uuidv4()], extras).then();
  }

  private surveyDone() {
    if (this.route.snapshot.queryParams.messageId) {
      this.store.dispatch(new NotificationDeleteAction(this.route.snapshot.queryParams.messageId));
    }

    setTimeout(() => {
      if (this.router.url.includes('module')) {
        history.back();
      }
    }, 3000);
  }

  offlineEquipmentData(data) {
    this.store.dispatch(new OfflineEquipmentData(data?.data?.equipment));
  }

  private openSparePart(data) {
    return this.store.dispatch(new OpenSparePartModuleAction(null, data));
  }

  private redirectMainPage(data: FrameMessageDataType<FrameMessageEnum.redirectMainPage>) {
    if (!data?.urlPath) {
      return;
    }
    this.router.navigate([data.urlPath], {
      queryParams: data?.urlParameters,
      queryParamsHandling: 'merge'
    }).then();
  }

  protected openInPCC(data) {
    this.store.dispatch(new OpenInPCCAction(data));
  }

  private redirectPccOrVLSurveyPage(
    data: FrameMessageDataType<FrameMessageEnum.redirectMainPage>
  ) {
    if (!data?.urlPath) {
      return;
    }

    this.store.dispatch(new GetPccOrVlinkSurvey({ position: 'VisionLinkEnd', getDetails: true }))
      .pipe(switchMap(() => this.store.selectOnce(CustomerState.pccOrVlinkSurvey)))
      .subscribe((survey) => {
        if (survey) {
          this.router.navigate([data.urlPath], {
            queryParams: { position: 'VisionLinkEnd' }
          });
        }
      });

  }

  startBackgroundFrame(data: any) {
    console.log('startFrame', FrameMessageEnum.startFrame);
    const lang = this.store.selectSnapshot(LoginState.language) || 'tr';

    this.sendMessage(FrameMessageEnum.startFrame, {
      finishPattern: `https://parts.cat.com/${lang}/borusancat`,
      timeout: 60000,
      ...data
    });
  }

  stopBackgroundFrame() {
    this.sendMessage(FrameMessageEnum.closeFrame);
  }

  closeSecondFrame() {
    this.sendMessage(FrameMessageEnum.closeSecondFrame, {
      notification: true,
      title: this.translateService.instant('_frame_close_title'),
      message: this.translateService.instant('_frame_close_message'),
      status: 'error',
    });
  }
}
