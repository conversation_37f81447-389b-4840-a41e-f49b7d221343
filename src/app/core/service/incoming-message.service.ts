import { Injectable } from '@angular/core';
import {
  NewBubblePushNotification,
  NewDeviceToken,
  NewPushNotification,
} from '../../modules/notification/state/notification/notification.actions';
import { Store } from '@ngxs/store';
import { IncomingMessageInterface } from '../interfaces/incoming-message.interface';
import { IncomingMessageEnum } from '../enum/incoming-message.enum';
import {
  BackGroundFrameStatusAction,
  CommonStoreAction,
  FinishDownloadAction,
  LanguageChangedAction,
  LeasingPlanAction,
  PaymentFinishedAction,
  SetAppStorageAction,
  ShowPermissionErrorAction,
  StartDownloadAction
} from '../../shared/state/common/common.actions';
import { PostMessageAction } from 'src/app/modules/customer/state/iframe/iframe.actions';
import {
  ChangeTokenAction,
  ClearCATSignInCode,
  LogoutProcessAction,
  SetLoginResponseAction,
  TrackerUserAction,
  UpdateLanguageCodeAction,
  UpdateLoadingAction,
} from 'src/app/modules/authentication/state/login/login.actions';
import { Router } from '@angular/router';
import { Navigate } from '@ngxs/router-plugin';
import { LoginResponseModel } from '../../modules/authentication/model/login-response.model';
import { CustomerModuleService } from '../../modules/customer/service/customer-module.service';
import { UserState } from '../../modules/customer/state/user/user.state';
import { filter, take } from 'rxjs/operators';
import { UserAction } from '../../modules/customer/state/user/user.actions';
import { Subject } from 'rxjs';
import { LogService } from '../../shared/service/log.service';
import {
  SidebarStatusAction,
  StaticFeedbackAction
} from '../../modules/customer/state/customer/customer.actions';
import { NotificationState } from '../../modules/notification/state/notification/notification.state';
import { NotificationActionTypeEnum } from '../../modules/notification/enum/notification-action-type.enum';
import { WindowManagerService } from '../../shared/service/window-manager.service';
import { VideoCallManagerService } from '../../shared/service/video-call-manager.service';
import { OpenLinkData } from '../interfaces/core-types.interface';
import { FrameMessageService } from './frame-message.service';
import { FrameMessageEnum } from '../enum/frame-message.enum';
import { SetLiteLoginResponse } from '../../modules/lite-user/state/lite-login.actions';
import { ModuleCodeEnum } from 'src/app/shared/enum/module-code.enum';
import { MobileInfoModel } from '../model/mobile-info.model';

@Injectable({
  providedIn: 'root',
})
export class IncomingMessageService implements IncomingMessageInterface {
  private handlers: Subject<any>[] | any = {};

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly customerModuleService: CustomerModuleService,
    private readonly log: LogService,
    private readonly windowManagerService: WindowManagerService,
    private readonly videoCallManagerService: VideoCallManagerService,
    private readonly messageService: FrameMessageService,
  ) { }

  getHandlerFunction() {
    return this.handlePostMessage.bind(this);
  }

  handlePostMessage(message) {
    console.log('RECEIVED MAIN message from APP ' + message);
    try {
      const event = JSON.parse(message);
      if (!event.type) {
        return;
      }
      if (event.type.startsWith('boom.')) {
        event.type = event.type.replace('boom.', 'boom');
      }

      if (!(event.type in IncomingMessageEnum)) {
        console.log('event not found');
        return;
      }

      const that = this;
      if (typeof that[event.type] === 'function') {
        that[event.type](event.data);
      } else {
        console.log('ERROR INCOMING EVENT NOT FOUND: ' + event.type);
      }
      // external handler
      if (this.handlers[event.type]) {
        this.handlers[event.type].next(event.data);
      }
    } catch (e) {
      console.log('handle message error: ' + e.message);
    }
  }

  public subscribe(event, callback) {
    this.observable(event).subscribe(callback);
  }

  public observable<T>(event: T): Subject<T> {
    if (!this.handlers[event]) {
      this.handlers[event] = new Subject<string | object>();
    }
    return this.handlers[event];
  }

  firebaseToken(data) {
    this.store.dispatch(new NewDeviceToken(data));
  }

  push(data) {
    console.log('###push notification ###' + JSON.stringify(data));

    const notification = NotificationState.normalizeNotification(data);
    if (notification.actionType === NotificationActionTypeEnum.SilentPush) {
      switch (notification.action.silentAction) {
        case 'MeRefresh':
          this.store.dispatch(new UserAction(false)); // user/me without loading
          break;
        case 'Logout':
          this.store.dispatch(new LogoutProcessAction(false));
          break;
      }
      return;
    }

    this.store.dispatch(new NewPushNotification(data));
  }

  pushBubble(data) {
    // console.log('###pushBubble notification ###' + JSON.stringify(data));
    this.store.dispatch(new NewBubblePushNotification(data));
  }

  userAgent(data) {
    this.store.dispatch(
      new CommonStoreAction({
        userAgent: data,
      })
    );
  }

  timezone(data) {
    this.store.dispatch(
      new CommonStoreAction({
        timezoneId: data,
      })
    );
  }

  version(data) {
    console.log('VERSION', data);
    this.store.dispatch(
      new CommonStoreAction({
        version: data,
      })
    );
  }

  trackerUser(data) {
    // console.log('INSIDER user', data);
    this.store.dispatch(
      new CommonStoreAction({
        trackerUser: data,
      })
    );
    this.store.dispatch(new TrackerUserAction(data));
  }

  protected mustChangePassword(data) {
    // console.log('MustChangePassword', data);
    if (data) {
      this.router.navigate(['settings', 'passwordchange']);
    }
    this.store.dispatch(
      new CommonStoreAction({
        mustChangePassword: data,
      })
    );
  }

  protected currentRegion(data) {
    console.log('currentRegion', data);
    this.store.dispatch(
      new CommonStoreAction({
        currentRegion: data,
      })
    );
  }

  protected countryCode(data) {
    console.log('countryCode', data);
    this.store.dispatch(
      new CommonStoreAction({
        countryCode: data,
      })
    );
  }

  protected publicMenuHeaderCompany(data) {
    console.log('publicMenuHeaderCompany', data);
    this.store.dispatch(
      new CommonStoreAction({
        publicMenuHeaderCompany: data,
      })
    );
  }

  mobileInfo(data: MobileInfoModel) {
    this.version(data.version);
    this.timezone(data.timezone);
    this.userAgent(data.userAgent);
    this.firebaseToken(data.firebaseToken);
    this.mustChangePassword(data.mustChangePassword);
    this.currentRegion(data.currentRegion);
    this.countryCode(data.countryCode);
    this.publicMenuHeaderCompany(data.publicMenuHeaderCompany);
    this.store.dispatch(
      new CommonStoreAction({
        adid: data.adid,
        adjustToken: data.adjustToken,
      })
    );
  }

  refreshTokenChanged(data) {
    // Tokenı değiştir
    this.store.dispatch(new ChangeTokenAction(data));
    // 724 e post yolla
    this.store.dispatch(
      new PostMessageAction(IncomingMessageEnum.refreshTokenChanged, data)
    );
  }

  open_module(module) {
    // this.router.navigate(['module'], { state: { module } });
    let data = module;
    this.log.action('DEEPLINK_ROUTE', 'INTERNAL', {
      module
    }).subscribe();
    if (typeof module === 'string') {
      const queryString = module.split('?')?.[1];
      const urlParams = new URLSearchParams(queryString);
      data = (Object as any).fromEntries(urlParams);
    }
    this.store.dispatch(new SidebarStatusAction(false));
    this.openLink({
      ...data,
      type: data.moduleCode ? 'module' : 'link',
      url: data.link,
    });
  }

  menuAction(data) {
    switch (data) {
      case 'contact':
        this.store.dispatch(new Navigate(['contact/menu']));
        break;
      case 'settings':
        this.store.dispatch(new Navigate(['settings/menu']));
        break;
      case 'logout':
        this.store.dispatch(new LogoutProcessAction());

        break;
    }
  }

  loginStarted(data) {
    console.log('loginStarted', data);
    this.store.dispatch(new UpdateLoadingAction(true));
  }

  loginResponse(data: { success: boolean; loginResponse: LoginResponseModel }) {
    if (data.success) {
      this.mustChangePassword(data.loginResponse.mustChangePassword);
      this.log.action('APP', 'OPEN');
      return this.store.dispatch(
        new SetLoginResponseAction(data.loginResponse)
      );
    }

    return this.store.dispatch(new UpdateLoadingAction(false));
  }

  liteLoginResponse(data: { success: boolean; loginResponse: LoginResponseModel }) {
    if (data.success) {
      this.log.action('APP', 'LITE USER OPEN');
      return this.store.dispatch([
        new SetLiteLoginResponse(data.loginResponse),
        new UpdateLanguageCodeAction(data?.loginResponse?.language)
      ]);
    }

    return this.store.dispatch(new UpdateLoadingAction(false));
  }

  appStorage(data) {
    if (data) {
      try { data = JSON.parse(data); } catch (e) { }
    }

    return this.store.dispatch(new SetAppStorageAction(data));
  }

  handleQR(data) {
    if (!this.store.selectSnapshot(UserState.username)) {
      console.log('Skipping link, waiting for user...');
      this.store
        .select(UserState.username)
        .pipe(
          filter((i) => !!i),
          take(1)
        )
        .subscribe((e) => {
          this.handleQR(data);
        });
      return;
    } else {
      console.log('handleQr::', data);
      if (data.includes('qr.cat.com')) {
        this.customerModuleService.handleOpenModuleLinkEvent({
          moduleCode: ModuleCodeEnum.CatQR,
          type: 'module',
          url: data
        });
      } else {
        this.messageService.sendMessage(FrameMessageEnum.closeQR);
      }
    }
  }

  openLink(data: OpenLinkData) {
    if (!this.store.selectSnapshot(UserState.username)) {
      console.log('Skipping link, waiting for user...');
      this.store
        .select(UserState.username)
        .pipe(
          filter((i) => !!i),
          take(1)
        )
        .subscribe((e) => {
          this.openLink(data);
        });
      return;
    }
    switch (data.type) {
      case 'module':
        this.customerModuleService.handleOpenModuleLinkEvent(data);

        break;
      default:
        if (data?.url) {
          // setTimeout(() => this.store.dispatch(new Navigate([data.url])), 100);
          setTimeout(() => this.router.navigateByUrl(data.url), 100);
        } else {
          return;
        }
    }
  }

  resumingApp(data) {
    console.log('resuming');
    // get me
    this.store.dispatch(new UserAction(false));
  }

  languageChanged(data) {
    this.store.dispatch(new LanguageChangedAction(data.ott));
  }

  permissionOk(data) {
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.permissionOk, data));
  }

  showPermissionError(data) {
    this.store.dispatch(new ShowPermissionErrorAction(data));
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.showPermissionError, data));
  }

  staticFeedbackClosed(data) {
    this.store.dispatch(new StaticFeedbackAction());

    this.router.navigate(['dashboard']);
  }

  feedbackClosed(data) {
    this.router.navigate(['dashboard']);
  }

  webviewReopened(data) {
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.webviewReopened, data));
  }

  soundRecordCollected(data) {
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.soundRecordCollected, data));
  }

  shopModuleClick(module) {
    this.router.navigate(['module', module.id], { state: { module } });
  }

  cat_open_form(data) {
    switch (data.code) {
      case 'get-offer':
        return this.windowManagerService.openOfferForm(data);
      case 'spare-parts-request':
        return this.windowManagerService.openSparePartForm();
      case 'construction-equipment-service':
      case 'power-system-service':
        return this.windowManagerService.openServiceForm();

    }
  }

  uploadedSoundID(data) {
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.uploadedSoundID, data));
  }

  boomlogPipe(data) {
    this.log.action(data.section, data.subsection, data.logData).subscribe();
  }


  boomlogEcommerce(data) {
    // console.log('handled',data);
    this.log.action(data.section, data.subsection, data.logData).subscribe();
  }

  currentLocation(data) {
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.currentLocation, data));
  }

  startDownload(data) {
    this.store.dispatch(new StartDownloadAction(data));
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.startDownload, data));
  }

  finishDownload(data) {
    this.store.dispatch(new FinishDownloadAction(data));
    // 724 e post yolla
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.finishDownload, data));
  }

  // refreshOtt(ott) {
  //  never used
  //   return this.store.dispatch(new LoginWithOttAction(ott));
  // }

  paymentFinished(data) {
    // paymentFinishedModel
    // { isPaymentClosedFromUser: boolean, isPaymentFinish: boolean, paymentWithError: boolean };
    // Ödeme webview kapatılma durumu
    this.store.dispatch(new PaymentFinishedAction(data));
    this.store.dispatch(new PostMessageAction(IncomingMessageEnum.paymentFinished, data));
  }

  systemFeatures(data) {
  }

  borusanBlockedAction() {
  }

  headerStatus() {}

  boomopenVideoCall(data: { source: string, sourceUrl?: string }) {
    const queue = 'DijitalBanko';
    this.videoCallManagerService.startVideoCall(queue, data.source);
  }

  boomopenTeklif(data: any): any {
    if (!data.contentType || data.contentType === '') {
      let type = data?.file?.indexOf(';base64') ? data?.file?.substring(0, data?.file?.indexOf(';base64')) : '';
      type = data?.file?.indexOf(':') ? type.substring(data?.file?.indexOf(':') + 1) : '';
      data.contentType = type;
    }
    return this.messageService.sendMessage(FrameMessageEnum.openTeklif, {
      filename: data.filename,
      contentType: data.contentType,
      file: data.file
    });
  }

  leasingPlans(data) {
    this.store.dispatch(new LeasingPlanAction({ isMobileLeasing: true, ...data }));
    this.router.navigate(['leasing']);
    this.messageService.sendMessage(FrameMessageEnum.openLeasingWebView);
  }

  frameFinished(data) {
    this.store.dispatch(new BackGroundFrameStatusAction('finished', data));
  }

  frameTimeout(data) {
    this.store.dispatch(new BackGroundFrameStatusAction('timeout', data));
  }

  catSigninError(data) {
    console.log('handled catSigninError on Frontend');
    this.store.dispatch(new BackGroundFrameStatusAction('error'));
    this.store.dispatch(new ClearCATSignInCode());
    this.messageService.closeSecondFrame();

    // setTimeout(() => this.customerModuleService.openPCCModule(), 500);
    this.customerModuleService.openPCCModule();
  }

  backgroundFlowError(data) {
    // BackgroundFlowType.samlFail
    // enum ErrorFlowType {
    //   samlFail,
    //   b2cLoginFail,
    //   catLoginFail,
    //   genericError,
    //   unknown,
    //   visionLinkAccessError,
    //   catSigninError,
    //   cwsIdError,
    // }

    this.store.dispatch(new BackGroundFrameStatusAction('error', data));
  }

}
