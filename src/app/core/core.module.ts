import { <PERSON>rro<PERSON><PERSON><PERSON><PERSON>, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { HttpErrorInterceptor } from './interceptor/http-error-interceptor.service';
import { TokenInterceptor } from './interceptor/token.interceptor';
import { FrameMessageService } from './service/frame-message.service';
import { ClientErrorInterceptorService } from './interceptor/client-error-interceptor.service';
import { CustomErrorHandlerService } from './custom-error.handler';
import { LoggerService } from '../shared/service/logger.service';

@NgModule({
  declarations: [],
  imports: [CommonModule],
  providers: [
    {
      provide: HTTP_INTERCEPTORS,
      useClass: HttpErrorInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: TokenInterceptor,
      multi: true,
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: ClientErrorInterceptorService,
      multi: true,
    },
    FrameMessageService,
    LoggerService,
    {
      provide: ErrorHandler,
      useClass: CustomErrorHandlerService
    },

  ],
})
export class CoreModule {}
