import { NotificationModel } from '../../modules/notification/model/notification.model';
import { NotificationActionTypeEnum } from '../../modules/notification/enum/notification-action-type.enum';
import { FeedbackModel } from '../../shared/models/customer.model';
import { SanitizedCustomerModel } from '../../modules/customer/model/sanitized-customer.model';
import { LiteUserModel } from 'src/app/modules/lite-user/model/lite-login.model';


export enum FrameMessageEnum {
  unauthorised = 'unauthorised',
  openModule = 'openModule',
  tokenChanged = 'tokenChanged',
  logout = 'logout',
  call_phone = 'call_phone',
  directionToMap = 'directionToMap',
  open_whatsapp = 'open_whatsapp',
  voice_record = 'voice_record',
  openTeklif = 'openTeklif',
  samlLoggedIn = 'samlLoggedIn',
  log = 'log',
  customerFinishLoad = 'customerFinishLoad',
  permissions = 'permissions',
  changeToken = 'changeToken',
  refreshTokenChanged = 'refreshTokenChanged',
  redirectMainPage = 'redirectMainPage',
  openStore = 'openStore',
  appStorage = 'appStorage',
  feedbackNotification = 'feedbackNotification',
  staticFeedback = 'staticFeedback',
  open_mail = 'open_mail',
  changeLanguage = 'changeLanguage',
  openCatalog = 'openCatalog',
  rate_us = 'rate_us',
  customerChanged = 'customerChanged',
  shareContent = 'shareContent',
  downloadFile = 'downloadFile',
  cancelDownloadFile = 'cancelDownloadFile',
  getMobileInfo = 'getMobileInfo',
  openAssistBox = 'openAssistBox',
  pageOpened = 'pageOpened',
  systemFeatures = 'systemFeatures',
  borusanBlockedAction = 'borusanBlockedAction',
  checkPermissions = 'checkPermissions',
  liteUserLogout = 'liteUserLogout',
  liteUserVerify = 'liteUserVerify',
  loginWithToken = 'loginWithToken',
  openMain = 'openMain',
  liteUserBack = 'liteUserBack',
  openOfflineMenu = 'openOfflineMenu',
  scanQr = 'scanQr',
  closeWebView = 'closeWebView',
  openLeasingWebView = 'openLeasingWebView',
  userXottResponse = 'userXottResponse',
  closeQR = 'closeQR',
  loginResponse = 'loginResponse',
  formBack = 'formBack',
  startFrame = 'startFrame',
  closeFrame = 'closeFrame', // close background frame
  closeSecondFrame = 'closeSecondFrame', // close spare part frame
}

export type FrameMessageDataType<T extends FrameMessageEnum> = T extends keyof FrameMessageData ? FrameMessageData[T] : any;

export interface FrameMessageData {
  [FrameMessageEnum.unauthorised]?: null;
  [FrameMessageEnum.logout]?: null;
  [FrameMessageEnum.call_phone]?: {
    phone: string;
  };
  [FrameMessageEnum.openModule]?: {
    url: string;
    title: string;
    user?: object;
    currentCustomer?: SanitizedCustomerModel,
    isSparePart?: boolean,
    listenEvent?: boolean,
    showProgress?: false,
    isPayment?: boolean,
    quotationNumber?: string,
    inspectionForm?: boolean,
    disableHeader?: boolean,
    backAction?: boolean,
    isInspection?: boolean,
    closeButton?: boolean,
    onCloseWarning?: boolean
  };
  [FrameMessageEnum.downloadFile]?: {
    url: string | ArrayBuffer
    id: string,
    isBase64?: boolean,
    extension?: string,
  };
  [FrameMessageEnum.directionToMap]?: {
    lng: string | number;
    lat: string | number;
  };
  [FrameMessageEnum.open_whatsapp]?: {
    phone: string;
  };

  [FrameMessageEnum.voice_record]?: {
    EquipmentNumber: string;
    SerialNumber: string;
    CustomerNumber: string;
    Source: string;
  };

  [FrameMessageEnum.openTeklif]?: {
    filename: string;
    contentType: string;
    file: any;
  };
  [FrameMessageEnum.log]?: any;
  [FrameMessageEnum.permissions]?: any[];
  [FrameMessageEnum.changeToken]?: any;
  [FrameMessageEnum.feedbackNotification]?: NotificationModel<NotificationActionTypeEnum.Feedback>;
  [FrameMessageEnum.staticFeedback]?: FeedbackModel;
  [FrameMessageEnum.changeLanguage]?: {
    lang: string;
  };
  [FrameMessageEnum.openStore]?: {
    url: string;
  };
  [FrameMessageEnum.shareContent]?: {
    url: string;
  };
  [FrameMessageEnum.redirectMainPage]?: {
    urlPath: string,
    urlParameters?: {},
  };
  [FrameMessageEnum.liteUserLogout]?: {
    data: Partial<LiteUserModel>
  };
  [FrameMessageEnum.liteUserVerify]?: {
    user: Partial<LiteUserModel>,
    verifyType: 'verifyMobilePhone' | 'verifyEmail'
  };
  [FrameMessageEnum.closeSecondFrame]?: {
    notification: boolean,
    title: string,
    message: string,
    status: 'error' | 'danger' | 'red' | 'warning' | 'yellow' | 'success' | 'green' | 'info' | 'blue',
  };
}
