export enum IncomingMessageEnum {
  push = 'push',
  pushBubble = 'pushBubble',
  firebaseToken = 'firebaseToken',
  userAgent = 'userAgent',
  timezone = 'timezone',
  version = 'version',
  mobileInfo = 'mobileInfo',
  open_module = 'open_module',
  menuAction = 'menuAction',
  loginResponse = 'loginResponse',
  liteLoginResponse = 'liteLoginResponse',
  loginStarted = 'loginStarted',
  appStorage = 'appStorage',
  openLink = 'openLink',
  resumingApp = 'resumingApp',
  languageChanged = 'languageChanged',
  refreshTokenChanged = 'refreshTokenChanged',
  permissionOk = 'permissionOk',
  showPermissionError = 'showPermissionError',
  staticFeedbackClosed = 'staticFeedbackClosed',
  feedbackClosed = 'feedbackClosed',
  webviewReopened = 'webviewReopened',
  shopModuleClick = 'shopModuleClick',
  cat_open_form = 'cat_open_form',
  uploadedSoundID = 'uploadedSoundID',
  boomlogPipe = 'boomlogPipe',
  boomlogEcommerce = 'boomlogEcommerce',
  currentLocation = 'currentLocation',
  startDownload = 'startDownload',
  finishDownload = 'finishDownload',
  paymentFinished = 'paymentFinished',
  trackerUser = 'trackerUser',
  systemFeatures = 'systemFeatures',
  borusanBlockedAction = 'borusanBlockedAction',
  headerStatus = 'headerStatus',
  boomopenVideoCall = 'boomopenVideoCall',
  boomopenTeklif = 'boomopenTeklif',
  soundRecordCollected = 'soundRecordCollected',
  leasingPlans = 'leasingPlans',
  handleQR = 'handleQR',
  frameFinished = 'frameFinished',
  frameTimeout = 'frameTimeout',
  catSigninError = 'frameTimeout',
}
