import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, RouterStateSnapshot, } from '@angular/router';
import { Observable } from 'rxjs';
import { Store } from '@ngxs/store';
import { LoginState } from '../../modules/authentication/state/login/login.state';
import { FrameMessageService } from '../service/frame-message.service';
import { FrameMessageEnum } from '../enum/frame-message.enum';

@Injectable({ providedIn: 'root' })
export class AuthGuard implements CanActivate {
  constructor(
    private store: Store,
    private frameMessageService: FrameMessageService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {
    if (this.store.selectSnapshot(LoginState.loginLoading) === true) {
      return true;
    }

    if (route.queryParams.ott) {
      return true;
    }
    const token = this.store.selectSnapshot(LoginState.token);

    if (token) {
      return true;
    }

    this.frameMessageService.sendMessage(FrameMessageEnum.unauthorised);

    return false;
  }
}
