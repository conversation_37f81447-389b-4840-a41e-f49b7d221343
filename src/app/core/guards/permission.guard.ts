import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  UrlTree,
  Router,
} from '@angular/router';
import { Store } from '@ngxs/store';
import { Observable } from 'rxjs';
import { CommonState } from 'src/app/shared/state/common/common.state';

@Injectable({
  providedIn: 'root',
})
export class PermissionGuard implements CanActivate {
  constructor(private store: Store, private router: Router) {}

  canActivate(
    childRoute: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ):
    | Observable<boolean | UrlTree>
    | Promise<boolean | UrlTree>
    | boolean
    | UrlTree {
    if (state.url.includes('passwordchange')) {
      return true;
    }

    const mustChangePassword = this.store.selectSnapshot(
      CommonState.mustChangePassword
    );

    if (mustChangePassword) {
      this.router.navigate(['settings', 'passwordchange'], { replaceUrl: true });
    }

    return !mustChangePassword;
  }
}
