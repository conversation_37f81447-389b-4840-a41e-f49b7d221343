import { SanitizedCustomerModel } from '../../modules/customer/model/sanitized-customer.model';

export interface FrameOptionsModel {
  url: string;
  title: string;
  user?: object;
  currentCustomer?: SanitizedCustomerModel;
  isSparePart?: boolean;
  listenEvent?: boolean;
  showProgress?: false;
  isPayment?: boolean;
  quotationNumber?: string;
  inspectionForm?: boolean;
  disableHeader?: boolean;
  backAction?: boolean;
  isInspection?: boolean;
  closeButton?: boolean;
  onCloseWarning?: boolean;
  loadingPage?: boolean;
  languageKey?: string;
}
