import { <PERSON>rror<PERSON><PERSON>ler, Injectable } from '@angular/core';
import { LoggerService } from '../shared/service/logger.service';
import { Router } from '@angular/router';
import { environment } from '../../environments/environment';

@Injectable()
export class CustomErrorHandlerService extends ErrorHandler {

  constructor(
    private logger: LoggerService,
    private router: Router
  ) {
    super();
  }

  handleError(error) {
    const logMessage = {
      message: error.message,
      name: error.name,
      url: window.location.href,
      stack: error.stack,
    };
    console.log('error handled:', logMessage);
    if (environment.production && error.name !== 'HttpErrorResponse') {
      this.logger.jsErrorLog(logMessage).subscribe();
    }

    super.handleError(error);
  }
}
