import { BrowserModule } from '@angular/platform-browser';
import { NgModule } from '@angular/core';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './container/app/app.component';
import { StoreModule } from './store.module';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { NgxsRouterPluginModule } from '@ngxs/router-plugin';
import { TranslateLoader, TranslateModule } from '@ngx-translate/core';
import { CustomTranslateLoader } from './core/service/custom-translate-loader';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { CustomerModule } from './modules/customer/customer.module';
import { SharedModule } from './shared/shared.module';
import { CoreModule } from './core/core.module';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { CustomerComponent } from './container/customer/customer.component';
import { LogoutComponent } from './container/logout/logout.component';
import { ConnectDialogComponent } from './shared/component/connect-dialog/connect-dialog.component';
import { RouteReuseStrategy } from '@angular/router';
import { CustomReuseStrategy } from './shared/custom-reuse.strategy';
import { BorusanUserModule } from './modules/borusan-user/borusan-user.module';
import { PromotionModule } from './modules/promotion/promotion.module';

@NgModule({
  declarations: [
    AppComponent,
    CustomerComponent,
    LogoutComponent,
    ConnectDialogComponent
  ],
  imports: [
    BrowserModule,
    BrowserAnimationsModule,
    AppRoutingModule,
    CoreModule,
    StoreModule,
    HttpClientModule,
    NgxsRouterPluginModule.forRoot(),
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: ServerTranslateFactory,
        deps: [HttpClient],
      },
    }),

    NgbModule,
    CustomerModule,
    SharedModule,
    BorusanUserModule,
    PromotionModule
  ],
  providers: [
    { provide: RouteReuseStrategy, useClass: CustomReuseStrategy }
  ],
  bootstrap: [AppComponent],
})
export class AppModule {}

export function ServerTranslateFactory(
  http: HttpClient,
): TranslateLoader {

  return new CustomTranslateLoader(http);
}

