import { NgModule } from '@angular/core';
import { NgxsModule } from '@ngxs/store';
import { LoginState } from './modules/authentication/state/login/login.state';
import { environment } from '../environments/environment';
import { NgxsStoragePluginModule } from '@ngxs/storage-plugin';
import { UserState } from './modules/customer/state/user/user.state';
import { CustomerState } from './modules/customer/state/customer/customer.state';
import { IframeState } from './modules/customer/state/iframe/iframe.state';
import { NgxsReduxDevtoolsPluginModule } from '@ngxs/devtools-plugin';
import { CatalogState } from './modules/customer/state/catalog/catalog.state';
import { DefinitionState } from './modules/definition/state/definition/definition.state';
import { ContactState } from './modules/customer/state/contact/contact.state';
import { CompanyState } from './modules/customer/state/company/company.state';
import { NotificationState } from './modules/notification/state/notification/notification.state';
import { CommonState } from './shared/state/common/common.state';
import { SettingsState } from './shared/state/settings/settings.state';
import { BorusanUserState } from './modules/borusan-user/state/borusan-user.state';
import { PromotionState } from './modules/promotion/state/promotion.state';
import { FormState } from './modules/customer/state/form/form.state';
import { VideocallState } from './modules/customer/state/videocall/videocall.state';
import { LiteLoginState } from './modules/lite-user/state/lite-login.state';
import { ModuleState } from './shared/state/module/module.state';

@NgModule({
  imports: [
    NgxsModule.forRoot(
      [
        LoginState,
        UserState,
        CustomerState,
        ContactState,
        IframeState,
        CatalogState,
        DefinitionState,
        CompanyState,
        NotificationState,
        CommonState,
        SettingsState,
        BorusanUserState,
        PromotionState,
        FormState,
        VideocallState,
        LiteLoginState,
        ModuleState,
      ],
      {
        developmentMode: !environment.production,
      }
    ),
    NgxsStoragePluginModule.forRoot({
      key: [
        'login_main',
        'common_main.timezoneId',
        'common_main.version',
        'common_main.appStorage',
        'notification_main.deviceToken',
      ],
      deserialize: (state) => {
        // for UTF-8 chars, decode is mandatory
        return JSON.parse(decodeURIComponent(escape(atob(state))));
      },
      serialize: (state) => {
        return btoa(unescape(encodeURIComponent(JSON.stringify(state))));
      },
    }),
    NgxsReduxDevtoolsPluginModule.forRoot({
      disabled: environment.production,
      name: 'Borusan Mainui',
    }),
  ],
  exports: [NgxsModule],
})
export class StoreModule {}
