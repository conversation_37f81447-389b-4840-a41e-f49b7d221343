import { Component, ElementRef, HostBinding, HostListener, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { fromEvent, merge, Observable } from 'rxjs';
import { mapTo } from 'rxjs/operators';
import { Location, registerLocaleData } from '@angular/common';
import { Select, Store } from '@ngxs/store';
import { HeaderStatusMainAction } from '../../modules/customer/state/customer/customer.actions';
import { LoginState } from '../../modules/authentication/state/login/login.state';
import { IncomingMessageService } from '../../core/service/incoming-message.service';
import { IncomingMessageEnum } from '../../core/enum/incoming-message.enum';
import { NgSelectConfig } from '@ng-select/ng-select';
import { stopLoadingAnimation } from '../../util/stop-loading-animation.util';
import { environment } from 'src/environments/environment';
import { Router } from '@angular/router';
import { browserLanguage } from '../../util/broser-language.util';
import { DisableDocumentScrollDirective } from 'src/app/shared/directive/document-search.directive';
import localeTr from '@angular/common/locales/tr';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit {
  @HostBinding('attr.appDisableDocumentScroll') disableScrollDirective = new DisableDocumentScrollDirective(this.elementRef);

  @Select(LoginState.loginLoading)
  loginLoading$: Observable<boolean>;

  @Select(LoginState.language)
  language$: Observable<string>;

  private connectionMonitor$: Observable<boolean>;
  connectionStatus = false;

  constructor(
    private readonly translateService: TranslateService,
    private readonly location: Location,
    private readonly store: Store,
    private readonly incomingMessageService: IncomingMessageService,
    private readonly selectConfig: NgSelectConfig,
    private readonly router: Router,
    private elementRef: ElementRef
  ) {
    const offline$ = fromEvent(window, 'offline').pipe(mapTo(false));
    const online$ = fromEvent(window, 'online').pipe(mapTo(true));
    this.connectionMonitor$ = merge(offline$, online$);
    this.incomingMessageService.subscribe(IncomingMessageEnum.resumingApp, data => {
      this.connectionStatus = !window.navigator.onLine;
    });
    this.managePostMessage();
    this.disableScrollDirective.searchInputOnFocus();
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event) {
    if (this.location.path() === '/dashboard') {
      console.log('Back button pressed, returned to Dashboard', this.location.path());
      this.store.dispatch(new HeaderStatusMainAction());
    }

    event.preventDefault();
    event.stopPropagation();
  }

  ngOnInit(): void {
    this.boomControl();
    console.log('APP started');
    stopLoadingAnimation();
    registerLocaleData(localeTr, 'tr-TR');
    this.translateService.setDefaultLang(this.store.selectSnapshot(LoginState.language) || browserLanguage());
    this.translateService.get('_no_items_found').subscribe(trns => {
      this.selectConfig.notFoundText = trns;
    });

    this.language$.subscribe((languageCode) => {
      if (languageCode) {
        this.translateService.use(languageCode);
        this.translateService.get('_no_items_found').subscribe(trns => {
          this.selectConfig.notFoundText = trns;
        });
      }
    });

    this.connectionMonitor$.subscribe(status => {
      this.connectionStatus = !status;
      console.log('CONNECTION_STATUS: ' + status);
    });
  }

  private managePostMessage() {
    const w = window as any;
    w.handlePostMessage = this.incomingMessageService.getHandlerFunction();
    if (typeof w.postMessageQueue !== 'undefined' && w.postMessageQueue?.length > 0) {
      while (w.postMessageQueue.length) {
        this.incomingMessageService.handlePostMessage(w.postMessageQueue.shift());
      }
    }
  }

  private boomControl() {
    const isLocal = ['local', 'development'].indexOf(environment.envName) !== -1;
    // const isLocal = ['local'].indexOf(environment.envName) !== -1;
    if (isLocal) {
      return;
    }

    const nonBOOM = !(window.navigator.userAgent.indexOf('BOOM') !== -1);
    if (nonBOOM) {
      console.log('NON-BOOM Redirected...');
      this.router.navigateByUrl('/', { skipLocationChange: true })
        .then(() => this.router.navigate(['non-boom']));
    }
  }
}
