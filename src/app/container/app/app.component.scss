.order-form {
  .form-item {
    display: block;
    margin-bottom: 15px;
    input[type=text],
    input[type=number] {
      width: 100%;
      background: #ECF2F4;
      border: 1px solid #D7E5EA;
      border-radius: 8px;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      box-sizing: border-box;
    }
    textarea {
      width: 100%;
      background: #ECF2F4;
      border: 1px solid #D7E5EA;
      border-radius: 8px;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      box-sizing: border-box;
      min-height: 100px;
      font-family: 'Poppins', sans-serif;
    }
    select {
      width: 100%;
      padding:15px 20px;
      color: #505050;
      font-size: 16px;
      border-radius: 8px;
      border: 1px solid #D7E5EA;
      background: #ECF2F4;
      font-family: 'Poppins', sans-serif;
    }
    .legal-text {
      font-size: 12px;
      line-height: 15px;
      max-height: 100px;
      overflow: auto;
      margin: 20px 0;
    }
    .confirmation {
      width: 100%;
      display: inline-block;
      .cb-area {
        float:left;
        width:30px;
        input {
          width:20px;
          height: 20px;
          border-radius: 8px;
          border: 2px solid #868686;
          position: relative;
          top:2px;
        }
      }
      .legal-text-area {
        float:left;
        width: calc(100% - 45px);
        font-size: 12px;
        line-height: 15px;
      }
    }
    .phone {
      font-weight: bold;
      margin: 20px 0;
    }
    .btn {
      background: #FFA300;
      padding: 15px;
      border-radius: 4px;
      font-weight: bold;
      width: 100%;
      color: #fff;
      font-size: 16px;
    }

    .field-error {
      display: block;
      color: red;
      margin-top:5px;
    }
  }
}
