import {
  AfterContentChecked,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  On<PERSON><PERSON>roy,
  OnInit
} from '@angular/core';
import { Select, Store } from '@ngxs/store';
import { LoginState } from '../../modules/authentication/state/login/login.state';
import { combineLatest, Observable, Subject, timer } from 'rxjs';
import { UserState } from '../../modules/customer/state/user/user.state';
import { LoaderComponent } from '../../shared/component/loader/loader.component';
import { TranslateService } from '@ngx-translate/core';
import { CatalogState } from '../../modules/customer/state/catalog/catalog.state';
import { AgreementModel, ApprovalAgreementModel } from '../../modules/definition/model/agreement.model';
import { delay, take, takeUntil, tap } from 'rxjs/operators';
import { FederatedService } from '../../modules/customer/service/federated.service';
import { systemFeature } from '../../util/system-feature.util';
import { SettingsState } from '../../shared/state/settings/settings.state';
import { SystemFeature } from '../../modules/customer/response/settings.response';
import { CommonState } from '../../shared/state/common/common.state';

@Component({
  selector: 'app-customer',
  templateUrl: './customer.component.html',
  styleUrls: ['./customer.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CustomerComponent implements OnInit, AfterContentChecked, OnDestroy {
  protected subscriptions$: Subject<boolean> = new Subject();

  title = 'mainui';

  @Select(LoginState.language)
  language$: Observable<string>;
  loading = true;

  @Select(UserState.userLoading)
  userLoading$: Observable<boolean>;

  @Select(UserState.agreements)
  agreements$: Observable<AgreementModel[]>;

  @Select(UserState.approvalAgreements)
  approvalAgreements$: Observable<ApprovalAgreementModel[]>;

  @Select(CatalogState.loading)
  catalogLoading$: Observable<boolean>;

  @Select(SettingsState.systemFeatures)
  systemFeatures$: Observable<SystemFeature[]>;
  systemFeatureCatPcc = false;
  private systemBackGroundLogin = false;

  public template = new LoaderComponent();

  constructor(
    private readonly translateService: TranslateService,
    private readonly cdRef: ChangeDetectorRef,
    private readonly federatedService: FederatedService,
    private readonly store: Store,
  ) {
  }

  ngOnInit(): void {
    this.language$.subscribe((languageCode) => {
      if (languageCode) {
        console.log('language changed: ' + languageCode);
        // this.translateService.setDefaultLang(languageCode);
        this.translateService.use(languageCode);
      }
    });
    combineLatest([this.userLoading$, this.catalogLoading$])
      .subscribe((value) => {
        this.loading = value.reduce((acc, item) => acc || item, false);
      });
    this.systemFeatures$
      .pipe(takeUntil(this.subscriptions$))
      .subscribe(features => {
        if (features) {
          this.systemFeatureCatPcc = systemFeature('cat_pcc', features, false);
          this.systemBackGroundLogin = systemFeature('background_login', features, false);
        }
      });

    if (this.store.selectSnapshot(LoginState.isSAMLLogin)) {
      timer(10000, 30 * 60000)
        .pipe(take(10)) // TODO restart appResuming
        .pipe(takeUntil(this.subscriptions$))
        .pipe(tap(() => {
            if (this.store.selectSnapshot(CommonState.isActiveBackGroundFrame)) {
              this.federatedService.stopBackgroundFrame();
            }
          }),
        )
        .pipe(delay(3000))
        .subscribe((reloginCount) => {
          if (this.systemFeatureCatPcc && this.systemBackGroundLogin) {
            this.federatedService.startBackgroundFrame(reloginCount);
          }
        });
    }

  }


  ngAfterContentChecked() {
    this.cdRef.detectChanges();
  }

  ngOnDestroy() {
    this.subscriptions$.next(true);
    this.subscriptions$.complete();
  }
}
