import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Store } from '@ngxs/store';
import { HeaderStatusAction } from 'src/app/modules/customer/state/customer/customer.actions';
import { LogService } from 'src/app/shared/service/log.service';
import { LoggerService } from 'src/app/shared/service/logger.service';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-non-boom',
  templateUrl: './non-boom.component.html',
  styleUrls: ['./non-boom.component.scss']
})
export class NonBoomComponent implements OnInit {

  data: any;
  warningIcon = `${environment.assets}/warning.svg`;
  playStore =  `${environment.assets}/badges/playstore.svg`;
  appStore =  `${environment.assets}/badges/appstore.svg`;

  constructor(
    private readonly store: Store,
    private readonly router: Router,
    private readonly logger: LoggerService,
    private readonly log: LogService,
  ) { }

  ngOnInit() {
    this.store.dispatch(
      new HeaderStatusAction({
        company: false,
        backButton: false,
        closeButton: false,
        hamburgerMenu: false,
        notificationIcon: false,
      })
    );
    this.boomControl();
  }

  private boomControl() {
    const isLocalDev = ['local', 'development'].indexOf(environment.envName) !== -1;
    const isBOOM = window.navigator.userAgent.indexOf('BOOM') !== -1;
    if (isBOOM) {
      console.log('BOOM Redirected...');
      // BOOM Redirect ERROR
      if (!isLocalDev) {
        this.boomErrorSend();
      }
      // Return redirect dashboard page
      this.router.navigateByUrl('/', {skipLocationChange: true}).then(() =>
      this.router.navigate(['dashboard']));
    }
    if (!isBOOM){
      this.log.log('NON_BOOM', 'NON_BOOM_REDIRECT', {
        message: 'The portaluimobile page was accessed outside of the BOOM360 application.',
        name: 'Access Outside the BOOM360 App',
        url: window.location.href,
      }).subscribe();
    }
  }

  private boomErrorSend(){
    const logMessage = {
      message: 'The portaluimobile/non-boom page was accessed inside of the BOOM360 application.',
      name: 'The BOOM360 App redirected non-boom',
      url: window.location.href,
    };
    this.logger.jsErrorLog(logMessage).subscribe();
  }

}
