@import "variable/icon-variable";

@font-face {
  font-family: "#{$cat-icon-font-family}";
  src: url("#{$cat-icon-font-path}/#{$cat-icon-font-family}.ttf?t20250728")
      format("truetype"),
    url("#{$cat-icon-font-path}/#{$cat-icon-font-family}.woff?t20250728")
      format("woff"),
    url("#{$cat-icon-font-path}/#{$cat-icon-font-family}.svg?t20250728##{$cat-icon-font-family}")
      format("svg");
  font-weight: normal;
  font-style: normal;
  font-display: block;
}

.icon {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: "#{$cat-icon-font-family}" !important;
  speak: never;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-filter {
  &:before {
    content: $icon-filter;
  }
}
.icon-direction {
  &:before {
    content: $icon-direction;
  }
}
.icon-whatsapp {
  &:before {
    content: $icon-whatsapp;
    color: #27d00a;
  }
}
.icon-edit {
  &:before {
    content: $icon-edit;
  }
}
.icon-headset {
  &:before {
    content: $icon-headset;
    color: #505050;
  }
}
.icon-padlock {
  &:before {
    content: $icon-padlock;
    color: #505050;
  }
}
.icon-phone-call {
  &:before {
    content: $icon-phone-call;
  }
}
.icon-boom-logo {
  &:before {
    content: $icon-boom-logo;
    color: #505050;
  }
}
.icon-pin {
  &:before {
    content: $icon-pin;
    color: #505050;
  }
}
.icon-search {
  &:before {
    content: $icon-search;
  }
}
.icon-back {
  &:before {
    content: $icon-back;
  }
}
.icon-chevron-down {
  &:before {
    content: $icon-chevron-down;
  }
}
.icon-chevron-right {
  &:before {
    content: $icon-chevron-right;
  }
}
.icon-contact {
  &:before {
    content: $icon-contact;
  }
}
.icon-contract {
  &:before {
    content: $icon-contract;
  }
}
.icon-hamburger {
  &:before {
    content: $icon-hamburger;
  }
}
.icon-language {
  &:before {
    content: $icon-language;
  }
}
.icon-message-success {
  &:before {
    content: $icon-message-success;
  }
}
.icon-notification-new {
  &:before {
    content: $icon-notification-new;
  }
}
.icon-notification {
  &:before {
    content: $icon-notification;
  }
}
.icon-plus {
  &:before {
    content: $icon-plus;
  }
}
.icon-success {
  &:before {
    content: $icon-success;
  }
}
.icon-x-bold {
  &:before {
    content: $icon-x-bold;
  }
}
.icon-x {
  &:before {
    content: $icon-x;
  }
}
.icon-location {
  &:before {
    content: $icon-location;
  }
}
.icon-clock {
  &:before {
    content: $icon-clock;
  }
}
.icon-download {
  &:before {
    content: $icon-download;
  }
}
.icon-chevron-up {
  &:before {
    content: $icon-chevron-up;
  }
}
.icon-camera {
  &:before {
    content: $icon-camera;
  }
}
.icon-connect-fail {
  &:before {
    content: $icon-connect-fail;
  }
}
.icon-repair {
  &:before {
    content: $icon-repair;
  }
}
.icon-sound {
  &:before {
    content: $icon-sound;
  }
}
.icon-exit {
  &:before {
    content: $icon-exit;
  }
}
.icon-settings {
  &:before {
    content: $icon-settings;
  }
}
.icon-cantact {
  &:before {
    content: $icon-cantact;
  }
}
.icon-phone {
  &:before {
    content: $icon-phone;
  }
}

.icon-eye {
  &:before {
    content: $icon-eye;
  }
}

.icon-eye-slash {
  &:before {
    content: $icon-eye-slash;
  }
}
.icon-question {
  &:before {
    content: $icon-question;
  }
}
.icon-rate {
  &:before {
    content: $icon-rate;
  }
}
.icon-warning {
  &:before {
    content: $icon-warning;
  }
}
.icon-spinner8 {
  &:before {
    content: $icon-spinner8;
  }
}
.icon-fiber-new {
  &:before {
    content: $icon-fiber-new;
  }
}
.icon-whatshot {
  &:before {
    content: $icon-whatshot;
    color: #ef2e2e;
  }
}
.icon-priority-high {
  &:before {
    content: $icon-priority-high;
  }
}
.icon-facebook {
  &:before {
    content: $icon-facebook;
  }
}
.icon-twitter {
  &:before {
    content: $icon-twitter;
  }
}
.icon-instagram {
  &:before {
    content: $icon-instagram;
  }
}
.icon-youtube {
  &:before {
    content: $icon-youtube;
  }
}
.icon-linkedin {
  &:before {
    content: $icon-linkedin;
  }
}
.icon-user-permission{
&::before{
  content: $icon-user-permission;
}
}
.icon-dot3{
  &::before{
    content: $icon-dot3;
  }
}
.icon-feet{
  &::before{
    content: $icon-feet;
    color: #ef2e2e;
  }
}
.icon-spinner8{
  &::before{
    content: $icon-spinner8;
  }
}
.icon-notification_settings{
  &::before{
    content: $icon-notification_settings;
  }
}
.icon-persons{
  &::before{
    content: $icon-persons;
  }
}
.icon-videocall{
  &::before{
    content: $icon-videocall;
  }
}
.icon-coins{
  &::before{
    content: $icon-coins;
  }
}
.icon-mobile-verify{
  &::before{
    content: $icon-mobile-verify;
  }
}
.icon-wifi{
  &::before{
    content: $icon-wifi;
  }
}
.icon-external-link{
  &::before{
    content: $icon-external-link;
  }
}
.icon-coupon {
  &:before {
    content: $icon-coupon;
  }
}
.icon-campaign {
  &:before {
    content: $icon-campaign;
  }
}
.icon-discount {
  &:before {
    content: $icon-discount;
  }
}
.icon-tl {
  &:before {
    content: $icon-tl;
  }
}
.icon-usd {
  &:before {
    content: $icon-usd;
  }
}
.icon-euro {
  &:before {
    content: $icon-euro;
  }
}
.icon-ruble {
  &:before {
    content: $icon-ruble;
  }
}
.icon-tenge {
  &:before {
    content: $icon-tenge;
  }
}
.icon-manat {
  &:before {
    content: $icon-manat;
  }
}
.icon-lari {
  &:before {
    content: $icon-lari;
  }
}
.icon-equipment {
  &:before {
    content: $icon-equipment;
  }
}
.icon-company {
  &:before {
    content: $icon-company;
  }
}
.icon-home {
  &:before {
    content: $icon-home;
  }
}
.icon-shopping-cart {
  &:before {
    content: $icon-shopping-cart;
  }
}

.icon-recalculate {
  &:before {
    content: $icon-recalculate;
  }
}

.icon-ladybird {
  &:before {
    content: $icon-ladybird;
  }
}

.icon-avatar{
  &::before{
    content: $icon-avatar;
  }
}

.icon-circle-avatar{
  &::before{
    content: $icon-circle-avatar;
  }
}

.icon-share{
  &::before{
    content: $icon-share;
  }
}

.icon-calendar{
  &::before{
    content: $icon-calendar;
  }
}

.icon-video-camera{
  &::before{
    content: $icon-video-camera;
  }
}

.icon-chev-left{
  &::before{
    content: $icon-chev-left;
  }
}

.icon-yellow-funnel{
  &::before{
    content: $icon-yellow-funnel;
  }
}

.icon-chatblock{
  &::before{
    content: $icon-chatblock;
  }
}

.icon-rebuild{
  &::before{
    content: $icon-rebuild;
  }
}

.icon-go-to-equipment{
  &::before{
    content: $icon-go-to-equipment;
  }
}

.icon-enter-smu {
  &::before {
    content: $icon-enter-smu;
  }
}

.icon-description {
  &::before {
    content: $icon-description;
  }
}

.icon-excavator {
  &::before {
    content: $icon-excavator;
  }
}

.icon-stu-risk {
  &::before {
    content: $icon-stu-risk;
  }
}

.icon-pcc-risk {
  &::before {
    content: $icon-pcc-risk;
  }
}

.icon-reminder-clock {
  &::before {
    content: $icon-reminder-clock;
  }
}

.icon-signal {
  &::before {
    content: $icon-signal;
  }
}

.icon-dna {
  &::before {
    content: $icon-dna;
  }
}

.icon-smu-reading {
  &::before {
    content: $icon-smu-reading;
  }
}

.icon-service-letters {
  &::before {
    content: $icon-service-letters;
  }
}

.icon-inspection-reminder {
  &::before {
    content: $icon-inspection-reminder;
  }
}
