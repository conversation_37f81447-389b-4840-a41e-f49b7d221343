*:not(input):not(textarea) {
  -webkit-user-select: none; /* disable selection/Copy of UIWebView */
  -webkit-touch-callout: none; /* disable the IOS popup when long-press on a link */
}
*:focus {
  outline: none !important;
}

button {
  padding: 1px 6px;
  color: #000
}

.font-weight-medium {
  font-weight: 500;
}

.font-weight-semi-bold {
  font-weight: 600;
}

.font-size-10px {
  font-size: 10px;
}

.font-size-11px {
  font-size: 11px;
}

.font-size-12px {
  font-size: 12px;
}

.font-size-13px {
  font-size: 13px;
}

.font-size-14px {
  font-size: 14px;
}

.font-size-16px {
  font-size: 16px;
}
.font-size-18px {
  font-size: 18px;
}

.font-size-22px {
  font-size: 22px;
}



.inner-container {
  padding: 0 22px;
  overflow-y: scroll;
  height: calc(100vh - 64px);
}

body{
  overflow: hidden;
}

.fit-body {
  img {
    max-width: 100%;
  }
}

.spinner{
  animation: spinner 0.75s linear infinite;
}

@keyframes spinner {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.hidden {
  display: none !important;
}
