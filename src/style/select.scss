$ng-select-highlight: #007eff;
$ng-select-primary-text: #868686;
$ng-select-disabled-text: #f9f9f9;
$ng-select-border: transparent;
$ng-select-border-radius: 16px;
$ng-select-bg: #EEF2F4;
$ng-select-selected: lighten($ng-select-highlight, 46);
$ng-select-marked: lighten($ng-select-highlight, 48);
$ng-select-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);
$ng-select-placeholder: #868686;
$ng-select-height: 50px;
$ng-select-value-padding-left: 20px;
$ng-select-value-font-size: 1rem;

@import "~@ng-select/ng-select/scss/default.theme";
@import "font/icon-font";

.ng-select {
  .ng-select-container {
    border-radius: 6px;
    border: 1px solid #d7e5ea;

    .ng-value-container {
      .ng-input {
        top: 12px !important;
      }
    }
    .ng-clear-wrapper{
      display: none;
    }

    .ng-arrow-wrapper {
      width: 28px;
      padding-right: 16px;

      .ng-arrow {
        @extend .icon;
        @extend .icon-chevron-down;
        font-size: 12px;
        color: #303030;
        border-width: 0;
        width: 12px;
        height: 12px;
      }
    }

  }

  &.clearable {
    .ng-select-container {
      .ng-clear-wrapper {
        display: unset;
      }
    }
  }


  .ng-dropdown-panel {
    &.ng-select-bottom {
      border-bottom-right-radius: 6px;
      border-bottom-left-radius: 6px;

      .ng-dropdown-panel-items {
        .ng-option {
          &:last-child {
            border-bottom-right-radius: 6px;
            border-bottom-left-radius: 6px;
          }
        }
      }
    }

    &.ng-select-top {
      border-top-right-radius: 6px;
      border-top-left-radius: 6px;

      .ng-dropdown-panel-items {
        .ng-option {
          &:first-child {
            border-top-right-radius: 6px;
            border-top-left-radius: 6px;
          }
        }
      }
    }

    @include rtl {
      direction: rtl;
      text-align: right;
    }
  }
}
