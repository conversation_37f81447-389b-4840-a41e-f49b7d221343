function urlSeperator() {
  const pathname = decodeURI(window.location.pathname);
  const hostname = decodeURI(window.location.hostname);
  const sepereted = pathname.split('/').filter((x) => x != '');
  const specialList = ['cart', 'orders', 'wishlist', 'saved-carts', 'address-book', 'login'];

  if (sepereted.length <= 1) {
    return {
      'scope'  : 'Home_View',
      'url'     : pathname,
      'hostname': hostname
    };
  }

  if (sepereted[sepereted.length-1] === 'cart') {
    return {
      'scope'  : 'Cart_View',
      'url'     : pathname,
      'hostname': hostname
    };
  }

  if (sepereted.length < 3 && specialList.some(r => sepereted.indexOf(r) >= 0)) {
    return {
      'scope'  : 'Boom_Shop_Page_Change',
      'page'    : sepereted[sepereted.length - 1],
      'url'     : pathname,
      'hostname': hostname
    };
  }
  if (sepereted.length > 6) {
    return {
      'scope'  : sepereted[sepereted.length - 2] === 'p' ? 'Boom_Shop_Product_View' : sepereted[sepereted.length - 2] === 'c' ? 'Boom_Shop_Category_View' : 'Boom_Shop_Other_View',
      'category': sepereted[2],
      'model'   : sepereted[4],
      'number'  : sepereted[sepereted.length - 1],
      'url'     : pathname,
      'hostname': hostname
    };
  }
  return {
    'url'     : pathname,
    'hostname': hostname
  };
}

function logCommerceAction(event, data) {
  console.log(':::EVENT', {
    event: event, data: data
  });
}

/// add to cart
function handleAddToCartLog() {
  window.reservedAddToCartService = ACC.borusanProductDetail.services.addToCartService;
  ACC.borusanProductDetail.services.addToCartService = function(params) {
    console.log(':::: params', params);
    logCommerceAction('addToCart', params);
    return window.reservedAddToCartService(params);
  };
}


function handleChangeCartLog() {
// change cart
  $('#updateCartForm0').on('submit', function($event) {
    var data = {};
    $('#updateCartForm0').serializeArray().map(function(x) {
      data[x.name] = x.value;
    });
    logCommerceAction('updateCart', data);
    console.log('SUBMIT', data);
  });
}



function handleMenuClickLog() {
// MEnu tiklamalari
  $('.js-footer-nav-item').click(function() {
    var _t = $(this);
    var type = _t.data('type');

    if (_t.data('url') != undefined) {
      console.log('::: TO URL', _t.data('url'));
      // window.location = _t.data('url');
      return;
    } else {
      console.log(':::type', type);
      logCommerceAction('menu_click', type);
    }
  });
}

function handleAppUrlLog() {

  $(document).ready(function() {
    console.log('URL', urlSeperator());
    logCommerceAction('page_opened', urlSeperator());
  });
}

handleMenuClickLog();
handleAddToCartLog();
handleChangeCartLog();
handleAppUrlLog();

