(function() {
  var a = '/lgnp/api';

  function n(encoded) {
    if (typeof window.handlePostMessage !== 'undefined') {
      window.handlePostMessage(encoded);
    } else {
      (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
    }
  }

  function shiftParam(queryString, key) {
    var remaining = [];
    var value = null;
    var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
    for (var i = 0; i < pairs.length; i++) {
      var pair = pairs[i].split('=');
      if (decodeURIComponent(pair[0]) === key) {
        value = decodeURIComponent(pair[1]);
      } else {
        remaining.push(pairs[i]);
      }
    }
    return [value, remaining.join('&')];
  }

  function uuidv4() {
    return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, c =>
      (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
    );
  }

  function xott(ott, appSessionId, onDone, onError) {
    var xhr = new XMLHttpRequest();
    xhr.open('GET', a + (ll ? '/liteuser/xott?ott=' : '/user/xott?ott=') + ott);
    xhr.responseType = 'json';
    xhr.setRequestHeader('ReqId', uuidv4());
    if (appSessionId) {
      xhr.setRequestHeader('AppSessionId', appSessionId);
    }

    xhr.onload = function() {
      if (xhr.status !== 200) {
        return onError(xhr.response);
      }
      var responseObj = xhr.response;

      return onDone(responseObj);
    };
    xhr.send();
  }


  function emitMessage(message) {
    const w = window;
    if (typeof w.Borusan !== 'undefined' && w.Borusan?.postMessage) {
      console.log('sent message to APP', message);
      w.Borusan.postMessage(message);
    } else if (w.flutter_inappwebview) {
      console.log('sent message to APP', message);
      w.flutter_inappwebview.callHandler('Borusan', message)
        .then(result => {
          console.log('message delivered');
        });
    } else {
      console.log('sent message FAIL', message);
    }
  }

  //handle mobile info
  const rawMobileInfo = (window.postMessageQueue || []).find(item => {
    return typeof item === 'string' && (JSON.parse(item) || {}).type === 'mobileInfo';
  });
  var mobileInfo = rawMobileInfo ? (JSON.parse(rawMobileInfo) || {}).data : null;
  const appSessionId = mobileInfo ? mobileInfo.firebaseToken : null;
  // End of handle mobile info

  var s = window.location.search;
  var ll = s.indexOf('lott') !== -1;
  if (s && (s.indexOf('ott') !== -1 || ll)) {
    var [ott, query] = !ll ? shiftParam(s, 'ott') : shiftParam(s, 'lott');

    if (!ott) {
      return;
    }
    window.history.replaceState(null, '', window.location.pathname + (query.length ? '?' + query : ''));

    n(JSON.stringify({
      type: 'loginStarted',
      data: new Date().getTime(),
    }));

    xott(ott, appSessionId, function(response) {
      n(JSON.stringify({
        type: ll ? 'liteLoginResponse' : 'loginResponse',
        data: {
          success      : true,
          loginResponse: response.data,
        },
      }));
    }, function(response) {
      n(JSON.stringify({
        type: 'loginResponse',
        data: {
          success      : false,
          loginResponse: response,
        },
      }));
    });
  }

  // emit getMobileInfo
  emitMessage(JSON.stringify({
    function: 'getMobileInfo',
    data: {}
  }));
})();
