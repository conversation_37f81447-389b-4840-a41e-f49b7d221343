console.log('message pipe handler loaded');
// window.postMessageHandlers = window.postMessageHandlers || {
//   initialData: (data) => {
//     console.log('initialData',data);
//   },
// };

function sendMessage(action, data) {
  var message = {
    function: action, data: data
  };
  emitMessage(JSON.stringify(message));
}

function emitCallHandler(message) {
  window.flutter_inappwebview.callHandler('Borusan', message)
    .then(function(result) {
      console.log('message delivered');
    });
}

function emitMessage(message) {
  if (typeof window.flutter_inappwebview !== 'undefined') {
    console.log('sent message to APP [action]', message);
    if (typeof window.flutter_inappwebview.callHandler !== 'function') {
      window.addEventListener('flutterInAppWebViewPlatformReady', function() {
        emitCallHandler(message);
      });
      return;
    }
    emitCallHandler(message);
  } else {
    console.log('sent message to flutter FAIL [action]', message);
  }
}

function messageHandler(message) {
  console.log('RECEIVED message from APP [action] ' + message);
  try {
    var event = JSON.parse(message);
    if (!event.type) {
      return;
    }

    if (typeof window.postMessageHandlers[event.type] === 'function') {
      window.postMessageHandlers[event.type](event.data);
    } else {
      console.log('ERROR INCOMING EVENT NOT FOUND [action]: ' + event.type);
    }

  } catch (e) {
    console.log('handle message error: ' + e.message);
  }
}

function managePostMessage() {
  var w = window;
  w.handlePostMessage = messageHandler;
  if (typeof w.postMessageQueue !== 'undefined' && w.postMessageQueue && w.postMessageQueue.length > 0) {
    while (w.postMessageQueue.length) {
      messageHandler(w.postMessageQueue.shift());
    }
  }
}

function boom360AddEventListener(eventName, callback) {
  (window.postMessageHandlers = window.postMessageHandlers || {})[eventName] = callback;
}

function log360(data) {
  return sendMessage('boom.logPipe', data);
}

function logAction(section, subsection, logData) {
  return log360({
    section: section, subsection: subsection, logData: logData,
  });
}

function sendCallPhoneAction(phoneNumber) {
  return sendMessage('call_phone', { phone: phoneNumber });
}

function close360WindowAction(logData) {
  return sendMessage('backToMain', logData);
}

managePostMessage();

boom360AddEventListener('initialData', function(data) {
  console.log('data', data);
  setCache(data);
});

window.addEventListener('flutterInAppWebViewPlatformReady', function() {
  sendMessage('getInitialData');
});

// function n(encoded) {
//   if (typeof window.handlePostMessage !== 'undefined') {
//     window.handlePostMessage(encoded);
//   } else {
//     (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
//   }
// }

function setCache(data) {
  localStorage.setItem('BOOM360', JSON.stringify(data));
}


