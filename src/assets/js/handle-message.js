// window.postMessageHandlers = window.postMessageHandlers || {
//   initialData: (data) => {
//     console.log('initialData',data);
//   },
// };
let lastActionTimeStamp = Date.now();

function sendMessage(action, data) {
  var message = {
    function: action, data: data
  };
  emitMessage(JSON.stringify(message));
}

function emitCallHandler(message) {
  window.flutter_inappwebview.callHandler('Borusan', message)
    .then(function(result) {
      console.log('message delivered');
    });
}

function emitMessage(message) {
  if (typeof window.flutter_inappwebview !== 'undefined') {
    console.log('sent message to APP', message);
    if (typeof window.flutter_inappwebview.callHandler !== 'function') {
      window.addEventListener('flutterInAppWebViewPlatformReady', function() {
        emitCallHandler(message);
      });
      return;
    }
    emitCallHandler(message);
  } else {
    console.log('sent message to flutter FAIL', message);
  }
}

function messageHandler(message) {
  console.log('RECEIVED message from APP ' + message);
  try {
    var event = JSON.parse(message);
    if (!event.type) {
      return;
    }

    if (typeof window.postMessageHandlers[event.type] === 'function') {
      window.postMessageHandlers[event.type](event.data);
    } else {
      console.log('ERROR INCOMING EVENT NOT FOUND: ' + event.type);
    }

  } catch (e) {
    console.log('handle message error: ' + e.message);
  }
}

function managePostMessage() {
  var w = window;
  w.handlePostMessage = messageHandler;
  if (typeof w.postMessageQueue !== 'undefined' && w.postMessageQueue && w.postMessageQueue.length > 0) {
    while (w.postMessageQueue.length) {
      messageHandler(w.postMessageQueue.shift());
    }
  }
}

function boom360AddEventListener(eventName, callback) {
  (window.postMessageHandlers = window.postMessageHandlers || {})[eventName] = callback;
}

function appMenu(modules) {
  var menu = document.querySelectorAll('div.app-modules ul')[1];
  // var menu = document.querySelector('div#app-remote-modules');
  if (!menu) {
    console.log('menu not found');
    return;
  }
  menu.innerHTML = '';

  for (var i = 0; i < modules.length; i++) {
    var module = modules[i];
    if (module.code === 'sparePart') {
      continue;
    }
    var element = document.createElement('li');
    element.addEventListener('click', (function(module) {
      return function() {
        sendMessage('shopModuleClick', module);
      };
    })(module));
    // element.innerHTML = '<a href="#">' + modules[i] + '</a>';

    // Dynamic Background Image
    var bgImageLink = 'https://prod.borusancat.com/lgnp/api/image/module?moduleid=';
    if (module.url.substring(0, module.url.indexOf('mobileview'))) {
      bgImageLink = module.url.substring(0, module.url.indexOf('mobileview'))
        + 'api/image/module?moduleid=';
    }
    console.log('Handle Message, Background Image url: ' + bgImageLink);

    element.innerHTML =
      '<div class="image-area service" style="background-image: url(\''
      + bgImageLink
      + module.id
      + '\')"></div><div class="title">' + module.name + '</div>';
    menu.append(element);

    // Static Background Image
    // element.innerHTML =
    //   '<div class="image-area service" style="background-image: url(\'https://prod.borusancat.com/lgnd/api/image/module?moduleid='
    //   + module.id + '\')"></div><div class="title">' + module.name + '</div>';
    // menu.append(element);
  }
}

window.addEventListener('flutterInAppWebViewPlatformReady', function() {
  sendMessage('getInitialData');
});

// function n(encoded) {
//   if (typeof window.handlePostMessage !== 'undefined') {
//     window.handlePostMessage(encoded);
//   } else {
//     (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
//   }
// }

var interval = null;

function intervalSessionCheck() {
  if (!ACC.borusanValidator.isNullOrEmpty(ACC.mobileUser) && ACC.mobileUser == 'true') {
    if (!ACC.borusanValidator.isNullOrEmpty(ACC.expiryTime)) {
      var time = Number(ACC.expiryTime);
      console.log('interval-time: ' + time);
      interval = setInterval(function() {
        sendMessage('shopJwtTokenExpired', null);
      }, time);
    }
  }
}

function unauthorizedClose() {
  if (!ACC.borusanValidator.isNullOrEmpty(ACC.mobileUserDisabled) && ACC.mobileUserDisabled == 'true') {
    sendMessage('eCommerceUnauthorized', null);
  }
}

$(document).ready(function() {
  unauthorizedClose();
  intervalSessionCheck();
});

(window.postMessageHandlers = window.postMessageHandlers || {}).shopJwtTokenChanged = function(data) {
  var params = { message: data };
  var url = ACC.config.encodedContextPath + '/borusan/web-view/extendSessionTime';
  ACC.borusanApiService.post(url, params).done(function() {
    clearInterval(interval);
    location.reload();
  });
};

function log360(data) {
  return sendMessage('boom.logEcommerce', data);
}

function logAction(section, subsection, logData) {
  return log360({
    section: section, subsection: subsection, logData: logData,
  });
}

function openVideoCall(sourceUrl) {
  let now = Date.now();
  if ((now - lastActionTimeStamp) < 1000) {
    logAction('E-COMMERCE', 'EVENT_REPLY_BLOCKED', {
      lastActionSentTimeStamp: lastActionTimeStamp,
      nowActionTimeStamp     : now,
    });
    return;
  }
  lastActionTimeStamp = Date.now();
  sendMessage('boom.openVideoCall', {
    source   : 'boomShop',
    sourceUrl: sourceUrl || window.location.pathname
  });

  close360WindowAction(null);
}

function close360WindowAction(logData) {
  window.history.go(-(window.history.length - 1));
  setTimeout(function() {
    sendMessage('backToMain', logData);
  }, 1);
}

function downloadFileBase64(data) {
  return sendMessage('boom.openTeklif', data);
}

/// ## logs
function urlSeperator() {
  const pathname = decodeURI(window.location.pathname);
  const hostname = decodeURI(window.location.hostname);
  const sepereted = pathname.split('/').filter((x) => x != '');
  const specialList = ['cart', 'orders', 'wishlist', 'saved-carts', 'address-book', 'login'];

  if (sepereted.length <= 1) {
    return {
      'scope'   : 'home_view',
      'url'     : pathname,
      'hostname': hostname
    };
  }

  if (sepereted[sepereted.length - 1] === 'cart') {
    return {
      'scope'   : 'cart_view',
      'url'     : pathname,
      'hostname': hostname
    };
  }

  if (sepereted.length < 3 && specialList.some(r => sepereted.indexOf(r) >= 0)) {
    return {
      'scope'   : 'page_change',
      'page'    : sepereted[sepereted.length - 1],
      'url'     : pathname,
      'hostname': hostname
    };
  }
  if (sepereted.length > 6) {
    return {
      'scope'   : sepereted[sepereted.length - 2] === 'p' ? 'product_view' :
        (sepereted[sepereted.length - 2] === 'c' ? 'category_view' : 'other_view'),
      'category': sepereted[2],
      'model'   : sepereted[4],
      'number'  : sepereted[sepereted.length - 1],
      'url'     : pathname,
      'hostname': hostname
    };
  }
  return {
    'scope'   : 'default',
    'url'     : pathname,
    'hostname': hostname
  };
}

function logCommerceAction(event, data) {
  logAction('E-COMMERCE', event, data);
}

/// add to cart
function handleAddToCartLog() {
  window.reservedAddToCartService = ACC.borusanProductDetail.services.addToCartService;
  ACC.borusanProductDetail.services.addToCartService = function(params) {
    // console.log(':::: params', params);
    logCommerceAction('ADD_TO_CART', params);
    return window.reservedAddToCartService(params);
  };
}

function handleChangeCartLog() {
// change cart
  $('#updateCartForm0').on('submit', function($event) {
    var data = {};
    $('#updateCartForm0').serializeArray().map(function(x) {
      data[x.name] = x.value;
    });
    logCommerceAction('UPDATE_CART', data);
    console.log('SUBMIT', data);
  });
}

function handleMenuClickLog() {
// MEnu tiklamalari
  $('.js-footer-nav-item').click(function() {
    var _t = $(this);
    var type = _t.data('type');

    if (_t.data('url') != undefined) {
      // console.log('::: TO URL', _t.data('url'));
      // window.location = _t.data('url');
      return;
    } else {
      console.log(':::MENU_CLICK type', type);
      logCommerceAction('MENU_CLICK', { scope: type });
    }
  });
}

function handleAppUrlLog() {

  $(document).ready(function() {
    // console.log('URL', urlSeperator());
    logCommerceAction('PAGE_OPENED', urlSeperator());
  });
}

function setCache(data) {
  localStorage.setItem('BOOM360', JSON.stringify(data));
}

managePostMessage();

boom360AddEventListener('initialData', function(data) {
  var modules = data.currentCustomer.modules;
  if (!modules) {
    console.log('no module found');
  }
  appMenu(modules);
  if (data.meta) {
    setCache({
      meta:data.meta,
    });
  }
});

handleMenuClickLog();
handleAddToCartLog();
handleChangeCartLog();
handleAppUrlLog();
