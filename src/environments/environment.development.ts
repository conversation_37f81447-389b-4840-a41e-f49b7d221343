export const environment = {
  production: true,
  envName: 'development',
  api: '/lgnd/api',
  imageApi: 'https://borusancat360devm.azureedge.net/api',
  assets: 'https://borusancat360devm.azureedge.net/portaluimobile/assets',
  language: 'https://prod.borusancat.com/lgnd/portaluimobile/assets',
  rootUrl: '',
  appUrl: 'https://prod.borusancat.com/lgnd/portaluimobile',
  frameUrl: 'https://prod.borusancat.com/lgnd/mobileview',
  versionCheckURL: '/lgnd/portaluimobile/version.json',
  logUrl: 'https://cateventlog.azurewebsites.net/logpost/boom-development',
  signinCatUrl: 'https://signin.cat.com/cwslogin.onmicrosoft.com/B2C_1A_P2_V1_SIGNIN_PROD/oauth2/v2.0/authorize?client_id=00dd9028-38b9-4b08-8286-ee9e175b13bb&nonce=defaultNonce&scope=openid&response_type=code&domain_hint=Borusan_direct&redirect_uri=https://prod.borusancat.com/lgnd/portaluimobile/auth/sso',
  ssoUrl: '/weblogin?language=DYNAMICLANG&ru=https%3A%2F%2Fprod.borusancat.com%2Flgnd%2Fportaluimobile%2Fauth%2Fsso',
};
