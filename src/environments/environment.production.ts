export const environment = {
  production: true,
  envName: 'production',
  api: '/lgnp/api',
  imageApi: 'https://borusancat360prodm.azureedge.net/api',
  assets: 'https://borusancat360prodm.azureedge.net/portaluimobile/assets',
  language: 'https://prod.borusancat.com/lgnp/portaluimobile/assets',
  rootUrl: '',
  appUrl: 'https://prod.borusancat.com/lgnp/portaluimobile',
  frameUrl: 'https://prod.borusancat.com/lgnp/mobileview',
  versionCheckURL: '/lgnp/portaluimobile/version.json',
  logUrl: 'https://cateventlog.azurewebsites.net/logpost/boom-prod',
  signinCatUrl: 'https://signin.cat.com/cwslogin.onmicrosoft.com/B2C_1A_P2_V1_SIGNIN_PROD/oauth2/v2.0/authorize?client_id=00dd9028-38b9-4b08-8286-ee9e175b13bb&nonce=defaultNonce&scope=openid&response_type=code&redirect_uri=https://prod.borusancat.com/lgnp/portaluimobile/auth/sso&domain_hint=Borusan_direct',
  ssoUrl: '/weblogin?language=DYNAMICLANG&ru=https%3A%2F%2Fprod.borusancat.com%2Flgnp%2Fportaluimobile%2Fauth%2Fsso',
};
