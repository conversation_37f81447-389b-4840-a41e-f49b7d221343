<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="color-scheme" content="light only" />
  <title>Mainui</title>
  <base href="/" />
  <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, viewport-fit=cover" />
  <link rel="icon" type="image/x-icon" href="favicon.ico" />
  <script type="application/javascript">
    !function(){function e(e){void 0!==window.handlePostMessage?window.handlePostMessage(e):(window.postMessageQueue=window.postMessageQueue||[]).push(e)}let t=(window.postMessageQueue||[]).find(e=>"string"==typeof e&&"mobileInfo"===(JSON.parse(e)||{}).type);var s=t?(JSON.parse(t)||{}).data:null;let n=s?s.firebaseToken:null;var o=window.location.search,a=-1!==o.indexOf("lott");if(o&&(-1!==o.indexOf("ott")||a)){var i,l,r,u,p,[$,g]=function e(t,s){for(var n=[],o=null,a=("?"===t[0]?t.substr(1):t).split("&"),i=0;i<a.length;i++){var l=a[i].split("=");decodeURIComponent(l[0])===s?o=decodeURIComponent(l[1]):n.push(a[i])}return[o,n.join("&")]}(o,a?"lott":"ott");if(!$)return;window.history.replaceState(null,"",window.location.pathname+(g.length?"?"+g:"")),e(JSON.stringify({type:"loginStarted",data:new Date().getTime()})),i=$,l=n,r=function(t){e(JSON.stringify({type:a?"liteLoginResponse":"loginResponse",data:{success:!0,loginResponse:t.data}}))},u=function(t){e(JSON.stringify({type:"loginResponse",data:{success:!1,loginResponse:t}}))},(p=new XMLHttpRequest).open("GET","/lgnq/api"+(a?"/liteuser/xott?ott=":"/user/xott?ott=")+i),p.responseType="json",p.setRequestHeader("ReqId","10000000-1000-4000-8000-100000000000".replace(/[018]/g,e=>(e^crypto.getRandomValues(new Uint8Array(1))[0]&15>>e/4).toString(16))),l&&p.setRequestHeader("AppSessionId",l),p.onload=function(){return 200!==p.status?u(p.response):r(p.response)},p.send()}!function e(t){let s=window;void 0!==s.Borusan&&s.Borusan?.postMessage?(console.log("sent message to APP",t),s.Borusan.postMessage(t)):s.flutter_inappwebview?(console.log("sent message to APP",t),s.flutter_inappwebview.callHandler("Borusan",t).then(e=>{console.log("message delivered")})):console.log("sent message FAIL",t)}(JSON.stringify({function:"getMobileInfo",data:{}}))}();
  </script>
  <!-- Global site tag (gtag.js) - Google Analytics -->
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-X6127QMK0J"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag() { dataLayer.push(arguments); }
    gtag('js', new Date());
    gtag('config', 'G-X6127QMK0J');
  </script>
  <script>
    if (typeof window.Borusan != "undefined" && typeof window.Borusan.postMessage != "undefined") {
      console.log('MESSAGE SENDED - Get Mobile Info');
      window.Borusan.postMessage(JSON.stringify({ function: 'getMobileInfo', data: {} }));
    }
  </script>
  <style>
    body {
      background-color: #ffffff;
    }
    #front-loader {
      z-index: 10000;
      position: absolute;
      left: 50%;
      top: calc(50vh + 29px);
      margin-left: -50px;
      margin-top: -50px;
      border: 16px solid #7f7e7e;
      border-radius: 50%;
      border-top: 16px solid #FFA300;
      border-bottom: 16px solid #FFA300;
      width: 100px;
      height: 100px;
      animation: spin 1s linear infinite;
      box-sizing: border-box;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
  <script type="application/javascript"> document.write('<div id="front-loader"></div>'); </script>

</head>

<body>

  <app-root></app-root>
</body>

</html>
