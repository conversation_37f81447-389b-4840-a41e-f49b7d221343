export const environment = {
  production: true,
  envName: 'stage',
  api: '/lgnq/api',
  imageApi: 'https://borusancat360qam.azureedge.net/api',
  assets: 'https://borusancat360qam.azureedge.net/portaluimobile/assets',
  language: 'https://prod.borusancat.com/lgnq/portaluimobile/assets',
  rootUrl: '',
  appUrl: 'https://prod.borusancat.com/lgnq/portaluimobile',
  frameUrl: 'https://prod.borusancat.com/lgnq/mobileview',
  versionCheckURL: '/lgnq/portaluimobile/version.json',
  logUrl: 'https://cateventlog.azurewebsites.net/logpost/boom-stage',
  signinCatUrl: 'https://signin.cat.com/cwslogin.onmicrosoft.com/B2C_1A_P2_V1_SIGNIN_STAGING/oauth2/v2.0/authorize?client_id=e55d4e38-27e8-4ee3-b42d-25968c95c9a5&nonce=defaultNonce&scope=openid&response_type=code&redirect_uri=https://prod.borusancat.com/lgnq/portaluimobile/auth/sso&domain_hint=Borusan_direct',
  ssoUrl: '/weblogin?language=DYNAMICLANG&ru=https%3A%2F%2Fprod.borusancat.com%2Flgnq%2Fportaluimobile%2Fauth%2Fsso',
};
