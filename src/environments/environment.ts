export const environment = {
  production: false,
  envName: 'local',
  api: '/api',
  imageApi: '/api',
  assets: '/assets',
  language: '/assets',
  rootUrl: '',
  frameUrl: 'http://' + window.location.hostname + ':2287/lgnt/mobileview',
  appUrl: window.location.origin,
  versionCheckURL: '/version.json',
  logUrl: 'https://cateventlog.azurewebsites.net/logpost/boom-development',
  signinCatUrl: 'https://signin.cat.com/cwslogin.onmicrosoft.com/B2C_1A_P2_V1_SIGNIN_STAGING/oauth2/v2.0/authorize?client_id=e55d4e38-27e8-4ee3-b42d-25968c95c9a5&nonce=defaultNonce&scope=openid&response_type=code&redirect_uri=https://prod.borusancat.com/lgnq/portaluimobile/auth/sso&domain_hint=<PERSON>rusan_direct',
  ssoUrl: '/weblogin?language=DYNAMICLANG&ru=https%3A%2F%2Ftrtest.borusancat.com%2Fboom-web%2Flogin-sso',
};
