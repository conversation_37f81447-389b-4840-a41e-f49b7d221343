<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8"/>
  <meta name="color-scheme" content="light only"/>
  <title>Mainui</title>
  <base href="/"/>
  <meta name="viewport"
        content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=0, viewport-fit=cover"/>
  <link rel="icon" type="image/x-icon" href="favicon.ico"/>
  <script type="application/javascript">
    if (window.location.hostname === 'localhost') {
      function n(encoded) {
        if (typeof window.handlePostMessage !== 'undefined') {
          console.log('MESSAGE POSTED');
          window.handlePostMessage(encoded);
        } else {
          console.log('MESSAGE QUEUED');
          (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
        }
      }

      n(JSON.stringify({
        'type': 'mobileInfo',
        'data': {
          'firebaseToken'          : 'fKwAMdN07078qRc_3w4THW:APA91bEjjkUmP9nSRb9AowFYSlGhCY4Q9i364e0iYUhxq9wfR88ScnSHzZHJpuNgVwuxEXKzQjm34htROB_lD-UdxhYmSsCtwQnpAZ12sXxpJk1Jjv8JBSY',
          'userAgent'              : 'Android 8.0.0, local device, BOOM v11.1.1, Dev',
          'version'                : '13.1.1',
          'timezone'               : 'c3c7e191-e7d4-4071-805d-cd11a931d21f',
          'mustChangePassword'     : false,
          'publicMenuHeaderCompany': '32f47d40-f093-4b99-b2d2-799d5b4ea0eb',
          'currentRegion'          : '95e110da-cdbf-49d7-af1e-0f3106c5a2d6',
          'countryCode'            : 'TR',
          'adid'                   : '9f7603668451868d6411b1c970e8b811',
          'adjustToken'            : 'nx64vzxxonwg',
          'storeVersion'           : '10.6.22'
        }
      }));

    }

    (function() {
      var a = '/api';
      function n(encoded) {
        if (typeof window.handlePostMessage !== 'undefined') {
          window.handlePostMessage(encoded);
        } else {
          (window.postMessageQueue = window.postMessageQueue || []).push(encoded);
        }
      }

      function shiftParam(queryString, key) {
        var remaining = [];
        var value = null;
        var pairs = (queryString[0] === '?' ? queryString.substr(1) : queryString).split('&');
        for (var i = 0; i < pairs.length; i++) {
          var pair = pairs[i].split('=');
          if (decodeURIComponent(pair[0]) === key) {
            value = decodeURIComponent(pair[1]);
          } else {
            remaining.push(pairs[i]);
          }
        }
        return [value, remaining.join('&')];
      }

      function uuidv4() {
        return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, c =>
          (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
        );
      }

      function xott(ott, appSessionId, onDone, onError) {
        var xhr = new XMLHttpRequest();
        xhr.open('GET', a + (ll ? '/liteuser/xott?ott=' : '/user/xott?ott=') + ott);
        xhr.responseType = 'json';
        xhr.setRequestHeader('ReqId', uuidv4());
        if (appSessionId) {
          xhr.setRequestHeader('AppSessionId', appSessionId);
        }

        xhr.onload = function() {
          if (xhr.status !== 200) {
            return onError(xhr.response);
          }
          var responseObj = xhr.response;

          return onDone(responseObj);
        };
        xhr.send();
      }


      function emitMessage(message) {
        const w = window;
        if (typeof w.Borusan !== 'undefined' && w.Borusan?.postMessage) {
          console.log('sent message to APP', message);
          w.Borusan.postMessage(message);
        } else if (w.flutter_inappwebview) {
          w.flutter_inappwebview.callHandler('Borusan', message)
            .then(result => {
              console.log('message delivered');
            });
        } else {
          console.log('sent message FAIL I', message);
        }
      }

      //handle mobile info
      const rawMobileInfo = (window.postMessageQueue || []).find(item => {
        return typeof item === 'string' && (JSON.parse(item) || {}).type === 'mobileInfo';
      });
      var mobileInfo = rawMobileInfo ? (JSON.parse(rawMobileInfo) || {}).data : null;
      const appSessionId = mobileInfo ? mobileInfo.firebaseToken : null;
      // End of handle mobile info

      var s = window.location.search;
      var ll = s.indexOf('lott') !== -1;
      if (s && (s.indexOf('ott') !== -1 || ll)) {
        var [ott, query] = !ll ? shiftParam(s, 'ott') : shiftParam(s, 'lott');

        if (!ott) {
          return;
        }
        window.history.replaceState(null, '', window.location.pathname + (query.length ? '?' + query : ''));

        n(JSON.stringify({
          type: 'loginStarted',
          data: new Date().getTime(),
        }));

        xott(ott, appSessionId, function(response) {
          n(JSON.stringify({
            type: ll ? 'liteLoginResponse' : 'loginResponse',
            data: {
              success      : true,
              loginResponse: response.data,
            },
          }));
        }, function(response) {
          n(JSON.stringify({
            type: 'loginResponse',
            data: {
              success      : false,
              loginResponse: response,
            },
          }));
        });
      }

      // emit getMobileInfo
      emitMessage(JSON.stringify({
        function: 'getMobileInfo',
        data: {}
      }));
    })();

  </script>
  <style>
    body {
      background-color: #ffffff;
    }

    #front-loader {
      z-index: 10000;
      position: absolute;
      left: 50%;
      top: calc(50vh + 29px);
      /*top: calc(50% + 29px + env(safe-area-inset-bottom, 0px) / 2);*/
      margin-left: -50px;
      margin-top: -50px;
      border: 16px solid #7f7e7e;
      border-radius: 50%;
      border-top: 16px solid #FFA300;
      border-bottom: 16px solid #FFA300;
      width: 100px;
      height: 100px;
      animation: spin 1s linear infinite;
      box-sizing: border-box;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  </style>
  <script type="application/javascript">
    document.write('<div id="front-loader"></div>');
  </script>
  <!--  <script src="//code.jivosite.com/widget/gSt9NFBSKs" async></script>-->
</head>

<body>

<app-root></app-root>
</body>

</html>
